# 日志清理定时任务

## 功能概述

日志清理定时任务是一个自动化的后台任务，用于定期清理应用日志文件，只保留最近24小时内的日志内容。这有助于：

- 控制日志文件大小，防止磁盘空间被占满
- 提高日志文件的读取性能
- 保持系统的整洁性

## 功能特性

### 1. 智能时间戳识别
支持多种常见的日志时间戳格式：
- `2024-01-01 12:00:00` - 标准格式
- `2024-01-01 12:00:00,123` - 带毫秒（逗号分隔）
- `2024-01-01 12:00:00.123456` - 带微秒（点分隔）
- `2024/01/01 12:00:00` - 斜杠分隔的日期格式
- `01/01/2024 12:00:00` - 美式日期格式

### 2. 安全的备份机制
- 在清理前自动创建备份文件
- 备份文件命名格式：`原文件名.backup.YYYYMMDD_HHMMSS`
- 自动清理旧备份，只保留最近5个备份文件

### 3. 智能内容保留
- 保留24小时内的所有日志
- 保留无法解析时间戳的行（如堆栈跟踪、多行日志等）
- 只有在实际需要清理时才创建备份文件

### 4. 多编码支持
自动检测和处理不同的文件编码：
- UTF-8（优先）
- GBK
- Latin-1

## 配置说明

### 默认配置
```python
# 在 app/main.py 中的配置
setup_log_cleanup_tasks(
    log_file_path="logs/app.log",  # 日志文件路径
    cleanup_interval=86400         # 清理间隔：24小时（86400秒）
)
```

### 自定义配置
可以通过修改 `app/main.py` 中的参数来自定义配置：

```python
# 自定义配置示例
setup_log_cleanup_tasks(
    log_file_path="logs/custom.log",  # 自定义日志文件路径
    cleanup_interval=43200            # 12小时执行一次
)
```

## 使用方法

### 1. 自动运行
日志清理任务在应用启动时自动注册并开始运行，无需手动干预。

### 2. 手动测试
可以使用提供的测试脚本验证功能：

```bash
python test_log_cleanup.py
```

### 3. 手动执行清理
如果需要立即执行日志清理，可以直接调用清理函数：

```python
from app.tasks.log_cleanup_tasks import cleanup_log_files

# 手动清理指定日志文件
results = cleanup_log_files("logs/app.log")
print(results)
```

## 监控和日志

### 任务执行日志
日志清理任务的执行情况会记录在应用日志中，包括：
- 任务开始和完成时间
- 处理的文件路径和行数统计
- 备份文件创建情况
- 任何错误或异常信息

### 示例日志输出
```
2024-01-01 12:00:00 INFO 开始清理日志文件: logs/app.log
2024-01-01 12:00:00 INFO 清理时间点: 2024-01-01 12:00:00
2024-01-01 12:00:00 INFO 原文件行数: 1000
2024-01-01 12:00:00 INFO 已创建备份文件: logs/app.log.backup.20240101_120000
2024-01-01 12:00:00 INFO 日志清理完成: 原始行数=1000, 保留行数=500, 删除行数=500
```

## 故障排除

### 常见问题

1. **文件不存在错误**
   - 确保日志文件路径正确
   - 检查文件权限

2. **编码错误**
   - 任务会自动尝试多种编码格式
   - 如果仍有问题，检查日志文件的实际编码

3. **权限错误**
   - 确保应用有读写日志文件的权限
   - 检查备份目录的写入权限

### 调试方法

1. **查看任务状态**
   ```python
   from app.services.task_manager import task_manager
   jobs = task_manager.get_jobs()
   print(jobs)
   ```

2. **手动执行测试**
   ```bash
   python test_log_cleanup.py
   ```

3. **检查应用日志**
   查看应用日志中关于日志清理任务的相关信息。

## 注意事项

1. **备份重要性**：虽然任务会自动创建备份，但建议定期备份重要日志到其他位置。

2. **磁盘空间**：确保有足够的磁盘空间来存储备份文件。

3. **时间同步**：确保服务器时间准确，以便正确识别24小时内的日志。

4. **性能影响**：对于非常大的日志文件，清理过程可能需要一些时间，建议在低峰期执行。

## 扩展功能

如果需要扩展功能，可以考虑：

1. **多文件支持**：修改任务以支持清理多个日志文件
2. **压缩备份**：将备份文件压缩以节省空间
3. **远程备份**：将备份文件上传到云存储
4. **自定义保留期**：支持不同的日志保留时间
5. **日志轮转**：集成更复杂的日志轮转策略 