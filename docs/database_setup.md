# 数据库初始化指南

本文档介绍如何初始化和设置 wecom_bridge 数据库。

## 概述

项目启动时会自动检查并创建必要的数据库和表：
- 数据库：`wecom_bridge`
- 表：`bili_users`, `comment_records`, `comment_replies`, `video_details`

## 自动初始化

### 应用启动时自动检查

当应用启动时，会自动执行以下检查：

1. **检查数据库是否存在**
   - 如果 `wecom_bridge` 数据库不存在，会自动创建
   - 使用 UTF8MB4 字符集和 unicode_ci 排序规则

2. **检查表是否存在**
   - 检查所有必需的表是否存在
   - 如果缺少表，会使用 SQLAlchemy 模型自动创建

3. **执行SQL文件（可选）**
   - 如果存在 `sql/table.sql` 文件，会执行其中的SQL语句
   - 用于创建额外的索引、约束等

## 手动初始化

### 使用初始化脚本

```bash
# 在项目根目录下运行
python scripts/init_database.py
```

### 使用Python模块

```python
from app.utils.db_init import init_database

# 基本初始化（只检查数据库和表）
success = init_database()

# 包含SQL文件的完整初始化
success = init_database("sql/table.sql")
```

## 配置要求

确保以下环境变量已正确配置：

```bash
# 数据库配置
DB_HOST=shared-mysql        # MySQL主机地址
DB_PORT=3306               # MySQL端口
DB_USER=root               # MySQL用户名
DB_PASSWORD=rootpassword   # MySQL密码
DB_NAME=wecom_bridge       # 数据库名称
```

## 数据库结构

### bili_users 表
B站用户信息表
- `id`: 主键ID
- `user_id`: B站用户ID（唯一）
- `username`: 用户名
- `avatar_url`: 头像URL
- `profile_url`: 主页URL
- `description`: 用户描述
- `machine_code`: 关联的机器码
- `machine_name`: 机器名称
- `login_status`: 登录状态
- `last_login_time`: 最后登录时间
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `is_disabled`: 是否禁用

### comment_records 表
评论记录表
- `id`: 内部主键ID
- `source_id`: 评论的唯一ID (rpid)
- `video_id`: 关联的视频ID
- `source_content`: 评论的内容
- `root_id`: 主楼评论的rpid（楼中楼评论）
- `root_reply_content`: 主楼评论的内容
- `target_id`: 回复的评论的rpid
- `target_reply_content`: 回复的评论的内容
- `reply_time`: 回复时间戳
- `uri`: 评论的超链接
- `user_mid`: 回复评论的用户的uid
- `user_nickname`: 回复评论的用户的昵称
- `path`: 评论链路径
- `comment_type`: 评论类型
- `reply_count`: 回复数量
- `like_count`: 点赞数量
- `importance_level`: 重要程度
- `status`: 处理状态
- `is_top`: 是否置顶
- `is_blocked`: 是否屏蔽
- `created_at`: 记录创建时间
- `updated_at`: 记录更新时间

### comment_replies 表
评论回复任务表
- `id`: 回复任务的唯一ID
- `comment_id`: 关联的评论记录ID
- `comment_source_id`: 要回复的评论的source_id
- `reply_content`: 原始回复的内容
- `needs_content_change`: 是否需要变更评论内容
- `changed_reply_content`: 变更后的评论内容
- `status`: 回复任务状态
- `reply_time`: 实际回复时间
- `error_message`: 回复失败时的错误信息
- `created_at`: 任务创建时间
- `updated_at`: 任务更新时间
- `comment_context`: 评论的上下文内容
- `is_satisfied`: 用户是否对回复感到满意

### video_details 表
视频详情表
- `id`: 主键ID
- `video_id`: 视频ID（唯一）
- `title`: 视频标题
- `video_content`: 视频内容描述
- `video_url`: 视频链接
- `user_id`: B站账号ID
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `video_script`: 视频文案
- `video_summary`: 视频总结
- `reply_setting`: 回复设定
- `comment_control_plan`: 控评方案
- `category`: 视频品类
- `promoted_products`: 主推产品（JSON格式）
- `common_questions`: 常见问题
- `is_disabled`: 是否禁用

## 故障排除

### 常见问题

1. **连接失败**
   ```
   检查MySQL服务是否运行
   检查网络连接
   验证主机地址和端口
   ```

2. **权限不足**
   ```
   确保用户有CREATE DATABASE权限
   确保用户有CREATE TABLE权限
   检查用户密码是否正确
   ```

3. **字符集问题**
   ```
   确保MySQL支持UTF8MB4字符集
   检查数据库配置
   ```

### 日志查看

初始化过程中的详细日志会输出到控制台，包括：
- 数据库连接状态
- 数据库创建过程
- 表检查和创建过程
- SQL文件执行过程
- 错误信息和警告

### 手动检查

可以通过MySQL客户端手动检查：

```sql
-- 检查数据库是否存在
SHOW DATABASES LIKE 'wecom_bridge';

-- 检查表是否存在
USE wecom_bridge;
SHOW TABLES;

-- 检查表结构
DESCRIBE bili_users;
DESCRIBE comment_records;
DESCRIBE comment_replies;
DESCRIBE video_details;
```

## 注意事项

1. **数据安全**：初始化过程不会删除现有数据
2. **幂等性**：可以多次运行初始化，不会产生副作用
3. **权限**：确保数据库用户有足够的权限
4. **备份**：在生产环境中，建议先备份现有数据 