# Coze 工作流参数映射文档

## 概述

本文档说明了 Coze 工作流参数与 `VideoDetail` 数据模型字段之间的映射关系。

## 字段映射表

| Coze 工作流参数 | 中文说明 | VideoDetail 模型字段 | 数据类型 | 处理说明 |
|---|---|---|---|---|
| `shipinbiaoti` | 视频标题 | `title` | String(256) | 直接映射 |
| `tuijian` | 产品推荐策略 | `promoted_products` | JSON → String | 需要类型转换 |
| `pinglun` | 视频评论 | *(从消息提取)* | String | 运行时提取 |
| `zhengwen` | 视频文案 | `video_script` | Text | 直接映射 |
| `zongjie` | 视频总结 | `video_summary` | Text | 直接映射 |
| `pinlei` | 所属品类 | `category` | String(50) | 直接映射 |
| `sheding` | 回复设定 | `reply_setting` | Text | 直接映射 |
| `kongping` | 控评方案（非必填） | `comment_control_plan` | Text | 可选字段 |

## 数据类型转换

### promoted_products 字段处理

`promoted_products` 在数据库中存储为 JSON 格式，但工作流需要字符串格式。转换逻辑如下：

```python
# 处理产品推荐数据：将JSON格式转换为字符串
promoted_products = video_details.get("promoted_products", "")
if promoted_products:
    if isinstance(promoted_products, (dict, list)):
        # 如果是JSON对象或数组，转换为字符串
        import json
        try:
            promoted_products_str = json.dumps(
                promoted_products, ensure_ascii=False
            )
        except Exception:
            promoted_products_str = str(promoted_products)
    elif isinstance(promoted_products, str):
        # 如果已经是字符串，直接使用
        promoted_products_str = promoted_products
    else:
        # 其他类型转换为字符串
        promoted_products_str = str(promoted_products)
else:
    promoted_products_str = ""
```

### 评论内容提取

`pinglun` 参数不是从数据库字段获取，而是从传入的消息中提取：

```python
# 从message中提取评论内容
comment = (
    message.split("评论内容：")[1].split("   ")[0]
    if "评论内容：" in message
    else ""
)
```

## 工作流调用示例

```python
payload = {
    "parameters": {
        "shipinbiaoti": video_details["title"],
        "zhengwen": video_details["video_script"],
        "tuijian": promoted_products_str,  # 经过类型转换
        "pinglun": comment,  # 从消息提取
        "zongjie": video_details["video_summary"],
        "pinlei": video_details["category"],
        "sheding": video_details["reply_setting"],
        "kongping": video_details.get("comment_control_plan", ""),
    },
    "workflow_id": "7469697317944967195",
}
```

## 注意事项

1. **数据完整性**：确保所有必需字段在数据库中都有值
2. **类型安全**：`promoted_products` 字段需要特殊处理以确保类型正确
3. **可选字段**：`comment_control_plan` 是可选字段，使用 `.get()` 方法避免 KeyError
4. **编码处理**：JSON 序列化时使用 `ensure_ascii=False` 保持中文字符 