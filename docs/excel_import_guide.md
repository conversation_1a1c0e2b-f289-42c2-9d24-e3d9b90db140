# Excel数据导入指南

本文档介绍如何使用Excel导入脚本将数据导入到video_details表。

## 概述

`scripts/import_excel_data.py` 脚本可以读取根目录下的 `imp_data.xlsx` 文件，并将数据导入到 `video_details` 表中。

## 前置条件

### 1. 安装依赖

确保已安装pandas和openpyxl依赖：

```bash
pip install "pandas>=2.2.0" "openpyxl>=3.1.0"
```

或者更新requirements.txt后重新安装：

```bash
pip install -r requirements.txt
```

### 2. 数据库配置

确保数据库连接配置正确，检查以下环境变量：

```bash
DB_HOST=shared-mysql        # MySQL主机地址
DB_PORT=3306               # MySQL端口
DB_USER=root               # MySQL用户名
DB_PASSWORD=rootpassword   # MySQL密码
DB_NAME=wecom_bridge       # 数据库名称
```

### 3. Excel文件准备

将要导入的Excel文件命名为 `imp_data.xlsx` 并放置在项目根目录下。

## Excel文件格式

脚本支持以下列名（中英文均可）：

| 字段名 | 中文列名 | 英文列名 | 是否必填 | 说明 |
|--------|----------|----------|----------|------|
| 视频ID | 视频ID/BV号 | video_id | 否 | 如果未提供，会从视频链接中提取 |
| 视频标题 | 标题/视频标题 | title | **是** | 视频的标题 |
| 视频内容 | 内容描述/视频内容 | video_content | 否 | 视频内容描述 |
| 视频链接 | 链接/视频链接 | video_url | **是** | 视频的URL地址 |
| 视频文案 | 文案/视频文案 | video_script | 否 | 视频的文案内容 |
| 视频总结 | 总结/视频总结 | video_summary | 否 | 视频内容总结 |
| 回复设定 | 回复设定 | reply_setting | 否 | 评论回复的设定 |
| 控评方案 | 控评方案 | comment_control_plan | 否 | 评论控制方案 |
| 视频品类 | 品类/视频品类 | category | 否 | 视频的分类 |
| 主推产品 | 主推产品 | promoted_products | 否 | 推广的产品，多个用逗号分隔 |
| 常见问题 | 常见问题 | common_questions | 否 | 相关的常见问题 |

### Excel文件示例

```
| 标题 | 视频链接 | 内容描述 | 品类 | 主推产品 |
|------|----------|----------|------|----------|
| 美妆教程视频 | https://www.bilibili.com/video/BV1234567890 | 详细的美妆教程 | 美妆 | 口红,粉底液 |
| 科技产品评测 | https://www.bilibili.com/video/BV0987654321 | 最新手机评测 | 科技 | iPhone15 |
```

## 使用方法

### 1. 直接运行脚本

在项目根目录下运行：

```bash
python scripts/import_excel_data.py
```

### 2. 在虚拟环境中运行

```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 运行脚本
python scripts/import_excel_data.py
```

### 3. 使用Docker环境

如果项目运行在Docker中：

```bash
# 进入容器
docker exec -it <container_name> bash

# 运行脚本
python scripts/import_excel_data.py
```

## 脚本功能

### 数据处理

1. **数据清理**: 自动处理空值、NaN值和空字符串
2. **字段映射**: 支持中英文列名自动映射
3. **产品解析**: 将逗号分隔的产品列表转换为JSON格式
4. **数据验证**: 检查必填字段（标题和视频链接）

### 错误处理

1. **跳过无效行**: 缺少必填字段的行会被跳过
2. **错误记录**: 详细记录每个错误的原因和位置
3. **事务安全**: 每行数据独立处理，单行失败不影响其他行

### 输出信息

脚本会显示详细的导入结果：

```
📊 导入结果:
   总行数: 100
   ✅ 成功: 85
   ⏭️  跳过: 10
   ❌ 失败: 5

⚠️  错误详情:
   - 第 3 行导入失败: 视频ID 'BV123456789' 已存在
   - 第 7 行导入失败: 跳过行：缺少标题
   ...
```

## 注意事项

### 1. 数据重复

- 如果视频ID已存在，该行会导入失败
- 脚本不会覆盖已有数据

### 2. 默认值

- `user_id`: 默认设置为1，可在脚本中修改
- `is_disabled`: 默认设置为True（禁用状态）

### 3. 性能考虑

- 大文件导入可能需要较长时间
- 建议分批导入大量数据

### 4. 数据备份

- 导入前建议备份数据库
- 可以先在测试环境验证

## 故障排除

### 常见错误

1. **文件不存在**
   ```
   ❌ Excel文件不存在: /path/to/imp_data.xlsx
   ```
   解决：确保Excel文件在项目根目录

2. **数据库连接失败**
   ```
   连接数据库时出错: ...
   ```
   解决：检查数据库配置和网络连接

3. **依赖缺失**
   ```
   ModuleNotFoundError: No module named 'pandas'
   ```
   解决：安装pandas和openpyxl依赖

4. **权限问题**
   ```
   Permission denied: ...
   ```
   解决：确保脚本有执行权限

### 调试模式

如需查看详细日志，可以修改脚本中的日志级别：

```python
logging.basicConfig(
    level=logging.DEBUG,  # 改为DEBUG
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

## 扩展功能

### 自定义字段映射

如果Excel文件使用不同的列名，可以修改 `convert_excel_row_to_video_create` 函数中的字段映射：

```python
video_data = {
    'title': clean_data(row.get('自定义标题列名')),
    'video_url': clean_data(row.get('自定义链接列名')),
    # ... 其他字段
}
```

### 批量处理

对于大量数据，可以考虑：

1. 分批读取Excel文件
2. 使用数据库批量插入
3. 添加进度条显示

## 相关文档

- [数据库初始化指南](database_setup.md)
- [API文档](../README.md)
- [视频管理接口](../app/api/routes/videos.py) 