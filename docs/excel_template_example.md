# Excel导入模板示例

## 模板格式

以下是Excel文件的标准格式示例：

### 基本模板（最小字段）

| 标题 | 视频链接 |
|------|----------|
| 美妆教程：如何画出完美底妆 | https://www.bilibili.com/video/BV1234567890 |
| 科技评测：iPhone15 Pro详细测评 | https://www.bilibili.com/video/BV0987654321 |

### 完整模板（所有字段）

| 标题 | 视频链接 | 视频ID | 内容描述 | 品类 | 主推产品 | 文案 | 总结 | 回复设定 | 控评方案 | 常见问题 |
|------|----------|--------|----------|------|----------|------|------|----------|----------|----------|
| 美妆教程：如何画出完美底妆 | https://www.bilibili.com/video/BV1234567890 | BV1234567890 | 详细的底妆教程，适合新手学习 | 美妆 | 粉底液,遮瑕膏,散粉 | 今天教大家如何画出完美底妆... | 本视频介绍了底妆的基本步骤 | 积极回复用户问题，推荐相关产品 | 删除恶意评论，置顶优质评论 | Q1:适合什么肤质？A1:适合所有肤质 |
| 科技评测：iPhone15 Pro详细测评 | https://www.bilibili.com/video/BV0987654321 | BV0987654321 | 全面评测iPhone15 Pro的各项功能 | 科技 | iPhone15 Pro,手机壳,钢化膜 | 今天为大家带来iPhone15 Pro的详细评测... | iPhone15 Pro在摄影和性能方面有显著提升 | 客观回复用户疑问，不做过度营销 | 屏蔽刷屏评论，鼓励理性讨论 | Q1:值得升级吗？A1:看个人需求和预算 |

## 字段说明

### 必填字段
- **标题**: 视频的标题，不能为空
- **视频链接**: 视频的完整URL地址，不能为空

### 可选字段
- **视频ID**: B站视频的BV号，如果不填会尝试从链接中提取
- **内容描述**: 视频内容的详细描述
- **品类**: 视频的分类，如"美妆"、"科技"、"美食"等
- **主推产品**: 推广的产品，多个产品用英文逗号分隔
- **文案**: 视频的详细文案内容
- **总结**: 视频内容的总结
- **回复设定**: 评论回复的策略和设定
- **控评方案**: 评论区管理的方案
- **常见问题**: 相关的常见问题和解答

## 数据格式要求

1. **文本格式**: 所有字段都应该是文本格式
2. **编码**: 建议使用UTF-8编码保存
3. **空值处理**: 空单元格会被自动处理为NULL
4. **特殊字符**: 避免使用特殊字符，如制表符、换行符等
5. **长度限制**: 
   - 标题：最大256字符
   - 视频ID：最大20字符
   - 其他文本字段：建议不超过数据库字段限制

## 创建Excel文件步骤

1. 打开Excel或WPS表格
2. 在第一行输入列标题（可以使用中文或英文）
3. 从第二行开始输入数据
4. 保存为`.xlsx`格式
5. 将文件重命名为`imp_data.xlsx`
6. 放置在项目根目录下

## 注意事项

1. **列标题**: 必须在第一行，脚本会自动识别中英文列名
2. **数据行**: 从第二行开始输入实际数据
3. **空行**: 避免在数据中间插入空行
4. **格式**: 保存为Excel 2007+格式（.xlsx）
5. **文件名**: 必须命名为`imp_data.xlsx`并放在项目根目录

## 常见问题

**Q: 可以使用CSV格式吗？**
A: 目前脚本只支持Excel格式(.xlsx)，不支持CSV。

**Q: 列的顺序重要吗？**
A: 不重要，脚本会根据列名自动识别字段。

**Q: 可以添加额外的列吗？**
A: 可以，脚本会忽略不识别的列。

**Q: 如何处理包含逗号的产品名？**
A: 建议使用中文逗号或分号分隔，或者修改脚本中的分隔符。 