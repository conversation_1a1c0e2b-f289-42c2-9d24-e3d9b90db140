fastapi==0.110.1
uvicorn==0.27.1
pydantic==2.6.3
pydantic-settings==2.2.1
python-multipart==0.0.9  # 用于处理文件上传的表单数据
dependency-injector==4.41.0
httpx==0.26.0
# 保留现有依赖
requests==2.31.0
pycryptodome==3.19.0
markupsafe==2.1.3
# 日志相关
python-json-logger==2.0.7
# 安全相关
cryptography==41.0.5
PyJWT==2.8.0
# 文件处理
python-magic==0.4.27
Pillow==10.1.0
# Redis 客户端
redis==5.0.1
# 数据库相关
sqlalchemy==2.0.40
pymysql==1.1.1
# 测试相关
pytest==7.4.3
pytest-cov==4.1.0
# WSGI服务器 - 用于部署
gunicorn==21.2.0
# 后台任务调度
apscheduler==3.10.4
daemonize==2.5.0
# 发现但未记录的依赖
faker==19.12.0  # 用于生成测试数据
mysql-connector-python==8.0.33
# Excel处理
pandas>=2.2.0  # 支持Python 3.13
openpyxl>=3.1.0  # 用于读取Excel文件 