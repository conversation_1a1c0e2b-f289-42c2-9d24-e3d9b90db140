# 配置指南

## 使用 .env 文件配置（推荐方式）

### 1. 创建 .env 文件

在项目根目录创建 `.env` 文件：

```bash
# 企业微信命令桥接服务配置文件
# 请根据实际环境修改以下配置

# ==================== 应用基础配置 ====================
DEBUG=False
HOST=0.0.0.0
PORT=8000

# ==================== IP访问限制配置 ====================
# 是否启用IP限制（True/False）
ENABLE_IP_RESTRICTION=True

# 允许访问的公网IP地址列表（逗号分隔）
# 请替换为你的真实公网IP地址
ALLOWED_IPS=your.public.ip.address

# 是否允许局域网IP访问（True/False）
# 生产环境建议设置为False，开发环境可以设置为True
ALLOW_LOCAL_ACCESS=False

# ==================== 数据库配置 ====================
DB_HOST=shared-mysql
DB_PORT=3306
DB_USER=root
DB_PASSWORD=rootpassword
DB_NAME=wecom_bridge

# ==================== Redis配置 ====================
REDIS_HOST=shared-redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PREFIX=wecom:

# ==================== 企业微信配置 ====================
# 请填入你的企业微信应用配置
WECOM_TOKEN=your_wecom_token
WECOM_ENCODING_AES_KEY=your_encoding_aes_key
WECOM_CORP_ID=your_corp_id
WECOM_AGENT_ID=your_agent_id
WECOM_SECRET=your_secret

# ==================== Coze API配置 ====================
COZE_API_TOKEN=your_coze_api_token
COZE_BOT_ID=your_bot_id
COZE_USER_ID=user123

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# ==================== 文件上传配置 ====================
UPLOADS_DIR=uploads
MAX_CONTENT_LENGTH=16777216

# ==================== 评论任务配置 ====================
# 评论任务执行间隔配置（单位：秒）
# 设置为0或不设置则禁用对应任务

# 未回复评论处理间隔（推荐：300秒 = 5分钟）
COMMENT_UNREPLIED_INTERVAL=300

# 待回复任务处理间隔（推荐：600秒 = 10分钟）  
COMMENT_PENDING_INTERVAL=600

# 是否启用回复功能（True/False）
IS_REPLY=True

# ==================== 推荐配置说明 ====================
# 1. 开发环境：可以设置较短间隔便于测试
#    COMMENT_UNREPLIED_INTERVAL=60   # 1分钟
#    COMMENT_PENDING_INTERVAL=120    # 2分钟
#
# 2. 生产环境：推荐使用较长间隔避免频繁执行
#    COMMENT_UNREPLIED_INTERVAL=300  # 5分钟
#    COMMENT_PENDING_INTERVAL=600    # 10分钟
#
# 3. 禁用所有任务：设置为0
#    COMMENT_UNREPLIED_INTERVAL=0
#    COMMENT_PENDING_INTERVAL=0

# ==================== 前端静态资源配置 ====================
FRONTEND_STATIC_DIR=app/static
```

### 2. 获取你的公网IP地址

```bash
# 方法1：使用curl
curl ifconfig.me

# 方法2：使用ipinfo.io
curl ipinfo.io/ip

# 方法3：使用httpbin
curl httpbin.org/ip
```

### 3. 配置示例

#### 生产环境配置
```bash
ENABLE_IP_RESTRICTION=True
ALLOWED_IPS=123.456.789.012  # 你的真实公网IP
ALLOW_LOCAL_ACCESS=False
DEBUG=False
LOG_LEVEL=INFO
```

#### 开发环境配置
```bash
ENABLE_IP_RESTRICTION=False  # 或者设置为True并允许局域网
ALLOWED_IPS=123.456.789.012
ALLOW_LOCAL_ACCESS=True
DEBUG=True
LOG_LEVEL=DEBUG
```

#### 测试环境配置
```bash
ENABLE_IP_RESTRICTION=True
ALLOWED_IPS=123.456.789.012,234.567.890.123  # 多个IP用逗号分隔
ALLOW_LOCAL_ACCESS=True
DEBUG=False
LOG_LEVEL=INFO
```

## 配置方式对比

### .env 文件方式（推荐）

**优势：**
- ✅ 配置集中管理，易于维护
- ✅ 支持注释，配置更清晰
- ✅ 不需要重新构建镜像
- ✅ 敏感信息不会被提交到代码仓库
- ✅ 支持环境变量替换
- ✅ 可以快速切换不同环境配置

**使用方法：**
```bash
# 1. 创建.env文件
cp .env.example .env

# 2. 修改配置
vim .env

# 3. 重启应用
docker-compose restart wecom-app
```

### Docker Compose 环境变量方式

**优势：**
- ✅ 配置明确，容器级别隔离
- ✅ 适合容器化部署

**劣势：**
- ❌ 配置分散在多个文件中
- ❌ 修改需要重启容器
- ❌ 敏感信息可能被提交到代码仓库
- ❌ 不支持注释

**使用方法：**
```yaml
# docker-compose.override.yml
services:
  wecom-app:
    environment:
      - ENABLE_IP_RESTRICTION=True
      - ALLOWED_IPS=your.ip.address
      - ALLOW_LOCAL_ACCESS=False
```

## 配置优先级

配置读取优先级（从高到低）：

1. **Docker Compose 环境变量** - 最高优先级
2. **系统环境变量**
3. **.env 文件**
4. **代码中的默认值** - 最低优先级

## 推荐的配置管理方式

### 1. 移除 Docker Compose 中的环境变量

修改 `docker-compose.override.yml`，移除IP限制相关的环境变量：

```yaml
services:
  wecom-app:
    image: 127.0.0.1:5000/wecom-app:latest
    container_name: wecom-bridge-app
    environment:
      - DB_HOST=shared-mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=rootpassword
      - DB_NAME=wecom_bridge
      - REDIS_HOST=shared-redis
      - REDIS_PORT=6379
      # 移除这行：- ENABLE_IP_RESTRICTION=False
    ports:
      - "8000:8000"
    # ... 其他配置
```

### 2. 使用 .env 文件管理所有配置

这样所有配置都在 `.env` 文件中，更容易管理和维护。

### 3. 不同环境使用不同的 .env 文件

```bash
# 开发环境
.env.dev

# 测试环境  
.env.test

# 生产环境
.env.prod
```

然后通过符号链接或复制的方式使用：
```bash
# 切换到生产环境
cp .env.prod .env
```

## 安全注意事项

1. **确保 .env 文件在 .gitignore 中**
2. **不要在代码仓库中提交包含敏感信息的 .env 文件**
3. **定期更换敏感配置（如API密钥）**
4. **生产环境使用强密码和限制性IP配置**

## 快速开始

```bash
# 1. 创建配置文件
cat > .env << 'EOF'
ENABLE_IP_RESTRICTION=True
ALLOWED_IPS=$(curl -s ifconfig.me)
ALLOW_LOCAL_ACCESS=False
DEBUG=False
LOG_LEVEL=INFO
EOF

# 2. 重启应用
docker-compose restart wecom-app

# 3. 查看日志验证
docker logs wecom-bridge-app | grep "IP访问限制配置"
``` 