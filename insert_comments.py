#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量插入评论数据到wecom_bridge数据库的脚本
"""

import mysql.connector
from mysql.connector import Error
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库配置 - 请根据实际情况修改
DB_CONFIG = {
    'host': 'localhost',
    'database': 'wecom_bridge',
    'user': 'root',
    'password': 'rootpassword',
    'charset': 'utf8mb4',
    'autocommit': False
}

# 评论内容列表
COMMENTS = [
    "讯优x7的防蹭网功能强吗",
    "现在只有3000g大套餐了，请问哪里可以买到1500g小套餐的x7.啊？"
    "我先点赞投个币先",
    "怎么计费的？",
    "讯优x7视频里的套餐没找到啊，没看到up说的3年套餐578.9",
    "upup讯优x7还推荐吗，在淘宝找了下没找到具体型号的，能发个链接吗",
    "上赞sz50和中兴f50有什么除了价格上的区别吗？预算充足的话这俩该怎么选呢",
    "是广吗？",
    "已三连，请问中兴f50平常插卡用的时候要开着套餐才能用吗，"
    "没开套餐的话只买机器插自己的卡能不能用呢",
    "upup，随身wifi可以直接一直开着放家里当宽带吗？最基础的迅优能不能这么用？"
    "这边的电信价格有点太坑了",
    "up，我现在租了一个房，拉宽带又很麻烦，我也是想弄一个这样的WiFi。"
    "日常就打打游戏，给电脑主机用的，手机有流量卡，只是单供给电脑的，有推荐的吗？",
    "在学校供词典笔（用来搜索）用，平时放假拿来刷视频，游戏少打，"
    "预算有现100以内，推荐哪款?",
    "🔥",
    "大品牌像华为这种的移动WIFI，他也是用物联网卡的吗？"
    "会不会运营商在以后偷偷的降速限流量？",
    "买了个迅优试试",
    "随身wifi我不用的时候不充钱可以吗？",
    "up，我请问一下，上赞那个可以插线接主机用以太网啥的吗，"
    "而且下steam游戏网速快吗",
    "这些随身wifi时都要办他们的套餐吗？我自己的联通流量卡可以用吗？"
    "这个随身wifi和手机开热点有什么区别啊",
    "已经买了上赞了 等我消息兄弟们",
    "有没有可以插国外卡的推荐呢？",
    "有些想买，但是怕下载3a游戏时断网或限速，上赞随身WIFI有这个问题吗",
    "华为的推荐度高吗",
    "我学生党，要连笔记本和手机，然后打算住宿或者租房，"
    "笔记本打算运行战雷、战术小队之类的游戏，后面打算运行3A大作，"
    "能推荐有性价比的随身WiFi嘛？",
    "Tcl t2g那个月套餐29.9元/300g的那个只能用一次怎么办，"
    "然后我买了之后，客服就反应只能用一次，而且别的都不划算，当时就是冲这个买的",
    "买了",
    "临时用下，期待",
    "讯优X739.9元1500G套餐会有坑吗？",
    "兄弟们，今天sz50 air到货了，可以等我测评一下",
    "电脑用WiFi选哪个",
    "有没有适合笔记本直播玩游戏的，流量共享太卡",
    "话说有没有适用大家庭用的随身wifi呢或者随身路由器呢",
    "有没有5g的这种呢",
    "up有没有移动WiFi可以自己装卡且支持5G的 就这俩要求因为可能涉及到跨境换卡",
    "算下来比手机卡划算太多了",
    "买个在宿舍试试看",
    "上赞这个",
    "芯片都不止149了，确实良心",
    "一步到位直接5g设备吧，过来人的经验",
    "早知道还是上赞",
    "up 我想过年回家使用 老家农村的 用电脑玩游戏 有什么推荐的吗？",
    "哪款比较轻盈，经常出去跑业务，放口袋太重的话也不行",
    "追求性价比有没有推荐的",
    "无敌华为，低调中兴TCL，新秀上赞",
    "大学宿舍用的有什么推荐吗？刷视频不卡就行",
    "想试试上赞的wifi，第一次用没啥经验，请问稳定性怎么样",
    "其实都差不了多少，无非就是看续航和价格",
    "如果设备出问题了，怎么解决？可以试用吗？",
    "up 想问一下，自己外地租房 预算有限 打电脑游戏 吃延迟 "
    "4g产品有没有比较推荐的",
    "请问下哪款可以当充电宝用的？出门就不用带这么多设备了",
    "入手了上赞",
    "比华为那些便宜很多，入门的话足够了。不过没有内置存储功能，"
    "也不能当充电宝用，功能稍微单一了点。",
    "上赞的不错",
    "up全国出差到处跑 能推荐一款不，主要的话是追剧，刷抖音，"
    "打游戏的话只玩王者荣耀",
    "我只想知道这种移动wifi开给电脑打lol能不能带得动妹子，"
    "玩劫不能卡，没触碰过这方面不懂，求大佬解答",
    "up，续航要好一些的选哪个",
    "自己有一张5g移动的流量卡，想买个来插卡用，有什么推荐？",
    "迅优x7月租多少，客服说零月租是真的吗",
    "有不要月租的移动wifi吗，想放到车上用，或者月租低于9.9元的？",
    "我只想知道这种移动wifi开给电脑打lol能不能带得动妹子，"
    "玩劫不能卡，没触碰过这方面不懂，求大佬解答",
    "up全国出差到处跑 能推荐一款不，主要的话是追剧，刷抖音，"
    "打游戏的话只玩王者荣耀",
    "求问up，后续工作经常要带ipad外出，是用流量卡开热点好，"
    "还是用这些随身wifi好啊？有点纠结",
    "5G的还是4G",
    "可以接上电源一直开机运行吗？有时候打游戏一打就是一天",
    "up，续航要好一些的选哪个",
    "自己有一张5g移动的流量卡，想买个来插卡用，有什么推荐？",
    "今天到手",
    "出租屋连投影仪用，没有外出需求，推荐什么？",
    "比较关心信号，能不能在人很多的地方，比如演唱会，或者偏僻的地方还有网",
    "现在下了一个随身WIFI，如果不急的话等我先用个个把月再说说体验",
    "本人主要想过年回家使用，家是普通小镇，买了是为了放假在家使用电脑"
    "还有其他手机之类电子产品，平常在学校晚上会断电，这时候会开手机热点给电脑，"
    "各位大大们有什么合适的推荐吗？",
    "测限速了好评，比无脑夸网速的好太多了，随身wifi怕的不是网慢，"
    "怕的是用一段时间后限速",
    "upup我刚参加工作，来租房里面大概住三四个月，只连手机电脑，"
    "平时打lol之类的，然后下载速度的话也不能太慢的，有推荐的吗？",
    "我感觉一月1500G太浪费了，有没有月租少点的随身Wi-Fi推荐的？",
    "看使用场景，我干施工的，项目在荒郊野岭，宽带拉不上，用这个办公很香，"
    "看个视频没啥问题，人多时每月用200-300G，一个月1500G的套餐足够，"
    "这网速最好的体验是一个人用，虽然体验比起正经宽带还是不如，但是便宜太多了，"
    "手机热点也不靠谱，我手机流量40G不够用，而且比起来流量包是天价",
    "挺喜欢上赞的 但是那个价格月租好难取舍 平时会打游戏或看剧的 4g又觉得卡",
    "@AI视频小助理 ，小笨总结过啦，请看这里的评论",
    "不想弄校园网想搞个随身wifi行不行的，学校那β宽带一个学期快一千块了",
    "买了上赞，看看到手怎么样",
    "我的移动wifi都用了1年了 一直良好没啥问题啊，也就晚上八点左右高峰期小卡一下",
    "买过一个4G的随身WiFi，除了打不了游戏别的毛病，B站看个高清问题不大，"
    "现在想换个5G的有没有推荐的",
    "建议有钱还是上5g吧，不然用一段时间到时还得再买个",
    "随身wifi出差用挺好，小小的也不占啥空间",
    "通过up的链接，买了迅游已用上，现测pc和手机租房内稳定50M宽带速率，"
    "1080p和游戏不卡。插个眼一段时间后回测",
    "随身WiFi用来打电脑游戏延迟高不高啊，能玩永劫吗？",
    "有没有可以自己插自己卡的5gWIFI",
    "想问一下那个电小果怎么样，我感觉快手上大部分是拖",
    "可以连电脑吗？或者是这个放在房间里可以接网线吗",
    "普通刷视频，看小说，本体要求100块钱以下。套餐20多。有什么可以推荐的？",
    "问问up要是所处的地方信号很差的话会对网速有影响嘛？_(:з」∠)_",
    "有可插卡的推荐吗？",
    "恶灵饿五年了",
    "不懂就问，这个跟手机流量有什么区别？",
    "你好，礼貌三连。请教一下。我想买一个能够连接电脑打游戏做直播的随身WiFi。"
    "请问有推荐嘛，网络最好能稳定的",
    "up我想给笔记本用，来玩诛仙和apxe有推荐的没？网速稳定的预算两百左右，"
    "我每天可能玩十个小时，所以要流量多点的",
    "感觉随身wifi除了续航最重要的就是别限速，现在在用上赞sz50max，"
    "快一个月了没限速，还有几天套餐就到期了，要是一直好用的话就开半年套餐",
    "推荐大家直接买",
    "pro，天际通的，京东买贵，直接闲鱼二手，一个月99块钱2000g，年包599，"
    "流量不虚标不限速，无锡宿舍用星期天3-4mb，下steam游戏80多g一个晚上就下好了，"
    "就是玩游戏延迟有点高，cfm手游延迟40-60，看情况，最最最关键的是，"
    "流量不虚标不限速，如果要5g的话还是多花点钱买中兴5g内置插卡版，"
    "那个也不虚标不限速，具体自己找找，但价格要300块以上了，预算充足的朋友们可以考虑",
    "请问下上赞流量用到 300 到 500G 会不会限速，是每月 1500G 吗",
    "刚买了",
    "的X7三台设备刷视频没问题，而且我打王者能到50ms偶尔卡顿不影响操作，"
    "看后续使用情况吧目前用了6天，体验不错的",
    "推荐一下，每个月天天刷视频追剧，外加打游戏，设备电脑和手机双连，哪一款比较好？",
    "up求一个80元以内的随身wifi，因为我有很大的下载量需求，"
    "流量费用每个月都60多了，而且最好是移动网。",
    "up，车载wifi追求性价比有没有推荐的",
    "学生党，经费有限还没买过这种随身WiFi，课余打打游戏追番，"
    "但快被延迟折磨疯了，放假回到村里更难受，视频看完感觉都挺好的。选不出来，求帮忙",
    "我有个问题，过年回老家的话有推荐的吗？就是短时间使用一下，这个还需要自己买卡吗",
    "可以供多少人使用",
    "只是想刷刷网课，刷刷视频推荐那个呢，一个手机一个ipad",
    "都2025年了，我都不知道",
    "up你好我是在外实习的，下班回宿舍会打打游戏，偶尔会下些视频素材，"
    "手机流量我自己也不太够用，就想买个随身给电脑用，手机开热点太卡，"
    "请问一下有没有推荐的？怎么选？",
    "新搬来的出租房这边宽带被垄断了，手机开热点给笔记本用太费电，"
    "只能选择随身WiFi代替了，",
    "在家用没啥毛病，下次带出去试试",
    "up我最近刚工作租房，拉宽带好贵，流量又不够。。想问下TCL合适吗?"
    "要连着一台手机和电脑工作，网速要求不是特别高，平时养宠物摄像头也要网，"
    "哪个套餐好些",
    "这些随身WiFi是随用随充吗",
    "upup，学校校园网很卡，打网游体验很差，再加上每天断电4小时，"
    "有什么5g的推荐吗？",
    "讲真，租房的话还是随身wifi好点，我最近搬家迁网线的钱都够买个不错的随身wifi了",
    "上赞这个还行，暑假那会买的，回老家太无聊了，又没宽带，农村老家也有信号，"
    "感觉比较好的一点是能随用随充，我就寒暑假过年用用还挺香的",
    "你好，礼貌三连。问一下。我想买一个能够连接电脑打游戏做直播的随身WiFi。有推荐嘛",
    "来了，第一第一",
    "那个还挺划算的，想买一个过年回家用，就怕网速会卡，up主有在农村测过吗，蹲个回复",
    "配置清单及对比图在这里~",
    "实名羡慕up这溢出屏幕的才华"
]

# 基础数据模板
BASE_DATA = {
    'video_id': 'BV1SW411Y7LG',
    'root_id': 1539959538,
    'root_reply_content': '怎么声音好像变了',
    'target_id': 1539959538,
    'target_reply_content': '',
    'reply_time': '2021-01-30 15:00:44',
    'uri': 'https://www.bilibili.com/video/BV1SW411Y7LG',
    'user_mid': 276273794,
    'user_nickname': 'Bruce_X',
    'path': '/1539959538/4026748504/',
    'comment_type': 2,
    'reply_count': 0,
    'like_count': 0,
    'importance_level': 0,
    'status': 'replied',
    'is_top': 0,
    'is_blocked': 0,
    'created_at': '2025-05-26 16:58:15',
    'updated_at': '2025-05-26 17:35:46'
}

def create_connection():
    """创建数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        if connection.is_connected():
            logger.info("成功连接到MySQL数据库")
            return connection
    except Error as e:
        logger.error(f"连接数据库时出错: {e}")
        return None

def insert_comments(connection):
    """批量插入评论数据"""
    cursor = connection.cursor()
    
    # 准备插入语句
    insert_query = """
    INSERT INTO comment_records 
    (id, source_id, video_id, source_content, root_id, root_reply_content, 
     target_id, target_reply_content, reply_time, uri, user_mid, user_nickname, 
     path, comment_type, reply_count, like_count, importance_level, status, 
     is_top, is_blocked, created_at, updated_at) 
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 
            %s, %s, %s, %s, %s, %s)
    """
    
    # 准备数据
    records = []
    start_id = 272
    start_source_id = 4026748505
    
    for i, comment in enumerate(COMMENTS):
        record = (
            start_id + i,                    # id
            start_source_id + i,             # source_id
            BASE_DATA['video_id'],           # video_id
            comment,                         # source_content
            BASE_DATA['root_id'],            # root_id
            BASE_DATA['root_reply_content'], # root_reply_content
            BASE_DATA['target_id'],          # target_id
            BASE_DATA['target_reply_content'], # target_reply_content
            BASE_DATA['reply_time'],         # reply_time
            BASE_DATA['uri'],                # uri
            BASE_DATA['user_mid'],           # user_mid
            BASE_DATA['user_nickname'],      # user_nickname
            BASE_DATA['path'],               # path
            BASE_DATA['comment_type'],       # comment_type
            BASE_DATA['reply_count'],        # reply_count
            BASE_DATA['like_count'],         # like_count
            BASE_DATA['importance_level'],   # importance_level
            BASE_DATA['status'],             # status
            BASE_DATA['is_top'],             # is_top
            BASE_DATA['is_blocked'],         # is_blocked
            BASE_DATA['created_at'],         # created_at
            BASE_DATA['updated_at']          # updated_at
        )
        records.append(record)
    
    try:
        # 执行批量插入
        cursor.executemany(insert_query, records)
        connection.commit()
        logger.info(f"成功插入 {len(records)} 条评论记录")
        
        # 显示插入的记录范围
        logger.info(f"插入记录ID范围: {start_id} - {start_id + len(records) - 1}")
        logger.info(f"插入记录source_id范围: "
                   f"{start_source_id} - {start_source_id + len(records) - 1}")
        
    except Error as e:
        logger.error(f"插入数据时出错: {e}")
        connection.rollback()
        raise
    finally:
        cursor.close()

def main():
    """主函数"""
    logger.info("开始执行评论数据插入脚本")
    
    # 创建数据库连接
    connection = create_connection()
    if not connection:
        logger.error("无法连接到数据库，脚本退出")
        return
    
    try:
        # 插入评论数据
        insert_comments(connection)
        logger.info("评论数据插入完成")
        
    except Exception as e:
        logger.error(f"执行过程中出现错误: {e}")
    finally:
        # 关闭数据库连接
        if connection.is_connected():
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    # 在执行前提示用户检查配置
    print("=" * 60)
    print("评论数据批量插入脚本")
    print("=" * 60)
    print("请确保已正确配置以下信息:")
    print("1. 数据库连接信息 (DB_CONFIG)")
    print("2. 数据库中存在 wecom_bridge.comment_records 表")
    print("3. 确认要插入的数据内容")
    print("=" * 60)
    
    confirm = input("确认配置无误并继续执行? (y/N): ")
    if confirm.lower() in ['y', 'yes']:
        main()
    else:
        print("脚本已取消执行") 