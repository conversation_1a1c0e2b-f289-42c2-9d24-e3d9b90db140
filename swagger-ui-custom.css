/* 改善中文显示效果 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

body, .swagger-ui .opblock-tag, .swagger-ui .opblock .opblock-summary-description, 
.swagger-ui .opblock-description-wrapper p, .swagger-ui .info .title,
.swagger-ui .info li, .swagger-ui .info p, .swagger-ui .response-col_status,
.swagger-ui table thead tr th, .swagger-ui .parameter__name, .swagger-ui .parameter__type,
.swagger-ui section.models h4, .swagger-ui .model-title, .swagger-ui .model {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 
    "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
}

.swagger-ui .info .title {
    font-weight: 700;
}

.swagger-ui .opblock-tag {
    font-size: 16px;
    font-weight: 500;
}

.swagger-ui .opblock .opblock-summary-description {
    font-size: 14px;
}

/* 提高中文可读性 */
.swagger-ui .markdown p, .swagger-ui .markdown pre, 
.swagger-ui .renderedMarkdown p, .swagger-ui .renderedMarkdown pre {
    font-size: 14px;
    line-height: 1.6;
    letter-spacing: 0.01em;
}

/* 改善表格中文显示 */
.swagger-ui table {
    font-size: 14px;
}

/* 提高请求/响应示例的可读性 */
.swagger-ui .highlight-code {
    font-size: 14px;
    line-height: 1.5;
}

/* 改善参数描述中文显示 */
.swagger-ui .parameter__name, .swagger-ui .parameter__type, 
.swagger-ui .parameter__deprecated, .swagger-ui .parameter__in,
.swagger-ui .parameter__enum, .swagger-ui .parameter__required {
    font-size: 14px;
}

.swagger-ui .parameter__name {
    font-weight: 500;
}

.swagger-ui .parameters-col_description {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
}

.swagger-ui .parameters-col_description p {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 修改字段描述样式 */
.swagger-ui .property-description {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-size: 13px;
    color: #3b4151;
    margin-top: 0.25rem;
}

.swagger-ui .model-box {
    padding: 0.5rem;
}
