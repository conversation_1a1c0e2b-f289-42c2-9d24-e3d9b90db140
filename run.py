#!/usr/bin/env python
"""
应用启动脚本
"""
import os
import sys
import uvicorn
from app.core.config import settings

if __name__ == "__main__":
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    print(f"日志文件配置: {settings.LOG_FILE}")
    print("前端静态文件目录: app/static/frontend_static/dist")
    print("可用前端路由:")
    print("  - /frontend/bz-login")
    print("  - /frontend/reply-admin")
    print("  - /frontend")
    
    # 启动服务器
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
