# Excel数据导入脚本使用说明

## 快速开始

### 1. 准备Excel文件
将要导入的Excel文件命名为 `imp_data.xlsx` 并放在项目根目录下。

### 2. 安装依赖
```bash
pip install "pandas>=2.2.0" "openpyxl>=3.1.0"
```

### 3. 运行导入脚本
```bash
python scripts/import_excel_data.py
```

## Excel文件格式要求

Excel文件需要包含以下列（支持中英文列名）：

**必填字段：**
- 标题/title - 视频标题
- 视频链接/video_url - 视频URL

**可选字段：**
- 视频ID/video_id - 视频BV号
- 内容描述/video_content - 视频内容描述
- 文案/video_script - 视频文案
- 总结/video_summary - 视频总结
- 回复设定/reply_setting - 回复设定
- 控评方案/comment_control_plan - 控评方案
- 品类/category - 视频品类
- 主推产品/promoted_products - 推广产品（多个用逗号分隔）
- 常见问题/common_questions - 常见问题

## 示例Excel格式

| 标题 | 视频链接 | 品类 | 主推产品 |
|------|----------|------|----------|
| 美妆教程 | https://www.bilibili.com/video/BV1234567890 | 美妆 | 口红,粉底液 |
| 科技评测 | https://www.bilibili.com/video/BV0987654321 | 科技 | iPhone15 |

## 注意事项

1. 确保数据库连接配置正确
2. 视频ID重复的行会导入失败
3. 缺少必填字段的行会被跳过
4. 默认所有导入的视频都是禁用状态

## 详细文档

更多详细信息请参考：[Excel数据导入指南](docs/excel_import_guide.md) 