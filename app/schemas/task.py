"""
任务相关数据模型
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import IntEnum


class TaskState(IntEnum):
    """任务状态枚举"""
    PENDING = 0    # 等待处理
    ACCEPTED = 1   # 已接收（处理中）
    SUCCESS = 2    # 处理成功
    FAILED = 3     # 处理失败


class WecomMessageData(BaseModel):
    """企业微信消息数据"""
    to_username: str
    from_username: str
    create_time: int
    msg_type: str
    agent_id: str
    content: Optional[str] = None
    msg_id: Optional[str] = None


class TaskData(BaseModel):
    """任务数据模型"""
    task_id: str = Field(..., alias="taskId")
    created_at: datetime
    updated_at: Optional[datetime] = None
    state: TaskState = TaskState.PENDING
    task_type: str = Field(..., alias="type")
    data: Dict[str, Any]
    wecom: Optional[WecomMessageData] = None
    file_path: Optional[str] = None  # 兼容旧数据，保留
    file_paths: List[str] = Field(default_factory=list)  # 新增：支持多文件路径


class TaskCreate(BaseModel):
    """创建任务请求模型"""
    task_type: str = Field(..., alias="type")
    data: Dict[str, Any]


class TaskUpdate(BaseModel):
    """更新任务状态请求模型"""
    state: TaskState
    result: Optional[str] = ""


class TaskResponse(BaseModel):
    """任务响应模型"""
    status: str
    task: Optional[TaskData] = None
    message: Optional[str] = None
    file_info: Optional[Dict[str, Any]] = None


class TasksListResponse(BaseModel):
    """任务列表响应模型"""
    status: str
    tasks: List[TaskData]
