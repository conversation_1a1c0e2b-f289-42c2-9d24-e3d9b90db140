"""
视频相关的数据模型定义
"""
from datetime import datetime
from typing import Optional, List, Any, Dict

from pydantic import BaseModel, Field, ConfigDict


class UserResponse(BaseModel):
    """用户响应模型"""
    id: int = Field(..., description="主键ID", example=1)
    user_id: str = Field(..., description="B站用户ID", example="12345678")
    username: str = Field(..., description="用户名", example="用户名示例")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    profile_url: Optional[str] = Field(None, description="主页URL")
    description: Optional[str] = Field(None, description="用户描述")
    machine_code: Optional[str] = Field(None, description="关联的机器码")
    machine_name: Optional[str] = Field(None, description="机器名称")
    login_status: Optional[str] = Field(None, description="登录状态")
    last_login_time: Optional[datetime] = Field(None, description="最后登录时间")
    is_disabled: Optional[bool] = Field(None, description="是否禁用")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class VideoBase(BaseModel):
    """视频基础模型"""
    title: str = Field(..., description="视频标题", example="视频标题示例")
    video_content: Optional[str] = Field(
        None, description="视频内容描述", example="这是一个视频的描述内容"
    )
    video_url: str = Field(
        ..., description="视频链接", example="https://example.com/video.mp4"
    )
    video_script: Optional[str] = Field(
        None, description="视频文案", example="视频的详细文案"
    )
    video_summary: Optional[str] = Field(
        None, description="视频总结", example="对视频内容的总结"
    )
    reply_setting: Optional[str] = Field(
        None, description="回复设定", example="设定如何回复视频评论"
    )
    comment_control_plan: Optional[str] = Field(
        None, description="控评方案", example="如何控制视频评论区"
    )
    category: Optional[str] = Field(
        None, description="视频品类", example="美妆"
    )
    promoted_products: Optional[Any] = Field(
        None, description="主推产品", example=[{"name": "产品1", "reason": "推荐理由1"}]
    )
    common_questions: Optional[str] = Field(
        None, description="常见问题", example="关于视频的常见问题与解答"
    )
    is_disabled: bool = Field(
        True, description="是否禁用", example=True
    )
    user_id: int = Field(..., description="B站账号ID", example=1)


class VideoCreate(VideoBase):
    """创建视频的请求模型"""
    video_id: Optional[str] = Field(
        None, description="视频ID", example="BV123456789"
    )


class VideoUpdate(BaseModel):
    """更新视频的请求模型"""
    title: Optional[str] = Field(None, description="视频标题", example="视频标题示例")
    video_content: Optional[str] = Field(
        None, description="视频内容描述", example="这是一个视频的描述内容"
    )
    video_url: Optional[str] = Field(
        None, description="视频链接", example="https://example.com/video.mp4"
    )
    video_script: Optional[str] = Field(
        None, description="视频文案", example="视频的详细文案"
    )
    video_summary: Optional[str] = Field(
        None, description="视频总结", example="对视频内容的总结"
    )
    reply_setting: Optional[str] = Field(
        None, description="回复设定", example="设定如何回复视频评论"
    )
    comment_control_plan: Optional[str] = Field(
        None, description="控评方案", example="如何控制视频评论区"
    )
    category: Optional[str] = Field(
        None, description="视频品类", example="美妆"
    )
    promoted_products: Optional[List[Dict[str, Any]]] = Field(
        None, description="主推产品", example=[{"name": "产品1", "reason": "推荐理由1"}]
    )
    common_questions: Optional[str] = Field(
        None, description="常见问题", example="关于视频的常见问题与解答"
    )
    is_disabled: Optional[bool] = Field(
        None, description="是否禁用", example=False
    )
    user_id: Optional[int] = Field(None, description="B站用户主键ID", example=1)


class VideoResponse(VideoBase):
    """视频响应模型"""
    id: int = Field(..., description="主键ID", example=1)
    video_id: str = Field(..., description="视频ID", example="BV123456789")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    user: Optional[UserResponse] = Field(None, description="关联的B站用户信息")

    model_config = ConfigDict(from_attributes=True)


class VideoListResponse(BaseModel):
    """视频列表响应模型"""
    total: int = Field(..., description="总数", example=10)
    items: List[VideoResponse] = Field(..., description="视频列表")

    model_config = ConfigDict(from_attributes=True)


class VideoBatchStatusUpdate(BaseModel):
    """批量更新视频状态的请求模型"""
    video_ids: List[Any] = Field(
        ...,
        description="视频ID列表",
        example=["BV123456789", "BV987654321"]
    )


class BatchUpdateResponse(BaseModel):
    """批量更新响应模型"""
    success: List[str] = Field(..., description="成功更新的视频ID列表")
    failed: List[Dict[str, str]] = Field(..., description="更新失败的视频信息列表")
    first_time_enabled: Optional[List[str]] = Field(
        None, description="首次启用的视频ID列表"
    )
