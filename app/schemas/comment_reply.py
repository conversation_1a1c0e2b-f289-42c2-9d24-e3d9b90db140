"""
评论回复相关的Pydantic模型
"""
from typing import Optional, Union, List
from pydantic import BaseModel
from datetime import datetime

from app.models.comment_reply import ReplyStatus


class CommentReplyBase(BaseModel):
    """评论回复基础模型"""
    comment_id: int
    comment_source_id: int
    reply_content: str
    needs_content_change: bool = False
    changed_reply_content: Optional[str] = None


class CommentReplyCreate(CommentReplyBase):
    """创建评论回复的请求模型"""
    pass


class CommentReplyUpdateRequest(BaseModel):
    """更新评论回复状态的请求模型"""
    status: ReplyStatus
    error_message: Optional[str] = None


class CommentReplySatisfactionRequest(BaseModel):
    """更新评论回复满意度的请求模型"""
    is_satisfied: bool


class UserInfo(BaseModel):
    """用户信息模型"""
    user_id: str
    username: str
    avatar_url: Optional[str] = None
    profile_url: Optional[str] = None
    description: Optional[str] = None
    
    class Config:
        from_attributes = True


class VideoInfo(BaseModel):
    """视频信息模型"""
    video_id: str
    title: str
    video_url: str
    video_content: Optional[str] = None
    
    class Config:
        from_attributes = True


class CommentInfo(BaseModel):
    """评论信息模型"""
    source_id: int
    source_content: Optional[str] = None
    user_mid: int
    user_nickname: Optional[str] = None
    reply_time: Optional[datetime] = None
    reply_count: Optional[int] = None
    like_count: Optional[int] = None
    
    class Config:
        from_attributes = True


class CommentReplyModel(CommentReplyBase):
    """评论回复的响应模型"""
    id: int
    status: ReplyStatus
    is_satisfied: Optional[bool] = None
    reply_time: Optional[datetime] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    # 添加关联信息
    user_info: Optional[UserInfo] = None
    video_info: Optional[VideoInfo] = None
    comment_info: Optional[CommentInfo] = None
    comment_context: Optional[str] = None

    class Config:
        from_attributes = True


class PaginatedResponse(BaseModel):
    """分页响应模型"""
    items: List[CommentReplyModel]
    total: int
    page: int
    page_size: int


class CommentReplyResponse(BaseModel):
    """API响应模型"""
    status: str
    message: Optional[str] = None
    data: Optional[
        Union[
            CommentReplyModel,
            List[CommentReplyModel],
            PaginatedResponse
        ]
    ] = None
