"""
评论相关的模型定义
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict, validator

from app.constants.status import CommentStatus


class CommentBase(BaseModel):
    """评论基础模型"""
    source_id: int = Field(..., description="评论的唯一ID (rpid)")
    video_id: Optional[str] = Field(None, description="关联的视频ID")
    source_content: Optional[str] = Field(None, description="评论的内容")
    root_id: Optional[int] = Field(
        None,
        description="主楼评论的rpid (楼中楼评论)"
    )
    root_reply_content: Optional[str] = Field(
        None,
        description="主楼评论的内容 (楼中楼评论)"
    )
    target_id: Optional[int] = Field(
        None,
        description="回复的评论的rpid (楼中楼评论)"
    )
    target_reply_content: Optional[str] = Field(
        None,
        description="回复的评论的内容 (楼中楼评论)"
    )
    reply_time: datetime = Field(..., description="回复时间戳 (秒级)")
    uri: Optional[str] = Field(None, description="评论的超链接")
    user_mid: int = Field(..., description="回复评论的用户的uid")
    user_nickname: Optional[str] = Field(None, description="回复评论的用户的昵称")
    path: Optional[str] = Field(
        None,
        description=(
            "评论链路径，格式如 /root_id/parent_id/current_id/，"
            "将根据评论关系自动生成"
        )
    )
    comment_type: Optional[int] = Field(
        None,
        description="评论类型(0=不需要回复, 1=@回复, 2=回复我的)"
    )
    reply_count: int = Field(0, description="回复数量")
    like_count: int = Field(0, description="点赞数量")
    importance_level: int = Field(0, description="重要程度")
    status: str = Field(CommentStatus.PENDING, description="处理状态")
    is_top: bool = Field(False, description="是否置顶")
    is_blocked: bool = Field(False, description="是否屏蔽")

    @validator('source_id', pre=True)
    def parse_source_id(cls, value):
        """将字符串转换为整数"""
        if isinstance(value, str):
            if value.strip() == "":
                raise ValueError("source_id不能为空")
            return int(value)
        return value

    @validator('user_mid', pre=True)
    def parse_user_mid(cls, value):
        """将字符串转换为整数"""
        if isinstance(value, str):
            if value.strip() == "":
                raise ValueError("user_mid不能为空")
            return int(value)
        return value

    @validator('root_id', pre=True)
    def parse_root_id(cls, value):
        """处理空字符串和字符串数字转换"""
        if isinstance(value, str):
            if value.strip() == "":
                return None
            return int(value)
        return value

    @validator('target_id', pre=True)
    def parse_target_id(cls, value):
        """处理空字符串和字符串数字转换"""
        if isinstance(value, str):
            if value.strip() == "":
                return None
            return int(value)
        return value

    @validator('reply_time', pre=True)
    def parse_timestamp(cls, value):
        """将时间戳转换为datetime对象"""
        if isinstance(value, (int, float)):
            # 判断是秒级还是毫秒级时间戳
            # 秒级时间戳通常在 10^9 到 10^10 之间 (1970-2286年)
            # 毫秒级时间戳通常在 10^12 到 10^13 之间
            if value > 1e12:  # 毫秒级时间戳
                return datetime.fromtimestamp(value / 1000)
            else:  # 秒级时间戳
                return datetime.fromtimestamp(value)
        return value


class CommentCreate(CommentBase):
    """创建评论的请求模型"""
    pass


class CommentUpdate(BaseModel):
    """更新评论的请求模型"""
    source_content: Optional[str] = Field(None, description="评论的内容")
    reply_count: Optional[int] = Field(None, description="回复数量")
    like_count: Optional[int] = Field(None, description="点赞数量")
    importance_level: Optional[int] = Field(None, description="重要程度")
    status: Optional[str] = Field(None, description="处理状态")
    is_top: Optional[bool] = Field(None, description="是否置顶")
    is_blocked: Optional[bool] = Field(None, description="是否屏蔽")


class CommentResponse(CommentBase):
    """评论响应模型"""
    id: int = Field(..., description="数据库主键ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class CommentListResponse(BaseModel):
    """评论列表响应模型"""
    total: int = Field(..., description="总评论数量")
    items: List[CommentResponse] = Field(..., description="评论列表")


class CommentBatchCreate(BaseModel):
    """批量创建评论的请求模型"""
    comments: List[CommentCreate] = Field(..., description="评论列表")


class CommentBatchResponse(BaseModel):
    """批量创建评论的响应模型"""
    success_count: int = Field(..., description="成功创建的评论数量")
    failed_count: int = Field(..., description="创建失败的评论数量")
    existed_count: int = Field(..., description="已存在的评论数量")
    failed_items: List[Dict[str, Any]] = Field([], description="创建失败的评论详情")
    existed_items: List[Dict[str, Any]] = Field([], description="已存在的评论详情")
