"""
评论回复仓储层
"""
import logging
from typing import Optional, List
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.comment_reply import CommentReply, ReplyStatus

logger = logging.getLogger("app.repositories.comment_reply")


class CommentReplyRepository:
    """评论回复仓储层，处理数据库操作"""

    def __init__(self, db: Session):
        """初始化仓储

        Args:
            db: 数据库会话
        """
        self.db = db

    def get_by_id(self, reply_id: int) -> Optional[CommentReply]:
        """通过ID获取评论回复

        Args:
            reply_id: 评论回复ID

        Returns:
            评论回复对象或None
        """
        return (
            self.db.query(CommentReply)
            .filter(CommentReply.id == reply_id)
            .first()
        )

    def get_one_by_status(self, status: str) -> Optional[CommentReply]:
        """获取指定状态的一条评论回复

        Args:
            status: 评论回复状态

        Returns:
            评论回复对象或None
        """
        reply_status = ReplyStatus(status)
        return (
            self.db.query(CommentReply)
            .filter(CommentReply.status == reply_status)
            .order_by(CommentReply.created_at.asc())
            .first()
        )

    def get_all_by_status(self, status: str) -> List[CommentReply]:
        """获取指定状态的所有评论回复

        Args:
            status: 评论回复状态

        Returns:
            评论回复对象列表
        """
        reply_status = ReplyStatus(status)
        return (
            self.db.query(CommentReply)
            .filter(CommentReply.status == reply_status)
            .order_by(CommentReply.created_at.asc())
            .all()
        )

    def get_all_replies(
        self, 
        page: int = 1, 
        page_size: int = 20, 
        status: Optional[str] = None
    ) -> tuple[List[CommentReply], int]:
        """获取评论回复列表，支持分页和状态过滤

        Args:
            page: 页码，从1开始
            page_size: 每页数量
            status: 过滤的状态，None表示获取全部

        Returns:
            元组(评论回复列表, 总记录数)
        """
        try:
            # 基本查询
            query = self.db.query(CommentReply)
            
            # 如果指定了状态，添加状态过滤
            if status:
                reply_status = ReplyStatus(status)
                query = query.filter(CommentReply.status == reply_status)
                
            # 获取总记录数
            total = query.count()
            
            # 添加分页
            offset = (page - 1) * page_size
            replies = (
                query
                .order_by(CommentReply.updated_at.desc())
                .offset(offset)
                .limit(page_size)
                .all()
            )
            
            # 单独加载每个回复的关联数据，处理可能出现的错误
            for reply in replies:
                try:
                    # 尝试加载评论数据
                    if reply.comment:
                        comment = reply.comment
                        try:
                            # 尝试加载视频数据
                            if comment.video:
                                video = comment.video
                                try:
                                    # 尝试加载用户数据
                                    if video.user:
                                        _ = video.user
                                except Exception as e:
                                    logger.warning(f"加载用户数据失败: {str(e)}")
                        except Exception as e:
                            logger.warning(f"加载视频数据失败: {str(e)}")
                except Exception as e:
                    logger.warning(f"加载评论数据失败: {str(e)}")
            
            return replies, total
        except Exception as e:
            logger.error(f"获取评论回复列表失败: {str(e)}")
            # 出现错误时返回空列表和0
            return [], 0

    def update_status(
        self,
        reply_id: int,
        status: ReplyStatus,
        error_message: Optional[str] = None
    ) -> Optional[CommentReply]:
        """更新评论回复状态

        Args:
            reply_id: 评论回复ID
            status: 新状态
            error_message: 错误信息，仅在状态为失败时有效

        Returns:
            更新后的评论回复对象或None
        """
        reply = self.get_by_id(reply_id)
        if not reply:
            logger.warning(f"尝试更新不存在的评论回复: {reply_id}")
            return None

        reply.status = status

        # 如果状态为完成，设置回复时间
        if status == ReplyStatus.completed:
            reply.reply_time = datetime.now()

        # 如果状态为失败，设置错误信息
        if status == ReplyStatus.failed and error_message:
            reply.error_message = error_message

        reply.updated_at = datetime.now()

        try:
            self.db.commit()
            self.db.refresh(reply)
            logger.info(f"评论回复 {reply_id} 状态已更新为 {status.value}")
            return reply
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新评论回复状态失败: {str(e)}")
            return None

    def update_satisfaction(
        self,
        reply_id: int,
        is_satisfied: bool
    ) -> Optional[CommentReply]:
        """更新评论回复满意度

        Args:
            reply_id: 评论回复ID
            is_satisfied: 是否满意

        Returns:
            更新后的评论回复对象或None
        """
        reply = self.get_by_id(reply_id)
        if not reply:
            logger.warning(f"尝试更新不存在的评论回复满意度: {reply_id}")
            return None

        reply.is_satisfied = is_satisfied
        reply.updated_at = datetime.now()

        try:
            self.db.commit()
            self.db.refresh(reply)
            logger.info(f"评论回复 {reply_id} 满意度已更新为 {is_satisfied}")
            return reply
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新评论回复满意度失败: {str(e)}")
            return None
