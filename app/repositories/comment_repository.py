"""
评论记录仓库类

提供CommentRecord相关的CRUD操作。
"""

from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import logging

from app.models.comment_record import CommentRecord
from app.repositories.base_repository import BaseRepository

logger = logging.getLogger("app.repositories.comment_repository")


class CommentRepository(BaseRepository[CommentRecord]):
    """评论记录仓库类，提供评论相关的CRUD操作"""

    def __init__(self, db: Session):
        """初始化

        Args:
            db: 数据库会话对象
        """
        super().__init__(db)

    def get_by_id(self, id: int) -> Optional[CommentRecord]:
        """通过内部ID获取评论记录

        Args:
            id: 评论记录的内部ID

        Returns:
            Optional[CommentRecord]: 返回查询到的评论记录，如果不存在则返回None
        """
        return (self.db.query(CommentRecord)
                .filter(CommentRecord.id == id)
                .first())

    def get_by_source_id(self, source_id: int) -> Optional[CommentRecord]:
        """通过source_id(B站评论唯一ID)获取评论记录

        Args:
            source_id: B站评论的唯一ID (rpid)

        Returns:
            Optional[CommentRecord]: 返回查询到的评论记录，如果不存在则返回None
        """
        return (
            self.db.query(CommentRecord)
            .filter(CommentRecord.source_id == source_id)
            .first()
        )

    def build_comment_path(self, entity: CommentRecord) -> str:
        """构建评论链路路径

        根据评论的root_id和target_id构建评论链路路径。
        路径格式为：/root_id/parent_id/current_id/

        Args:
            entity: 评论记录实体

        Returns:
            str: 构建好的评论链路路径
        """
        # 如果评论已经有path，则直接返回
        if entity.path:
            return entity.path

        path = "/"

        # 如果是根评论（一级评论），path为 /current_id/
        if not entity.root_id:
            path += f"{entity.source_id}/"
            return path

        # 如果是回复根评论的评论（二级评论），path为 /root_id/current_id/
        is_reply_to_root_comment = (
            not entity.target_id or entity.root_id == entity.target_id
        )
        if entity.root_id and is_reply_to_root_comment:
            path += f"{entity.root_id}/{entity.source_id}/"
            return path

        # 如果是回复其他评论的评论（三级及以上评论），需要查找target_id的评论
        if entity.target_id and entity.target_id != entity.root_id:
            # 查找target评论
            target_comment = self.get_by_source_id(entity.target_id)
            if target_comment and target_comment.path:
                # 使用target评论的path，并添加当前评论ID
                path = target_comment.path.rstrip("/")  # 移除末尾的"/"
                path += f"/{entity.source_id}/"
            else:
                # 如果找不到target评论或target评论没有path，使用默认结构
                root = entity.root_id
                target = entity.target_id
                source = entity.source_id
                path += f"{root}/{target}/{source}/"

        return path

    def get_by_video_id(self, video_id: str) -> List[CommentRecord]:
        """获取指定视频的所有评论

        Args:
            video_id: 视频ID

        Returns:
            List[CommentRecord]: 评论记录列表
        """
        return (
            self.db.query(CommentRecord)
            .filter(CommentRecord.video_id == video_id)
            .all()
        )

    def get_all(self) -> List[CommentRecord]:
        """获取所有评论记录

        Returns:
            List[CommentRecord]: 评论记录列表
        """
        return self.db.query(CommentRecord).all()

    def create(self, entity: CommentRecord) -> CommentRecord:
        """创建新的评论记录

        Args:
            entity: 评论记录实体

        Returns:
            CommentRecord: 创建成功后的评论记录
        """
        try:
            # 构建评论链路路径
            if not entity.path:
                entity.path = self.build_comment_path(entity)

            self.db.add(entity)
            self.db.commit()
            self.db.refresh(entity)
            return entity
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"创建评论记录失败: {str(e)}")
            raise

    def batch_create(
        self, entities: List[CommentRecord]
    ) -> Tuple[
        List[CommentRecord], List[Dict[str, Any]], List[Dict[str, Any]]
    ]:
        """批量创建评论记录

        Args:
            entities: 评论记录实体列表

        Returns:
            Tuple[
                List[CommentRecord],
                List[Dict[str, Any]],
                List[Dict[str, Any]]
            ]:
                成功创建的评论记录列表、失败的评论记录详情和
                已存在的评论记录详情
        """
        successful_records = []
        failed_records = []
        existed_records = []

        logger.debug(f"开始批量创建 {len(entities)} 条评论记录")

        for entity in entities:
            try:
                # 检查是否已存在相同source_id的评论
                existing = self.get_by_source_id(entity.source_id)
                if existing:
                    # 如果评论已存在且新评论内容不为空，则更新评论内容
                    if (entity.source_content and
                            entity.source_content.strip() and
                            existing.source_content != entity.source_content):

                        logger.info(
                            f"评论ID '{entity.source_id}' 已存在，更新评论内容"
                        )

                        # 更新评论内容和其他可能变化的字段
                        existing.source_content = entity.source_content
                        existing.reply_count = entity.reply_count
                        existing.like_count = entity.like_count
                        existing.importance_level = entity.importance_level
                        existing.status = entity.status
                        existing.is_top = entity.is_top
                        existing.is_blocked = entity.is_blocked
                        existing.user_nickname = entity.user_nickname

                        # 如果提供了新的视频ID，也进行更新
                        if entity.video_id:
                            existing.video_id = entity.video_id

                        # 更新记录
                        self.db.add(existing)
                        self.db.flush()
                        successful_records.append(existing)
                        logger.debug(
                            f"评论更新成功: source_id={existing.source_id}, "
                            f"id={existing.id}"
                        )
                    else:
                        # 如果评论内容为空或相同，则标记为已存在
                        logger.info(f"评论已存在: source_id={entity.source_id}")
                        existed_records.append({
                            "source_id": entity.source_id,
                            "error": "评论已存在",
                            "data": {
                                "source_id": entity.source_id,
                                "user_mid": entity.user_mid,
                                "video_id": entity.video_id
                            }
                        })
                    continue

                # 构建评论链路路径
                if not entity.path:
                    entity.path = self.build_comment_path(entity)
                    source_id = entity.source_id
                    logger.debug(
                        f"构建评论路径: source_id={source_id}, "
                        f"path={entity.path}"
                    )

                # 添加到数据库
                self.db.add(entity)
                self.db.flush()  # 刷新会话，获取生成的ID
                successful_records.append(entity)
                logger.debug(
                    f"评论添加成功: source_id={entity.source_id}, "
                    f"id={entity.id}"
                )
            except Exception as e:
                logger.error(
                    f"添加评论记录失败: source_id={entity.source_id}, "
                    f"error={str(e)}"
                )
                failed_records.append({
                    "source_id": entity.source_id,
                    "error": str(e),
                    "data": {
                        "source_id": entity.source_id,
                        "user_mid": entity.user_mid,
                        "video_id": entity.video_id
                    }
                })

        if successful_records:
            try:
                logger.debug(f"提交 {len(successful_records)} 条评论记录到数据库")
                self.db.commit()
                # 刷新所有成功创建的实体
                for entity in successful_records:
                    self.db.refresh(entity)
                logger.info(f"成功批量创建 {len(successful_records)} 条评论记录")
            except Exception as e:
                self.db.rollback()
                logger.error(f"提交批量评论记录失败: {str(e)}")
                # 将所有原本成功的记录标记为失败
                for entity in successful_records:
                    failed_records.append({
                        "source_id": entity.source_id,
                        "error": f"提交事务失败: {str(e)}",
                        "data": {
                            "source_id": entity.source_id,
                            "user_mid": entity.user_mid,
                            "video_id": entity.video_id
                        }
                    })
                successful_records = []
        else:
            logger.info("没有成功创建的评论记录，跳过提交")

        logger.debug(
            f"批量创建评论完成: 成功={len(successful_records)}, "
            f"失败={len(failed_records)}, 已存在={len(existed_records)}"
        )
        return successful_records, failed_records, existed_records

    def update(self, entity: CommentRecord) -> CommentRecord:
        """更新评论记录

        Args:
            entity: 要更新的评论记录实体

        Returns:
            CommentRecord: 更新后的评论记录
        """
        try:
            self.db.add(entity)
            self.db.commit()
            self.db.refresh(entity)
            return entity
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"更新评论记录失败: {str(e)}")
            raise

    def delete(self, id: int) -> bool:
        """删除评论记录

        Args:
            id: 评论记录的内部ID

        Returns:
            bool: 是否成功删除
        """
        entity = self.get_by_id(id)
        if not entity:
            return False

        try:
            self.db.delete(entity)
            self.db.commit()
            return True
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"删除评论记录失败: {str(e)}")
            return False
