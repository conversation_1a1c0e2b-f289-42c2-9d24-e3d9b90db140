"""
视频Repository

提供对VideoDetail模型的CRUD操作。
"""

import logging
import re
from typing import Optional, List, Dict, Any

from app.models.video import VideoDetail
from app.repositories.base_repository import BaseRepository

logger = logging.getLogger("app.repositories.video_repository")


class VideoRepository(BaseRepository[VideoDetail]):
    """视频Repository类，实现对VideoDetail模型的CRUD操作"""

    def get_by_id(self, id: int) -> Optional[VideoDetail]:
        """通过主键ID获取视频详情

        Args:
            id: 主键ID

        Returns:
            Optional[VideoDetail]: 视频详情，如果不存在则返回None
        """
        try:
            video = self.db.query(VideoDetail).filter(
                VideoDetail.id == id
            ).first()

            if not video:
                logger.warning(f"未找到视频ID: {id}")
                return None

            return video
        except Exception as e:
            logger.error(f"获取视频详情失败: {str(e)}")
            return None

    def get_by_video_id(self, video_id: str) -> Optional[VideoDetail]:
        """通过视频ID获取视频详情

        Args:
            video_id: 视频ID

        Returns:
            Optional[VideoDetail]: 视频详情，如果不存在则返回None
        """
        try:
            video = self.db.query(VideoDetail).filter(
                VideoDetail.video_id == video_id
            ).first()

            if not video:
                logger.warning(f"未找到视频ID: {video_id}")
                return None

            return video
        except Exception as e:
            logger.error(f"获取视频详情失败: {str(e)}")
            return None

    def get_all(self) -> List[VideoDetail]:
        """获取所有视频详情，按创建时间降序排序

        Returns:
            List[VideoDetail]: 视频详情列表
        """
        try:
            return self.db.query(VideoDetail).order_by(
                VideoDetail.created_at.desc()
            ).all()
        except Exception as e:
            logger.error(f"获取所有视频详情失败: {str(e)}")
            return []

    def create(self, entity: VideoDetail) -> VideoDetail:
        """创建新视频详情

        Args:
            entity: 视频详情实体

        Returns:
            VideoDetail: 创建后的视频详情实体
        """
        try:
            self.db.add(entity)
            self.db.commit()
            self.db.refresh(entity)
            logger.info(f"添加新视频详情: {entity.video_id}")
            return entity
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建视频详情失败: {str(e)}")
            raise

    def update(self, entity: VideoDetail) -> VideoDetail:
        """更新视频详情

        Args:
            entity: 视频详情实体

        Returns:
            VideoDetail: 更新后的视频详情实体
        """
        try:
            db_video = self.get_by_video_id(entity.video_id)
            if not db_video:
                logger.warning(f"更新失败，未找到视频ID: {entity.video_id}")
                raise ValueError(f"视频不存在: {entity.video_id}")

            # 更新实体属性
            for key, value in entity.__dict__.items():
                if key != "_sa_instance_state" and hasattr(db_video, key):
                    setattr(db_video, key, value)

            self.db.commit()
            self.db.refresh(db_video)
            logger.info(f"更新视频详情: {entity.video_id}")
            return db_video
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新视频详情失败: {str(e)}")
            raise

    def delete(self, id: str) -> bool:
        """删除视频详情

        Args:
            id: 视频ID

        Returns:
            bool: 是否成功删除
        """
        try:
            video = self.get_by_video_id(id)
            if not video:
                logger.warning(f"删除失败，未找到视频ID: {id}")
                return False

            self.db.delete(video)
            self.db.commit()
            logger.info(f"成功删除视频详情: {id}")
            return True
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除视频详情失败: {str(e)}")
            return False

    def to_dict(self, video: VideoDetail) -> Dict[str, Any]:
        """将视频详情实体转换为字典

        Args:
            video: 视频详情实体

        Returns:
            Dict[str, Any]: 视频详情字典
        """
        return {
            "video_id": video.video_id,
            "title": video.title,
            "video_content": video.video_content,
            "video_url": video.video_url,
            "video_script": video.video_script,
            "video_summary": video.video_summary,
            "reply_setting": video.reply_setting,
            "comment_control_plan": video.comment_control_plan,
            "category": video.category,
            "promoted_products": video.promoted_products,
            "common_questions": video.common_questions,
            "is_disabled": video.is_disabled,
            "created_at": video.created_at,
            "updated_at": video.updated_at,
        }
        
    @staticmethod
    def extract_video_id_from_url(url: str) -> Optional[str]:
        """从B站视频URL中提取视频ID
        
        Args:
            url: B站视频URL
            
        Returns:
            Optional[str]: 视频ID，如果无法提取则返回None
        """
        try:
            # 匹配B站视频URL中的BV号
            pattern = (
                r'(?:https?://)?(?:www\.)?bilibili\.com/video/([A-Za-z0-9]+)'
            )
            match = re.search(pattern, url)
            
            if match:
                return match.group(1)
            
            # 如果直接提供的是BV号
            if url.startswith('BV'):
                return url
                
            logger.warning(f"无法从URL中提取视频ID: {url}")
            return None
        except Exception as e:
            logger.error(f"提取视频ID时出错: {str(e)}")
            return None

    def get_video_details(self, video_id: str) -> Optional[Dict[str, Any]]:
        """获取视频详情

        Args:
            video_id: 视频ID

        Returns:
            Optional[Dict[str, Any]]: 视频详情字典，如果不存在则返回None
        """
        try:
            video = self.get_by_video_id(video_id)
            if not video:
                logger.warning(f"未找到视频详情: {video_id}")
                return None
                
            # 构建视频详情字典，包含所有workflow需要的字段
            return {
                "title": video.title,
                "video_content": video.video_content,
                "product_info": video.promoted_products,  # 保持向后兼容
                "promoted_products": video.promoted_products,  # workflow需要
                "summarize": video.video_summary,  # 保持向后兼容
                "video_summary": video.video_summary,  # workflow需要
                "video_script": video.video_script,  # workflow需要
                "category": video.category,  # workflow需要
                "reply_setting": video.reply_setting,  # workflow需要
                "comment_control_plan": video.comment_control_plan,
            }
        except Exception as e:
            logger.error(f"获取视频详情失败: {str(e)}")
            return None
