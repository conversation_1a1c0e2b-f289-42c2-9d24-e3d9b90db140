"""
B站用户仓库

实现对B站用户模型的CRUD操作。
"""

from typing import List, Optional, Dict, Any, Union
from sqlalchemy import update
from app.models.user import BiliUser
from app.repositories.base_repository import BaseRepository


class UserRepository(BaseRepository[BiliUser]):
    """B站用户仓库实现类"""

    def get(self, id_or_user_id: Union[int, str]) -> Optional[BiliUser]:
        """通过ID或B站用户ID获取用户
        
        根据参数类型自动选择查询方式：
        - 如果是int类型，则按照数据库主键ID查询
        - 如果是str类型，则按照B站用户ID查询

        Args:
            id_or_user_id: 数据库ID或B站用户ID

        Returns:
            Optional[BiliUser]: 返回查询到的用户，如果不存在则返回None
        """
        if isinstance(id_or_user_id, int):
            return self.get_by_id(id_or_user_id)
        return self.get_by_user_id(id_or_user_id)
    
    def get_by_smart_id(self, id_or_user_id: str) -> Optional[BiliUser]:
        """智能识别ID类型并获取用户
        
        尝试识别参数是数字ID还是字符串用户ID：
        - 如果是纯数字，先尝试按数据库ID查询，找不到再按B站用户ID查询
        - 如果是非数字，按B站用户ID查询

        Args:
            id_or_user_id: 数据库ID或B站用户ID的字符串表示

        Returns:
            Optional[BiliUser]: 返回查询到的用户，如果不存在则返回None
        """
        if id_or_user_id.isdigit():
            # 如果是纯数字，先尝试按数据库ID查询
            user = self.get_by_id(int(id_or_user_id))
            if user:
                return user
            # 找不到再尝试按B站用户ID查询
            return self.get_by_user_id(id_or_user_id)
        else:
            # 非数字ID，按B站用户ID查询
            return self.get_by_user_id(id_or_user_id)

    def get_by_id(self, id: int) -> Optional[BiliUser]:
        """通过ID获取用户

        Args:
            id: 用户ID

        Returns:
            Optional[BiliUser]: 返回查询到的用户，如果不存在则返回None
        """
        return self.db.query(BiliUser).filter(BiliUser.id == id).first()

    def get_by_user_id(self, user_id: str) -> Optional[BiliUser]:
        """通过B站用户ID获取用户

        Args:
            user_id: B站用户ID

        Returns:
            Optional[BiliUser]: 返回查询到的用户，如果不存在则返回None
        """
        return self.db.query(BiliUser).filter(
            BiliUser.user_id == user_id
        ).first()

    def get_by_machine_code(self, machine_code: str) -> Optional[BiliUser]:
        """通过设备码获取用户

        Args:
            machine_code: 设备码

        Returns:
            Optional[BiliUser]: 返回查询到的用户，如果不存在则返回None
        """
        return self.db.query(BiliUser).filter(
            BiliUser.machine_code == machine_code
        ).first()

    def get_all(self) -> List[BiliUser]:
        """获取所有用户

        Returns:
            List[BiliUser]: 用户列表
        """
        return self.db.query(BiliUser).all()

    def get_all_with_filters(
        self, filters: Dict[str, Any], page: int = 1, page_size: int = 20
    ) -> tuple[List[BiliUser], int]:
        """获取所有用户并支持筛选和分页

        Args:
            filters: 筛选条件
            page: 页码
            page_size: 每页数量

        Returns:
            tuple[List[BiliUser], int]: 用户列表和总数
        """
        query = self.db.query(BiliUser)

        # 应用筛选条件
        if filters:
            if 'username' in filters and filters['username']:
                query = query.filter(
                    BiliUser.username.like(f"%{filters['username']}%")
                )
            if 'user_id' in filters and filters['user_id']:
                query = query.filter(BiliUser.user_id == filters['user_id'])
            if 'login_status' in filters and filters['login_status']:
                query = query.filter(
                    BiliUser.login_status == filters['login_status']
                )
            if 'machine_code' in filters and filters['machine_code']:
                query = query.filter(
                    BiliUser.machine_code == filters['machine_code']
                )
            if 'is_disabled' in filters and filters['is_disabled'] is not None:
                query = query.filter(
                    BiliUser.is_disabled == filters['is_disabled']
                )

        # 获取总数
        total = query.count()

        # 注意：videos是@property，不需要joinedload
        
        # 分页
        query = query.order_by(BiliUser.id.desc())
        query = query.offset((page - 1) * page_size).limit(page_size)

        return query.all(), total

    def create(self, user: BiliUser) -> BiliUser:
        """创建新用户

        Args:
            user: 要创建的用户

        Returns:
            BiliUser: 创建后的用户
        """
        try:
            self.db.add(user)
            self.db.commit()
            self.db.refresh(user)
            return user
        except Exception as e:
            self.db.rollback()
            raise e

    def update(self, user: BiliUser) -> BiliUser:
        """更新用户

        Args:
            user: 要更新的用户

        Returns:
            BiliUser: 更新后的用户
        """
        try:
            self.db.commit()
            self.db.refresh(user)
            return user
        except Exception as e:
            self.db.rollback()
            raise e
    
    def update_by_user_id(self, user_id: str, data: Dict[str, Any]) -> bool:
        """通过用户ID更新用户信息

        Args:
            user_id: B站用户ID
            data: 要更新的数据

        Returns:
            bool: 是否成功更新
        """
        try:
            stmt = (
                update(BiliUser)
                .where(BiliUser.user_id == user_id)
                .values(**data)
            )
            self.db.execute(stmt)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise e

    def delete(self, id: int) -> bool:
        """删除用户

        Args:
            id: 用户ID

        Returns:
            bool: 是否成功删除
        """
        user = self.get_by_id(id)
        if not user:
            return False
        
        try:
            self.db.delete(user)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise e
