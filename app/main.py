"""
应用入口模块
"""
import os
import sys
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.utils import get_openapi

from app.core.config import settings
from app.core.container import container
from app.core.logging_setup import configure_logging
from app.utils.db_init import init_database
from app.middlewares import RequestLoggerMiddleware
from app.api.routes import wecom, tasks, health, users
# 导入视频路由
from app.api.routes.videos import router as videos_router
# 导入评论路由
from app.api.routes.comments import router as comments_router
# 导入评论回复路由
from app.api.routes.comment_replies import router as comment_replies_router
# 导入指令路由
from app.api.routes.commands import router as commands_router
# 导入前端应用路由
from app.api.routes.frontend import router as frontend_router
# 导入调度服务和任务
from app.services.scheduler_service import scheduler_service
from app.tasks import (
    setup_comment_tasks, setup_log_cleanup_tasks, setup_video_crawl_tasks
)

# 输出当前工作目录，用于调试日志路径问题
print(f"当前工作目录: {os.getcwd()}")
print(f"Python版本: {sys.version}")
print(f"日志文件配置: {settings.LOG_FILE}")

# 配置日志
logger = configure_logging()


# 创建FastAPI应用
def create_app() -> FastAPI:
    """创建并配置FastAPI应用

    Returns:
        FastAPI: 配置好的FastAPI应用
    """
    logger.info("开始初始化应用...")

    # 初始化数据库
    logger.info("开始数据库初始化检查...")
    sql_file_path = os.path.join(os.getcwd(), "sql", "table.sql")
    if os.path.exists(sql_file_path):
        if init_database(sql_file_path):
            logger.info("数据库初始化成功")
        else:
            logger.error("数据库初始化失败")
    else:
        # 如果SQL文件不存在，只进行基本的数据库和表检查
        if init_database():
            logger.info("数据库基本检查完成")
        else:
            logger.error("数据库基本检查失败")

    app = FastAPI(
        title=settings.PROJECT_NAME,
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        description="企业微信命令桥接服务API",
        version="1.0.0",
        docs_url="/docs",  # 启用Swagger UI
        redoc_url="/redoc",  # 启用ReDoc
        redirect_slashes=False  # 禁用URL斜杠自动重定向
    )

    # 自定义OpenAPI模式生成
    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema

        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )

        # 这里可以添加自定义的OpenAPI模式设置
        app.openapi_schema = openapi_schema
        return app.openapi_schema

    app.openapi = custom_openapi

    # 配置CORS
    if settings.BACKEND_CORS_ORIGINS:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[
                str(origin)
                for origin in settings.BACKEND_CORS_ORIGINS
            ],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    # 添加请求日志中间件
    app.add_middleware(RequestLoggerMiddleware)
    logger.info("已添加请求日志中间件，将记录所有路由的请求和响应参数")

    # 注册容器
    app.container = container

    # 配置依赖注入 - 确保所有路由都能访问到容器
    from app.api.routes import commands
    commands.router.container = container

    # 同样配置其他路由的容器（如果需要）
    videos_router.container = container
    comments_router.container = container
    comment_replies_router.container = container

    # 注册路由
    app.include_router(health.router, tags=["健康检查"])
    app.include_router(wecom.router, tags=["企业微信"])
    app.include_router(
        tasks.router,
        prefix=f"{settings.API_V1_STR}/tasks",
        tags=["任务管理"]
    )
    app.include_router(
        users.router,
        prefix=f"{settings.API_V1_STR}/users",
        tags=["用户管理"]
    )
    app.include_router(
        videos_router,
        prefix=f"{settings.API_V1_STR}/videos",
        tags=["视频管理"]
    )
    app.include_router(
        comments_router,
        prefix=f"{settings.API_V1_STR}/comments",
        tags=["评论管理"]
    )
    app.include_router(
        comment_replies_router,
        prefix=f"{settings.API_V1_STR}/comment-replies",
        tags=["评论回复管理"]
    )
    app.include_router(
        commands_router,
        prefix=f"{settings.API_V1_STR}/commands",
        tags=["指令下发管理"]
    )

    # 确保上传目录存在
    uploads_dir = settings.UPLOADS_DIR
    os.makedirs(uploads_dir, exist_ok=True)
    logger.info(f"上传目录已确认: {uploads_dir}")

    # 创建静态文件目录
    static_dir = "app/static"
    os.makedirs(static_dir, exist_ok=True)
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

    # 挂载js/css等静态文件
    frontend_static_dir = f"{settings.FRONTEND_STATIC_DIR}/dist"
    logger.info(f"前端静态资源目录: {frontend_static_dir}")

    # 检查前端静态资源目录是否存在
    frontend_static_path = f"{frontend_static_dir}/static"
    if os.path.exists(frontend_static_path):
        app.mount(
            "/frontend/static",
            StaticFiles(directory=frontend_static_path),
            name="frontend_static"
        )
        logger.info(f"前端静态资源挂载成功: {frontend_static_path}")
    else:
        logger.warning(
            f"前端静态资源目录不存在: {frontend_static_path}，跳过挂载"
        )

    # 注册前端spa文件路由
    app.include_router(frontend_router, tags=["前端单页面应用"])

    # 初始化调度器
    scheduler_service.start()

    # 设置评论相关的定时任务
    try:
        # 从配置中获取Coze相关参数
        coze_service = container.coze_service()
        coze_bot_id = settings.COZE_BOT_ID
        coze_user_id = settings.COZE_USER_ID

        # 从.env获取任务间隔配置（秒）
        unreplied_interval = settings.COMMENT_UNREPLIED_INTERVAL
        pending_interval = settings.COMMENT_PENDING_INTERVAL

        # 检查配置是否有效（大于0才启用任务）
        if not unreplied_interval or unreplied_interval <= 0:
            logger.info(
                "未回复评论处理任务已禁用（COMMENT_UNREPLIED_INTERVAL未设置或为0）"
            )
            unreplied_interval = None
            
        if not pending_interval or pending_interval <= 0:
            logger.info(
                "待回复任务处理任务已禁用（COMMENT_PENDING_INTERVAL未设置或为0）"
            )
            pending_interval = None

        # 只有在至少有一个任务启用时才初始化
        if unreplied_interval or pending_interval:
            # 初始化评论任务
            task_ids = setup_comment_tasks(
                coze_service=coze_service,
                coze_bot_id=coze_bot_id,
                coze_user_id=coze_user_id,
                unreplied_comments_interval=unreplied_interval or 300,
                # 如果只启用其中一个，给未启用的一个默认值
                pending_replies_interval=pending_interval or 600
            )

            logger.info(f"评论任务初始化成功: {task_ids}")
            logger.info(
                f"任务配置: 未回复评论={unreplied_interval or '禁用'}秒, "
                f"待回复处理={pending_interval or '禁用'}秒"
            )
        else:
            logger.info("所有评论任务均已禁用，跳过任务初始化")
            
    except Exception as e:
        logger.error(f"评论任务初始化失败: {str(e)}")

    # 设置日志清理定时任务
    try:
        # 设置日志清理任务，每天执行一次（86400秒）
        log_cleanup_task_ids = setup_log_cleanup_tasks(
            log_file_path="logs/app.log",
            cleanup_interval=86400  # 24小时
        )

        logger.info(f"日志清理任务初始化成功: {log_cleanup_task_ids}")
    except Exception as e:
        logger.error(f"日志清理任务初始化失败: {str(e)}")

    # 设置视频爬评定时任务
    try:
        # 设置每天凌晨2点执行的视频爬评任务
        video_crawl_task_ids = setup_video_crawl_tasks()

        logger.info(f"视频爬评任务初始化成功: {video_crawl_task_ids}")
    except Exception as e:
        logger.error(f"视频爬评任务初始化失败: {str(e)}")

    logger.info("应用初始化完成")
    return app


app = create_app()
