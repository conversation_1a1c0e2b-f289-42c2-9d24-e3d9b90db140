"""
请求和响应日志中间件

用于记录所有路由接口的请求参数和响应参数
"""

import json
import logging
import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import Receive

logger = logging.getLogger("app.middlewares.request_logger")


class RequestLoggerMiddleware(BaseHTTPMiddleware):
    """
    中间件：记录所有请求和响应参数
    """
    
    async def dispatch(
        self, request: Request, call_next: Callable
    ) -> Response:
        """处理请求并记录日志
        
        Args:
            request: 客户端请求
            call_next: 下一个处理函数
            
        Returns:
            Response: 响应对象
        """
        # 生成请求ID
        request_id = str(time.time())
        
        # 记录请求信息
        await self._log_request(request, request_id)
        
        # 调用下一个中间件或路由处理函数
        start_time = time.time()
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 记录响应信息
        await self._log_response(request, response, process_time, request_id)
        
        return response
    
    async def _log_request(self, request: Request, request_id: str) -> None:
        """记录请求参数日志
        
        Args:
            request: 客户端请求
            request_id: 请求唯一标识
        """
        # 获取请求头
        headers = dict(request.headers.items())
        # 移除敏感信息
        if 'authorization' in headers:
            headers['authorization'] = '[FILTERED]'
        if 'cookie' in headers:
            headers['cookie'] = '[FILTERED]'
            
        # 获取请求路径和查询参数
        path = request.url.path
        query_params = dict(request.query_params)
        
        # 获取请求体（仅对POST, PUT, PATCH等请求获取请求体）
        body = {}
        if request.method in ["POST", "PUT", "PATCH", "DELETE"]:
            try:
                # 备份一下请求体内容，因为请求体只能读取一次
                body_bytes = await request.body()
                # 将请求体还原，以便后续处理程序能够再次读取
                request._receive = _receive_wrapper(
                    body_bytes, request._receive
                )
                
                # 尝试解析请求体为JSON
                try:
                    body = json.loads(body_bytes)
                except json.JSONDecodeError:
                    # 如果不是JSON，则转为字符串
                    body = body_bytes.decode('utf-8', errors='replace')
            except Exception as e:
                logger.warning(f"无法获取请求体: {str(e)}")
        
        # 记录请求日志
        log_data = {
            "request_id": request_id,
            "type": "request",
            "method": request.method,
            "path": path,
            "query_params": query_params,
            "headers": headers,
            "body": body,
            "client": {
                "host": request.client.host if request.client else None,
                "port": request.client.port if request.client else None
            }
        }
        
        # 将请求参数记录到日志
        logger.info(f"请求: {json.dumps(log_data, ensure_ascii=False)}")
    
    async def _log_response(
        self, request: Request, response: Response, 
        process_time: float, request_id: str
    ) -> None:
        """记录响应参数日志
        
        Args:
            request: 客户端请求
            response: 服务器响应
            process_time: 处理时间（秒）
            request_id: 请求唯一标识
        """
        # 记录响应状态码和处理时间
        path = request.url.path
        status_code = response.status_code
        
        # 获取响应体内容
        response_body = {}
        
        # 检查是否已经有响应主体
        if hasattr(response, 'body') and response.body:
            response_body_bytes = response.body
            try:
                # 尝试将响应体解析为JSON
                try:
                    response_body = json.loads(response_body_bytes)
                except json.JSONDecodeError:
                    # 如果不是JSON，则转为字符串
                    response_body = response_body_bytes.decode(
                        'utf-8', errors='replace'
                    )
            except Exception as e:
                logger.warning(f"无法解析响应体: {str(e)}")
        
        # 记录响应日志
        log_data = {
            "request_id": request_id,
            "type": "response",
            "method": request.method,
            "path": path,
            "status_code": status_code,
            "process_time": f"{process_time:.6f}s",
            "headers": dict(response.headers.items()),
            "body": response_body
        }
        
        # 将响应参数记录到日志
        logger.info(f"响应: {json.dumps(log_data, ensure_ascii=False)}")


def _receive_wrapper(body: bytes, receive: Receive) -> Receive:
    """包装原始接收函数，使其可以多次读取请求体
    
    Args:
        body: 请求体内容
        receive: 原始接收函数
        
    Returns:
        Receive: 新的接收函数
    """
    async def wrapped_receive() -> dict:
        data = await receive()
        if data["type"] == "http.request":
            data["body"] = body
        return data
    
    return wrapped_receive 