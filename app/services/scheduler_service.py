"""
后台任务调度服务
提供基于APScheduler的后台定时任务管理功能
"""
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.pool import ThreadPoolExecutor
import logging
import atexit
from typing import Callable

logger = logging.getLogger(__name__)


class SchedulerService:
    """后台任务调度服务"""
    
    def __init__(self):
        """初始化调度器"""
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': ThreadPoolExecutor(20)
        }
        job_defaults = {
            'coalesce': True,
            'max_instances': 1
        }
        
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        # 确保应用退出时关闭调度器
        atexit.register(self.shutdown)
        
        # 状态标记
        self._running = False
    
    def start(self):
        """启动调度器"""
        if not self._running:
            self.scheduler.start()
            self._running = True
            logger.info("后台任务调度器已启动")
        return self
    
    def shutdown(self):
        """关闭调度器"""
        if self._running:
            self.scheduler.shutdown()
            self._running = False
            logger.info("后台任务调度器已关闭")
    
    def add_job(self, job_id: str, func: Callable, trigger: str = 'interval', 
                **trigger_args) -> str:
        """添加定时任务
        
        Args:
            job_id: 任务ID
            func: 要执行的函数
            trigger: 触发器类型 (interval, cron, date)
            **trigger_args: 触发器参数
            
        Returns:
            str: 任务ID
        """
        try:
            job = self.scheduler.add_job(
                func=func,
                trigger=trigger,
                id=job_id,
                replace_existing=True,
                **trigger_args
            )
            logger.info(f"任务已添加: {job_id}")
            return job.id
        except Exception as e:
            logger.error(f"添加任务失败: {str(e)}")
            raise
    
    def remove_job(self, job_id: str) -> bool:
        """移除定时任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            bool: 是否成功移除
        """
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"任务已移除: {job_id}")
            return True
        except Exception as e:
            logger.error(f"移除任务失败: {str(e)}")
            return False
    
    def get_jobs(self) -> list:
        """获取所有任务列表"""
        return self.scheduler.get_jobs()
    
    def pause_job(self, job_id: str) -> bool:
        """暂停任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            bool: 是否成功暂停
        """
        try:
            self.scheduler.pause_job(job_id)
            logger.info(f"任务已暂停: {job_id}")
            return True
        except Exception as e:
            logger.error(f"暂停任务失败: {str(e)}")
            return False
    
    def resume_job(self, job_id: str) -> bool:
        """恢复任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            bool: 是否成功恢复
        """
        try:
            self.scheduler.resume_job(job_id)
            logger.info(f"任务已恢复: {job_id}")
            return True
        except Exception as e:
            logger.error(f"恢复任务失败: {str(e)}")
            return False


# 创建全局实例
scheduler_service = SchedulerService() 