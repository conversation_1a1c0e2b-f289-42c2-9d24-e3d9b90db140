"""
视频首次启用服务 - 使用Redis缓存管理首次启用状态
"""
import logging
import redis
from typing import List
from app.core.config import settings

logger = logging.getLogger("app.services.video_first_enable_service")


class VideoFirstEnableService:
    """视频首次启用服务类"""

    def __init__(self, redis_client: redis.Redis):
        """初始化服务
        
        Args:
            redis_client: Redis客户端实例
        """
        self.redis = redis_client
        self.prefix = settings.REDIS_PREFIX
        self.first_enable_key_format = (
            f"{self.prefix}first_enable:{{video_id}}"
        )

    def mark_video_for_first_enable(self, video_id: str) -> bool:
        """标记视频为待首次启用状态
        
        在创建视频时调用，将视频ID存储到Redis中
        
        Args:
            video_id: 视频ID
            
        Returns:
            bool: 是否标记成功
        """
        try:
            key = self.first_enable_key_format.format(video_id=video_id)
            # 设置键值，过期时间为30天（防止永久占用内存）
            self.redis.set(key, "1", ex=30 * 24 * 3600)
            logger.info(f"已标记视频 {video_id} 为待首次启用状态")
            return True
        except Exception as e:
            logger.error(f"标记视频 {video_id} 首次启用状态失败: {str(e)}")
            return False

    def check_and_consume_first_enable(self, video_id: str) -> bool:
        """检查并消费首次启用标记
        
        在启用视频时调用，如果存在首次启用标记则删除并返回True
        
        Args:
            video_id: 视频ID
            
        Returns:
            bool: 是否为首次启用（True表示首次启用，False表示非首次启用）
        """
        try:
            key = self.first_enable_key_format.format(video_id=video_id)
            
            # 检查键是否存在
            if self.redis.exists(key):
                # 删除键（消费标记）
                self.redis.delete(key)
                logger.info(f"视频 {video_id} 首次启用，已消费标记")
                return True
            else:
                logger.debug(f"视频 {video_id} 非首次启用")
                return False
        except Exception as e:
            logger.error(f"检查视频 {video_id} 首次启用状态失败: {str(e)}")
            return False

    def batch_check_and_consume_first_enable(
        self, video_ids: List[str]
    ) -> List[str]:
        """批量检查并消费首次启用标记
        
        Args:
            video_ids: 视频ID列表
            
        Returns:
            List[str]: 首次启用的视频ID列表
        """
        first_time_enabled = []
        
        for video_id in video_ids:
            if self.check_and_consume_first_enable(video_id):
                first_time_enabled.append(video_id)
        
        return first_time_enabled

    def remove_first_enable_mark(self, video_id: str) -> bool:
        """移除首次启用标记
        
        在删除视频或其他需要清理的场景下调用
        
        Args:
            video_id: 视频ID
            
        Returns:
            bool: 是否移除成功
        """
        try:
            key = self.first_enable_key_format.format(video_id=video_id)
            deleted = self.redis.delete(key)
            if deleted:
                logger.info(f"已移除视频 {video_id} 的首次启用标记")
            return bool(deleted)
        except Exception as e:
            logger.error(f"移除视频 {video_id} 首次启用标记失败: {str(e)}")
            return False

    def get_all_pending_first_enable_videos(self) -> List[str]:
        """获取所有待首次启用的视频ID列表
        
        Returns:
            List[str]: 待首次启用的视频ID列表
        """
        try:
            pattern = self.first_enable_key_format.format(video_id="*")
            keys = self.redis.keys(pattern)
            
            # 从键名中提取视频ID
            video_ids = []
            prefix_len = len(f"{self.prefix}first_enable:")
            for key in keys:
                video_id = key[prefix_len:]
                video_ids.append(video_id)
            
            logger.info(f"找到 {len(video_ids)} 个待首次启用的视频")
            return video_ids
        except Exception as e:
            logger.error(f"获取待首次启用视频列表失败: {str(e)}")
            return [] 