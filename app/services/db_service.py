"""
数据库服务模块

提供MySQL数据库连接和操作的服务。
"""

import logging
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

from app.models.video import Base

logger = logging.getLogger("app.services.db_service")


class DBService:
    """数据库服务类

    提供MySQL数据库连接和会话管理功能。
    """

    def __init__(
        self,
        db_host: str,
        db_port: int,
        db_user: str,
        db_password: str,
        db_name: str,
    ):
        """初始化数据库服务

        Args:
            db_host: 数据库主机地址
            db_port: 数据库端口
            db_user: 数据库用户名
            db_password: 数据库密码
            db_name: 数据库名称
        """
        self.connection_string = (
            f"mysql+pymysql://{db_user}:{db_password}"
            f"@{db_host}:{db_port}/{db_name}"
        )
        try:
            self.engine = create_engine(
                self.connection_string,
                pool_pre_ping=True,       # 使用前检查连接
                pool_recycle=1800,        # 连接在池中的最大生存时间减为30分钟
                pool_size=10,             # 连接池大小
                max_overflow=20,          # 允许超出pool_size的连接数量
                pool_timeout=30,          # 等待连接超时时间 
                connect_args={
                    "connect_timeout": 10,  # 建立连接超时时间
                    "read_timeout": 30,    # 读取超时时间
                    "write_timeout": 30    # 写入超时时间
                },
                echo_pool=False           # 不记录连接池日志
            )
            self.SessionLocal = sessionmaker(
                autocommit=False, autoflush=False, bind=self.engine
            )
            # 创建所有表
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库连接初始化成功")
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {str(e)}")
            raise

    def get_db(self) -> Session:
        """获取数据库会话

        Returns:
            Session: 数据库会话对象
        """
        db = self.SessionLocal()
        try:
            return db
        except Exception as e:
            logger.error(f"获取数据库会话失败: {str(e)}")
            db.close()
            raise

    def close_db(self, db: Session) -> None:
        """关闭数据库会话

        Args:
            db: 数据库会话对象
        """
        if db:
            try:
                db.close()
                logger.debug("数据库会话已关闭")
            except Exception as e:
                logger.error(f"关闭数据库会话失败: {str(e)}")