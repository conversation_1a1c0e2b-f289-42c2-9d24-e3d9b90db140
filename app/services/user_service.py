"""
B站用户相关服务

提供用户的业务逻辑处理
"""

from typing import Any, Optional, List, Dict
from sqlalchemy.orm import Session
from datetime import datetime
import redis.client
import logging

from app.core.config import settings
from app.models.user import BiliUser
from app.repositories.user_repository import UserRepository
from app.schemas.user import (
    UserCreate, UserUpdate, UserListFilters, PaginationParams, LoginStatusUpdate,
    QRCodeUpload, BatchDisableRequest
)

logger = logging.getLogger("app.services.user_service")


class UserService:
    def __init__(self, db: Session):
        self.db = db
        self.user_repo = UserRepository(db)

    def get_users(
        self,
        username: Optional[str] = None,
        user_id: Optional[str] = None,
        login_status: Optional[str] = None,
        machine_code: Optional[str] = None,
        is_disabled: Optional[bool] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """获取B站用户列表，支持筛选和分页

        Args:
            username: 用户名(模糊匹配)
            user_id: B站用户ID
            login_status: 登录状态
            machine_code: 关联的机器码
            is_disabled: 是否禁用
            page: 页码
            page_size: 每页数量

        Returns:
            Dict[str, Any]: 用户列表响应
        """
        # 创建筛选条件和分页参数
        filters = UserListFilters(
            username=username,
            user_id=user_id,
            login_status=login_status,
            machine_code=machine_code,
            is_disabled=is_disabled
        )

        pagination = PaginationParams(
            page=page,
            page_size=page_size
        )

        users, total = self.user_repo.get_all_with_filters(
            filters.dict(exclude_none=True),
            pagination.page,
            pagination.page_size
        )

        # 为每个用户添加关联视频数量
        user_list = []
        for user in users:
            user_dict = user.__dict__
            # 计算关联视频数量
            user_dict['video_count'] = (
                len(user.videos) if hasattr(user, 'videos') else 0
            )
            user_list.append(user_dict)

        return {
            "items": user_list,
            "total": total,
            "page": pagination.page,
            "page_size": pagination.page_size
        }

    def get_all_users(self) -> List[Dict[str, Any]]:
        """获取所有B站用户，不分页

        Returns:
            List[Dict[str, Any]]: 所有用户列表
        """
        users = self.user_repo.get_all()

        # 为每个用户添加关联视频数量
        user_list = []
        for user in users:
            user_dict = user.__dict__
            # 计算关联视频数量
            user_dict['video_count'] = (
                len(user.videos) if hasattr(user, 'videos') else 0
            )
            user_list.append(user_dict)

        return user_list

    def create_user(self, user_in: UserCreate) -> BiliUser:
        """创建新的B站用户

        Args:
            user_in: 用户创建请求

        Returns:
            BiliUser: 创建的用户信息
            
        Raises:
            ValueError: 用户ID已存在时抛出
        """
        # 检查用户ID是否已存在
        existing_user = self.user_repo.get_by_user_id(user_in.user_id)
        if existing_user:
            raise ValueError(f"用户ID为 {user_in.user_id} 的用户已存在")

        # 创建新用户
        user = BiliUser(**user_in.dict())
        return self.user_repo.create(user)

    def batch_update_user_status(
        self, request: BatchDisableRequest
    ) -> List[Dict[str, Any]]:
        """批量禁用或启用B站用户

        Args:
            request: 批量禁用请求，包含ids和is_disabled字段

        Returns:
            List[Dict[str, Any]]: 更新后的用户列表
            
        Raises:
            ValueError: ids列表为空时抛出
        """
        # 记录请求内容，帮助调试
        logger.info(f"批量禁用/启用用户请求: request={request}")

        ids = request.ids

        if len(ids) == 0:
            raise ValueError("ids列表不能为空")

        # 处理业务逻辑
        updated_users = []
        not_found_ids = []

        for user_id in ids:
            try:
                # 确保user_id是整型
                if not isinstance(user_id, int):
                    logger.warning(f"非整数ID被转换: {user_id}")
                    user_id = int(user_id)

                user = self.user_repo.get_by_smart_id(str(user_id))

                if not user:
                    not_found_ids.append(str(user_id))
                    continue

                # 更新用户禁用状态
                user.is_disabled = request.is_disabled
                updated_user = self.user_repo.update(user)

                # 构建规范的用户响应数据，确保包含所有必要字段
                user_data = {
                    "id": updated_user.id,
                    "username": updated_user.username,
                    "user_id": updated_user.user_id,
                    "avatar_url": updated_user.avatar_url,
                    "profile_url": updated_user.profile_url,
                    "description": updated_user.description,
                    "machine_code": updated_user.machine_code,
                    "machine_name": updated_user.machine_name,
                    "login_status": updated_user.login_status,
                    "is_disabled": updated_user.is_disabled,
                    "created_at": updated_user.created_at,
                    "updated_at": updated_user.updated_at,
                    "video_count": (
                        len(updated_user.videos)
                        if hasattr(updated_user, 'videos')
                        else 0
                    )
                }

                updated_users.append(user_data)
            except Exception as e:
                logger.error(f"处理用户ID {user_id} 时出错: {str(e)}")
                # 继续处理下一个用户
                continue

        # 如果有未找到的用户，记录日志
        if not_found_ids:
            logger.warning(
                f"批量更新用户禁用状态时未找到以下用户ID: {', '.join(not_found_ids)}"
            )

        logger.info(f"成功更新了 {len(updated_users)} 个用户的禁用状态")
        return updated_users

    def get_user_detail(self, user_id: str) -> Dict[str, Any]:
        """获取B站用户详情

        Args:
            user_id: B站用户ID

        Returns:
            Dict[str, Any]: 用户详情
            
        Raises:
            ValueError: 特殊路径参数或用户不存在时抛出
        """
        # 特殊处理 "all" 路径参数，避免与用户ID混淆
        if user_id == "all":
            raise ValueError("请使用 /api/v1/users/all/ 端点获取所有用户")

        user = self.user_repo.get_by_smart_id(user_id)

        if not user:
            raise ValueError(f"未找到ID为 {user_id} 的用户")

        # 返回用户详情，包括视频数量
        user_dict = user.__dict__
        user_dict['video_count'] = (
            len(user.videos) if hasattr(user, 'videos') else 0
        )

        return user_dict

    def update_user(self, user_id: str, user_in: UserUpdate) -> BiliUser:
        """更新B站用户信息

        Args:
            user_id: B站用户ID
            user_in: 用户更新请求

        Returns:
            BiliUser: 更新后的用户信息
            
        Raises:
            ValueError: 用户不存在时抛出
        """
        user = self.user_repo.get_by_smart_id(user_id)

        if not user:
            raise ValueError(f"未找到ID为 {user_id} 的用户")

        # 更新用户属性
        update_data = user_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)

        return self.user_repo.update(user)

    def delete_user(self, user_id: str) -> None:
        """删除B站用户

        Args:
            user_id: B站用户ID
            
        Raises:
            ValueError: 用户不存在时抛出
        """
        user = self.user_repo.get_by_smart_id(user_id)

        if not user:
            raise ValueError(f"未找到ID为 {user_id} 的用户")

        self.user_repo.delete(user.id)

    def update_login_status(
        self, user_id: str, status_update: LoginStatusUpdate
    ) -> BiliUser:
        """更新B站用户的登录状态

        Args:
            user_id: B站用户ID
            status_update: 登录状态更新请求

        Returns:
            BiliUser: 更新后的用户信息
            
        Raises:
            ValueError: 用户不存在时抛出
        """
        user = self.user_repo.get_by_smart_id(user_id)

        if not user:
            raise ValueError(f"未找到ID为 {user_id} 的用户")

        # 更新登录状态和相关机器信息
        user.login_status = status_update.login_status
        user.last_login_time = datetime.now()

        # 如果提供了机器码和机器名称，也进行更新
        if status_update.machine_code:
            user.machine_code = status_update.machine_code
        if status_update.machine_name:
            user.machine_name = status_update.machine_name

        return self.user_repo.update(user)

    def toggle_user_disable_status(self, user_id: str, is_disabled: bool) -> BiliUser:
        """禁用或启用B站用户

        Args:
            user_id: B站用户ID
            is_disabled: 是否禁用用户

        Returns:
            BiliUser: 更新后的用户信息
            
        Raises:
            ValueError: 用户不存在时抛出
        """
        user = self.user_repo.get_by_smart_id(user_id)

        if not user:
            raise ValueError(f"未找到ID为 {user_id} 的用户")

        # 更新用户禁用状态
        user.is_disabled = is_disabled

        return self.user_repo.update(user)

    def upload_qrcode(
        self, qrcode_data: QRCodeUpload, redis_client: redis.client.Redis
    ) -> Dict[str, Any]:
        """上传二维码接口

        将base64编码的二维码存储在Redis中，设置10分钟过期时间

        Args:
            qrcode_data: 二维码上传请求数据，包含机器码和二维码base64编码
            redis_client: Redis客户端

        Returns:
            Dict[str, Any]: 上传成功的二维码信息
            
        Raises:
            Exception: Redis操作失败时抛出
        """
        try:
            # 构建Redis键
            redis_key = f"{settings.REDIS_PREFIX}qrcode:{qrcode_data.machine_code}"

            logger.info(f"正在上传二维码，机器码: {qrcode_data.machine_code}")

            # 存储二维码数据并设置过期时间(10分钟)
            redis_client.set(
                redis_key,
                qrcode_data.qrcode_base64,
                ex=600  # 600秒 = 10分钟
            )

            logger.info(f"二维码上传成功，机器码: {qrcode_data.machine_code}")

            return {
                "machine_code": qrcode_data.machine_code,
                "qrcode_base64": qrcode_data.qrcode_base64
            }
        except Exception as e:
            logger.error(f"上传二维码失败: {str(e)}")
            raise Exception(f"上传二维码失败: {str(e)}")

    def get_qrcode(
        self, machine_code: str, redis_client: redis.client.Redis
    ) -> Dict[str, Any]:
        """获取二维码接口

        根据机器码从Redis中获取之前上传的二维码数据

        Args:
            machine_code: 机器码
            redis_client: Redis客户端

        Returns:
            Dict[str, Any]: 二维码信息

        Raises:
            ValueError: 当未找到指定机器码的二维码数据时抛出
            Exception: Redis操作失败时抛出
        """
        try:
            # 构建Redis键
            redis_key = f"{settings.REDIS_PREFIX}qrcode:{machine_code}"

            logger.info(f"正在获取二维码，机器码: {machine_code}")

            # 从Redis中获取二维码数据
            qrcode_base64 = redis_client.get(redis_key)

            if not qrcode_base64:
                logger.warning(f"未找到机器码为 {machine_code} 的二维码数据或已过期")
                raise ValueError(
                    f"未找到机器码为 {machine_code} 的二维码数据或已过期"
                )

            logger.info(f"二维码获取成功，机器码: {machine_code}")

            return {
                "machine_code": machine_code,
                "qrcode_base64": qrcode_base64
            }
        except redis.client.RedisError as e:
            logger.error(f"Redis连接错误: {str(e)}")
            raise Exception(f"访问Redis失败: {str(e)}")
        except ValueError as e:
            raise e
        except Exception as e:
            logger.error(f"获取二维码失败: {str(e)}")
            raise Exception(f"获取二维码失败: {str(e)}")

    def update_user_by_machine_code(
        self, machine_code: str, user_in: UserUpdate
    ) -> BiliUser:
        """通过设备码查询并更新用户信息

        Args:
            machine_code: 设备码
            user_in: 用户更新信息，至少包含登录状态和B站UID

        Returns:
            BiliUser: 更新后的用户信息
            
        Raises:
            ValueError: 创建新用户时缺少必要信息时抛出
        """
        # 根据设备码查询用户
        user = self.user_repo.get_by_machine_code(machine_code)

        if not user:
            # 用户不存在时，创建新用户
            logger.info(
                f"未找到关联设备码为 {machine_code} 的用户，将创建新用户"
            )
            
            # 确保必要的用户信息在user_in中
            if not user_in.user_id:
                raise ValueError("创建新用户时必须提供user_id")
                
            # 创建新用户基本信息
            new_user = BiliUser(
                machine_code=machine_code,
                machine_name=user_in.machine_name or "未知设备",
                username=user_in.username or f"用户_{machine_code[-6:]}",
                user_id=user_in.user_id,
                login_status=user_in.login_status or "offline",
                is_disabled=False,
                last_login_time=datetime.now()
            )
            
            # 创建用户并获取创建后的用户对象
            user = self.user_repo.create(new_user)
        else:
            # 更新用户属性
            update_data = user_in.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(user, field, value)

            # 如果更新包含登录状态，同时更新登录时间
            if 'login_status' in update_data:
                user.last_login_time = datetime.now()

            # 更新用户信息
            user = self.user_repo.update(user)

        return user

    def get_login_status_by_machine_code(
        self, machine_code: str
    ) -> Dict[str, Any]:
        """通过机器码查询用户登录状态

        Args:
            machine_code: 设备码

        Returns:
            Dict[str, Any]: 只包含登录状态的字典

        Raises:
            ValueError: 当未找到关联该设备码的用户时抛出
        """
        # 根据设备码查询用户
        user = self.user_repo.get_by_machine_code(machine_code)
        
        # 如果未找到用户，抛出异常
        if not user:
            raise ValueError(f"未找到关联设备码为 {machine_code} 的用户")

        # 返回登录状态
        return {"login_status": user.login_status}

    def get_user_by_machine_code(
        self, machine_code: str
    ) -> Dict[str, Any]:
        """通过机器码查询用户完整信息

        Args:
            machine_code: 设备码

        Returns:
            Dict[str, Any]: 包含用户完整信息的字典

        Raises:
            ValueError: 当未找到关联该设备码的用户时抛出
        """
        # 根据设备码查询用户
        user = self.user_repo.get_by_machine_code(machine_code)
        
        # 如果未找到用户，抛出异常
        if not user:
            raise ValueError(f"未找到关联设备码为 {machine_code} 的用户")

        # 构建可序列化的用户字典
        user_dict = {
            "id": user.id,
            "username": user.username,
            "user_id": user.user_id,
            "avatar_url": user.avatar_url,
            "profile_url": user.profile_url,
            "description": user.description,
            "machine_code": user.machine_code,
            "machine_name": user.machine_name,
            "login_status": user.login_status,
            "is_disabled": user.is_disabled,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
            "last_login_time": user.last_login_time
        }
        
        # 计算关联视频数量
        user_dict['video_count'] = (
            len(user.videos) if hasattr(user, 'videos') else 0
        )
        
        return user_dict 