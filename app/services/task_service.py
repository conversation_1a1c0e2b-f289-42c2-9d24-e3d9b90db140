"""任务业务逻辑服务"""
import logging
import os
from pathlib import Path
from typing import Tuple, Optional, Dict, Any
from fastapi import UploadFile

from app.repositories.task_repository import TaskRepository
from app.services.wecom_service import WecomService
from app.schemas.task import TaskState, TaskUpdate
from app.core.config import settings

logger = logging.getLogger(__name__)


class TaskService:
    """任务业务逻辑服务"""

    def __init__(
        self,
        task_repository: TaskRepository,
        wecom_service: WecomService
    ):
        self.task_repository = task_repository
        self.wecom_service = wecom_service

    def get_all_tasks(self):
        """获取所有任务"""
        logger.info("获取所有任务列表")
        tasks = self.task_repository.get_all_tasks()
        logger.info(f"成功获取任务列表，共{len(tasks)}个任务")
        return tasks

    def get_next_pending_task(self):
        """获取下一个待处理任务并更新状态"""
        logger.info("获取队列中的下一个任务")
        task = self.task_repository.get_next_task()

        if task:
            logger.info(
                f"获取到待处理任务: ID={task.task_id}, 类型={task.task_type}"
            )
            # 更新任务状态为"已接收"
            updated_task, success = self.task_repository.update_task_state(
                task.task_id, TaskState.ACCEPTED
            )

            # 发送状态更新消息给用户（通知用户任务已被接收）
            if success and task.wecom:
                logger.info(f"发送任务接收通知: ID={task.task_id}")
                self.wecom_service.send_task_result_message(
                    task.task_id,
                    "",
                    TaskState.ACCEPTED
                )

            return updated_task if success else task
        else:
            logger.info("队列中没有待处理任务")
            return None

    def get_task_by_id(self, task_id: str):
        """根据ID获取任务"""
        return self.task_repository.get_task_by_id(task_id)

    def update_task_status(
        self, task_id: str, update: TaskUpdate
    ) -> Tuple[bool, str, Optional[Any]]:
        """更新任务状态

        Returns:
            Tuple[bool, str, Optional[Any]]: (是否成功, 消息, 任务对象)
        """
        # 发送任务状态更新消息
        wecom_result, message = self.wecom_service.send_task_result_message(
            task_id,
            update.result,
            update.state
        )

        # 如果状态为成功或失败，从队列中删除任务
        if wecom_result and update.state in [
            TaskState.SUCCESS, TaskState.FAILED
        ]:
            removed = self.task_repository.remove_task(task_id)
            if removed:
                message = f"{message} (任务已从队列中删除)"

        # 获取最新任务状态
        task = self.task_repository.get_task_by_id(task_id)
        return wecom_result, message, task

    def update_task_with_notification(
        self, task_id: str, update: TaskUpdate
    ) -> Dict[str, Any]:
        """更新任务状态并发送通知

        Args:
            task_id: 任务ID
            update: 任务更新数据

        Returns:
            Dict[str, Any]: 包含status、message和task的字典
        """
        try:
            wecom_result, message, task = self.update_task_status(
                task_id, update
            )

            return {
                "status": "success" if wecom_result else "partial_success",
                "message": message,
                "task": task
            }
        except Exception as e:
            logger.error(f"更新任务{task_id}状态失败: {str(e)}")
            return {
                "status": "error",
                "message": f"更新任务状态失败: {str(e)}",
                "task": None
            }

    async def upload_task_file(
        self, task_id: str, file: UploadFile
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """上传任务文件

        Returns:
            Tuple[bool, str, Optional[Dict]]: (是否成功, 消息, 文件信息)
        """
        # 检查任务是否存在
        task = self.task_repository.get_task_by_id(task_id)
        if not task:
            return False, f"找不到任务ID: {task_id}", None

        # 检查文件大小
        await file.seek(0)
        file_content = await file.read()
        file_size = len(file_content)
        if file_size == 0:
            logger.warning(f"文件大小为0字节: {file.filename}")
            return (
                False,
                f"无法上传空文件: {file.filename}。文件大小为0字节。",
                None
            )

        # 重置文件指针位置
        await file.seek(0)

        # 处理文件保存
        try:
            file_path, original_filename = await self._save_uploaded_file(
                task_id, file, file_content
            )
        except Exception as e:
            logger.error(f"文件保存失败: {str(e)}")
            return False, f"文件保存失败: {str(e)}", None

        # 更新任务文件路径
        updated_task, success = self.task_repository.update_task_file(
            task_id, file_path
        )

        if success:
            file_info = {
                "original_name": original_filename,
                "saved_path": file_path,
                "file_size": file_size,
                "task": updated_task
            }
            return (
                True,
                f"文件上传成功: {original_filename}，大小: {file_size}字节",
                file_info
            )
        else:
            return False, "更新任务文件信息失败", None

    async def _save_uploaded_file(
        self, task_id: str, file: UploadFile, file_content: bytes
    ) -> Tuple[str, str]:
        """保存上传的文件

        Returns:
            Tuple[str, str]: (文件路径, 原始文件名)
        """
        # 确保上传目录存在
        os.makedirs(settings.UPLOADS_DIR, exist_ok=True)

        # 为每个任务创建单独的子目录
        task_upload_dir = os.path.join(
            settings.UPLOADS_DIR, f"task_{task_id}"
        )
        os.makedirs(task_upload_dir, exist_ok=True)

        # 获取原始文件名
        original_filename = file.filename or ""

        # 处理文件名和扩展名
        file_name = self._process_filename(original_filename)
        file_path = os.path.join(task_upload_dir, file_name)

        # 保存文件
        try:
            logger.info(
                f"保存上传文件: {file_path}，文件大小: {len(file_content)}字节"
            )
            with open(file_path, "wb") as buffer:
                buffer.write(file_content)
        except OSError as e:
            # 特别处理macOS的Errno 36错误（文件名过长）
            if (
                "[Errno 36]" in str(e) or "File name too long" in str(e)
            ):
                file_path = self._handle_long_filename_error(
                    task_upload_dir, original_filename, file_content
                )
            else:
                raise e

        return file_path, original_filename

    def _process_filename(self, original_filename: str) -> str:
        """处理文件名，确保符合系统限制"""
        if not original_filename:
            return "file"

        # 获取文件扩展名
        file_extension = (
            Path(original_filename).suffix.lower()
            if original_filename else ""
        )
        if file_extension.startswith('.'):
            file_extension = file_extension[1:]

        # 验证文件类型
        if (
            file_extension
            and file_extension not in settings.ALLOWED_EXTENSIONS
        ):
            raise ValueError(f"不支持的文件类型: {file_extension}")

        # 检查文件名长度限制
        max_filename_length = 100
        if len(original_filename) > max_filename_length:
            logger.warning(
                f"文件名超过安全限制({max_filename_length}字符): "
                f"{len(original_filename)}字符"
            )
            # 执行截断处理
            filename_stem = Path(original_filename).stem
            truncated_stem = filename_stem[:max_filename_length - 10] + "..."
            truncated_filename = (
                f"{truncated_stem}.{file_extension}"
                if file_extension else truncated_stem
            )
            logger.info(
                f"文件名过长，已截断: {original_filename} -> {truncated_filename}"
            )
            return truncated_filename

        return original_filename

    def _handle_long_filename_error(
        self, task_upload_dir: str, original_filename: str, file_content: bytes
    ) -> str:
        """处理文件名过长错误"""
        # 获取文件扩展名
        file_extension = (
            Path(original_filename).suffix.lower()
            if original_filename else ""
        )
        if file_extension.startswith('.'):
            file_extension = file_extension[1:]

        # 生成一个更短的安全文件名
        safe_file_name = "file"
        if file_extension:
            safe_file_name = f"{safe_file_name}.{file_extension}"
        safe_file_path = os.path.join(task_upload_dir, safe_file_name)

        logger.warning(
            f"尝试使用更短的安全文件名: {safe_file_path}"
        )
        try:
            with open(safe_file_path, "wb") as buffer:
                buffer.write(file_content)
            logger.info(
                f"使用安全文件名成功保存文件: {safe_file_path}"
            )
            return safe_file_path
        except Exception as inner_e:
            logger.error(
                f"使用安全文件名仍然失败: {str(inner_e)}"
            )
            raise Exception(
                f"文件保存失败，无法处理此文件名: {str(inner_e)}"
            )

    def update_task_file_path(
        self, task_id: str, file_path: str
    ) -> Dict[str, Any]:
        """更新任务文件路径

        Args:
            task_id: 任务ID
            file_path: 文件路径

        Returns:
            Dict: 更新结果
        """
        return {
            "status": "success",
            "message": f"文件路径更新成功: {file_path}"
        }

    def trigger_process_unreplied_comments(self) -> Dict[str, Any]:
        """手动触发处理未回复评论任务

        Returns:
            Dict: 任务执行结果
        """
        try:
            from app.tasks.comment_tasks import process_unreplied_comments
            from app.services.comment_service import CommentService
            from app.core.deps import get_db

            # 获取数据库会话
            db = next(get_db())

            # 创建评论服务实例
            comment_service = CommentService(db)

            # 执行任务
            results = process_unreplied_comments(comment_service)

            return results
        except Exception as e:
            logger.error(
                f"触发处理未回复评论任务失败: {str(e)}"
            )
            return {"error": str(e)}

    def trigger_process_pending_replies(self) -> Dict[str, Any]:
        """手动触发处理待回复任务

        Returns:
            Dict: 任务执行结果
        """
        try:
            from app.tasks.comment_tasks import process_pending_replies
            from app.services.reply_service import ReplyService
            from app.core.config import settings
            from app.core.deps import get_db
# 删除未使用的导入
            from app.core.container import container

            # 获取数据库会话
            db = next(get_db())

            # 获取coze服务
            coze_service = container.coze_service()

            # 创建回复服务实例
            reply_service = ReplyService(
                db_session=db,
                coze_service=coze_service,
                bot_id=settings.COZE_BOT_ID,
                user_id=settings.COZE_USER_ID
            )

            # 执行任务
            results = process_pending_replies(reply_service)

            return results
        except Exception as e:
            logger.error(
                f"触发处理待回复任务失败: {str(e)}"
            )
            return {"error": str(e)}
