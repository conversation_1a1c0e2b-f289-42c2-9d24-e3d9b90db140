"""
评论服务模块

提供与评论相关的服务功能，包括获取未回复的评论和创建待回复记录等。
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session


from app.models.comment_record import CommentRecord
from app.models.comment_reply import CommentReply, ReplyStatus
from app.repositories.comment_repository import CommentRepository
from app.schemas.comment import CommentCreate
from app.utils.video_utils import extract_video_id_from_uri, is_video_uri
from app.core.exceptions import CommentAlreadyExistsError, InvalidVideoUriError
from app.core.config import settings
from app.constants.status import CommentStatus

logger = logging.getLogger("app.services.comment_service")


class CommentService:
    """评论服务类

    提供与评论相关的服务功能，包括获取未回复的评论和创建待回复记录等。
    """

    def __init__(self, db_session: Session):
        """初始化评论服务

        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
        self.comment_repo = CommentRepository(db_session)

    def create_comment(self, comment_data: CommentCreate) -> CommentRecord:
        """创建新评论记录

        Args:
            comment_data: 评论创建数据

        Returns:
            CommentRecord: 创建的评论记录

        Raises:
            CommentAlreadyExistsError: 评论ID已存在且无需更新
            InvalidVideoUriError: URI不是视频链接
        """
        # 检查评论ID是否已存在
        existing_comment = self.comment_repo.get_by_source_id(
            comment_data.source_id
        )
        if existing_comment:
            # 如果评论已存在且新评论内容不为空，则更新评论内容
            if (comment_data.source_content and
                    comment_data.source_content.strip() and
                    existing_comment.source_content !=
                    comment_data.source_content):

                logger.info(
                    f"评论ID '{comment_data.source_id}' 已存在，更新评论内容"
                )

                # 根据IS_REPLY配置决定评论状态
                comment_status = comment_data.status

                # 更新评论内容和其他可能变化的字段
                existing_comment.source_content = comment_data.source_content
                existing_comment.reply_count = comment_data.reply_count
                existing_comment.like_count = comment_data.like_count
                existing_comment.importance_level = (
                    comment_data.importance_level
                )
                existing_comment.status = comment_status
                existing_comment.is_top = comment_data.is_top
                existing_comment.is_blocked = comment_data.is_blocked
                existing_comment.user_nickname = comment_data.user_nickname

                # 如果提供了新的视频ID，也进行更新
                if comment_data.video_id:
                    existing_comment.video_id = comment_data.video_id
                elif comment_data.uri and not existing_comment.video_id:
                    # 尝试从URI中提取视频ID
                    video_id = extract_video_id_from_uri(comment_data.uri)
                    if video_id:
                        existing_comment.video_id = video_id
                        logger.info(f"从URI中提取视频ID并更新: {video_id}")

                return self.comment_repo.update(existing_comment)
            else:
                # 如果评论内容为空或相同，则抛出异常
                raise CommentAlreadyExistsError(
                    f"评论ID '{comment_data.source_id}' 已存在"
                )

        # 检查URI是否为视频链接
        if comment_data.uri and not is_video_uri(comment_data.uri):
            logger.warning(f"URI不是视频链接，跳过创建评论: {comment_data.uri}")
            raise InvalidVideoUriError("只支持bilibili视频链接的评论创建")

        # 如果未提供video_id但提供了uri，尝试从uri中提取
        video_id = comment_data.video_id
        if not video_id and comment_data.uri:
            video_id = extract_video_id_from_uri(comment_data.uri)
            if video_id:
                logger.info(f"从URI中提取视频ID成功: {video_id}")
            else:
                logger.warning(f"无法从URI({comment_data.uri})中提取视频ID")

        # 根据IS_REPLY配置决定评论状态
        comment_status = comment_data.status
        if not settings.IS_REPLY:
            comment_status = CommentStatus.REPLIED
            logger.info("IS_REPLY=False，设置评论状态为replied")

        # 记录时间戳转换信息
        if comment_data.reply_time:
            formatted_time = comment_data.reply_time.strftime(
                '%Y-%m-%d %H:%M:%S'
            )
            logger.info(
                f"评论 {comment_data.source_id} 时间戳转换: "
                f"datetime对象={comment_data.reply_time}, "
                f"格式化时间={formatted_time}"
            )

        # 创建新评论
        new_comment = CommentRecord(
            source_id=comment_data.source_id,
            video_id=video_id,
            source_content=comment_data.source_content,
            root_id=comment_data.root_id,
            root_reply_content=comment_data.root_reply_content,
            target_id=comment_data.target_id,
            target_reply_content=comment_data.target_reply_content,
            reply_time=comment_data.reply_time,
            uri=comment_data.uri,
            user_mid=comment_data.user_mid,
            user_nickname=comment_data.user_nickname,
            path=comment_data.path,
            comment_type=comment_data.comment_type,
            reply_count=comment_data.reply_count,
            like_count=comment_data.like_count,
            importance_level=comment_data.importance_level,
            status=comment_status,
            is_top=comment_data.is_top,
            is_blocked=comment_data.is_blocked
        )

        return self.comment_repo.create(new_comment)

    def batch_create_comments(
        self, comments_data: List[CommentCreate]
    ) -> Tuple[List[CommentRecord], List[Dict], List[Dict]]:
        """批量创建评论记录

        Args:
            comments_data: 评论创建数据列表

        Returns:
            Tuple[List[CommentRecord], List[Dict], List[Dict]]:
                (成功创建的记录, 失败的记录, 已存在的记录)
        """
        comment_entities = []

        # 将请求数据转换为模型实体
        for comment_data in comments_data:
            # 检查URI是否为视频链接，如果不是则跳过此条评论
            if comment_data.uri and not is_video_uri(comment_data.uri):
                logger.warning(
                    f"URI不是视频链接，跳过评论: source_id={comment_data.source_id}, "
                    f"uri={comment_data.uri}"
                )
                continue

            # 如果未提供video_id但提供了uri，尝试从uri中提取
            video_id = comment_data.video_id
            logger.info(
                f"处理评论: source_id={comment_data.source_id}, "
                f"初始video_id={video_id}"
            )

            if not video_id and comment_data.uri:
                video_id = extract_video_id_from_uri(comment_data.uri)
                if video_id:
                    logger.info(
                        f"从URI '{comment_data.uri}' 中提取视频ID成功: {video_id}"
                    )
                else:
                    logger.warning(
                        f"无法从URI '{comment_data.uri}' 中提取视频ID"
                    )

            logger.info(f"最终设置的video_id值: {video_id}")

            # 记录时间戳转换信息
            if comment_data.reply_time:
                formatted_time = comment_data.reply_time.strftime(
                    '%Y-%m-%d %H:%M:%S'
                )
                logger.info(
                    f"评论 {comment_data.source_id} 时间戳转换: "
                    f"datetime对象={comment_data.reply_time}, "
                    f"格式化时间={formatted_time}"
                )

            # 根据IS_REPLY配置决定评论状态
            comment_status = comment_data.status

            comment = CommentRecord(
                source_id=comment_data.source_id,
                video_id=video_id,
                source_content=comment_data.source_content,
                root_id=comment_data.root_id,
                root_reply_content=comment_data.root_reply_content,
                target_id=comment_data.target_id,
                target_reply_content=comment_data.target_reply_content,
                reply_time=comment_data.reply_time,
                uri=comment_data.uri,
                user_mid=comment_data.user_mid,
                user_nickname=comment_data.user_nickname,
                path=comment_data.path,
                comment_type=comment_data.comment_type,
                reply_count=comment_data.reply_count,
                like_count=comment_data.like_count,
                importance_level=comment_data.importance_level,
                status=comment_status,
                is_top=comment_data.is_top,
                is_blocked=comment_data.is_blocked
            )
            comment_entities.append(comment)

        # 批量创建评论
        successful_records, failed_records, existed_records = (
            self.comment_repo.batch_create(comment_entities)
        )

        logger.info(
            f"批量创建评论结果: 成功={len(successful_records)}, "
            f"失败={len(failed_records)}, 已存在={len(existed_records)}"
        )
        for i, record in enumerate(successful_records):
            logger.info(
                f"成功记录 #{i + 1}: id={record.id}, "
                f"source_id={record.source_id}, video_id={record.video_id}"
            )

        return successful_records, failed_records, existed_records

    def get_unreplied_comments(
        self, limit: int = 10, debug: bool = False
    ) -> List[CommentRecord]:
        """获取未回复的评论

        查询所有未被回复的评论记录

        Args:
            limit: 返回的最大记录数量
            debug: 是否启用调试模式

        Returns:
            List[CommentRecord]: 未回复的评论记录列表
        """
        try:
            if debug:
                logger.info(f"调试模式: 开始查询未回复评论, 限制数量={limit}")
                # 先检查有多少条满足条件的评论
                total_pending = self.db_session.query(CommentRecord).filter(
                    CommentRecord.status == CommentStatus.PENDING,
                    CommentRecord.is_blocked.is_(False)
                ).count()
                logger.info(f"调试模式: 总共有 {total_pending} 条待处理评论")

            # 使用左连接查询没有关联回复记录的评论
            unreplied_comments = (
                self.db_session.query(CommentRecord)
                .outerjoin(
                    CommentReply, CommentRecord.id == CommentReply.comment_id
                )
                .filter(
                    CommentReply.id.is_(None),  # 使用is_None判断没有回复的评论
                    CommentRecord.status == CommentStatus.PENDING,
                    CommentRecord.is_blocked.is_(False),
                    CommentRecord.comment_type.in_([
                        CommentRecord.TYPE_MENTION,
                        CommentRecord.TYPE_REPLY
                    ])
                )
                .order_by(CommentRecord.reply_time.asc())
                .limit(limit)
                .all()
            )

            if debug:
                # 输出更详细的查询结果信息
                ids = [c.id for c in unreplied_comments]
                logger.info(
                    f"调试模式: 使用LEFT JOIN查询到 "
                    f"{len(unreplied_comments)} 条评论, IDs: {ids[:5]}..."
                )

                # 对比使用子查询方式
                replied_ids = self.db_session.query(
                    CommentReply.comment_id
                ).distinct().all()
                replied_ids = [id[0] for id in replied_ids]

                subquery_comments = (
                    self.db_session.query(CommentRecord)
                    .filter(
                        CommentRecord.id.notin_(replied_ids)
                        if replied_ids else True,
                        CommentRecord.status == CommentStatus.PENDING,
                        CommentRecord.is_blocked.is_(False),
                        CommentRecord.comment_type.in_([
                            CommentRecord.TYPE_MENTION,
                            CommentRecord.TYPE_REPLY
                        ])
                    )
                    .order_by(CommentRecord.reply_time.asc())
                    .limit(limit)
                    .all()
                )

                subquery_ids = [c.id for c in subquery_comments]
                logger.info(
                    f"调试模式: 使用NOT IN子查询到 "
                    f"{len(subquery_comments)} 条评论, "
                    f"IDs: {subquery_ids[:5]}..."
                )

            logger.info(f"获取到{len(unreplied_comments)}条未回复的评论")
            return unreplied_comments
        except Exception as e:
            logger.error(f"获取未回复评论失败: {str(e)}")
            return []

    def create_reply_task(
        self, comment: CommentRecord, reply_content: Optional[str] = None
    ) -> Optional[CommentReply]:
        """创建评论回复任务

        为指定评论创建一个待回复的任务记录

        Args:
            comment: 要回复的评论记录
            reply_content: 回复内容，如果为None则创建空回复等待后续处理

        Returns:
            Optional[CommentReply]: 创建的回复任务记录，如果创建失败则返回None
        """
        try:
            # 创建新的回复任务
            new_reply = CommentReply(
                comment_id=comment.id,
                comment_source_id=comment.source_id,
                reply_content="" if reply_content is None else reply_content,
                needs_content_change=True if reply_content is None else False,
                status=ReplyStatus.pending,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

            # 添加到数据库
            self.db_session.add(new_reply)
            self.db_session.commit()

            logger.info(
                f"成功创建评论回复任务: comment_id={comment.id}, "
                f"reply_id={new_reply.id}"
            )
            return new_reply
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"创建评论回复任务失败: {str(e)}")
            return None

    def process_unreplied_comments(self, debug: bool = True) -> Dict[str, Any]:
        """处理所有未回复的评论

        获取未回复的评论，并为每个评论创建待回复任务

        Args:
            debug: 是否启用调试模式

        Returns:
            Dict[str, Any]: 处理结果统计
        """
        results = {
            "total_comments": 0,
            "success_count": 0,
            "failed_count": 0
        }

        try:
            # 获取未回复的评论
            logger.info("开始查询未回复的评论")
            unreplied_comments = self.get_unreplied_comments(
                limit=50, debug=debug
            )
            logger.info(f"查询完成，获取到 {len(unreplied_comments)} 条未回复评论")

            results["total_comments"] = len(unreplied_comments)

            # 为每个评论创建回复任务
            for comment in unreplied_comments:
                logger.info(f"正在为评论ID={comment.id}创建回复任务")
                reply_task = self.create_reply_task(comment)
                if reply_task:
                    results["success_count"] += 1
                    logger.info(f"评论ID={comment.id}回复任务创建成功")
                    # 更新评论状态为已回复，避免重复创建任务
                    comment.status = CommentStatus.REPLIED
                    self.db_session.add(comment)
                    self.db_session.commit()
                    logger.info(f"评论ID={comment.id}状态已更新为replied")
                else:
                    results["failed_count"] += 1
                    logger.info(f"评论ID={comment.id}回复任务创建失败")

            logger.info(f"未回复评论处理结果: {results}")
            return results
        except Exception as e:
            self.db_session.rollback()  # 发生异常时回滚数据库事务
            logger.error(f"处理未回复评论失败: {str(e)}")
            results["error"] = str(e)
            return results
