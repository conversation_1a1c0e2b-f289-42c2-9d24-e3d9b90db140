"""
视频服务 - 处理视频相关的业务逻辑
"""
import logging
from typing import Dict, Any, List, Optional
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
import redis

from app.models.video import VideoDetail
from app.repositories.video_repository import VideoRepository
from app.schemas.video import (
    VideoCreate,
    VideoUpdate
)

logger = logging.getLogger("app.services.video_service")


class VideoService:
    """处理视频相关的业务逻辑"""

    def __init__(
        self, db: Session, redis_client: Optional[redis.Redis] = None
    ):
        self.db = db
        self.repository = VideoRepository(db)

    def _resolve_user_id(self, user_id: int) -> str:
        """
        将数据库主键ID转换为B站用户ID

        Args:
            user_id: 数据库主键ID

        Returns:
            str: B站用户ID

        Raises:
            HTTPException: 当用户不存在时
        """
        try:
            from app.repositories.user_repository import UserRepository
            user_repo = UserRepository(self.db)
            user = user_repo.get_by_id(user_id)

            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"未找到用户ID: {user_id}"
                )

            return user.user_id
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"解析用户ID失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="解析用户ID失败"
            )

    def get_videos(self, skip: int = 0, limit: int = 10) -> Dict[str, Any]:
        """
        获取视频列表，默认按创建时间降序排序

        Args:
            skip: 分页偏移量
            limit: 分页限制数

        Returns:
            Dict[str, Any]: 包含视频列表的响应对象

        Raises:
            HTTPException: 当获取视频列表失败时
        """
        try:
            # 查询结果已按创建时间降序排序
            videos = self.repository.get_all()
            start = min(skip, len(videos))
            end = min(skip + limit, len(videos))

            return {
                "total": len(videos),
                "items": videos[start:end]
            }
        except Exception as e:
            logger.error(f"获取视频列表失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取视频列表失败"
            )

    def get_video_by_id(self, video_id: str) -> VideoDetail:
        """
        通过视频ID获取视频详情

        Args:
            video_id: 视频ID

        Returns:
            VideoDetail: 视频详情对象

        Raises:
            HTTPException: 当视频不存在或获取失败时
        """
        try:
            video = self.repository.get_by_video_id(video_id)

            if not video:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"未找到视频ID: {video_id}"
                )

            return video
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取视频详情失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取视频详情失败"
            )

    def create_video(self, video_data: VideoCreate) -> VideoDetail:
        """
        创建新视频

        Args:
            video_data: 视频创建数据

        Returns:
            VideoDetail: 创建的视频详情对象

        Raises:
            HTTPException: 当创建失败或视频ID已存在时
        """
        try:
            # 如果未提供video_id但提供了video_url，尝试从URL中提取video_id
            if not hasattr(video_data, 'video_id') or not video_data.video_id:
                if not video_data.video_url:
                    raise HTTPException(
                        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                        detail="必须提供video_id或有效的video_url"
                    )

                # 从URL中提取视频ID
                video_id = self.repository.extract_video_id_from_url(
                    video_data.video_url
                )
                if not video_id:
                    raise HTTPException(
                        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                        detail=(
                            f"无法从URL中提取有效的视频ID: {video_data.video_url}"
                        )
                    )

                # 设置视频ID
                video_data.video_id = video_id

            # 检查视频ID是否已存在
            existing_video = self.repository.get_by_video_id(
                video_data.video_id
            )
            if existing_video:
                msg = f"视频ID '{video_data.video_id}' 已存在"
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=msg
                )

            # 创建新视频
            new_video = VideoDetail(
                video_id=video_data.video_id,
                title=video_data.title,
                video_content=video_data.video_content,
                video_url=video_data.video_url,
                video_script=video_data.video_script,
                video_summary=video_data.video_summary,
                reply_setting=video_data.reply_setting,
                comment_control_plan=video_data.comment_control_plan,
                category=video_data.category,
                promoted_products=video_data.promoted_products,
                common_questions=video_data.common_questions,
                is_disabled=video_data.is_disabled,
                user_id=self._resolve_user_id(video_data.user_id)
            )

            result = self.repository.create(new_video)
            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"创建视频失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建视频失败"
            )

    def update_video(
            self,
            video_id: str,
            video_data: VideoUpdate) -> VideoDetail:
        """
        更新视频信息

        Args:
            video_id: 视频ID
            video_data: 视频更新数据

        Returns:
            VideoDetail: 更新后的视频详情对象

        Raises:
            HTTPException: 当更新失败或视频不存在时
        """
        try:
            # 检查视频是否存在
            existing_video = self.repository.get_by_video_id(video_id)
            if not existing_video:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"未找到视频ID: {video_id}"
                )

            # 更新视频信息
            if video_data.title is not None:
                existing_video.title = video_data.title
            if video_data.video_content is not None:
                existing_video.video_content = video_data.video_content
            if video_data.video_url is not None:
                existing_video.video_url = video_data.video_url
            if video_data.video_script is not None:
                existing_video.video_script = video_data.video_script
            if video_data.video_summary is not None:
                existing_video.video_summary = video_data.video_summary
            if video_data.reply_setting is not None:
                existing_video.reply_setting = video_data.reply_setting
            if video_data.comment_control_plan is not None:
                existing_video.comment_control_plan = (
                    video_data.comment_control_plan
                )
            if video_data.category is not None:
                existing_video.category = video_data.category
            if video_data.promoted_products is not None:
                existing_video.promoted_products = video_data.promoted_products
            if video_data.common_questions is not None:
                existing_video.common_questions = video_data.common_questions
            if video_data.is_disabled is not None:
                existing_video.is_disabled = video_data.is_disabled
            if video_data.user_id is not None:
                existing_video.user_id = video_data.user_id

            updated_video = self.repository.update(existing_video)
            return updated_video
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"更新视频失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新视频失败"
            )

    def batch_update_status(
        self, video_ids: List[str], is_disabled: bool
    ) -> Dict[str, Any]:
        """
        批量更新视频启用状态

        Args:
            video_ids: 视频ID列表
            is_disabled: 是否禁用

        Returns:
            Dict[str, Any]: 包含成功和失败信息的响应

        Raises:
            HTTPException: 当所有视频更新都失败时
        """
        results = {
            "success": [],
            "failed": []
        }

        if not video_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须提供至少一个视频ID"
            )

        try:
            for video_id in video_ids:
                try:
                    # 获取视频 - 可能是通过主键ID(数字)或视频ID(字符串)
                    video = None

                    # 尝试将 video_id 转换为整数
                    numeric_id = None
                    try:
                        if isinstance(video_id, int):
                            numeric_id = video_id
                        else:
                            numeric_id = int(video_id)
                        # 首先尝试通过主键ID查询
                        video = self.repository.get_by_id(numeric_id)
                    except (ValueError, TypeError):
                        # 如果不是有效的整数ID，忽略错误
                        pass

                    # 如果通过主键ID没有找到，尝试通过video_id查询
                    if not video and isinstance(video_id, str):
                        video = self.repository.get_by_video_id(video_id)

                    if not video:
                        results["failed"].append({
                            "video_id": video_id,
                            "reason": f"未找到视频ID: {video_id}"
                        })
                        continue

                    # 更新视频状态
                    video.is_disabled = is_disabled
                    self.repository.update(video)
                    results["success"].append(str(video_id))

                except Exception as e:
                    error_msg = f"更新视频状态失败 - ID: {video_id}, 错误: {str(e)}"
                    logger.error(error_msg)
                    results["failed"].append({
                        "video_id": str(video_id),
                        "reason": f"更新失败: {str(e)}"
                    })

            # 如果所有视频都更新失败
            if not results["success"] and results["failed"]:
                error_detail = "所有视频状态更新失败"
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=error_detail
                )

            return results
        except HTTPException:
            raise
        except Exception as e:
            error_msg = f"批量更新视频状态失败: {str(e)}"
            logger.error(error_msg)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="批量更新视频状态失败"
            )
