"""
评论回复服务层
处理评论回复相关的业务逻辑
"""
import logging
from typing import Optional, Dict, Any, Union

from fastapi import HTTPException
from sqlalchemy.orm import Session

from app.repositories.comment_reply import CommentReplyRepository
from app.models.comment_reply import ReplyStatus, CommentReply
from app.schemas.comment_reply import (
    CommentReplyResponse,
    CommentReplyUpdateRequest,
    CommentReplyModel,
    CommentReplySatisfactionRequest,
    UserInfo,
    VideoInfo,
    CommentInfo,
    PaginatedResponse
)
from app.utils import clean_reply_content

logger = logging.getLogger("app.services.comment_reply_service")


class CommentReplyService:
    """评论回复服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.reply_repo = CommentReplyRepository(db)

    def get_pending_confirmation_reply(self) -> CommentReplyResponse:
        """获取一条待确认的评论回复并更新状态为处理中

        Returns:
            CommentReplyResponse: 评论回复详情或空结果提示

        Raises:
            HTTPException: 当没有待确认回复或更新状态失败时
        """
        logger.info("开始获取待确认的评论回复")

        reply = self.reply_repo.get_one_by_status(
            status="waiting_for_confirmation"
        )

        if not reply:
            logger.info("当前没有待确认的评论回复")
            raise HTTPException(
                status_code=404,
                detail="当前没有待确认的评论回复"
            )

        # 更新状态为处理中
        updated_reply = self.reply_repo.update_status(
            reply_id=reply.id,
            status=ReplyStatus.processing
        )

        if not updated_reply:
            logger.error(f"更新评论回复状态失败: reply_id={reply.id}")
            raise HTTPException(
                status_code=500,
                detail="更新评论回复状态失败"
            )

        # 将数据库模型转换为Pydantic模型
        reply_model = self._convert_to_model(updated_reply)

        logger.info(f"成功获取并更新评论回复: id={reply.id}")
        return self._build_success_response(
            message="已获取待确认评论回复并更新状态为处理中",
            data=reply_model
        )

    def resend_reply_command(self, reply_id: int) -> CommentReplyResponse:
        """重复下发回复指令

        Args:
            reply_id: 评论回复ID

        Returns:
            CommentReplyResponse: 操作结果

        Raises:
            HTTPException: 当回复不存在或下发失败时
        """
        logger.info(f"开始重复下发回复指令: reply_id={reply_id}")

        # 获取回复记录
        reply = self.reply_repo.get_by_id(reply_id)
        if not reply:
            logger.warning(f"未找到评论回复: reply_id={reply_id}")
            raise HTTPException(
                status_code=404,
                detail=f"未找到ID为{reply_id}的评论回复"
            )

        # 检查回复状态是否允许重复下发
        if not reply.reply_content:
            raise HTTPException(
                status_code=400,
                detail="回复内容为空，无法下发指令"
            )

        # 验证评论和视频的有效性
        is_valid, machine_code, error_msg = (
            self._validate_comment_and_video(reply)
        )
        if not is_valid:
            logger.warning(f"{error_msg}, reply_id={reply_id}")
            raise HTTPException(
                status_code=400,
                detail=error_msg
            )

        # 检查配置是否允许回复
        from app.core.config import settings
        if not settings.IS_REPLY:
            raise HTTPException(
                status_code=400,
                detail="回复功能已禁用，无法下发指令"
            )

        # 构建指令参数
        ext_params = self._build_reply_command_params(reply)
        if not ext_params:
            error_msg = "构建回复指令参数失败"
            logger.error(f"{error_msg}, reply_id={reply_id}")
            raise HTTPException(
                status_code=500,
                detail=error_msg
            )

        # 下发回复指令
        success = self._send_reply_command(machine_code, ext_params)
        if not success:
            error_msg = f"下发回复指令失败: machine_code={machine_code}"
            logger.error(f"{error_msg}, reply_id={reply_id}")
            raise HTTPException(
                status_code=500,
                detail="下发回复指令失败"
            )

        # 更新回复状态为处理中
        updated_reply = self.reply_repo.update_status(
            reply_id=reply_id,
            status=ReplyStatus.processing,
            error_message=None  # 清除之前的错误信息
        )

        # 将数据库模型转换为Pydantic模型
        reply_model = self._convert_to_model(updated_reply)

        logger.info(f"成功重复下发回复指令: reply_id={reply_id}")
        return self._build_success_response(
            message="重复下发回复指令成功",
            data=reply_model
        )

    def update_reply_status(
        self,
        reply_id: int,
        update_data: CommentReplyUpdateRequest
    ) -> CommentReplyResponse:
        """更新评论回复状态

        Args:
            reply_id: 评论回复ID
            update_data: 包含新状态和错误信息的请求体

        Returns:
            CommentReplyResponse: 更新后的评论回复信息

        Raises:
            HTTPException: 当回复不存在时
        """
        logger.info(
            f"开始更新评论回复状态: reply_id={reply_id}, "
            f"status={update_data.status}"
        )

        reply = self.reply_repo.get_by_id(reply_id)

        if not reply:
            logger.warning(f"未找到评论回复: reply_id={reply_id}")
            raise HTTPException(
                status_code=404,
                detail=f"未找到ID为{reply_id}的评论回复"
            )

        updated_reply = self.reply_repo.update_status(
            reply_id=reply_id,
            status=update_data.status,
            error_message=update_data.error_message
        )

        # 将数据库模型转换为Pydantic模型
        reply_model = self._convert_to_model(updated_reply)

        logger.info(f"成功更新评论回复状态: reply_id={reply_id}")
        return self._build_success_response(
            message="评论回复状态更新成功",
            data=reply_model
        )

    def update_reply_satisfaction(
        self,
        reply_id: int,
        satisfaction_data: CommentReplySatisfactionRequest
    ) -> CommentReplyResponse:
        """更新评论回复满意度

        Args:
            reply_id: 评论回复ID
            satisfaction_data: 包含是否满意的请求体

        Returns:
            CommentReplyResponse: 更新后的评论回复信息

        Raises:
            HTTPException: 当回复不存在或更新失败时
        """
        logger.info(
            f"开始更新评论回复满意度: reply_id={reply_id}, "
            f"is_satisfied={satisfaction_data.is_satisfied}"
        )

        reply = self.reply_repo.get_by_id(reply_id)

        if not reply:
            logger.warning(f"未找到评论回复: reply_id={reply_id}")
            raise HTTPException(
                status_code=404,
                detail=f"未找到ID为{reply_id}的评论回复"
            )

        updated_reply = self.reply_repo.update_satisfaction(
            reply_id=reply_id,
            is_satisfied=satisfaction_data.is_satisfied
        )

        if not updated_reply:
            logger.error(f"更新评论回复满意度失败: reply_id={reply_id}")
            raise HTTPException(
                status_code=500,
                detail="更新评论回复满意度失败"
            )

        # 将数据库模型转换为Pydantic模型
        reply_model = self._convert_to_model(updated_reply)

        logger.info(f"成功更新评论回复满意度: reply_id={reply_id}")
        return self._build_success_response(
            message="评论回复满意度更新成功",
            data=reply_model
        )

    def get_comment_replies_list(
        self,
        page: int = 1,
        page_size: int = 20,
        status: Optional[str] = None
    ) -> CommentReplyResponse:
        """获取评论回复列表

        Args:
            page: 页码，从1开始
            page_size: 每页数量，最大100
            status: 过滤的状态，None表示获取全部

        Returns:
            CommentReplyResponse: 评论回复列表及分页信息

        Raises:
            HTTPException: 当状态值无效时
        """
        logger.info(
            f"开始获取评论回复列表: page={page}, page_size={page_size}, "
            f"status={status}"
        )

        # 验证状态值
        self._validate_reply_status(status)

        replies, total = self.reply_repo.get_all_replies(
            page=page,
            page_size=page_size,
            status=status
        )

        # 将数据库模型转换为Pydantic模型
        reply_models = []
        for reply in replies:
            model = self._convert_to_model(reply)
            if model:  # 只添加成功转换的模型
                reply_models.append(model)

        # 构建分页响应数据
        paginated_data = PaginatedResponse(
            items=reply_models,
            total=total,
            page=page,
            page_size=page_size
        )

        logger.info(
            f"成功获取评论回复列表: 共{total}条记录，当前页{len(reply_models)}条"
        )
        return self._build_success_response(
            message=f"共获取到{total}条评论回复记录",
            data=paginated_data
        )

    def _validate_comment_and_video(
        self, reply: CommentReply
    ) -> tuple[bool, Optional[str], Optional[str]]:
        """验证评论和视频的有效性

        Args:
            reply: 评论回复记录

        Returns:
            Tuple[bool, Optional[str], Optional[str]]: (是否有效, 机器码, 错误消息)
        """
        if not reply.comment:
            return False, None, "找不到关联的评论记录"

        comment = reply.comment

        if not comment.video:
            return False, None, "评论没有关联的视频"

        if comment.video.is_disabled:
            error_msg = (
                f"视频已禁用，不下发回复指令: "
                f"video_id={comment.video.video_id}"
            )
            return False, None, error_msg

        if not comment.video.user:
            return False, None, "视频没有关联的用户"

        if comment.video.user.is_disabled:
            error_msg = (
                f"用户已禁用，不下发回复指令: "
                f"user_id={comment.video.user.user_id}"
            )
            return False, None, error_msg

        if not comment.video.user.machine_code:
            return False, None, "无法下发回复指令: 找不到关联的用户机器码"

        return True, comment.video.user.machine_code, None

    def _build_reply_command_params(
        self, reply: CommentReply
    ) -> Optional[Dict[str, Any]]:
        """构建回复指令参数

        Args:
            reply: 评论回复任务记录

        Returns:
            Optional[Dict[str, Any]]: 指令参数，如果构建失败则返回None
        """
        if not reply.comment:
            return None

        comment = reply.comment

        return {
            "reply_id": reply.id,
            "reply_to": comment.user_nickname,
            "need_reply_message": comment.source_content,
            "reply_content": reply.reply_content,
            "comment_type": comment.comment_type,
            "bv": comment.video_id,
        }

    def _send_reply_command(
        self, machine_code: str, ext_params: Dict[str, Any]
    ) -> bool:
        """下发回复指令

        Args:
            machine_code: 机器码
            ext_params: 指令参数

        Returns:
            bool: 是否成功下发指令
        """
        try:
            from app.core.container import Container
            from app.schemas.command import CommandType

            command_repository = Container.command_repository()

            result = command_repository.save_command(
                machine_code=machine_code,
                command_type=CommandType.REPLY,
                ext_params=ext_params
            )

            if result:
                logger.info(
                    f"成功下发回复指令: machine_code={machine_code}, "
                    f"reply_id={ext_params.get('reply_id')}"
                )
                return True
            else:
                logger.error(
                    f"下发回复指令失败: machine_code={machine_code}, "
                    f"reply_id={ext_params.get('reply_id')}"
                )
                return False
        except Exception as e:
            logger.error(f"下发回复指令异常: {str(e)}")
            return False

    def _convert_to_model(
        self, reply: CommentReply
    ) -> Optional[CommentReplyModel]:
        """将数据库模型转换为Pydantic模型

        Args:
            reply: 数据库评论回复对象

        Returns:
            转换后的Pydantic模型或None
        """
        if not reply:
            return None

        try:
            # 安全地提取关联信息
            comment_info = self._extract_comment_info(reply.comment)

            # 只有当comment存在时才提取video和user信息
            video_info = VideoInfo(
                video_id="", title="", video_url="", video_content=None
            )
            user_info = UserInfo(
                user_id="", username="", avatar_url=None,
                profile_url=None, description=None
            )

            if reply.comment and reply.comment.video:
                video_info = self._extract_video_info(reply.comment.video)
                user_info = self._extract_user_info(reply.comment.video.user)

            return CommentReplyModel(
                id=reply.id,
                comment_id=reply.comment_id,
                comment_source_id=reply.comment_source_id,
                reply_content=reply.reply_content,
                needs_content_change=reply.needs_content_change,
                changed_reply_content=reply.changed_reply_content,
                status=reply.status,
                is_satisfied=reply.is_satisfied,
                reply_time=reply.reply_time,
                error_message=reply.error_message,
                created_at=reply.created_at,
                updated_at=reply.updated_at,
                user_info=user_info,
                video_info=video_info,
                comment_info=comment_info,
                comment_context=reply.comment_context
            )
        except Exception as e:
            logger.warning(f"转换评论回复模型失败: {str(e)}")
            return None

    def _validate_reply_status(self, status: Optional[str]) -> None:
        """验证回复状态"""
        if status and status not in [s.value for s in ReplyStatus]:
            raise HTTPException(
                status_code=400,
                detail=f"无效的状态值: {status}。"
                f"有效状态: {[s.value for s in ReplyStatus]}"
            )

    def _build_success_response(
        self,
        message: str,
        data: Optional[
            Union[CommentReplyModel, PaginatedResponse, Dict[str, Any]]
        ] = None
    ) -> CommentReplyResponse:
        """构建成功响应"""
        return CommentReplyResponse(
            status="success",
            message=message,
            data=data
        )

    def _extract_user_info(self, user) -> UserInfo:
        """提取用户信息"""
        if not user:
            return UserInfo(
                user_id="",
                username="",
                avatar_url=None,
                profile_url=None,
                description=None
            )

        return UserInfo(
            user_id=user.user_id,
            username=user.username,
            avatar_url=user.avatar_url,
            profile_url=user.profile_url,
            description=user.description
        )

    def _extract_video_info(self, video) -> VideoInfo:
        """提取视频信息"""
        if not video:
            return VideoInfo(
                video_id="",
                title="",
                video_url="",
                video_content=None
            )

        return VideoInfo(
            video_id=video.video_id,
            title=video.title,
            video_url=video.video_url,
            video_content=video.video_content
        )

    def _extract_comment_info(self, comment) -> CommentInfo:
        """提取评论信息"""
        if not comment:
            return CommentInfo(
                source_id=0,
                source_content=None,
                user_mid=0,
                user_nickname=None,
                reply_time=None,
                reply_count=None,
                like_count=None
            )

        # 清理评论内容，去掉回复前缀
        cleaned_content = None
        if comment.source_content:
            cleaned_content = clean_reply_content(comment.source_content)

        return CommentInfo(
            source_id=comment.source_id,
            source_content=cleaned_content,
            user_mid=comment.user_mid,
            user_nickname=comment.user_nickname,
            reply_time=comment.reply_time,
            reply_count=comment.reply_count,
            like_count=comment.like_count
        )
