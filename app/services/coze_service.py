"""
Coze API服务模块

提供与Coze平台交互的API接口，支持对话生成、工作流调用等功能。
"""

import json
import logging
import requests
import time
from typing import Dict, Any, Optional

from app.utils import clean_reply_content

logger = logging.getLogger("app.services.coze_service")


class CozeService:
    """Coze平台服务类

    提供与Coze平台的交互功能，包括对话生成、工作流调用等。
    """

    def __init__(self, api_token: str, db_service=None, wecom_service=None):
        """初始化Coze服务

        Args:
            api_token: Coze平台API访问令牌
            db_service: 数据库服务实例，用于获取视频详情
            wecom_service: 企微服务实例，用于发送通知
        """
        self.api_token = api_token
        self.db_service = db_service
        self.wecom_service = wecom_service
        self.base_url = "https://api.coze.cn/v3"
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
        }

    def create_chat(
        self, user_id: str, bot_id: str, message: str
    ) -> Dict[str, Any]:
        """发起对话

        Args:
            user_id: 用户ID
            bot_id: 机器人ID
            message: 消息内容

        Returns:
            Dict[str, Any]: API响应内容
        """
        url = f"{self.base_url}/chat"
        payload = {
            "user_id": user_id,
            "bot_id": bot_id,
            "additional_messages": [
                {
                    "content": message,
                    "content_type": "text",
                    "role": "user",
                    "type": "question",
                }
            ],
        }

        logger.info(f"创建Coze对话: user_id={user_id}, bot_id={bot_id}")
        response = requests.post(url, headers=self.headers, json=payload)
        return response.json()

    def get_chat_messages(
        self, conversation_id: str, chat_id: str
    ) -> Dict[str, Any]:
        """获取对话消息

        Args:
            conversation_id: 会话ID
            chat_id: 对话ID

        Returns:
            Dict[str, Any]: API响应内容
        """
        url = f"{self.base_url}/chat/message/list"
        params = {"conversation_id": conversation_id, "chat_id": chat_id}

        logger.info(
            f"获取Coze对话消息: conversation_id={conversation_id}, "
            f"chat_id={chat_id}"
        )
        response = requests.get(url, headers=self.headers, params=params)
        return response.json()

    def chat_and_wait(
        self, user_id: str, bot_id: str, message: str, video_url: str = ""
    ) -> Optional[str]:
        """发送消息并等待回复

        Args:
            user_id: 用户ID
            bot_id: 机器人ID
            message: 消息内容
            video_url: 视频URL，可选

        Returns:
            Optional[str]: 助手回复内容，如果失败则返回None
        """
        try:
            # 获取视频详情
            video_details = self.get_video_details(video_url)
            if video_details:
                # 从message中提取评论内容
                comment = (
                    message.split("评论内容：")[1].split("   ")[0]
                    if "评论内容：" in message
                    else ""
                )

                # 组装新的消息格式
                message = f"""
视频标题：{video_details['title']}
视频内容描述：{video_details['video_content']}
产品信息：{video_details['product_info']}
评论内容：{comment}
                """.strip()
                logger.info(f"已组装视频详情消息: video_url={video_url}")
        except Exception as e:
            logger.error(f"组装视频详情失败: {str(e)}")

        # 创建对话
        chat_response = self.create_chat(user_id, bot_id, message)

        if chat_response.get("code") != 0:
            logger.error(f"创建对话失败: {chat_response}")
            return None

        conversation_id = chat_response["data"]["conversation_id"]
        chat_id = chat_response["data"]["id"]
        logger.info(
            f"创建对话成功: conversation_id={conversation_id}, " f"chat_id={chat_id}"
        )

        # 等待回复生成
        time.sleep(10)

        # 获取回复
        messages = self.get_chat_messages(conversation_id, chat_id)
        logger.debug(f"获取对话消息响应: {messages}")

        if messages.get("code") != 0:
            logger.error(f"获取消息失败: {messages}")
            return None

        # 查找助手的回复
        for msg in messages["data"]:
            if msg["role"] == "assistant" and msg["type"] == "answer":
                return msg["content"]

        logger.warning(
            f"未获取到回复: conversation_id={conversation_id}, " f"chat_id={chat_id}"
        )
        return None

    def extract_video_id(self, video_url: str) -> str:
        """从视频链接中提取视频ID

        Args:
            video_url: 视频链接

        Returns:
            str: 视频ID，如果提取失败则返回空字符串
        """
        try:
            # 处理B站链接格式
            if "bilibili.com/video/" in video_url:
                video_id = (
                    video_url.split("bilibili.com/video/")[1]
                    .split("?")[0]
                    .strip("/")
                )
                return video_id
            return ""
        except Exception as e:
            logger.error(f"提取视频ID失败: {str(e)}")
            return ""

    def get_video_details(self, video_url: str) -> Optional[Dict[str, Any]]:
        """通过视频链接获取视频详情

        Args:
            video_url: 视频链接

        Returns:
            Optional[Dict[str, Any]]: 视频详情，如果获取失败则返回None
        """
        if not video_url:
            return None

        try:
            video_id = self.extract_video_id(video_url)
            if not video_id:
                logger.warning(f"无法从链接中提取视频ID: {video_url}")
                return None

            # 从数据库获取视频详情
            if self.db_service:
                db = None
                try:
                    db = self.db_service.get_db()
                    from app.repositories.video_repository import (
                        VideoRepository
                    )
                    video_repo = VideoRepository(db)
                    video_details = video_repo.get_video_details(video_id)
                    if video_details:
                        logger.info(f"从数据库获取到视频详情: {video_id}")
                        return video_details
                    else:
                        logger.warning(f"数据库中未找到视频详情: {video_id}")
                        return None
                except Exception as e:
                    logger.error(f"获取视频详情时出错: {str(e)}")
                    return None
                finally:
                    if db:
                        self.db_service.close_db(db)
            else:
                logger.error("数据库服务未初始化")
                return None

        except Exception as e:
            logger.error(f"获取视频详情失败: {str(e)}")
            return None

    def chat_with_workflow(
            self,
            user_id: str,
            bot_id: str,
            message: str = "",
            video_url: str = "",
            original_comment: str = "",
            context_comments: str = "") -> Optional[str]:
        """直接通过workflow获取回复

        Args:
            user_id: 用户ID
            bot_id: 机器人ID，仅用于识别，workflow模式不会实际使用
            message: 消息内容（向后兼容，包含原评论和上下文）
            video_url: 视频URL，可选
            original_comment: 原评论内容
            context_comments: 上下文评论内容

        Returns:
            Optional[str]: 工作流执行结果，如果失败则返回None
        """
        try:
            # 获取视频详情
            video_details = self.get_video_details(video_url)
            if not video_details:
                logger.error(f"未找到视频详情: {video_url}")
                return None

            # 处理产品推荐数据：将JSON格式转换为字符串
            promoted_products = video_details.get("promoted_products", "")
            if promoted_products:
                if isinstance(promoted_products, (dict, list)):
                    # 如果是JSON对象或数组，转换为字符串
                    import json
                    try:
                        promoted_products_str = json.dumps(
                            promoted_products, ensure_ascii=False
                        )
                    except Exception:
                        promoted_products_str = str(promoted_products)
                elif isinstance(promoted_products, str):
                    # 如果已经是字符串，直接使用
                    promoted_products_str = promoted_products
                else:
                    # 其他类型转换为字符串
                    promoted_products_str = str(promoted_products)
            else:
                promoted_products_str = ""

            # 处理评论内容参数
            # 如果提供了原评论参数，优先使用；否则使用 message
            pinglun = original_comment if original_comment else message
            # 如果提供了上下文评论参数，优先使用；否则使用 message
            shangxiawen = context_comments if context_comments else message
            
            # 去除评论中的"回复 @用户名："前缀
            if pinglun:
                pinglun = clean_reply_content(pinglun)

            # 调用workflow
            url = f"{self.base_url.replace('v3', 'v1')}/workflow/run"
            payload = {
                "parameters": {
                    "shipinbiaoti": video_details["title"],
                    "zhengwen": video_details["video_script"],
                    "tuijian": promoted_products_str,
                    "pinglun": pinglun,
                    "zongjie": video_details["video_summary"],
                    "pinlei": video_details["category"],
                    "sheding": video_details["reply_setting"],
                    "kongping": video_details.get("comment_control_plan", ""),
                    "shangxiawen": shangxiawen
                },
                "workflow_id": "7469697317944967195",
            }

            logger.info("调用Coze工作流: workflow_id=7469697317944967195")
            response = requests.post(url, headers=self.headers, json=payload)
            result = response.json()

            if result.get("code") != 0:
                logger.error(f"调用workflow失败: {result}")
                # 发送企微机器人通知
                self._send_workflow_failure_notification(
                    user_id, video_url, result, response,
                    original_comment, context_comments)
                return None

            # 解析返回的JSON字符串
            data = eval(result["data"])
            # 优先使用output1，如果为空则使用output
            output = data.get("output1") or data.get("output")

            # 检查output是否为'不需要回复'，如果是则发送企微通知
            if output and output.strip() == "不需要回复":
                self._send_no_reply_notification(
                    user_id, video_url, output,
                    original_comment, context_comments)

            logger.info(f"工作流执行成功: user_id={user_id}")
            return output

        except Exception as e:
            # 打印错误信息与请求响应的结果
            logger.error(f"调用workflow失败: {str(e)}, URL: {video_url}")
            # 发送企微机器人通知
            self._send_workflow_failure_notification(
                user_id, video_url, str(e), None, "", "")
            return None

    def _send_workflow_failure_notification(
            self,
            user_id: str,
            video_url: str,
            error_info: Any,
            response: Optional[requests.Response] = None,
            original_comment: str = "",
            context_comments: str = ""
    ):
        """发送workflow调用失败的企微机器人通知

        Args:
            user_id: 用户ID
            video_url: 视频URL
            error_info: 错误信息
            response: HTTP响应对象（可选）
            original_comment: 原评论内容
            context_comments: 上下文评论内容
        """
        if not self.wecom_service:
            logger.warning("WecomService未配置，无法发送企微通知")
            return

        try:
            # 构建markdown格式的通知内容，提升可读性
            notification_content = f"""# 🚨 Coze工作流调用失败通知

## 📋 详细信息
- **用户ID**: `{user_id}`
- **视频URL**: {video_url or '`未提供`'}
- **错误信息**: `{error_info}`
- **时间**: `{time.strftime('%Y-%m-%d %H:%M:%S')}`

## 💬 评论信息
- **原评论内容**: `{original_comment or '未提供'}`
- **评论上下文**: `{context_comments or '未提供'}`

## 🔍 接口响应详情"""

            if response:
                # 格式化响应内容，使其更易读
                response_text = response.text[:500]
                if len(response.text) > 500:
                    response_text += "..."

                # 格式化响应头为JSON字符串
                headers_json = json.dumps(
                    dict(response.headers), indent=2, ensure_ascii=False
                )
                notification_content += f"""
- **状态码**: `{response.status_code}`
- **响应头**: ```json\n{headers_json}\n```
- **响应内容**: ```\n{response_text}\n```"""
            else:
                notification_content += "\n- **响应信息**: `无响应信息（异常情况）`"

            # 使用markdown格式发送群机器人通知，参考wecom_service的实现
            webhook_content = {"content": notification_content}
            success, error = self.wecom_service.send_to_group_webhook(
                "markdown", webhook_content)

            if success:
                logger.info(f"Coze工作流失败通知发送成功: user_id={user_id}")
            else:
                logger.error(f"Coze工作流失败通知发送失败: {error}")
                # 如果markdown发送失败，尝试使用text格式作为备选方案
                fallback_content = f"""🚨 Coze工作流调用失败通知

详细信息：
• 用户ID: {user_id}
• 视频URL: {video_url or '未提供'}
• 错误信息: {error_info}
• 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

评论信息：
• 原评论内容: {original_comment or '未提供'}
• 评论上下文: {context_comments or '未提供'}

接口响应详情："""

                if response:
                    fallback_content += f"""
• 状态码: {response.status_code}
• 响应内容: {response.text[:200]}{'...' if len(response.text) > 200 else ''}"""
                else:
                    fallback_content += "\n• 无响应信息（异常情况）"

                fallback_webhook_content = {"content": fallback_content}
                fallback_success, fallback_error = (
                    self.wecom_service.send_to_group_webhook(
                        "text", fallback_webhook_content)
                )

                if fallback_success:
                    logger.info(
                        f"Coze工作流失败通知备选方案发送成功: user_id={user_id}"
                    )
                else:
                    logger.error(
                        f"Coze工作流失败通知备选方案也发送失败: {fallback_error}"
                    )

        except Exception as e:
            logger.error(f"发送Coze工作流失败通知时出现异常: {str(e)}")

    def _send_no_reply_notification(
            self,
            user_id: str,
            video_url: str,
            output: str,
            original_comment: str = "",
            context_comments: str = ""
    ):
        """发送'不需要回复'的企微机器人通知

        Args:
            user_id: 用户ID
            video_url: 视频URL
            output: 工作流输出结果
            original_comment: 原评论内容
            context_comments: 上下文评论内容
        """
        if not self.wecom_service:
            logger.warning("WecomService未配置，无法发送企微通知")
            return

        try:
            # 构建通知内容
            notification_content = f"""ℹ️ Coze工作流输出'不需要回复'通知

📋 详细信息：
• 用户ID: {user_id}
• 视频URL: {video_url or '未提供'}
• 工作流输出: {output}
• 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

💬 评论信息：
• 原评论内容: {original_comment or '未提供'}
• 评论上下文: {context_comments or '未提供'}

💡 说明: 工作流判断此评论不需要回复"""

            # 发送群机器人通知
            webhook_content = {"content": notification_content}
            success, error = self.wecom_service.send_to_group_webhook(
                "text", webhook_content)

            if success:
                logger.info(f"'不需要回复'通知发送成功: user_id={user_id}")
            else:
                logger.error(f"'不需要回复'通知发送失败: {error}")

        except Exception as e:
            logger.error(f"发送'不需要回复'通知时出现异常: {str(e)}")
