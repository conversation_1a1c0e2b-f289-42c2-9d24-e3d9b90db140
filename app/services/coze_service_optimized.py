"""
Coze API服务模块（优化版）

提供与Coze平台交互的API接口，支持对话生成、工作流调用等功能。
"""

import json
import logging
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass
from functools import wraps
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

logger = logging.getLogger("app.services.coze_service")


@dataclass
class CozeConfig:
    """Coze服务配置类"""
    api_token: str
    base_url: str = "https://api.coze.cn/v3"
    workflow_id: str = "7469697317944967195"
    chat_wait_time: int = 10
    request_timeout: int = 30
    max_retries: int = 3
    retry_backoff_factor: float = 0.3


def retry_on_failure(max_retries: int = 3):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt
                        logger.warning(
                            f"{func.__name__} 失败，{wait_time}秒后重试..."
                            f"错误: {str(e)}"
                        )
                        time.sleep(wait_time)
            raise last_exception
        return wrapper
    return decorator


class CozeAPIClient:
    """Coze API 客户端，负责底层 API 调用"""
    
    def __init__(self, config: CozeConfig):
        self.config = config
        self.session = self._create_session()
        
    def _create_session(self) -> requests.Session:
        """创建带有重试机制的 HTTP 会话"""
        session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=self.config.retry_backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置默认请求头
        session.headers.update({
            "Authorization": f"Bearer {self.config.api_token}",
            "Content-Type": "application/json",
        })
        
        return session
    
    @retry_on_failure(max_retries=3)
    def create_chat(self, user_id: str, bot_id: str, message: str) -> Dict[str, Any]:
        """发起对话"""
        # 输入验证
        if not all([user_id, bot_id, message]):
            raise ValueError("user_id, bot_id 和 message 都不能为空")
            
        url = f"{self.config.base_url}/chat"
        payload = {
            "user_id": user_id,
            "bot_id": bot_id,
            "additional_messages": [
                {
                    "content": message,
                    "content_type": "text",
                    "role": "user",
                    "type": "question",
                }
            ],
        }
        
        response = self.session.post(
            url, 
            json=payload,
            timeout=self.config.request_timeout
        )
        response.raise_for_status()
        return response.json()
    
    @retry_on_failure(max_retries=3)
    def run_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """运行工作流"""
        url = f"{self.config.base_url.replace('v3', 'v1')}/workflow/run"
        payload = {
            "parameters": parameters,
            "workflow_id": self.config.workflow_id,
        }
        
        response = self.session.post(
            url,
            json=payload,
            timeout=self.config.request_timeout
        )
        response.raise_for_status()
        return response.json()


class VideoDetailsCache:
    """视频详情缓存"""
    
    def __init__(self, ttl: int = 3600):
        self._cache = {}
        self._ttl = ttl
        
    def get(self, video_id: str) -> Optional[Dict[str, Any]]:
        """获取缓存的视频详情"""
        if video_id in self._cache:
            data, timestamp = self._cache[video_id]
            if time.time() - timestamp < self._ttl:
                return data
            else:
                del self._cache[video_id]
        return None
    
    def set(self, video_id: str, data: Dict[str, Any]):
        """缓存视频详情"""
        self._cache[video_id] = (data, time.time())


class CozeService:
    """优化后的 Coze 服务类"""
    
    def __init__(
        self, config: CozeConfig, db_service=None, wecom_service=None
    ):
        self.config = config
        self.api_client = CozeAPIClient(config)
        self.db_service = db_service
        self.wecom_service = wecom_service
        self.video_cache = VideoDetailsCache()
        
    def extract_video_id(self, video_url: str) -> str:
        """从视频链接中提取视频ID"""
        if not video_url:
            return ""
            
        try:
            if "bilibili.com/video/" in video_url:
                video_id = (
                    video_url.split("bilibili.com/video/")[1]
                    .split("?")[0]
                    .strip("/")
                )
                return video_id
            return ""
        except Exception as e:
            logger.error(f"提取视频ID失败: {str(e)}")
            return ""
    
    def get_video_details(self, video_url: str) -> Optional[Dict[str, Any]]:
        """通过视频链接获取视频详情（带缓存）"""
        if not video_url:
            return None
            
        video_id = self.extract_video_id(video_url)
        if not video_id:
            logger.warning(f"无法从链接中提取视频ID: {video_url}")
            return None
        
        # 先检查缓存
        cached_details = self.video_cache.get(video_id)
        if cached_details:
            logger.info(f"从缓存获取视频详情: {video_id}")
            return cached_details
        
        # 从数据库获取
        if not self.db_service:
            logger.error("数据库服务未初始化")
            return None
            
        db = None
        try:
            db = self.db_service.get_db()
            from app.repositories.video_repository import VideoRepository
            
            video_repo = VideoRepository(db)
            video_details = video_repo.get_video_details(video_id)
            
            if video_details:
                # 缓存结果
                self.video_cache.set(video_id, video_details)
                logger.info(f"从数据库获取到视频详情: {video_id}")
                return video_details
            else:
                logger.warning(f"数据库中未找到视频详情: {video_id}")
                return None
                
        except Exception as e:
            logger.error(f"获取视频详情时出错: {str(e)}")
            return None
        finally:
            if db:
                self.db_service.close_db(db)
    
    def _prepare_workflow_parameters(
        self, 
        video_details: Dict[str, Any],
        original_comment: str,
        context_comments: str,
        message: str
    ) -> Dict[str, Any]:
        """准备工作流参数"""
        # 处理产品推荐数据
        promoted_products = video_details.get("promoted_products", "")
        if promoted_products and isinstance(promoted_products, (dict, list)):
            promoted_products_str = json.dumps(
                promoted_products, ensure_ascii=False
            )
        else:
            promoted_products_str = str(promoted_products or "")
        
        # 处理评论内容
        pinglun = original_comment if original_comment else message
        shangxiawen = context_comments if context_comments else message
        
        return {
            "shipinbiaoti": video_details["title"],
            "zhengwen": video_details["video_script"],
            "tuijian": promoted_products_str,
            "pinglun": pinglun,
            "zongjie": video_details["video_summary"],
            "pinlei": video_details["category"],
            "sheding": video_details["reply_setting"],
            "kongping": video_details.get("comment_control_plan", ""),
            "shangxiawen": shangxiawen
        }
    
    def chat_with_workflow(
        self,
        user_id: str,
        bot_id: str,
        message: str = "",
        video_url: str = "",
        original_comment: str = "",
        context_comments: str = ""
    ) -> Optional[str]:
        """通过工作流获取回复"""
        try:
            # 获取视频详情
            video_details = self.get_video_details(video_url)
            if not video_details:
                logger.error(f"未找到视频详情: {video_url}")
                return None
            
            # 准备参数
            parameters = self._prepare_workflow_parameters(
                video_details, original_comment, context_comments, message
            )
            
            # 调用工作流
            logger.info(f"调用Coze工作流: workflow_id={self.config.workflow_id}")
            result = self.api_client.run_workflow(parameters)
            
            if result.get("code") != 0:
                logger.error(f"调用workflow失败: {result}")
                self._send_workflow_failure_notification(
                    user_id, video_url, result
                )
                return None
            
            # 安全地解析返回数据
            try:
                data = json.loads(result["data"])
            except json.JSONDecodeError:
                logger.error(f"解析workflow返回数据失败: {result['data']}")
                return None
            
            # 获取输出
            output = data.get("output1") or data.get("output")
            
            # 检查是否需要发送通知
            if output and output.strip() == "不需要回复":
                self._send_no_reply_notification(user_id, video_url, output)
            
            logger.info(f"工作流执行成功: user_id={user_id}")
            return output
            
        except Exception as e:
            logger.error(f"调用workflow失败: {str(e)}, URL: {video_url}")
            self._send_workflow_failure_notification(
                user_id, video_url, str(e)
            )
            return None
    
    def _send_workflow_failure_notification(
        self, user_id: str, video_url: str, error_info: Any
    ):
        """发送失败通知（简化版）"""
        if not self.wecom_service:
            return
            
        notification = self._build_failure_notification(
            user_id, video_url, error_info
        )
        self._send_notification(notification)
    
    def _build_failure_notification(
        self, user_id: str, video_url: str, error_info: Any
    ) -> Dict[str, Any]:
        """构建失败通知内容"""
        return {
            "content": f"""# 🚨 Coze工作流调用失败通知

## 📋 详细信息
- **用户ID**: `{user_id}`
- **视频URL**: {video_url or '`未提供`'}
- **错误信息**: `{error_info}`
- **时间**: `{time.strftime('%Y-%m-%d %H:%M:%S')}`"""
        }
    
    def _send_notification(self, notification: Dict[str, Any]):
        """发送通知的通用方法"""
        try:
            success, error = self.wecom_service.send_to_group_webhook(
                "markdown", notification
            )
            if not success:
                logger.error(f"通知发送失败: {error}")
        except Exception as e:
            logger.error(f"发送通知时出现异常: {str(e)}") 