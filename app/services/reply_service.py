"""
评论回复服务模块

提供评论回复相关的服务功能，包括获取待回复记录、生成回复内容等。
"""

import logging
import time
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy.orm import Session

from app.models.comment_reply import CommentReply, ReplyStatus
from app.models.comment_record import CommentRecord
from app.services.coze_service import CozeService
from app.utils import clean_reply_content

logger = logging.getLogger("app.services.reply_service")


class ReplyService:
    """评论回复服务类

    提供评论回复相关的服务功能，包括获取待回复记录、生成回复内容等。
    """

    def __init__(
        self, db_session: Session, coze_service: CozeService,
        bot_id: str, user_id: str, max_retries: int = 3,
        retry_delay: int = 2, max_workers: int = 5
    ):
        """初始化评论回复服务

        Args:
            db_session: 数据库会话
            coze_service: Coze服务实例
            bot_id: Coze机器人ID
            user_id: Coze用户ID
            max_retries: 最大重试次数
            retry_delay: 重试间隔(秒)
            max_workers: 最大并行处理数
        """
        self.db_session = db_session
        self.coze_service = coze_service
        self.bot_id = bot_id
        self.user_id = user_id
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.max_workers = max_workers

    def get_pending_replies(self, limit: int = 10) -> List[CommentReply]:
        """获取待处理的评论回复任务

        Args:
            limit: 返回的最大记录数量

        Returns:
            List[CommentReply]: 待处理的评论回复任务列表
        """
        try:
            pending_replies = self.db_session.query(CommentReply).filter(
                CommentReply.status == ReplyStatus.pending
            ).order_by(CommentReply.created_at.asc()).limit(limit).all()

            logger.info(f"获取到{len(pending_replies)}条待处理的评论回复任务")
            return pending_replies
        except Exception as e:
            logger.error(f"获取待处理评论回复任务失败: {str(e)}")
            return []

    def get_comment_by_id(self, comment_id: int) -> Optional[CommentRecord]:
        """根据ID获取评论记录

        Args:
            comment_id: 评论记录ID

        Returns:
            Optional[CommentRecord]: 评论记录，如果不存在则返回None
        """
        try:
            comment = self.db_session.query(CommentRecord).filter(
                CommentRecord.id == comment_id
            ).first()
            return comment
        except Exception as e:
            logger.error(f"获取评论记录失败: {str(e)}")
            return None

    def _is_valid_conversation_comment(
        self, comment: CommentRecord, target_user_nickname: str,
        bot_username: str
    ) -> bool:
        """检查评论是否是有效的对话评论

        过滤掉回复给其他用户的评论，只保留机器人用户和目标用户之间的直接对话

        Args:
            comment: 评论记录
            target_user_nickname: 目标用户昵称
            bot_username: 机器人用户名

        Returns:
            bool: 是否是有效的对话评论
        """
        try:
            # 如果评论不是来自目标用户或机器人用户，直接过滤掉
            if comment.user_nickname not in [
                target_user_nickname, bot_username
            ]:
                return False

            # 检查评论内容是否包含回复信息
            content = comment.source_content.strip()
            if content.startswith("回复 @"):
                # 提取被回复的用户名
                try:
                    # 格式：回复 @用户名 :内容
                    reply_part = content.split(":", 1)[0]  # 获取冒号前的部分
                    replied_user = reply_part.replace("回复 @", "").strip()

                    # 只保留回复给目标用户或机器人用户的评论
                    if replied_user not in [
                        target_user_nickname, bot_username
                    ]:
                        logger.debug(
                            f"过滤掉回复给其他用户的评论: "
                            f"comment_id={comment.id}, "
                            f"replied_user={replied_user}"
                        )
                        return False
                except Exception as e:
                    logger.warning(f"解析回复用户名失败: {str(e)}")
                    # 解析失败时保守处理，保留评论
                    return True

            return True
        except Exception as e:
            logger.error(f"检查评论有效性失败: {str(e)}")
            # 出错时保守处理，保留评论
            return True

    def get_comment_context(
        self, comment: CommentRecord, max_context_count: int = 5
    ) -> List[Tuple[CommentRecord, int]]:
        """获取评论的上下文

        追踪评论的上下文关系，包括主评论和相关的楼中楼评论
        自动识别目标用户和机器人用户，只返回这两个用户之间的对话

        Args:
            comment: 当前评论记录
            max_context_count: 最大上下文评论条数，默认为5

        Returns:
            List[Tuple[CommentRecord, int]]: 评论上下文列表，包含评论记录和深度
        """
        try:
            # 初始化结果列表
            context_comments = []

            # 获取目标用户昵称（当前评论的发布者）
            target_user_nickname = comment.user_nickname

            # 通过video_id获取机器人用户信息
            bot_username = None
            if comment.video_id:
                from app.models.video import VideoDetail
                video_detail = self.db_session.query(VideoDetail).filter(
                    VideoDetail.video_id == comment.video_id
                ).first()

                if video_detail and video_detail.user:
                    bot_username = video_detail.user.username

            # 如果无法获取机器人用户名，则使用原有逻辑（获取所有上下文）
            if not bot_username or not target_user_nickname:
                logger.warning(
                    f"无法获取用户信息，使用原有逻辑: "
                    f"bot_username={bot_username}, "
                    f"target_user_nickname={target_user_nickname}")
                return self._get_all_comment_context(
                    comment, max_context_count)

            # 如果当前评论是楼中楼评论(root_id不为0且不为None)
            if comment.root_id and comment.root_id != 0:
                # 先获取主评论
                main_comment_query = self.db_session.query(
                    CommentRecord
                ).filter(
                    (CommentRecord.source_id == comment.root_id) &
                    (CommentRecord.user_nickname.in_([
                        target_user_nickname, bot_username]))
                )

                main_comment_candidate = main_comment_query.first()
                # 检查主评论是否是有效的对话评论
                if (main_comment_candidate and
                        self._is_valid_conversation_comment(
                            main_comment_candidate, target_user_nickname,
                            bot_username
                        )):
                    context_comments.append((main_comment_candidate, 0))

                # 获取楼中楼评论，只获取这两个用户之间的对话
                # 修改查询条件：获取所有楼中楼评论，不限制当前评论时间
                sub_comments_query = self.db_session.query(
                    CommentRecord
                ).filter(
                    (CommentRecord.root_id == comment.root_id) &
                    (CommentRecord.source_id != comment.root_id) &
                    (CommentRecord.user_nickname.in_([
                        target_user_nickname, bot_username]))
                ).order_by(
                    CommentRecord.reply_time.asc()
                )

                # 限制楼中楼评论条数
                remaining_count = (
                    max_context_count - 1
                    if main_comment_candidate else max_context_count
                )
                all_sub_comments = sub_comments_query.limit(
                    remaining_count
                ).all()

                # 进行回复对象过滤
                sub_comments = [
                    c for c in all_sub_comments
                    if self._is_valid_conversation_comment(
                        c, target_user_nickname, bot_username
                    )
                ]

                for sub_comment in sub_comments:
                    context_comments.append((sub_comment, 1))

            else:
                # 如果是主评论，获取该主评论及其下的楼中楼评论
                context_comments.append((comment, 0))

                # 获取该主评论下的楼中楼评论，只获取这两个用户之间的对话
                # 移除时间限制，获取完整的对话上下文
                sub_comments_query = self.db_session.query(
                    CommentRecord
                ).filter(
                    (CommentRecord.root_id == comment.source_id) &
                    (CommentRecord.user_nickname.in_([
                        target_user_nickname, bot_username]))
                ).order_by(
                    CommentRecord.reply_time.asc()
                ).limit(max_context_count)

                # 获取评论并进行回复对象过滤
                all_sub_comments = sub_comments_query.all()
                sub_comments = [
                    c for c in all_sub_comments
                    if self._is_valid_conversation_comment(
                        c, target_user_nickname, bot_username
                    )
                ]

                for sub_comment in sub_comments:
                    context_comments.append((sub_comment, 1))

            # 优化：如果上下文只有一条评论且内容与当前评论相同，则无需拼装
            if (len(context_comments) == 1 and
                    context_comments[0][0].source_content.strip() ==
                    comment.source_content.strip()):
                logger.debug(
                    f"上下文只有一条评论且内容相同，无需拼装: "
                    f"comment_id={comment.id}"
                )
                return []

            return context_comments
        except Exception as e:
            logger.error(f"获取评论上下文失败: {str(e)}")
            return [(comment, 0)]  # 出错时返回当前评论作为上下文

    def _get_all_comment_context(
        self, comment: CommentRecord, max_context_count: int = 5
    ) -> List[Tuple[CommentRecord, int]]:
        """获取所有评论上下文（原有逻辑）

        Args:
            comment: 当前评论记录
            max_context_count: 最大上下文评论条数

        Returns:
            List[Tuple[CommentRecord, int]]: 评论上下文列表
        """
        try:
            context_comments = []

            # 如果当前评论是楼中楼评论(root_id不为0且不为None)
            if comment.root_id and comment.root_id != 0:
                # 先获取主评论
                main_comment = self.db_session.query(CommentRecord).filter(
                    CommentRecord.source_id == comment.root_id
                ).first()

                if main_comment:
                    context_comments.append((main_comment, 0))

                # 获取相关楼中楼评论，按时间排序，限制条数
                # 移除source_id限制，获取完整的对话上下文
                sub_comments = self.db_session.query(
                    CommentRecord
                ).filter(
                    (CommentRecord.root_id == comment.root_id) &
                    (CommentRecord.source_id != comment.root_id) &
                    (CommentRecord.reply_time <= comment.reply_time)  # 只保留时间过滤
                ).order_by(CommentRecord.reply_time.asc())

                # 限制楼中楼评论条数（总条数减去主评论）
                remaining_count = (
                    max_context_count - 1
                    if main_comment else max_context_count
                )
                sub_comments = sub_comments.limit(remaining_count).all()

                for sub_comment in sub_comments:
                    context_comments.append((sub_comment, 1))

            else:
                # 如果是主评论，获取该主评论及其下的楼中楼评论
                context_comments.append((comment, 0))

                # 获取该主评论下的楼中楼评论，按时间排序
                # 移除时间限制，获取完整的楼中楼对话
                sub_comments = self.db_session.query(CommentRecord).filter(
                    CommentRecord.root_id == comment.source_id
                ).order_by(CommentRecord.reply_time.asc()).limit(
                    max_context_count
                ).all()

                for sub_comment in sub_comments:
                    context_comments.append((sub_comment, 1))

            return context_comments
        except Exception as e:
            logger.error(f"获取所有评论上下文失败: {str(e)}")
            return [(comment, 0)]

    def _build_message_with_context(

        self, comment_context: List[Tuple[CommentRecord, int]]
    ) -> str:
        """构建消息内容

        将评论上下文构建为格式化的消息内容

        Args:
            comment_context: 评论上下文列表

        Returns:
            str: 格式化的消息内容
        """
        message = ""

        # 添加评论上下文
        for ctx_comment, depth in comment_context:
            indent = "    " * depth
            # 格式化时间
            reply_time = ""
            if ctx_comment.reply_time:
                reply_time = ctx_comment.reply_time.strftime(
                    '%Y-%m-%d %H:%M:%S'
                )

            user = ctx_comment.user_nickname
            # 清理评论内容，去掉回复前缀
            cleaned_content = clean_reply_content(
                ctx_comment.source_content
            )

            user_info = f"{indent}用户：{user} {reply_time}"
            content_info = f"{indent}内容：{cleaned_content}"

            message += f"{user_info}\n{content_info}\n\n"

        return message.strip()

    def _verify_video_info(self, comment: CommentRecord) -> Optional[str]:
        """验证视频信息

        检查评论关联的视频信息是否可用

        Args:
            comment: 评论记录

        Returns:
            Optional[str]: 视频URL，如果没有或无效则返回None
        """
        if not comment.video or not comment.video.video_url:
            return None

        # 验证视频信息是否可获取
        video_url = comment.video.video_url
        video_details = self.coze_service.get_video_details(video_url)

        if not video_details:
            logger.warning(f"无法获取视频详情: video_url={video_url}")
            return None

        return video_url

    def _extract_original_and_context_comments(
        self, comment: CommentRecord
    ) -> Tuple[str, str]:
        """提取原评论和上下文评论

        将当前评论与上下文评论分离

        Args:
            comment: 当前评论记录

        Returns:
            Tuple[str, str]: (原评论内容, 上下文评论内容)
        """
        # 获取评论上下文
        comment_context = self.get_comment_context(comment)

        # 提取原评论内容
        original_comment = comment.source_content

        # 构建上下文评论内容（排除当前评论）
        context_comments_list = []
        for ctx_comment, depth in comment_context:
            # 跳过当前评论
            if ctx_comment.id == comment.id:
                continue

            indent = "    " * depth
            # 格式化时间
            created_time = ""
            if ctx_comment.reply_time:
                created_time = ctx_comment.reply_time.strftime(
                    '%Y-%m-%d %H:%M:%S'
                )

            user = ctx_comment.user_nickname
            user_info = f"{indent}用户：{user} {created_time}"
            content_info = f"{indent}内容：{ctx_comment.source_content}"

            context_comments_list.append(f"{user_info}\n{content_info}")

        # 合并上下文评论内容
        context_comments = "\n\n".join(context_comments_list)

        return original_comment, context_comments

    def generate_reply_content(
        self, comment: CommentRecord
    ) -> Optional[str]:
        """生成评论回复内容

        使用Coze服务生成评论的回复内容，添加重试机制

        Args:
            comment: 评论记录

        Returns:
            Optional[str]: 生成的回复内容，如果生成失败则返回None
        """
        retries = 0
        while retries < self.max_retries:
            try:
                # 获取评论上下文
                comment_context = self.get_comment_context(comment)

                # 构建完整消息内容（向后兼容）
                message = self._build_message_with_context(comment_context)

                # 提取原评论和上下文评论
                original_comment, context_comments = (
                    self._extract_original_and_context_comments(comment)
                )

                # 验证并获取视频URL
                video_url = self._verify_video_info(comment)
                if not video_url:
                    logger.warning(
                        f"评论无关联视频或视频信息无效: comment_id={comment.id}"
                    )

                # 调用Coze服务获取回复，传递分离的原评论和上下文评论
                reply_content = self.coze_service.chat_with_workflow(
                    self.user_id, self.bot_id, message, video_url or "",
                    original_comment, context_comments
                )

                # 如果 reply_content = 不需要回复，直接返回 None
                if reply_content == "不需要回复":
                    logger.info(
                        f"Coze服务返回不需要回复: comment_id={comment.id}"
                    )
                    return None

                if reply_content:
                    logger.info(
                        f"成功生成评论回复内容: comment_id={comment.id}, "
                        f"content_length={len(reply_content)}"
                    )
                    return reply_content
                else:
                    retries += 1
                    logger.warning(
                        f"第{retries}次生成评论回复内容为空: "
                        f"comment_id={comment.id}，"
                        f"将在{self.retry_delay}秒后重试"
                    )
                    time.sleep(self.retry_delay)
            except Exception as e:
                retries += 1
                logger.error(
                    f"第{retries}次生成评论回复内容失败: "
                    f"{str(e)}，"
                    f"将在{self.retry_delay}秒后重试"
                )
                time.sleep(self.retry_delay)

        logger.error(
            f"在{self.max_retries}次尝试后仍无法生成回复内容: "
            f"comment_id={comment.id}"
        )
        return None

    def _update_basic_reply_info(
        self, reply: CommentReply, content: str,
        status: ReplyStatus = ReplyStatus.processing
    ) -> None:
        """更新回复任务的基本信息

        Args:
            reply: 评论回复任务记录
            content: 回复内容
            status: 新的任务状态
        """
        # 获取关联的评论记录并更新上下文
        comment = self.get_comment_by_id(reply.comment_id)
        if comment:
            comment_context = self.get_comment_context(comment)
            reply.comment_context = self._build_message_with_context(
                comment_context
            )

        # 更新基本字段
        reply.reply_content = content
        reply.status = status
        reply.updated_at = datetime.now()

        self.db_session.commit()
        logger.info(
            f"成功更新评论回复任务: id={reply.id}, "
            f"status={status.value if status else None}"
        )

    def _handle_reply_disabled_case(self, reply: CommentReply) -> None:
        """处理回复功能被禁用的情况

        Args:
            reply: 评论回复任务记录
        """
        reply.status = ReplyStatus.completed
        reply.updated_at = datetime.now()
        self.db_session.commit()
        logger.info(
            f"IS_REPLY=False，回复任务直接标记为已完成: "
            f"reply_id={reply.id}"
        )

    def _validate_comment_and_video(
        self, comment_id: int
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """验证评论和视频的有效性

        Args:
            comment_id: 评论ID

        Returns:
            Tuple[bool, Optional[str], Optional[str]]:
            (是否有效, 机器码, 错误消息)
        """
        comment = self.get_comment_by_id(comment_id)

        if not comment:
            return False, None, "找不到关联的评论记录"

        if not comment.video:
            return False, None, "评论没有关联的视频"

        if comment.video.is_disabled:
            error_msg = (
                f"视频已禁用，不下发回复指令: "
                f"video_id={comment.video.video_id}"
            )
            return False, None, error_msg

        if not comment.video.user:
            return False, None, "视频没有关联的用户"

        if comment.video.user.is_disabled:
            error_msg = (
                f"用户已禁用，不下发回复指令: "
                f"user_id={comment.video.user.user_id}"
            )
            return False, None, error_msg

        if not comment.video.user.machine_code:
            return False, None, "无法下发回复指令: 找不到关联的用户机器码"

        return True, comment.video.user.machine_code, None

    def _build_reply_command_params(
        self, reply: CommentReply, comment_id: int
    ) -> Optional[Dict[str, Any]]:
        """构建回复指令参数

        Args:
            reply: 评论回复任务记录
            comment_id: 评论ID

        Returns:
            Optional[Dict[str, Any]]: 指令参数，如果构建失败则返回None
        """
        comment = self.get_comment_by_id(comment_id)
        if not comment:
            return None

        return {
            "reply_id": reply.id,
            "reply_to": comment.user_nickname,
            "need_reply_message": comment.source_content,
            "reply_content": reply.reply_content,
            "comment_type": comment.comment_type,
            "bv": comment.video_id,
        }

    def _send_reply_command(
        self, machine_code: str, ext_params: Dict[str, Any]
    ) -> bool:
        """下发回复指令

        Args:
            machine_code: 机器码
            ext_params: 指令参数

        Returns:
            bool: 是否成功下发指令
        """
        try:
            from app.core.container import Container
            from app.schemas.command import CommandType

            command_repository = Container.command_repository()

            result = command_repository.save_command(
                machine_code=machine_code,
                command_type=CommandType.REPLY,
                ext_params=ext_params
            )

            if result:
                logger.info(
                    f"成功下发回复指令: machine_code={machine_code}, "
                    f"reply_id={ext_params.get('reply_id')}"
                )
                return True
            else:
                logger.error(
                    f"下发回复指令失败: machine_code={machine_code}, "
                    f"reply_id={ext_params.get('reply_id')}"
                )
                return False
        except Exception as e:
            logger.error(f"下发回复指令异常: {str(e)}")
            return False

    def _mark_reply_as_failed(
        self, reply: CommentReply, error_message: str
    ) -> None:
        """将回复任务标记为失败

        Args:
            reply: 评论回复任务记录
            error_message: 错误消息
        """
        try:
            # 获取关联的评论记录并更新上下文信息
            comment = self.get_comment_by_id(reply.comment_id)
            if comment:
                comment_context = self.get_comment_context(comment)
                reply.comment_context = self._build_message_with_context(
                    comment_context
                )

            # 更新任务状态为失败
            reply.status = ReplyStatus.failed
            reply.error_message = error_message
            reply.updated_at = datetime.now()
            self.db_session.commit()
        except Exception as e:
            logger.error(f"标记回复任务为失败时出错: reply_id={reply.id}, error={str(e)}")
            self.db_session.rollback()
            # 重新抛出异常，让调用方知道操作失败
            raise e

    def _handle_reply_enabled_case(self, reply: CommentReply) -> None:
        """处理回复功能启用的情况

        Args:
            reply: 评论回复任务记录
        """
        try:
            # 验证评论和视频的有效性
            is_valid, machine_code, error_msg = (
                self._validate_comment_and_video(reply.comment_id)
            )

            if not is_valid:
                logger.warning(f"{error_msg}, reply_id={reply.id}")
                self._mark_reply_as_failed(reply, error_msg)
                return

            # 构建指令参数
            ext_params = self._build_reply_command_params(
                reply, reply.comment_id
            )
            if not ext_params:
                error_msg = "构建回复指令参数失败"
                logger.error(f"{error_msg}, reply_id={reply.id}")
                self._mark_reply_as_failed(reply, error_msg)
                return

            # 下发回复指令
            success = self._send_reply_command(machine_code, ext_params)
            if not success:
                error_msg = f"下发回复指令失败: machine_code={machine_code}"
                self._mark_reply_as_failed(reply, error_msg)

        except Exception as e:
            logger.error(f"处理回复启用情况异常: {str(e)}")
            # 异常情况不影响主流程，已在日志中记录

    def update_reply_task(
        self, reply: CommentReply, content: str,
        status: ReplyStatus = ReplyStatus.processing
    ) -> bool:
        """更新评论回复任务

        更新回复任务的内容和状态，并根据配置决定是否下发回复指令

        Args:
            reply: 评论回复任务记录
            content: 回复内容
            status: 新的任务状态

        Returns:
            bool: 更新是否成功
        """
        try:
            # 更新基本回复信息
            self._update_basic_reply_info(reply, content, status)

            # 检查回复功能配置
            from app.core.config import settings

            if not settings.IS_REPLY:
                # 回复功能被禁用，直接标记为完成
                self._handle_reply_disabled_case(reply)
            else:
                # 回复功能启用，处理指令下发
                self._handle_reply_enabled_case(reply)

            return True

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"更新评论回复任务失败: {str(e)}")
            return False

    def _process_single_reply(self, reply: CommentReply) -> bool:
        """处理单个评论回复任务

        Args:
            reply: 评论回复任务

        Returns:
            bool: 处理是否成功
        """
        # 获取关联的评论记录
        comment = self.get_comment_by_id(reply.comment_id)
        if not comment:
            logger.error(f"找不到关联的评论记录: comment_id={reply.comment_id}")
            # 更新任务状态为失败
            try:
                reply.status = ReplyStatus.failed
                reply.error_message = (
                    f"找不到关联的评论记录: comment_id={reply.comment_id}"
                )
                reply.updated_at = datetime.now()
                self.db_session.commit()
                logger.info(f"回复任务已标记为失败: reply_id={reply.id} (找不到评论记录)")
            except Exception as e:
                logger.error(
                    f"更新回复任务状态失败: reply_id={reply.id}, error={str(e)}"
                )
                self.db_session.rollback()
                # 重新抛出异常，让调用方知道操作失败
                raise e
            return False

        # 生成回复内容
        reply_content = self.generate_reply_content(comment)
        if not reply_content:
            # 生成回复内容失败，更新任务状态为失败，同时更新评论上下文信息
            try:
                # 获取评论上下文并更新到回复任务中
                comment_context = self.get_comment_context(comment)
                reply.comment_context = self._build_message_with_context(
                    comment_context
                )

                # 更新任务状态为失败
                reply.status = ReplyStatus.failed
                reply.error_message = "生成回复内容失败"
                reply.updated_at = datetime.now()
                self.db_session.commit()
                logger.info(f"回复任务已标记为失败: reply_id={reply.id}")
            except Exception as e:
                logger.error(
                    f"更新回复任务状态失败: reply_id={reply.id}, error={str(e)}"
                )
                self.db_session.rollback()
                # 重新抛出异常，让调用方知道操作失败
                raise e
            return False

        # 更新回复任务
        return self.update_reply_task(reply, reply_content)

    def _process_single_reply_with_new_session(
        self, reply: CommentReply
    ) -> bool:
        """使用新数据库会话处理单个评论回复任务（线程安全）

        为每个任务创建独立的数据库会话，确保线程安全

        Args:
            reply: 评论回复任务

        Returns:
            bool: 处理是否成功
        """
        # 获取依赖
        from app.core.container import Container

        # 为当前线程创建新的数据库会话
        container = Container()
        task_db = None

        try:
            task_db = container.db_service().get_db()

            # 使用新的会话创建服务实例
            task_reply_service = ReplyService(
                task_db, self.coze_service, self.bot_id, self.user_id,
                self.max_retries, self.retry_delay, self.max_workers
            )

            # 处理单个任务
            return task_reply_service.process_single_reply(reply)

        except Exception as e:
            logger.error(
                f"线程安全处理回复任务异常: reply_id={reply.id}, error={str(e)}"
            )
            return False
        finally:
            # 确保会话被正确关闭
            if task_db:
                container.db_service().close_db(task_db)

    def process_pending_replies(
        self, use_parallel: bool = True
    ) -> Dict[str, Any]:
        """处理所有待处理的评论回复任务

        获取待处理的评论回复任务，从Coze获取回复内容，并更新任务状态
        支持并行处理模式

        Args:
            use_parallel: 是否使用并行处理模式

        Returns:
            Dict[str, Any]: 处理结果统计
        """
        results = {
            "total_replies": 0,
            "success_count": 0,
            "failed_count": 0
        }

        try:
            # 获取待处理的评论回复任务
            pending_replies = self.get_pending_replies()
            results["total_replies"] = len(pending_replies)

            if not pending_replies:
                logger.info("没有待处理的评论回复任务")
                return results

            if use_parallel and self.max_workers > 1:
                # 并行处理模式
                # 警告：当前实现存在线程安全问题，多个线程共享同一个db_session
                # 建议为每个线程创建独立的数据库会话
                logger.warning(
                    "并行处理模式可能存在数据库会话线程安全问题，"
                    "建议使用串行模式或为每个线程创建独立会话"
                )
                with ThreadPoolExecutor(
                    max_workers=self.max_workers
                ) as executor:
                    process_results = list(executor.map(
                        self._process_single_reply, pending_replies
                    ))

                    # 统计处理结果
                    for success in process_results:
                        if success:
                            results["success_count"] += 1
                        else:
                            results["failed_count"] += 1
            else:
                # 串行处理模式
                for reply in pending_replies:
                    if self._process_single_reply(reply):
                        results["success_count"] += 1
                    else:
                        results["failed_count"] += 1

            logger.info(f"评论回复任务处理结果: {results}")
            return results
        except Exception as e:
            logger.error(f"处理评论回复任务失败: {str(e)}")
            results["error"] = str(e)
            return results

    def process_pending_replies_thread_safe(
        self, use_parallel: bool = True
    ) -> Dict[str, Any]:
        """线程安全地处理所有待处理的评论回复任务

        为每个线程创建独立的数据库会话，避免线程安全问题

        Args:
            use_parallel: 是否使用并行处理模式

        Returns:
            Dict[str, Any]: 处理结果统计
        """
        results = {
            "total_replies": 0,
            "success_count": 0,
            "failed_count": 0
        }

        try:
            # 获取待处理的评论回复任务
            pending_replies = self.get_pending_replies()
            results["total_replies"] = len(pending_replies)

            if not pending_replies:
                logger.info("没有待处理的评论回复任务")
                return results

            if use_parallel and self.max_workers > 1:
                # 线程安全的并行处理模式
                logger.info(
                    f"使用线程安全的并行处理模式，工作线程数: {self.max_workers}"
                )
                with ThreadPoolExecutor(
                    max_workers=self.max_workers
                ) as executor:
                    process_results = list(executor.map(
                        self._process_single_reply_with_new_session,
                        pending_replies
                    ))

                    # 统计处理结果
                    for success in process_results:
                        if success:
                            results["success_count"] += 1
                        else:
                            results["failed_count"] += 1
            else:
                # 串行处理模式
                for reply in pending_replies:
                    if self._process_single_reply(reply):
                        results["success_count"] += 1
                    else:
                        results["failed_count"] += 1

            logger.info(f"评论回复任务处理结果: {results}")
            return results
        except Exception as e:
            logger.error(f"处理评论回复任务失败: {str(e)}")
            results["error"] = str(e)
            return results

    def process_single_reply(self, reply: CommentReply) -> bool:
        """处理单个评论回复任务

        此方法用于独立处理单个回复任务，与_process_single_reply的区别是
        此方法包含完整的事务管理和异常处理

        Args:
            reply: 评论回复任务

        Returns:
            bool: 处理是否成功
        """
        try:
            # 为任务更新状态为处理中
            reply.status = ReplyStatus.processing
            reply.updated_at = datetime.now()
            self.db_session.commit()

            # 处理单个回复
            success = self._process_single_reply(reply)

            # 注意：_process_single_reply 方法内部已经处理了失败状态的更新
            # 这里不需要再次更新状态，避免事务冲突

            return success
        except Exception as e:
            # 出现异常，回滚并记录错误
            self.db_session.rollback()
            logger.error(f"处理评论回复任务异常: reply_id={reply.id}, error={str(e)}")

            # 尝试更新任务状态为失败
            try:
                # 尝试获取评论上下文信息并更新
                comment = self.get_comment_by_id(reply.comment_id)
                if comment:
                    comment_context = self.get_comment_context(comment)
                    reply.comment_context = self._build_message_with_context(
                        comment_context
                    )

                reply.status = ReplyStatus.failed
                reply.error_message = str(e)[:255]  # 限制错误消息长度
                reply.updated_at = datetime.now()
                self.db_session.commit()
            except Exception as update_error:
                logger.error(f"更新回复任务状态失败: {str(update_error)}")
                self.db_session.rollback()

            return False
