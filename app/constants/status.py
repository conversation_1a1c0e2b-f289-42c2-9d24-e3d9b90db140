"""
状态常量定义模块

定义系统中使用的各种状态常量，避免硬编码字符串
"""


class CommentStatus:
    """评论记录状态常量"""
    PENDING = "pending"      # 待处理
    REPLIED = "replied"      # 已回复
    IGNORED = "ignored"      # 已忽略
    PROCESSING = "processing"  # 处理中
    FAILED = "failed"        # 处理失败
    
    # 所有有效状态的列表
    ALL_STATUSES = [PENDING, REPLIED, IGNORED, PROCESSING, FAILED]
    
    @classmethod
    def is_valid(cls, status: str) -> bool:
        """检查状态是否有效"""
        return status in cls.ALL_STATUSES


class TaskStatus:
    """任务状态常量"""
    PENDING = "pending"      # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消
    
    # 所有有效状态的列表
    ALL_STATUSES = [PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED]
    
    @classmethod
    def is_valid(cls, status: str) -> bool:
        """检查状态是否有效"""
        return status in cls.ALL_STATUSES


class CommentType:
    """评论类型常量"""
    NO_REPLY = 0    # 不需要回复
    MENTION = 1     # @回复
    REPLY = 2       # 回复我的
    
    # 所有有效类型的列表
    ALL_TYPES = [NO_REPLY, MENTION, REPLY]
    
    @classmethod
    def is_valid(cls, comment_type: int) -> bool:
        """检查评论类型是否有效"""
        return comment_type in cls.ALL_TYPES 