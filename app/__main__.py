"""
app包入口模块，允许作为Python模块执行
"""
import uvicorn
from app.core.config import settings

if __name__ == '__main__':
    print("使用 python -m app 启动应用...")
    print("主机: 0.0.0.0")
    print(f"端口: {settings.PORT}")
    print(f"调试模式: {settings.DEBUG}")
    print("=" * 50)
    
    # 启动Uvicorn服务器
    # 注意：app.main:app 会自动调用 create_app() 函数，包含数据库初始化
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=settings.PORT,
        reload=settings.DEBUG
    )
