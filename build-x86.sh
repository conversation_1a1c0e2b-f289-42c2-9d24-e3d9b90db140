#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始构建 x86_64 架构的镜像...${NC}"

# 加载环境变量
if [ -f .env.registry ]; then
  source .env.registry
else
  echo -e "${RED}警告: .env.registry 文件不存在，使用默认配置${NC}"
  REGISTRY_SERVER="111.230.32.57:5000"
fi

# 设置镜像信息
IMAGE_NAME="wecom-app"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${REGISTRY_SERVER}/${IMAGE_NAME}:${IMAGE_TAG}"

# 启用 BuildKit
export DOCKER_BUILDKIT=1

# 检查本地.env文件
if [ ! -f ".env" ]; then
  echo -e "${RED}错误: 未找到本地.env文件，此文件是必须的${NC}"
  echo -e "${YELLOW}请创建.env文件后再尝试构建${NC}"
  exit 1
fi

# 复制.env文件到构建上下文
echo -e "${GREEN}复制本地.env文件到构建上下文...${NC}"
cp .env .env.docker
echo -e "${GREEN}已复制.env文件 - 该文件将被打包到镜像中${NC}"

# 登录到私有仓库
if [ ! -z "${REGISTRY_USERNAME}" ] && [ ! -z "${REGISTRY_PASSWORD}" ]; then
  echo -e "${GREEN}登录到私有仓库 ${REGISTRY_SERVER}...${NC}"
  echo ${REGISTRY_PASSWORD} | docker login ${REGISTRY_SERVER} -u ${REGISTRY_USERNAME} --password-stdin
else
  echo -e "${YELLOW}注意: 未提供仓库凭据，跳过登录步骤${NC}"
fi

# 确保 qemu 已经设置
echo -e "${GREEN}1. 设置跨平台构建环境...${NC}"
docker run --privileged --rm tonistiigi/binfmt --install all

# 采用两步方式处理不安全注册表
echo -e "${GREEN}2. 使用两步方式处理不安全注册表...${NC}"
echo -e "${YELLOW}1) 先构建本地镜像，不直接推送${NC}"

# 构建 x86_64 架构镜像
echo -e "${GREEN}3. 开始构建 x86_64 架构镜像...${NC}"
echo -e "${YELLOW}目标镜像: ${FULL_IMAGE_NAME}${NC}"

# 询问是否使用缓存
read -p "是否使用构建缓存? (y/n, 默认: y): " USE_CACHE
USE_CACHE=${USE_CACHE:-y}

CACHE_OPTION=""
if [[ "${USE_CACHE}" != "y" && "${USE_CACHE}" != "Y" ]]; then
  CACHE_OPTION="--no-cache"
  echo -e "${YELLOW}禁用构建缓存${NC}"
else
  echo -e "${YELLOW}启用构建缓存${NC}"
fi

# 检查static文件夹是否存在
if [ ! -d "app/static" ]; then
  echo -e "${RED}警告: app/static 文件夹不存在${NC}"
  echo -e "${YELLOW}请确保static文件夹存在并包含必要的静态文件${NC}"
else
  echo -e "${GREEN}检测到 app/static 文件夹，将被包含在镜像中${NC}"
  # 显示static文件夹内容
  echo -e "${YELLOW}static文件夹内容:${NC}"
  ls -la app/static/
fi

# 创建或修改Dockerfile.x86
echo -e "${GREEN}准备Dockerfile.x86...${NC}"
cat > Dockerfile.x86 << EOF
FROM --platform=linux/amd64 python:3.10-slim-buster

WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码（包括static文件夹）
COPY . .

# 确保static文件夹被正确复制
RUN if [ -d "/app/app/static" ]; then \
      echo "static文件夹已成功复制到镜像中"; \
      ls -la /app/app/static/; \
    else \
      echo "警告: static文件夹未找到"; \
    fi

# 确保.env文件被正确复制到镜像中
COPY .env.docker /app/.env

# 添加提示信息表明.env和static已经包含在镜像中
RUN echo "环境配置文件(.env)和静态文件(static)已包含在镜像中" > /app/.env_included

# 启动命令
CMD ["python", "-m", "app"]
EOF

echo -e "${GREEN}Dockerfile.x86已创建，确保.env文件会被包含在镜像中${NC}"

# 本地构建镜像（不推送）
LOCAL_TAG="wecom-app-local:latest"
echo -e "${GREEN}开始本地构建镜像 ${LOCAL_TAG}...${NC}"
docker buildx build --platform linux/amd64 \
  -t ${LOCAL_TAG} \
  ${CACHE_OPTION} \
  --load \
  -f Dockerfile.x86 \
  .

if [ $? -ne 0 ]; then
  echo -e "${RED}本地构建失败，请检查错误信息${NC}"
  # 清理临时文件
  rm -f .env.docker
  exit 1
fi

echo -e "${GREEN}本地镜像构建成功: ${LOCAL_TAG}${NC}"
echo -e "${YELLOW}注意: 此镜像包含:${NC}"
echo -e "${YELLOW}  - .env配置文件在/app/.env路径${NC}"
echo -e "${YELLOW}  - static静态文件在/app/app/static路径${NC}"

# 标记并推送到目标注册表
echo -e "${GREEN}4. 标记并推送到目标注册表...${NC}"
echo -e "${YELLOW}本地镜像: ${LOCAL_TAG}${NC}"
echo -e "${YELLOW}目标镜像: ${FULL_IMAGE_NAME}${NC}"

docker tag ${LOCAL_TAG} ${FULL_IMAGE_NAME}
if [ $? -ne 0 ]; then
  echo -e "${RED}标记镜像失败${NC}"
  # 清理临时文件
  rm -f .env.docker
  exit 1
fi

echo -e "${GREEN}开始推送镜像到 ${FULL_IMAGE_NAME}...${NC}"
docker push ${FULL_IMAGE_NAME}

# 检查结果
if [ $? -eq 0 ]; then
  echo -e "${GREEN}x86_64 架构镜像已成功构建并推送到 ${FULL_IMAGE_NAME}${NC}"
  echo -e "${YELLOW}该镜像已包含:${NC}"
  echo -e "${YELLOW}  - .env配置文件${NC}"
  echo -e "${YELLOW}  - app/static静态文件夹${NC}"
  echo -e "${YELLOW}现在可以在服务器上使用以下命令拉取镜像:${NC}"
  echo -e "${GREEN}docker pull ${FULL_IMAGE_NAME}${NC}"
  
  # 生成服务器上使用的部署命令
  echo -e "${YELLOW}在服务器上执行以下命令来更新和重启服务:${NC}"
  cat << EOF
${GREEN}
# 拉取最新镜像
docker pull ${FULL_IMAGE_NAME}

# 使用 simple-deploy.sh 来部署服务
./simple-deploy.sh use-registry-image ${FULL_IMAGE_NAME}
${NC}
EOF
else
  echo -e "${RED}推送失败，请检查错误信息${NC}"
fi

# 清理临时文件
rm -f .env.docker
echo -e "${GREEN}已清理临时文件${NC}"