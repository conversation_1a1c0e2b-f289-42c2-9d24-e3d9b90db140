/*! For license information please see lib-router.90824fd8.js.LICENSE.txt */
"use strict";(self.webpackChunkweb_dashboard=self.webpackChunkweb_dashboard||[]).push([["118"],{50790:function(e,t,r){var n,a,o,i;function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}r.d(t,{J0:()=>d,OF:()=>O,RQ:()=>T,WK:()=>F,WS:()=>S,X3:()=>_,Zn:()=>P,aU:()=>n,cP:()=>v,cm:()=>L,fp:()=>b,lX:()=>u,p7:()=>Q,pC:()=>M,q_:()=>c}),(o=n||(n={})).Pop="POP",o.Push="PUSH",o.Replace="REPLACE";let s="popstate";function u(e){return void 0===e&&(e={}),y(function(e,t){let{pathname:r,search:n,hash:a}=e.location;return p("",{pathname:r,search:n,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:m(t)},null,e)}function c(e){return void 0===e&&(e={}),y(function(e,t){let{pathname:r="/",search:n="",hash:a=""}=v(e.location.hash.substr(1));return r.startsWith("/")||r.startsWith(".")||(r="/"+r),p("",{pathname:r,search:n,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){let r=e.document.querySelector("base"),n="";if(r&&r.getAttribute("href")){let t=e.location.href,r=t.indexOf("#");n=-1===r?t:t.slice(0,r)}return n+"#"+("string"==typeof t?t:m(t))},function(e,t){h("/"===e.pathname.charAt(0),"relative pathnames are not supported in hash history.push("+JSON.stringify(t)+")")},e)}function d(e,t){if(!1===e||null==e)throw Error(t)}function h(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw Error(t)}catch(e){}}}function f(e,t){return{usr:e.state,key:e.key,idx:t}}function p(e,t,r,n){return void 0===r&&(r=null),l({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?v(t):t,{state:r,key:t&&t.key||n||Math.random().toString(36).substr(2,8)})}function m(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&"?"!==r&&(t+="?"===r.charAt(0)?r:"?"+r),n&&"#"!==n&&(t+="#"===n.charAt(0)?n:"#"+n),t}function v(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function y(e,t,r,a){void 0===a&&(a={});let{window:o=document.defaultView,v5Compat:i=!1}=a,u=o.history,c=n.Pop,h=null,v=y();function y(){return(u.state||{idx:null}).idx}function g(){c=n.Pop;let e=y(),t=null==e?null:e-v;v=e,h&&h({action:c,location:b.location,delta:t})}function w(e){let t="null"!==o.location.origin?o.location.origin:o.location.href,r="string"==typeof e?e:m(e);return d(t,"No window.location.(origin|href) available to create URL for href: "+(r=r.replace(/ $/,"%20"))),new URL(r,t)}null==v&&(v=0,u.replaceState(l({},u.state,{idx:v}),""));let b={get action(){return c},get location(){return e(o,u)},listen(e){if(h)throw Error("A history only accepts one active listener");return o.addEventListener(s,g),h=e,()=>{o.removeEventListener(s,g),h=null}},createHref:e=>t(o,e),createURL:w,encodeLocation(e){let t=w(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){c=n.Push;let a=p(b.location,e,t);r&&r(a,e);let l=f(a,v=y()+1),s=b.createHref(a);try{u.pushState(l,"",s)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;o.location.assign(s)}i&&h&&h({action:c,location:b.location,delta:1})},replace:function(e,t){c=n.Replace;let a=p(b.location,e,t);r&&r(a,e);let o=f(a,v=y()),l=b.createHref(a);u.replaceState(o,"",l),i&&h&&h({action:c,location:b.location,delta:0})},go:e=>u.go(e)};return b}(i=a||(a={})).data="data",i.deferred="deferred",i.redirect="redirect",i.error="error";let g=new Set(["lazy","caseSensitive","path","id","index","children"]);function w(e,t,r,n){return void 0===r&&(r=[]),void 0===n&&(n={}),e.map((e,a)=>{let o=[...r,String(a)],i="string"==typeof e.id?e.id:o.join("-");if(d(!0!==e.index||!e.children,"Cannot specify children on an index route"),d(!n[i],'Found a route id collision on id "'+i+"\".  Route id's must be globally unique within Data Router usages"),!0===e.index){let r=l({},e,t(e),{id:i});return n[i]=r,r}{let r=l({},e,t(e),{id:i,children:void 0});return n[i]=r,e.children&&(r.children=w(e.children,t,o,n)),r}})}function b(e,t,r){return void 0===r&&(r="/"),E(e,t,r,!1)}function E(e,t,r,n){let a=P(("string"==typeof t?v(t):t).pathname||"/",r);if(null==a)return null;let o=function e(t,r,n,a){void 0===r&&(r=[]),void 0===n&&(n=[]),void 0===a&&(a="");let o=(t,o,i)=>{var l,s;let u,c,h={relativePath:void 0===i?t.path||"":i,caseSensitive:!0===t.caseSensitive,childrenIndex:o,route:t};h.relativePath.startsWith("/")&&(d(h.relativePath.startsWith(a),'Absolute route path "'+h.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),h.relativePath=h.relativePath.slice(a.length));let f=T([a,h.relativePath]),p=n.concat(h);t.children&&t.children.length>0&&(d(!0!==t.index,'Index routes must not have child routes. Please remove all child routes from route path "'+f+'".'),e(t.children,r,p,f)),(null!=t.path||t.index)&&r.push({path:f,score:(l=f,s=t.index,c=(u=l.split("/")).length,u.some(x)&&(c+=-2),s&&(c+=2),u.filter(e=>!x(e)).reduce((e,t)=>e+(R.test(t)?3:""===t?1:10),c)),routesMeta:p})};return t.forEach((e,t)=>{var r;if(""!==e.path&&null!=(r=e.path)&&r.includes("?"))for(let r of function e(t){let r=t.split("/");if(0===r.length)return[];let[n,...a]=r,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(0===a.length)return o?[i,""]:[i];let l=e(a.join("/")),s=[];return s.push(...l.map(e=>""===e?i:[i,e].join("/"))),o&&s.push(...l),s.map(e=>t.startsWith("/")&&""===e?"/":e)}(e.path))o(e,t,r);else o(e,t)}),r}(e);o.sort((e,t)=>{var r,n;return e.score!==t.score?t.score-e.score:(r=e.routesMeta.map(e=>e.childrenIndex),n=t.routesMeta.map(e=>e.childrenIndex),r.length===n.length&&r.slice(0,-1).every((e,t)=>e===n[t])?r[r.length-1]-n[n.length-1]:0)});let i=null;for(let e=0;null==i&&e<o.length;++e){let t=function(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return h(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}(a);i=function(e,t,r){void 0===r&&(r=!1);let{routesMeta:n}=e,a={},o="/",i=[];for(let e=0;e<n.length;++e){let l=n[e],s=e===n.length-1,u="/"===o?t:t.slice(o.length)||"/",c=D({path:l.relativePath,caseSensitive:l.caseSensitive,end:s},u),d=l.route;if(!c&&s&&r&&!n[n.length-1].route.index&&(c=D({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:T([o,c.pathname]),pathnameBase:j(T([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=T([o,c.pathnameBase]))}return i}(o[e],t,n)}return i}function S(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}let R=/^:[\w-]+$/,x=e=>"*"===e;function D(e,t){var r,n,a;let o,i;"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[l,s]=(r=e.path,n=e.caseSensitive,a=e.end,void 0===n&&(n=!1),void 0===a&&(a=!0),h("*"===r||!r.endsWith("*")||r.endsWith("/*"),'Route path "'+r+'" will be treated as if it were "'+r.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+r.replace(/\*$/,"/*")+'".'),o=[],i="^"+r.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,r)=>(o.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)")),r.endsWith("*")?(o.push({paramName:"*"}),i+="*"===r||"/*"===r?"(.*)$":"(?:\\/(.+)|\\/*)$"):a?i+="\\/*$":""!==r&&"/"!==r&&(i+="(?:(?=\\/|$))"),[new RegExp(i,n?void 0:"i"),o]),u=t.match(l);if(!u)return null;let c=u[0],d=c.replace(/(.)\/+$/,"$1"),f=u.slice(1);return{params:s.reduce((e,t,r)=>{let{paramName:n,isOptional:a}=t;if("*"===n){let e=f[r]||"";d=c.slice(0,c.length-e.length).replace(/(.)\/+$/,"$1")}let o=f[r];return a&&!o?e[n]=void 0:e[n]=(o||"").replace(/%2F/g,"/"),e},{}),pathname:c,pathnameBase:d,pattern:e}}function P(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}function C(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field [")+JSON.stringify(n)+"].  Please separate it out to the `to."+r+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function k(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function L(e,t){let r=k(e);return t?r.map((e,t)=>t===r.length-1?e.pathname:e.pathnameBase):r.map(e=>e.pathnameBase)}function M(e,t,r,n){let a,o;void 0===n&&(n=!1),"string"==typeof e?a=v(e):(d(!(a=l({},e)).pathname||!a.pathname.includes("?"),C("?","pathname","search",a)),d(!a.pathname||!a.pathname.includes("#"),C("#","pathname","hash",a)),d(!a.search||!a.search.includes("#"),C("#","search","hash",a)));let i=""===e||""===a.pathname,s=i?"/":a.pathname;if(null==s)o=r;else{let e=t.length-1;if(!n&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let u=function(e,t){var r;let n;void 0===t&&(t="/");let{pathname:a,search:o="",hash:i=""}="string"==typeof e?v(e):e;return{pathname:a?a.startsWith("/")?a:(r=a,n=t.replace(/\/+$/,"").split("/"),r.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"):t,search:U(o),hash:A(i)}}(a,o),c=s&&"/"!==s&&s.endsWith("/"),h=(i||"."===s)&&r.endsWith("/");return!u.pathname.endsWith("/")&&(c||h)&&(u.pathname+="/"),u}let T=e=>e.join("/").replace(/\/\/+/g,"/"),j=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),U=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",A=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";class _ extends Error{}class O{constructor(e,t,r,n){void 0===n&&(n=!1),this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}}function F(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}let B=["post","put","patch","delete"],z=new Set(B),N=new Set(["get",...B]),I=new Set([301,302,303,307,308]),H=new Set([307,308]),W={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},J={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},$={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},V=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,q=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Y="remix-router-transitions";function Q(e){let t,r,o,i,s,u,c=e.window?e.window:"undefined"!=typeof window?window:void 0,f=void 0!==c&&void 0!==c.document&&void 0!==c.document.createElement,m=!f;if(d(e.routes.length>0,"You must provide a non-empty routes array to createRouter"),e.mapRouteProperties)t=e.mapRouteProperties;else if(e.detectErrorBoundary){let r=e.detectErrorBoundary;t=e=>({hasErrorBoundary:r(e)})}else t=q;let v={},y=w(e.routes,t,void 0,v),g=e.basename||"/",R=e.dataStrategy||eo,x=e.patchRoutesOnNavigation,D=l({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),C=null,k=new Set,L=null,M=null,T=null,j=null!=e.hydrationData,U=b(y,e.history.location,g),A=null;if(null==U&&!x){let t=eg(404,{pathname:e.history.location.pathname}),{matches:r,route:n}=ey(y);U=r,A={[n.id]:t}}if(U&&!e.hydrationData&&ta(U,y,e.history.location.pathname).active&&(U=null),U)if(U.some(e=>e.route.lazy))o=!1;else if(U.some(e=>e.route.loader))if(D.v7_partialHydration){let t=e.hydrationData?e.hydrationData.loaderData:null,r=e.hydrationData?e.hydrationData.errors:null;if(r){let e=U.findIndex(e=>void 0!==r[e.route.id]);o=U.slice(0,e+1).every(e=>!ee(e.route,t,r))}else o=U.every(e=>!ee(e.route,t,r))}else o=null!=e.hydrationData;else o=!0;else if(o=!1,U=[],D.v7_partialHydration){let t=ta(null,y,e.history.location.pathname);t.active&&t.matches&&(U=t.matches)}let _={historyAction:e.history.action,location:e.history.location,matches:U,initialized:o,navigation:W,restoreScrollPosition:null==e.hydrationData&&null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||A,fetchers:new Map,blockers:new Map},O=n.Pop,B=!1,z=!1,N=new Map,Q=null,K=!1,et=!1,er=[],ea=new Set,el=new Map,ed=0,eh=-1,eb=new Map,ex=new Set,eM=new Map,eO=new Map,eF=new Set,eB=new Map,ez=new Map;function eN(e,t){void 0===t&&(t={}),_=l({},_,e);let r=[],n=[];D.v7_fetcherPersist&&_.fetchers.forEach((e,t)=>{"idle"===e.state&&(eF.has(t)?n.push(t):r.push(t))}),[...k].forEach(e=>e(_,{deletedFetchers:n,viewTransitionOpts:t.viewTransitionOpts,flushSync:!0===t.flushSync})),D.v7_fetcherPersist&&(r.forEach(e=>_.fetchers.delete(e)),n.forEach(e=>e7(e)))}function eI(t,a,o){var i,s;let u,c,{flushSync:d}=void 0===o?{}:o,h=null!=_.actionData&&null!=_.navigation.formMethod&&eP(_.navigation.formMethod)&&"loading"===_.navigation.state&&(null==(i=t.state)?void 0:i._isRedirect)!==!0;u=a.actionData?Object.keys(a.actionData).length>0?a.actionData:null:h?_.actionData:null;let f=a.loaderData?ep(_.loaderData,a.loaderData,a.matches||[],a.errors):_.loaderData,p=_.blockers;p.size>0&&(p=new Map(p)).forEach((e,t)=>p.set(t,$));let m=!0===B||null!=_.navigation.formMethod&&eP(_.navigation.formMethod)&&(null==(s=t.state)?void 0:s._isRedirect)!==!0;if(r&&(y=r,r=void 0),K||O===n.Pop||(O===n.Push?e.history.push(t,t.state):O===n.Replace&&e.history.replace(t,t.state)),O===n.Pop){let e=N.get(_.location.pathname);e&&e.has(t.pathname)?c={currentLocation:_.location,nextLocation:t}:N.has(t.pathname)&&(c={currentLocation:t,nextLocation:_.location})}else if(z){let e=N.get(_.location.pathname);e?e.add(t.pathname):(e=new Set([t.pathname]),N.set(_.location.pathname,e)),c={currentLocation:_.location,nextLocation:t}}eN(l({},a,{actionData:u,loaderData:f,historyAction:O,location:t,initialized:!0,navigation:W,revalidation:"idle",restoreScrollPosition:tn(t,a.matches||_.matches),preventScrollReset:m,blockers:p}),{viewTransitionOpts:c,flushSync:!0===d}),O=n.Pop,B=!1,z=!1,K=!1,et=!1,er=[]}async function eH(t,r){if("number"==typeof t)return void e.history.go(t);let a=X(_.location,_.matches,g,D.v7_prependBasename,t,D.v7_relativeSplatPath,null==r?void 0:r.fromRouteId,null==r?void 0:r.relative),{path:o,submission:i,error:s}=G(D.v7_normalizeFormMethod,!1,a,r),u=_.location,c=p(_.location,o,r&&r.state);c=l({},c,e.history.encodeLocation(c));let d=r&&null!=r.replace?r.replace:void 0,h=n.Push;!0===d?h=n.Replace:!1===d||null!=i&&eP(i.formMethod)&&i.formAction===_.location.pathname+_.location.search&&(h=n.Replace);let f=r&&"preventScrollReset"in r?!0===r.preventScrollReset:void 0,m=!0===(r&&r.flushSync),v=e6({currentLocation:u,nextLocation:c,historyAction:h});return v?void e9(v,{state:"blocked",location:c,proceed(){e9(v,{state:"proceeding",proceed:void 0,reset:void 0,location:c}),eH(t,r)},reset(){let e=new Map(_.blockers);e.set(v,$),eN({blockers:e})}}):await eW(h,c,{submission:i,pendingError:s,preventScrollReset:f,replace:r&&r.replace,enableViewTransition:r&&r.viewTransition,flushSync:m})}async function eW(t,n,o){var i,u,c,d;let h;s&&s.abort(),s=null,O=t,K=!0===(o&&o.startUninterruptedRevalidation),i=_.location,u=_.matches,L&&T&&(L[tr(i,u)]=T()),B=!0===(o&&o.preventScrollReset),z=!0===(o&&o.enableViewTransition);let f=r||y,p=o&&o.overrideNavigation,m=b(f,n,g),v=!0===(o&&o.flushSync),w=ta(m,f,n.pathname);if(w.active&&w.matches&&(m=w.matches),!m){let{error:e,notFoundMatches:t,route:r}=te(n.pathname);eI(n,{matches:t,loaderData:{},errors:{[r.id]:e}},{flushSync:v});return}if(_.initialized&&!et&&(c=_.location,d=n,c.pathname===d.pathname&&c.search===d.search&&(""===c.hash?""!==d.hash:c.hash===d.hash||""!==d.hash||!1))&&!(o&&o.submission&&eP(o.submission.formMethod)))return void eI(n,{matches:m},{flushSync:v});s=new AbortController;let E=ec(e.history,n,s.signal,o&&o.submission);if(o&&o.pendingError)h=[ev(m).route.id,{type:a.error,error:o.pendingError}];else if(o&&o.submission&&eP(o.submission.formMethod)){let t=await eJ(E,n,o.submission,m,w.active,{replace:o.replace,flushSync:v});if(t.shortCircuited)return;if(t.pendingActionResult){let[e,r]=t.pendingActionResult;if(eS(r)&&F(r.error)&&404===r.error.status){s=null,eI(n,{matches:t.matches,loaderData:{},errors:{[e]:r.error}});return}}m=t.matches||m,h=t.pendingActionResult,p=eU(n,o.submission),v=!1,w.active=!1,E=ec(e.history,E.url,E.signal)}let{shortCircuited:S,matches:R,loaderData:x,errors:D}=await e$(E,n,m,w.active,p,o&&o.submission,o&&o.fetcherSubmission,o&&o.replace,o&&!0===o.initialHydration,v,h);S||(s=null,eI(n,l({matches:R||m},em(h),{loaderData:x,errors:D})))}async function eJ(e,t,r,o,i,l){var s;let u;if(void 0===l&&(l={}),eK(),eN({navigation:{state:"submitting",location:t,formMethod:(s=r).formMethod,formAction:s.formAction,formEncType:s.formEncType,formData:s.formData,json:s.json,text:s.text}},{flushSync:!0===l.flushSync}),i){let r=await to(o,t.pathname,e.signal);if("aborted"===r.type)return{shortCircuited:!0};if("error"===r.type){let e=ev(r.partialMatches).route.id;return{matches:r.partialMatches,pendingActionResult:[e,{type:a.error,error:r.error}]}}if(r.matches)o=r.matches;else{let{notFoundMatches:e,error:r,route:n}=te(t.pathname);return{matches:e,pendingActionResult:[n.id,{type:a.error,error:r}]}}}let c=eT(o,t);if(c.route.action||c.route.lazy){if(u=(await eX("action",_,e,[c],o,null))[c.route.id],e.signal.aborted)return{shortCircuited:!0}}else u={type:a.error,error:eg(405,{method:e.method,pathname:t.pathname,routeId:c.route.id})};if(eR(u)){let t;return t=l&&null!=l.replace?l.replace:eu(u.response.headers.get("Location"),new URL(e.url),g)===_.location.pathname+_.location.search,await eQ(e,u,!0,{submission:r,replace:t}),{shortCircuited:!0}}if(eE(u))throw eg(400,{type:"defer-action"});if(eS(u)){let e=ev(o,c.route.id);return!0!==(l&&l.replace)&&(O=n.Push),{matches:o,pendingActionResult:[e.route.id,u]}}return{matches:o,pendingActionResult:[c.route.id,u]}}async function e$(t,n,a,o,i,u,c,d,h,f,p){let m=i||eU(n,u),v=u||c||ej(m),w=!K&&(!D.v7_partialHydration||!h);if(o){if(w){let e=eV(p);eN(l({navigation:m},void 0!==e?{actionData:e}:{}),{flushSync:f})}let e=await to(a,n.pathname,t.signal);if("aborted"===e.type)return{shortCircuited:!0};if("error"===e.type){let t=ev(e.partialMatches).route.id;return{matches:e.partialMatches,loaderData:{},errors:{[t]:e.error}}}if(e.matches)a=e.matches;else{let{error:e,notFoundMatches:t,route:r}=te(n.pathname);return{matches:t,loaderData:{},errors:{[r.id]:e}}}}let b=r||y,[E,S]=Z(e.history,_,a,v,n,D.v7_partialHydration&&!0===h,D.v7_skipActionErrorRevalidation,et,er,ea,eF,eM,ex,b,g,p);if(tt(e=>!(a&&a.some(t=>t.route.id===e))||E&&E.some(t=>t.route.id===e)),eh=++ed,0===E.length&&0===S.length){let e=e2();return eI(n,l({matches:a,loaderData:{},errors:p&&eS(p[1])?{[p[0]]:p[1].error}:null},em(p),e?{fetchers:new Map(_.fetchers)}:{}),{flushSync:f}),{shortCircuited:!0}}if(w){let e={};if(!o){e.navigation=m;let t=eV(p);void 0!==t&&(e.actionData=t)}S.length>0&&(S.forEach(e=>{let t=_.fetchers.get(e.key),r=eA(void 0,t?t.data:void 0);_.fetchers.set(e.key,r)}),e.fetchers=new Map(_.fetchers)),eN(e,{flushSync:f})}S.forEach(e=>{e4(e.key),e.controller&&el.set(e.key,e.controller)});let R=()=>S.forEach(e=>e4(e.key));s&&s.signal.addEventListener("abort",R);let{loaderResults:x,fetcherResults:P}=await eG(_,a,E,S,t);if(t.signal.aborted)return{shortCircuited:!0};s&&s.signal.removeEventListener("abort",R),S.forEach(e=>el.delete(e.key));let C=ew(x);if(C)return await eQ(t,C.result,!0,{replace:d}),{shortCircuited:!0};if(C=ew(P))return ex.add(C.key),await eQ(t,C.result,!0,{replace:d}),{shortCircuited:!0};let{loaderData:k,errors:L}=ef(_,a,x,p,S,P,eB);eB.forEach((e,t)=>{e.subscribe(r=>{(r||e.done)&&eB.delete(t)})}),D.v7_partialHydration&&h&&_.errors&&(L=l({},_.errors,L));let M=e2(),T=e5(eh),j=M||T||S.length>0;return l({matches:a,loaderData:k,errors:L},j?{fetchers:new Map(_.fetchers)}:{})}function eV(e){if(e&&!eS(e[1]))return{[e[0]]:e[1].data};if(_.actionData)if(0===Object.keys(_.actionData).length)return null;else return _.actionData}async function eq(t,n,a,o,i,l,u,c,h){var f,p;function m(e){if(!e.route.action&&!e.route.lazy){let e=eg(405,{method:h.formMethod,pathname:a,routeId:n});return e0(t,n,e,{flushSync:u}),!0}return!1}if(eK(),eM.delete(t),!l&&m(o))return;let v=_.fetchers.get(t);eZ(t,(f=h,p=v,{state:"submitting",formMethod:f.formMethod,formAction:f.formAction,formEncType:f.formEncType,formData:f.formData,json:f.json,text:f.text,data:p?p.data:void 0}),{flushSync:u});let w=new AbortController,E=ec(e.history,a,w.signal,h);if(l){let e=await to(i,a,E.signal);if("aborted"===e.type)return;if("error"===e.type)return void e0(t,n,e.error,{flushSync:u});if(!e.matches)return void e0(t,n,eg(404,{pathname:a}),{flushSync:u});if(m(o=eT(i=e.matches,a)))return}el.set(t,w);let S=ed,R=(await eX("action",_,E,[o],i,t))[o.route.id];if(E.signal.aborted){el.get(t)===w&&el.delete(t);return}if(D.v7_fetcherPersist&&eF.has(t)){if(eR(R)||eS(R))return void eZ(t,e_(void 0))}else{if(eR(R))return(el.delete(t),eh>S)?void eZ(t,e_(void 0)):(ex.add(t),eZ(t,eA(h)),eQ(E,R,!1,{fetcherSubmission:h,preventScrollReset:c}));if(eS(R))return void e0(t,n,R.error)}if(eE(R))throw eg(400,{type:"defer-action"});let x=_.navigation.location||_.location,P=ec(e.history,x,w.signal),C=r||y,k="idle"!==_.navigation.state?b(C,_.navigation.location,g):_.matches;d(k,"Didn't find any matches after fetcher action");let L=++ed;eb.set(t,L);let M=eA(h,R.data);_.fetchers.set(t,M);let[T,j]=Z(e.history,_,k,h,x,!1,D.v7_skipActionErrorRevalidation,et,er,ea,eF,eM,ex,C,g,[o.route.id,R]);j.filter(e=>e.key!==t).forEach(e=>{let t=e.key,r=_.fetchers.get(t),n=eA(void 0,r?r.data:void 0);_.fetchers.set(t,n),e4(t),e.controller&&el.set(t,e.controller)}),eN({fetchers:new Map(_.fetchers)});let U=()=>j.forEach(e=>e4(e.key));w.signal.addEventListener("abort",U);let{loaderResults:A,fetcherResults:F}=await eG(_,k,T,j,P);if(w.signal.aborted)return;w.signal.removeEventListener("abort",U),eb.delete(t),el.delete(t),j.forEach(e=>el.delete(e.key));let B=ew(A);if(B)return eQ(P,B.result,!1,{preventScrollReset:c});if(B=ew(F))return ex.add(B.key),eQ(P,B.result,!1,{preventScrollReset:c});let{loaderData:z,errors:N}=ef(_,k,A,void 0,j,F,eB);if(_.fetchers.has(t)){let e=e_(R.data);_.fetchers.set(t,e)}e5(L),"loading"===_.navigation.state&&L>eh?(d(O,"Expected pending action"),s&&s.abort(),eI(_.navigation.location,{matches:k,loaderData:z,errors:N,fetchers:new Map(_.fetchers)})):(eN({errors:N,loaderData:ep(_.loaderData,z,k,N),fetchers:new Map(_.fetchers)}),et=!1)}async function eY(t,r,n,a,o,i,l,s,u){let c=_.fetchers.get(t);eZ(t,eA(u,c?c.data:void 0),{flushSync:l});let h=new AbortController,f=ec(e.history,n,h.signal);if(i){let e=await to(o,n,f.signal);if("aborted"===e.type)return;if("error"===e.type)return void e0(t,r,e.error,{flushSync:l});if(!e.matches)return void e0(t,r,eg(404,{pathname:n}),{flushSync:l});a=eT(o=e.matches,n)}el.set(t,h);let p=ed,m=(await eX("loader",_,f,[a],o,t))[a.route.id];if(eE(m)&&(m=await eL(m,f.signal,!0)||m),el.get(t)===h&&el.delete(t),!f.signal.aborted){if(eF.has(t))return void eZ(t,e_(void 0));if(eR(m))if(eh>p)return void eZ(t,e_(void 0));else{ex.add(t),await eQ(f,m,!1,{preventScrollReset:s});return}if(eS(m))return void e0(t,r,m.error);d(!eE(m),"Unhandled fetcher deferred data"),eZ(t,e_(m.data))}}async function eQ(t,r,a,o){let{submission:i,fetcherSubmission:u,preventScrollReset:h,replace:m}=void 0===o?{}:o;r.response.headers.has("X-Remix-Revalidate")&&(et=!0);let v=r.response.headers.get("Location");d(v,"Expected a Location header on the redirect Response"),v=eu(v,new URL(t.url),g);let y=p(_.location,v,{_isRedirect:!0});if(f){let t=!1;if(r.response.headers.has("X-Remix-Reload-Document"))t=!0;else if(V.test(v)){let r=e.history.createURL(v);t=r.origin!==c.location.origin||null==P(r.pathname,g)}if(t)return void(m?c.location.replace(v):c.location.assign(v))}s=null;let w=!0===m||r.response.headers.has("X-Remix-Replace")?n.Replace:n.Push,{formMethod:b,formAction:E,formEncType:S}=_.navigation;!i&&!u&&b&&E&&S&&(i=ej(_.navigation));let R=i||u;if(H.has(r.response.status)&&R&&eP(R.formMethod))await eW(w,y,{submission:l({},R,{formAction:v}),preventScrollReset:h||B,enableViewTransition:a?z:void 0});else{let e=eU(y,i);await eW(w,y,{overrideNavigation:e,fetcherSubmission:u,preventScrollReset:h||B,enableViewTransition:a?z:void 0})}}async function eX(e,r,n,o,i,l){let s,u={};try{s=await ei(R,e,r,n,o,i,l,v,t)}catch(e){return o.forEach(t=>{u[t.route.id]={type:a.error,error:e}}),u}for(let[e,t]of Object.entries(s)){var c;if(eD((c=t).result)&&I.has(c.result.status)){let r=t.result;u[e]={type:a.redirect,response:function(e,t,r,n,a,o){let i=e.headers.get("Location");if(d(i,"Redirects returned/thrown from loaders/actions must have a Location header"),!V.test(i)){let l=n.slice(0,n.findIndex(e=>e.route.id===r)+1);i=X(new URL(t.url),l,a,!0,i,o),e.headers.set("Location",i)}return e}(r,n,e,i,g,D.v7_relativeSplatPath)}}else u[e]=await es(t)}return u}async function eG(t,r,n,o,i){let l=t.matches,s=eX("loader",t,i,n,r,null),u=Promise.all(o.map(async r=>{if(!r.matches||!r.match||!r.controller)return Promise.resolve({[r.key]:{type:a.error,error:eg(404,{pathname:r.path})}});{let n=(await eX("loader",t,ec(e.history,r.path,r.controller.signal),[r.match],r.matches,r.key))[r.match.route.id];return{[r.key]:n}}})),c=await s,d=(await u).reduce((e,t)=>Object.assign(e,t),{});return await Promise.all([eC(r,c,i.signal,l,t.loaderData),ek(r,d,o)]),{loaderResults:c,fetcherResults:d}}function eK(){et=!0,er.push(...tt()),eM.forEach((e,t)=>{el.has(t)&&ea.add(t),e4(t)})}function eZ(e,t,r){void 0===r&&(r={}),_.fetchers.set(e,t),eN({fetchers:new Map(_.fetchers)},{flushSync:!0===(r&&r.flushSync)})}function e0(e,t,r,n){void 0===n&&(n={});let a=ev(_.matches,t);e7(e),eN({errors:{[a.route.id]:r},fetchers:new Map(_.fetchers)},{flushSync:!0===(n&&n.flushSync)})}function e1(e){return D.v7_fetcherPersist&&(eO.set(e,(eO.get(e)||0)+1),eF.has(e)&&eF.delete(e)),_.fetchers.get(e)||J}function e7(e){let t=_.fetchers.get(e);el.has(e)&&!(t&&"loading"===t.state&&eb.has(e))&&e4(e),eM.delete(e),eb.delete(e),ex.delete(e),eF.delete(e),ea.delete(e),_.fetchers.delete(e)}function e4(e){let t=el.get(e);t&&(t.abort(),el.delete(e))}function e3(e){for(let t of e){let e=e_(e1(t).data);_.fetchers.set(t,e)}}function e2(){let e=[],t=!1;for(let r of ex){let n=_.fetchers.get(r);d(n,"Expected fetcher: "+r),"loading"===n.state&&(ex.delete(r),e.push(r),t=!0)}return e3(e),t}function e5(e){let t=[];for(let[r,n]of eb)if(n<e){let e=_.fetchers.get(r);d(e,"Expected fetcher: "+r),"loading"===e.state&&(e4(r),eb.delete(r),t.push(r))}return e3(t),t.length>0}function e8(e){_.blockers.delete(e),ez.delete(e)}function e9(e,t){let r=_.blockers.get(e)||$;d("unblocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"proceeding"===t.state||"blocked"===r.state&&"unblocked"===t.state||"proceeding"===r.state&&"unblocked"===t.state,"Invalid blocker state transition: "+r.state+" -> "+t.state);let n=new Map(_.blockers);n.set(e,t),eN({blockers:n})}function e6(e){let{currentLocation:t,nextLocation:r,historyAction:n}=e;if(0===ez.size)return;ez.size>1&&h(!1,"A router only supports one blocker at a time");let a=Array.from(ez.entries()),[o,i]=a[a.length-1],l=_.blockers.get(o);if((!l||"proceeding"!==l.state)&&i({currentLocation:t,nextLocation:r,historyAction:n}))return o}function te(e){let t=eg(404,{pathname:e}),{matches:n,route:a}=ey(r||y);return tt(),{notFoundMatches:n,route:a,error:t}}function tt(e){let t=[];return eB.forEach((r,n)=>{(!e||e(n))&&(r.cancel(),t.push(n),eB.delete(n))}),t}function tr(e,t){return M&&M(e,t.map(e=>S(e,_.loaderData)))||e.key}function tn(e,t){if(L){let r=L[tr(e,t)];if("number"==typeof r)return r}return null}function ta(e,t,r){if(x){if(!e)return{active:!0,matches:E(t,r,g,!0)||[]};else if(Object.keys(e[0].params).length>0)return{active:!0,matches:E(t,r,g,!0)}}return{active:!1,matches:null}}async function to(e,n,a){if(!x)return{type:"success",matches:e};let o=e;for(;;){let e=null==r,i=r||y,l=v;try{await x({path:n,matches:o,patch:(e,r)=>{a.aborted||en(e,r,i,l,t)}})}catch(e){return{type:"error",error:e,partialMatches:o}}finally{e&&!a.aborted&&(y=[...y])}if(a.aborted)return{type:"aborted"};let s=b(i,n,g);if(s)return{type:"success",matches:s};let u=E(i,n,g,!0);if(!u||o.length===u.length&&o.every((e,t)=>e.route.id===u[t].route.id))return{type:"success",matches:null};o=u}}return i={get basename(){return g},get future(){return D},get state(){return _},get routes(){return y},get window(){return c},initialize:function(){if(C=e.history.listen(t=>{let{action:r,location:n,delta:a}=t;if(u){u(),u=void 0;return}h(0===ez.size||null!=a,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let o=e6({currentLocation:_.location,nextLocation:n,historyAction:r});if(o&&null!=a){let t=new Promise(e=>{u=e});e.history.go(-1*a),e9(o,{state:"blocked",location:n,proceed(){e9(o,{state:"proceeding",proceed:void 0,reset:void 0,location:n}),t.then(()=>e.history.go(a))},reset(){let e=new Map(_.blockers);e.set(o,$),eN({blockers:e})}});return}return eW(r,n)}),f){var t=c,r=N;try{let e=t.sessionStorage.getItem(Y);if(e){let t=JSON.parse(e);for(let[e,n]of Object.entries(t||{}))n&&Array.isArray(n)&&r.set(e,new Set(n||[]))}}catch(e){}let e=()=>(function(e,t){if(t.size>0){let r={};for(let[e,n]of t)r[e]=[...n];try{e.sessionStorage.setItem(Y,JSON.stringify(r))}catch(e){h(!1,"Failed to save applied view transitions in sessionStorage ("+e+").")}}})(c,N);c.addEventListener("pagehide",e),Q=()=>c.removeEventListener("pagehide",e)}return _.initialized||eW(n.Pop,_.location,{initialHydration:!0}),i},subscribe:function(e){return k.add(e),()=>k.delete(e)},enableScrollRestoration:function(e,t,r){if(L=e,T=t,M=r||null,!j&&_.navigation===W){j=!0;let e=tn(_.location,_.matches);null!=e&&eN({restoreScrollPosition:e})}return()=>{L=null,T=null,M=null}},navigate:eH,fetch:function(e,t,n,a){if(m)throw Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");e4(e);let o=!0===(a&&a.flushSync),i=r||y,l=X(_.location,_.matches,g,D.v7_prependBasename,n,D.v7_relativeSplatPath,t,null==a?void 0:a.relative),s=b(i,l,g),u=ta(s,i,l);if(u.active&&u.matches&&(s=u.matches),!s)return void e0(e,t,eg(404,{pathname:l}),{flushSync:o});let{path:c,submission:d,error:h}=G(D.v7_normalizeFormMethod,!0,l,a);if(h)return void e0(e,t,h,{flushSync:o});let f=eT(s,c),p=!0===(a&&a.preventScrollReset);if(d&&eP(d.formMethod))return void eq(e,t,c,f,s,u.active,o,p,d);eM.set(e,{routeId:t,path:c}),eY(e,t,c,f,s,u.active,o,p,d)},revalidate:function(){if(eK(),eN({revalidation:"loading"}),"submitting"!==_.navigation.state){if("idle"===_.navigation.state)return void eW(_.historyAction,_.location,{startUninterruptedRevalidation:!0});eW(O||_.historyAction,_.navigation.location,{overrideNavigation:_.navigation,enableViewTransition:!0===z})}},createHref:t=>e.history.createHref(t),encodeLocation:t=>e.history.encodeLocation(t),getFetcher:e1,deleteFetcher:function(e){if(D.v7_fetcherPersist){let t=(eO.get(e)||0)-1;t<=0?(eO.delete(e),eF.add(e)):eO.set(e,t)}else e7(e);eN({fetchers:new Map(_.fetchers)})},dispose:function(){C&&C(),Q&&Q(),k.clear(),s&&s.abort(),_.fetchers.forEach((e,t)=>e7(t)),_.blockers.forEach((e,t)=>e8(t))},getBlocker:function(e,t){let r=_.blockers.get(e)||$;return ez.get(e)!==t&&ez.set(e,t),r},deleteBlocker:e8,patchRoutes:function(e,n){let a=null==r;en(e,n,r||y,v,t),a&&(y=[...y],eN({}))},_internalFetchControllers:el,_internalActiveDeferreds:eB,_internalSetRoutes:function(e){r=w(e,t,void 0,v={})}}}function X(e,t,r,n,a,o,i,l){let s,u;if(i){for(let e of(s=[],t))if(s.push(e),e.route.id===i){u=e;break}}else s=t,u=t[t.length-1];let c=M(a||".",L(s,o),P(e.pathname,r)||e.pathname,"path"===l);if(null==a&&(c.search=e.search,c.hash=e.hash),(null==a||""===a||"."===a)&&u){let e=eM(c.search);if(u.route.index&&!e)c.search=c.search?c.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&e){let e=new URLSearchParams(c.search),t=e.getAll("index");e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let r=e.toString();c.search=r?"?"+r:""}}return n&&"/"!==r&&(c.pathname="/"===c.pathname?r:T([r,c.pathname])),m(c)}function G(e,t,r,n){var a;let o,i;if(!n||!(null!=n&&("formData"in n&&null!=n.formData||"body"in n&&void 0!==n.body)))return{path:r};if(n.formMethod&&(a=n.formMethod,!N.has(a.toLowerCase())))return{path:r,error:eg(405,{method:n.formMethod})};let l=()=>({path:r,error:eg(400,{type:"invalid-body"})}),s=n.formMethod||"get",u=e?s.toUpperCase():s.toLowerCase(),c=eb(r);if(void 0!==n.body){if("text/plain"===n.formEncType){if(!eP(u))return l();let e="string"==typeof n.body?n.body:n.body instanceof FormData||n.body instanceof URLSearchParams?Array.from(n.body.entries()).reduce((e,t)=>{let[r,n]=t;return""+e+r+"="+n+"\n"},""):String(n.body);return{path:r,submission:{formMethod:u,formAction:c,formEncType:n.formEncType,formData:void 0,json:void 0,text:e}}}else if("application/json"===n.formEncType){if(!eP(u))return l();try{let e="string"==typeof n.body?JSON.parse(n.body):n.body;return{path:r,submission:{formMethod:u,formAction:c,formEncType:n.formEncType,formData:void 0,json:e,text:void 0}}}catch(e){return l()}}}if(d("function"==typeof FormData,"FormData is not available in this environment"),n.formData)o=ed(n.formData),i=n.formData;else if(n.body instanceof FormData)o=ed(n.body),i=n.body;else if(n.body instanceof URLSearchParams)i=eh(o=n.body);else if(null==n.body)o=new URLSearchParams,i=new FormData;else try{o=new URLSearchParams(n.body),i=eh(o)}catch(e){return l()}let h={formMethod:u,formAction:c,formEncType:n&&n.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(eP(h.formMethod))return{path:r,submission:h};let f=v(r);return t&&f.search&&eM(f.search)&&o.append("index",""),f.search="?"+o,{path:m(f),submission:h}}function K(e,t,r){void 0===r&&(r=!1);let n=e.findIndex(e=>e.route.id===t);return n>=0?e.slice(0,r?n+1:n):e}function Z(e,t,r,n,a,o,i,s,u,c,d,h,f,p,m,v){let y=v?eS(v[1])?v[1].error:v[1].data:void 0,g=e.createURL(t.location),w=e.createURL(a),E=r;o&&t.errors?E=K(r,Object.keys(t.errors)[0],!0):v&&eS(v[1])&&(E=K(r,v[0]));let S=v?v[1].statusCode:void 0,R=i&&S&&S>=400,x=E.filter((e,r)=>{var a,i,c;let d,h,{route:f}=e;if(f.lazy)return!0;if(null==f.loader)return!1;if(o)return ee(f,t.loaderData,t.errors);if(a=t.loaderData,i=t.matches[r],c=e,d=!i||c.route.id!==i.route.id,h=void 0===a[c.route.id],d||h||u.some(t=>t===e.route.id))return!0;let p=t.matches[r];return er(e,l({currentUrl:g,currentParams:p.params,nextUrl:w,nextParams:e.params},n,{actionResult:y,actionStatus:S,defaultShouldRevalidate:!R&&(s||g.pathname+g.search===w.pathname+w.search||g.search!==w.search||et(p,e))}))}),D=[];return h.forEach((e,a)=>{if(o||!r.some(t=>t.route.id===e.routeId)||d.has(a))return;let i=b(p,e.path,m);if(!i)return void D.push({key:a,routeId:e.routeId,path:e.path,matches:null,match:null,controller:null});let u=t.fetchers.get(a),h=eT(i,e.path),v=!1;f.has(a)?v=!1:c.has(a)?(c.delete(a),v=!0):v=u&&"idle"!==u.state&&void 0===u.data?s:er(h,l({currentUrl:g,currentParams:t.matches[t.matches.length-1].params,nextUrl:w,nextParams:r[r.length-1].params},n,{actionResult:y,actionStatus:S,defaultShouldRevalidate:!R&&s})),v&&D.push({key:a,routeId:e.routeId,path:e.path,matches:i,match:h,controller:new AbortController})}),[x,D]}function ee(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=null!=t&&void 0!==t[e.id],a=null!=r&&void 0!==r[e.id];return(!!n||!a)&&("function"==typeof e.loader&&!0===e.loader.hydrate||!n&&!a)}function et(e,t){let r=e.route.path;return e.pathname!==t.pathname||null!=r&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function er(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if("boolean"==typeof r)return r}return t.defaultShouldRevalidate}function en(e,t,r,n,a){var o;let i;if(e){let t=n[e];d(t,"No route found to patch children into: routeId = "+e),t.children||(t.children=[]),i=t.children}else i=r;let l=w(t.filter(e=>!i.some(t=>(function e(t,r){return"id"in t&&"id"in r&&t.id===r.id||t.index===r.index&&t.path===r.path&&t.caseSensitive===r.caseSensitive&&((!t.children||0===t.children.length)&&(!r.children||0===r.children.length)||t.children.every((t,n)=>{var a;return null==(a=r.children)?void 0:a.some(r=>e(t,r))}))})(e,t))),a,[e||"_","patch",String((null==(o=i)?void 0:o.length)||"0")],n);i.push(...l)}async function ea(e,t,r){if(!e.lazy)return;let n=await e.lazy();if(!e.lazy)return;let a=r[e.id];d(a,"No route found in manifest");let o={};for(let e in n){let t=void 0!==a[e]&&"hasErrorBoundary"!==e;h(!t,'Route "'+a.id+'" has a static property "'+e+'" defined but its lazy function is also returning a value for this property. The lazy route property "'+e+'" will be ignored.'),t||g.has(e)||(o[e]=n[e])}Object.assign(a,o),Object.assign(a,l({},t(a),{lazy:void 0}))}async function eo(e){let{matches:t}=e,r=t.filter(e=>e.shouldLoad);return(await Promise.all(r.map(e=>e.resolve()))).reduce((e,t,n)=>Object.assign(e,{[r[n].route.id]:t}),{})}async function ei(e,t,r,n,o,i,s,u,c,d){let h=i.map(e=>e.route.lazy?ea(e.route,c,u):void 0),f=i.map((e,r)=>{let i=h[r],s=o.some(t=>t.route.id===e.route.id),u=async r=>(r&&"GET"===n.method&&(e.route.lazy||e.route.loader)&&(s=!0),s?el(t,n,e,i,r,d):Promise.resolve({type:a.data,result:void 0}));return l({},e,{shouldLoad:s,resolve:u})}),p=await e({matches:f,request:n,params:i[0].params,fetcherKey:s,context:d});try{await Promise.all(h)}catch(e){}return p}async function el(e,t,r,n,o,i){let l,s,u=n=>{let a,l=new Promise((e,t)=>a=t);s=()=>a(),t.signal.addEventListener("abort",s);let u=a=>"function"!=typeof n?Promise.reject(Error("You cannot call the handler for a route which defines a boolean "+('"'+e+'" [routeId: ')+r.route.id+"]")):n({request:t,params:r.params,context:i},...void 0!==a?[a]:[]);return Promise.race([(async()=>{try{let e=await (o?o(e=>u(e)):u());return{type:"data",result:e}}catch(e){return{type:"error",result:e}}})(),l])};try{let o=r.route[e];if(n)if(o){let e,[t]=await Promise.all([u(o).catch(t=>{e=t}),n]);if(void 0!==e)throw e;l=t}else if(await n,o=r.route[e])l=await u(o);else{if("action"!==e)return{type:a.data,result:void 0};let n=new URL(t.url),o=n.pathname+n.search;throw eg(405,{method:t.method,pathname:o,routeId:r.route.id})}else if(o)l=await u(o);else{let e=new URL(t.url),r=e.pathname+e.search;throw eg(404,{pathname:r})}d(void 0!==l.result,"You defined "+("action"===e?"an action":"a loader")+" for route "+('"'+r.route.id+"\" but didn't return anything from your `")+e+"` function. Please return a value or `null`.")}catch(e){return{type:a.error,result:e}}finally{s&&t.signal.removeEventListener("abort",s)}return l}async function es(e){var t,r,n,o,i,l,s;let{result:u,type:c}=e;if(eD(u)){let e;try{let t=u.headers.get("Content-Type");e=t&&/\bapplication\/json\b/.test(t)?null==u.body?null:await u.json():await u.text()}catch(e){return{type:a.error,error:e}}return c===a.error?{type:a.error,error:new O(u.status,u.statusText,e),statusCode:u.status,headers:u.headers}:{type:a.data,data:e,statusCode:u.status,headers:u.headers}}if(c===a.error){if(ex(u)){if(u.data instanceof Error)return{type:a.error,error:u.data,statusCode:null==(r=u.init)?void 0:r.status};u=new O((null==(t=u.init)?void 0:t.status)||500,void 0,u.data)}return{type:a.error,error:u,statusCode:F(u)?u.status:void 0}}return(s=u)&&"object"==typeof s&&"object"==typeof s.data&&"function"==typeof s.subscribe&&"function"==typeof s.cancel&&"function"==typeof s.resolveData?{type:a.deferred,deferredData:u,statusCode:null==(n=u.init)?void 0:n.status,headers:(null==(o=u.init)?void 0:o.headers)&&new Headers(u.init.headers)}:ex(u)?{type:a.data,data:u.data,statusCode:null==(i=u.init)?void 0:i.status,headers:null!=(l=u.init)&&l.headers?new Headers(u.init.headers):void 0}:{type:a.data,data:u}}function eu(e,t,r){if(V.test(e)){let n=new URL(e.startsWith("//")?t.protocol+e:e),a=null!=P(n.pathname,r);if(n.origin===t.origin&&a)return n.pathname+n.search+n.hash}return e}function ec(e,t,r,n){let a=e.createURL(eb(t)).toString(),o={signal:r};if(n&&eP(n.formMethod)){let{formMethod:e,formEncType:t}=n;o.method=e.toUpperCase(),"application/json"===t?(o.headers=new Headers({"Content-Type":t}),o.body=JSON.stringify(n.json)):"text/plain"===t?o.body=n.text:"application/x-www-form-urlencoded"===t&&n.formData?o.body=ed(n.formData):o.body=n.formData}return new Request(a,o)}function ed(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,"string"==typeof n?n:n.name);return t}function eh(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function ef(e,t,r,n,a,o,i){let s,u,c,h,f,p,{loaderData:m,errors:v}=(u={},c=null,h=!1,f={},p=n&&eS(n[1])?n[1].error:void 0,t.forEach(e=>{if(!(e.route.id in r))return;let n=e.route.id,a=r[n];if(d(!eR(a),"Cannot handle redirect results in processLoaderData"),eS(a)){let e=a.error;void 0!==p&&(e=p,p=void 0),c=c||{},1;{let r=ev(t,n);null==c[r.route.id]&&(c[r.route.id]=e)}u[n]=void 0,h||(h=!0,s=F(a.error)?a.error.status:500),a.headers&&(f[n]=a.headers)}else eE(a)?(i.set(n,a.deferredData),u[n]=a.deferredData.data,null==a.statusCode||200===a.statusCode||h||(s=a.statusCode)):(u[n]=a.data,a.statusCode&&200!==a.statusCode&&!h&&(s=a.statusCode)),a.headers&&(f[n]=a.headers)}),void 0!==p&&n&&(c={[n[0]]:p},u[n[0]]=void 0),{loaderData:u,errors:c,statusCode:s||200,loaderHeaders:f});return a.forEach(t=>{let{key:r,match:n,controller:a}=t,i=o[r];if(d(i,"Did not find corresponding fetcher result"),!a||!a.signal.aborted)if(eS(i)){let t=ev(e.matches,null==n?void 0:n.route.id);v&&v[t.route.id]||(v=l({},v,{[t.route.id]:i.error})),e.fetchers.delete(r)}else if(eR(i))d(!1,"Unhandled fetcher revalidation redirect");else if(eE(i))d(!1,"Unhandled fetcher deferred data");else{let t=e_(i.data);e.fetchers.set(r,t)}}),{loaderData:m,errors:v}}function ep(e,t,r,n){let a=l({},t);for(let o of r){let r=o.route.id;if(t.hasOwnProperty(r)?void 0!==t[r]&&(a[r]=t[r]):void 0!==e[r]&&o.route.loader&&(a[r]=e[r]),n&&n.hasOwnProperty(r))break}return a}function em(e){return e?eS(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function ev(e,t){return(t?e.slice(0,e.findIndex(e=>e.route.id===t)+1):[...e]).reverse().find(e=>!0===e.route.hasErrorBoundary)||e[0]}function ey(e){let t=1===e.length?e[0]:e.find(e=>e.index||!e.path||"/"===e.path)||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function eg(e,t){let{pathname:r,routeId:n,method:a,type:o,message:i}=void 0===t?{}:t,l="Unknown Server Error",s="Unknown @remix-run/router error";return 400===e?(l="Bad Request",a&&r&&n?s="You made a "+a+' request to "'+r+'" but did not provide a `loader` for route "'+n+'", so there is no way to handle the request.':"defer-action"===o?s="defer() is not supported in actions":"invalid-body"===o&&(s="Unable to encode submission body")):403===e?(l="Forbidden",s='Route "'+n+'" does not match URL "'+r+'"'):404===e?(l="Not Found",s='No route matches URL "'+r+'"'):405===e&&(l="Method Not Allowed",a&&r&&n?s="You made a "+a.toUpperCase()+' request to "'+r+'" but did not provide an `action` for route "'+n+'", so there is no way to handle the request.':a&&(s='Invalid request method "'+a.toUpperCase()+'"')),new O(e||500,l,Error(s),!0)}function ew(e){let t=Object.entries(e);for(let e=t.length-1;e>=0;e--){let[r,n]=t[e];if(eR(n))return{key:r,result:n}}}function eb(e){let t="string"==typeof e?v(e):e;return m(l({},t,{hash:""}))}function eE(e){return e.type===a.deferred}function eS(e){return e.type===a.error}function eR(e){return(e&&e.type)===a.redirect}function ex(e){return"object"==typeof e&&null!=e&&"type"in e&&"data"in e&&"init"in e&&"DataWithResponseInit"===e.type}function eD(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"object"==typeof e.headers&&void 0!==e.body}function eP(e){return z.has(e.toLowerCase())}async function eC(e,t,r,n,a){let o=Object.entries(t);for(let i=0;i<o.length;i++){let[l,s]=o[i],u=e.find(e=>(null==e?void 0:e.route.id)===l);if(!u)continue;let c=n.find(e=>e.route.id===u.route.id),d=null!=c&&!et(c,u)&&(a&&a[u.route.id])!==void 0;eE(s)&&d&&await eL(s,r,!1).then(e=>{e&&(t[l]=e)})}}async function ek(e,t,r){for(let n=0;n<r.length;n++){let{key:a,routeId:o,controller:i}=r[n],l=t[a];e.find(e=>(null==e?void 0:e.route.id)===o)&&eE(l)&&(d(i,"Expected an AbortController for revalidating fetcher deferred result"),await eL(l,i.signal,!0).then(e=>{e&&(t[a]=e)}))}}async function eL(e,t,r){if(void 0===r&&(r=!1),!await e.deferredData.resolveData(t)){if(r)try{return{type:a.data,data:e.deferredData.unwrappedData}}catch(e){return{type:a.error,error:e}}return{type:a.data,data:e.deferredData.data}}}function eM(e){return new URLSearchParams(e).getAll("index").some(e=>""===e)}function eT(e,t){let r="string"==typeof t?v(t).search:t.search;if(e[e.length-1].route.index&&eM(r||""))return e[e.length-1];let n=k(e);return n[n.length-1]}function ej(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:o,json:i}=e;if(t&&r&&n){if(null!=a)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};else if(null!=o)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};else if(void 0!==i)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:i,text:void 0}}}function eU(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function eA(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function e_(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}Symbol("deferred")},32053:function(e,t,r){r.d(t,{aj:()=>m,cP:()=>v,lr:()=>P,pG:()=>x});var n,a,o,i,l,s,u=r(52983),c=r(63730),d=r(83696),h=r(50790);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function p(e){return void 0===e&&(e=""),new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let n=e[r];return t.concat(Array.isArray(n)?n.map(e=>[r,e]):[[r,n]])},[]))}try{window.__reactRouterVersion="6"}catch(e){}function m(e,t){return(0,h.p7)({basename:null==t?void 0:t.basename,future:f({},null==t?void 0:t.future,{v7_prependBasename:!0}),history:(0,h.lX)({window:null==t?void 0:t.window}),hydrationData:(null==t?void 0:t.hydrationData)||y(),routes:e,mapRouteProperties:d.us,dataStrategy:null==t?void 0:t.dataStrategy,patchRoutesOnNavigation:null==t?void 0:t.patchRoutesOnNavigation,window:null==t?void 0:t.window}).initialize()}function v(e,t){return(0,h.p7)({basename:null==t?void 0:t.basename,future:f({},null==t?void 0:t.future,{v7_prependBasename:!0}),history:(0,h.q_)({window:null==t?void 0:t.window}),hydrationData:(null==t?void 0:t.hydrationData)||y(),routes:e,mapRouteProperties:d.us,dataStrategy:null==t?void 0:t.dataStrategy,patchRoutesOnNavigation:null==t?void 0:t.patchRoutesOnNavigation,window:null==t?void 0:t.window}).initialize()}function y(){var e;let t=null==(e=window)?void 0:e.__staticRouterHydrationData;return t&&t.errors&&(t=f({},t,{errors:function(e){if(!e)return null;let t=Object.entries(e),r={};for(let[e,n]of t)if(n&&"RouteErrorResponse"===n.__type)r[e]=new h.OF(n.status,n.statusText,n.data,!0===n.internal);else if(n&&"Error"===n.__type){if(n.__subType){let t=window[n.__subType];if("function"==typeof t)try{let a=new t(n.message);a.stack="",r[e]=a}catch(e){}}if(null==r[e]){let t=Error(n.message);t.stack="",r[e]=t}}else r[e]=n;return r}(t.errors)})),t}let g=u.createContext({isTransitioning:!1}),w=u.createContext(new Map),b=(o||(o=r.t(u,2))).startTransition,E=(i||(i=r.t(c,2))).flushSync;function S(e){E?E(e):e()}(o||(o=r.t(u,2))).useId;class R{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=t=>{"pending"===this.status&&(this.status="resolved",e(t))},this.reject=e=>{"pending"===this.status&&(this.status="rejected",t(e))}})}}function x(e){let{fallbackElement:t,router:r,future:n}=e,[a,o]=u.useState(r.state),[i,l]=u.useState(),[s,c]=u.useState({isTransitioning:!1}),[h,f]=u.useState(),[p,m]=u.useState(),[v,y]=u.useState(),E=u.useRef(new Map),{v7_startTransition:x}=n||{},P=u.useCallback(e=>{if(x)b?b(e):e();else e()},[x]),C=u.useCallback((e,t)=>{let{deletedFetchers:n,flushSync:a,viewTransitionOpts:i}=t;n.forEach(e=>E.current.delete(e)),e.fetchers.forEach((e,t)=>{void 0!==e.data&&E.current.set(t,e.data)});let s=null==r.window||null==r.window.document||"function"!=typeof r.window.document.startViewTransition;if(!i||s)return void(a?S(()=>o(e)):P(()=>o(e)));if(a){S(()=>{p&&(h&&h.resolve(),p.skipTransition()),c({isTransitioning:!0,flushSync:!0,currentLocation:i.currentLocation,nextLocation:i.nextLocation})});let t=r.window.document.startViewTransition(()=>{S(()=>o(e))});t.finished.finally(()=>{S(()=>{f(void 0),m(void 0),l(void 0),c({isTransitioning:!1})})}),S(()=>m(t));return}p?(h&&h.resolve(),p.skipTransition(),y({state:e,currentLocation:i.currentLocation,nextLocation:i.nextLocation})):(l(e),c({isTransitioning:!0,flushSync:!1,currentLocation:i.currentLocation,nextLocation:i.nextLocation}))},[r.window,p,h,E,P]);u.useLayoutEffect(()=>r.subscribe(C),[r,C]),u.useEffect(()=>{s.isTransitioning&&!s.flushSync&&f(new R)},[s]),u.useEffect(()=>{if(h&&i&&r.window){let e=h.promise,t=r.window.document.startViewTransition(async()=>{P(()=>o(i)),await e});t.finished.finally(()=>{f(void 0),m(void 0),l(void 0),c({isTransitioning:!1})}),m(t)}},[P,i,h,r.window]),u.useEffect(()=>{h&&i&&a.location.key===i.location.key&&h.resolve()},[h,p,a.location,i]),u.useEffect(()=>{!s.isTransitioning&&v&&(l(v.state),c({isTransitioning:!0,flushSync:!1,currentLocation:v.currentLocation,nextLocation:v.nextLocation}),y(void 0))},[s.isTransitioning,v]),u.useEffect(()=>{},[]);let k=u.useMemo(()=>({createHref:r.createHref,encodeLocation:r.encodeLocation,go:e=>r.navigate(e),push:(e,t,n)=>r.navigate(e,{state:t,preventScrollReset:null==n?void 0:n.preventScrollReset}),replace:(e,t,n)=>r.navigate(e,{replace:!0,state:t,preventScrollReset:null==n?void 0:n.preventScrollReset})}),[r]),L=r.basename||"/",M=u.useMemo(()=>({router:r,navigator:k,static:!1,basename:L}),[r,k,L]),T=u.useMemo(()=>({v7_relativeSplatPath:r.future.v7_relativeSplatPath}),[r.future.v7_relativeSplatPath]);return u.createElement(u.Fragment,null,u.createElement(d.w3.Provider,{value:M},u.createElement(d.FR.Provider,{value:a},u.createElement(w.Provider,{value:E.current},u.createElement(g.Provider,{value:s},u.createElement(d.F0,{basename:L,location:a.location,navigationType:a.historyAction,navigator:k,future:T},a.initialized||r.future.v7_partialHydration?u.createElement(D,{routes:r.routes,future:r.future,state:a}):t))))),null)}let D=u.memo(function(e){let{routes:t,future:r,state:n}=e;return(0,d.DY)(t,void 0,n,r)});function P(e){let t=u.useRef(p(e)),r=u.useRef(!1),n=(0,d.TH)(),a=u.useMemo(()=>{var e,a;let o;return e=n.search,a=r.current?null:t.current,o=p(e),a&&a.forEach((e,t)=>{o.has(t)||a.getAll(t).forEach(e=>{o.append(t,e)})}),o},[n.search]),o=(0,d.s0)(),i=u.useCallback((e,t)=>{let n=p("function"==typeof e?e(a):e);r.current=!0,o("?"+n,t)},[o,a]);return[a,i]}"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement,(n=l||(l={})).UseScrollRestoration="useScrollRestoration",n.UseSubmit="useSubmit",n.UseSubmitFetcher="useSubmitFetcher",n.UseFetcher="useFetcher",n.useViewTransitionState="useViewTransitionState",(a=s||(s={})).UseFetcher="useFetcher",a.UseFetchers="useFetchers",a.UseScrollRestoration="useScrollRestoration"},83696:function(e,t,r){r.d(t,{AW:()=>U,DY:()=>S,F0:()=>A,FR:()=>d,SN:()=>M,TH:()=>g,i7:()=>function e(t,r){void 0===r&&(r=[]);let n=[];return l.Children.forEach(t,(t,a)=>{if(!l.isValidElement(t))return;let o=[...r,a];if(t.type===l.Fragment)return void n.push.apply(n,e(t.props.children,o));t.type!==U&&(0,s.J0)(!1),t.props.index&&t.props.children&&(0,s.J0)(!1);let i={id:t.props.id||o.join("-"),caseSensitive:t.props.caseSensitive,element:t.props.element,Component:t.props.Component,index:t.props.index,path:t.props.path,loader:t.props.loader,action:t.props.action,errorElement:t.props.errorElement,ErrorBoundary:t.props.ErrorBoundary,hasErrorBoundary:null!=t.props.ErrorBoundary||null!=t.props.errorElement,shouldRevalidate:t.props.shouldRevalidate,handle:t.props.handle,lazy:t.props.lazy};t.props.children&&(i.children=e(t.props.children,o)),n.push(i)}),n},j3:()=>j,oQ:()=>v,s0:()=>b,us:()=>O,w3:()=>c});var n,a,o,i,l=r(52983),s=r(50790);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}let c=l.createContext(null),d=l.createContext(null),h=l.createContext(null),f=l.createContext(null),p=l.createContext({outlet:null,matches:[],isDataRoute:!1}),m=l.createContext(null);function v(e,t){let{relative:r}=void 0===t?{}:t;y()||(0,s.J0)(!1);let{basename:n,navigator:a}=l.useContext(h),{hash:o,pathname:i,search:u}=function(e,t){let{relative:r}=void 0===t?{}:t,{future:n}=l.useContext(h),{matches:a}=l.useContext(p),{pathname:o}=g(),i=JSON.stringify((0,s.cm)(a,n.v7_relativeSplatPath));return l.useMemo(()=>(0,s.pC)(e,JSON.parse(i),o,"path"===r),[e,i,o,r])}(e,{relative:r}),c=i;return"/"!==n&&(c="/"===i?n:(0,s.RQ)([n,i])),a.createHref({pathname:c,search:u,hash:o})}function y(){return null!=l.useContext(f)}function g(){return y()||(0,s.J0)(!1),l.useContext(f).location}function w(e){l.useContext(h).static||l.useLayoutEffect(e)}function b(){let{isDataRoute:e}=l.useContext(p);return e?function(){var e;let t,{router:r}=(P.UseNavigateStable,(t=l.useContext(c))||(0,s.J0)(!1),t),n=L(C.UseNavigateStable),a=l.useRef(!1);return w(()=>{a.current=!0}),l.useCallback(function(e,t){void 0===t&&(t={}),a.current&&("number"==typeof e?r.navigate(e):r.navigate(e,u({fromRouteId:n},t)))},[r,n])}():function(){y()||(0,s.J0)(!1);let e=l.useContext(c),{basename:t,future:r,navigator:n}=l.useContext(h),{matches:a}=l.useContext(p),{pathname:o}=g(),i=JSON.stringify((0,s.cm)(a,r.v7_relativeSplatPath)),u=l.useRef(!1);return w(()=>{u.current=!0}),l.useCallback(function(r,a){if(void 0===a&&(a={}),!u.current)return;if("number"==typeof r)return void n.go(r);let l=(0,s.pC)(r,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:(0,s.RQ)([t,l.pathname])),(a.replace?n.replace:n.push)(l,a.state,a)},[t,n,i,o,e])}()}let E=l.createContext(null);function S(e,t,r,n){let a;y()||(0,s.J0)(!1);let{navigator:o}=l.useContext(h),{matches:i}=l.useContext(p),c=i[i.length-1],d=c?c.params:{};c&&c.pathname;let m=c?c.pathnameBase:"/";c&&c.route;let v=g();if(t){var w;let e="string"==typeof t?(0,s.cP)(t):t;"/"===m||(null==(w=e.pathname)?void 0:w.startsWith(m))||(0,s.J0)(!1),a=e}else a=v;let b=a.pathname||"/",E=b;if("/"!==m){let e=m.replace(/^\//,"").split("/");E="/"+b.replace(/^\//,"").split("/").slice(e.length).join("/")}let S=(0,s.fp)(e,{pathname:E}),P=function(e,t,r,n){var a,o;if(void 0===t&&(t=[]),void 0===r&&(r=null),void 0===n&&(n=null),null==e){if(!r)return null;if(r.errors)e=r.matches;else{if(null==(o=n)||!o.v7_partialHydration||0!==t.length||r.initialized||!(r.matches.length>0))return null;e=r.matches}}let i=e,u=null==(a=r)?void 0:a.errors;if(null!=u){let e=i.findIndex(e=>e.route.id&&(null==u?void 0:u[e.route.id])!==void 0);e>=0||(0,s.J0)(!1),i=i.slice(0,Math.min(i.length,e+1))}let c=!1,d=-1;if(r&&n&&n.v7_partialHydration)for(let e=0;e<i.length;e++){let t=i[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(d=e),t.route.id){let{loaderData:e,errors:n}=r,a=t.route.loader&&void 0===e[t.route.id]&&(!n||void 0===n[t.route.id]);if(t.route.lazy||a){c=!0,i=d>=0?i.slice(0,d+1):[i[0]];break}}}return i.reduceRight((e,n,a)=>{var o,s;let h,f=!1,p=null,m=null;r&&(h=u&&n.route.id?u[n.route.id]:void 0,p=n.route.errorElement||R,c&&(d<0&&0===a?(o="route-fallback",s=0,T[o]||(T[o]=!0),f=!0,m=null):d===a&&(f=!0,m=n.route.hydrateFallbackElement||null)));let v=t.concat(i.slice(0,a+1)),y=()=>{let t;return t=h?p:f?m:n.route.Component?l.createElement(n.route.Component,null):n.route.element?n.route.element:e,l.createElement(D,{match:n,routeContext:{outlet:e,matches:v,isDataRoute:null!=r},children:t})};return r&&(n.route.ErrorBoundary||n.route.errorElement||0===a)?l.createElement(x,{location:r.location,revalidation:r.revalidation,component:p,error:h,children:y(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):y()},null)}(S&&S.map(e=>Object.assign({},e,{params:Object.assign({},d,e.params),pathname:(0,s.RQ)([m,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?m:(0,s.RQ)([m,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,r,n);return t&&P?l.createElement(f.Provider,{value:{location:u({pathname:"/",search:"",hash:"",state:null,key:"default"},a),navigationType:s.aU.Pop}},P):P}let R=l.createElement(function(){var e;let t,r,n,a=(t=l.useContext(m),r=k(C.UseRouteError),n=L(C.UseRouteError),void 0!==t?t:null==(e=r.errors)?void 0:e[n]),o=(0,s.WK)(a)?a.status+" "+a.statusText:a instanceof Error?a.message:JSON.stringify(a),i=a instanceof Error?a.stack:null;return l.createElement(l.Fragment,null,l.createElement("h2",null,"Unexpected Application Error!"),l.createElement("h3",{style:{fontStyle:"italic"}},o),i?l.createElement("pre",{style:{padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"}},i):null,null)},null);class x extends l.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?l.createElement(p.Provider,{value:this.props.routeContext},l.createElement(m.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function D(e){let{routeContext:t,match:r,children:n}=e,a=l.useContext(c);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),l.createElement(p.Provider,{value:t},n)}var P=((n=P||{}).UseBlocker="useBlocker",n.UseRevalidator="useRevalidator",n.UseNavigateStable="useNavigate",n),C=((a=C||{}).UseBlocker="useBlocker",a.UseLoaderData="useLoaderData",a.UseActionData="useActionData",a.UseRouteError="useRouteError",a.UseNavigation="useNavigation",a.UseRouteLoaderData="useRouteLoaderData",a.UseMatches="useMatches",a.UseRevalidator="useRevalidator",a.UseNavigateStable="useNavigate",a.UseRouteId="useRouteId",a);function k(e){let t=l.useContext(d);return t||(0,s.J0)(!1),t}function L(e){let t,r=((t=l.useContext(p))||(0,s.J0)(!1),t),n=r.matches[r.matches.length-1];return n.route.id||(0,s.J0)(!1),n.route.id}function M(){let{matches:e,loaderData:t}=k(C.UseMatches);return l.useMemo(()=>e.map(e=>(0,s.WS)(e,t)),[e,t])}let T={};function j(e){var t;let r;return t=e.context,(r=l.useContext(p).outlet)?l.createElement(E.Provider,{value:t},r):r}function U(e){(0,s.J0)(!1)}function A(e){let{basename:t="/",children:r=null,location:n,navigationType:a=s.aU.Pop,navigator:o,static:i=!1,future:c}=e;y()&&(0,s.J0)(!1);let d=t.replace(/^\/*/,"/"),p=l.useMemo(()=>({basename:d,navigator:o,static:i,future:u({v7_relativeSplatPath:!1},c)}),[d,c,o,i]);"string"==typeof n&&(n=(0,s.cP)(n));let{pathname:m="/",search:v="",hash:g="",state:w=null,key:b="default"}=n,E=l.useMemo(()=>{let e=(0,s.Zn)(m,d);return null==e?null:{location:{pathname:e,search:v,hash:g,state:w,key:b},navigationType:a}},[d,m,v,g,w,b,a]);return null==E?null:l.createElement(h.Provider,{value:p},l.createElement(f.Provider,{children:r,value:E}))}(i||(i=r.t(l,2))).startTransition;var _=((o=_||{})[o.pending=0]="pending",o[o.success=1]="success",o[o.error=2]="error",o);function O(e){let t={hasErrorBoundary:null!=e.ErrorBoundary||null!=e.errorElement};return e.Component&&Object.assign(t,{element:l.createElement(e.Component),Component:void 0}),e.HydrateFallback&&Object.assign(t,{hydrateFallbackElement:l.createElement(e.HydrateFallback),HydrateFallback:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:l.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}new Promise(()=>{}),l.Component}}]);