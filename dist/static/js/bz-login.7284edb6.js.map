{"version": 3, "file": "static/js/bz-login.7284edb6.js", "sources": ["webpack://web-dashboard/./node_modules/.modern-js/bz-login/runtime-global-context.js", "webpack://web-dashboard/./node_modules/.modern-js/bz-login/routes.js", "webpack://web-dashboard/./node_modules/.modern-js/bz-login/runtime-register.js", "webpack://web-dashboard/./node_modules/.modern-js/bz-login/index.jsx", "webpack://web-dashboard/./src/bz-login/routes/layout.tsx", "webpack://web-dashboard/./src/modern.runtime.ts", "webpack://web-dashboard/"], "sourcesContent": ["import { setGlobalContext } from '@modern-js/runtime/context';\nlet appConfig;\nlet appInit;\nlet layoutApp;\n\nimport { routes } from './routes';\n\nconst entryName = 'bz-login';\nsetGlobalContext({\n  entryName,\n  layoutApp,\n  routes,\n  appInit,\n  appConfig,\n});", "\n    \n    import { lazy } from \"react\";\n    import loadable, { lazy as loadableLazy } from \"@modern-js/runtime/loadable\"\n  \n    \n    \n    import { createShouldRevalidate, handleRouteModule,  handleRouteModuleError} from '@modern-js/runtime/router';\n  \n    import RootLayout from '@_modern_js_src/bz-login/routes/layout'\n    \n    \n    \n    \n    \n    if(typeof document !== 'undefined'){\n      window._routeModules = {}\n    }\n  \n    \n    export const routes = [\n  {\n  \"path\": \"/\",\n  \"children\": [\n    {\n      \"_component\": \"@_modern_js_src/bz-login/routes/page\",\n      \"index\": true,\n      \"id\": \"bz-login_page\",\n      \"type\": \"nested\",\n      \"lazyImport\": () => import(/* webpackChunkName: \"bz-login_page\" */ '@_modern_js_src/bz-login/routes/page').then(routeModule => handleRouteModule(routeModule, \"bz-login_page\")).catch(handleRouteModuleError),\n      \"component\": lazy(() => import(/* webpackChunkName: \"bz-login_page\" */ '@_modern_js_src/bz-login/routes/page').then(routeModule => handleRouteModule(routeModule, \"bz-login_page\")).catch(handleRouteModuleError))\n    }\n  ],\n  \"isRoot\": true,\n  \"_component\": \"@_modern_js_src/bz-login/routes/layout\",\n  \"id\": \"bz-login_layout\",\n  \"type\": \"nested\",\n  \"lazyImport\": () => import('@_modern_js_src/bz-login/routes/layout').then(routeModule => handleRouteModule(routeModule, \"bz-login_layout\")).catch(handleRouteModuleError),\n  \"component\": RootLayout\n},\n];\n  ", "import { registerPlugin, mergeConfig } from '@modern-js/runtime/plugin';\nimport { getGlobalAppConfig, getGlobalLayoutApp, getCurrentEntryName } from '@modern-js/runtime/context';\n\nimport modernRuntime from '@_modern_js_src/modern.runtime';\nconst runtimeConfig = typeof modernRuntime === 'function' ? modernRuntime(getCurrentEntryName()) : modernRuntime\n\nconst plugins = [];\n\nimport { routerPlugin } from '@modern-js/runtime/router';\n\nplugins.push(routerPlugin(mergeConfig({\"serverBase\":[\"/frontend/bz-login\"]}, (runtimeConfig || {})['router'], ((runtimeConfig || {})['routerByEntries'] || {})['bz-login'], (getGlobalAppConfig() || {})['router'])));\n\nregisterPlugin(plugins, runtimeConfig);\n", "import '@modern-js/runtime/registry/bz-login';\nimport { createRoot } from '@modern-js/runtime/react';\nimport { render } from '@modern-js/runtime/browser';\n\n\n\n\n\n\n\n\n\nconst ModernRoot = createRoot();\n\nrender(<ModernRoot />, 'root');\n", "import { Outlet } from '@modern-js/runtime/router';\r\n\r\nexport default function Layout() {\r\n    return (\r\n        <div>\r\n            <Outlet />\r\n        </div>\r\n    );\r\n}\r\n", "import { defineRuntimeConfig } from '@modern-js/runtime';\r\n\r\nexport default defineRuntimeConfig({});\r\n", "import \"core-js\";"], "names": ["appConfig", "appInit", "layoutApp", "document", "window", "routes", "routeModule", "handleRouteModule", "handleRouteModuleError", "lazy", "RootLayout", "setGlobalContext", "entryName", "runtimeConfig", "modernRuntime", "getCurrentEntryName", "plugins", "routerPlugin", "mergeConfig", "getGlobalAppConfig", "registerPlugin", "ModernRoot", "createRoot", "render", "Layout", "Outlet", "defineRuntimeConfig"], "mappings": "yHACIA,EACAC,EACAC,E,oDCYG,AAAoB,cAApB,OAAOC,UACRC,CAAAA,OAAO,aAAa,CAAG,CAAC,GAInB,IAAMC,EAAS,CACxB,CACA,KAAQ,IACR,SAAY,CACV,CACE,WAAc,uCACd,MAAS,GACT,GAAM,gBACN,KAAQ,SACR,WAAc,IAAM,yFAAuF,IAAI,CAACC,AAAAA,GAAeC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAkBD,EAAa,kBAAkB,KAAK,CAACE,EAAAA,EAAsBA,EAC5M,UAAaC,AAAAA,GAAAA,EAAAA,IAAAA,AAAAA,EAAK,IAAM,yFAAuF,IAAI,CAACH,AAAAA,GAAeC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAkBD,EAAa,kBAAkB,KAAK,CAACE,EAAAA,EAAsBA,EAClN,EACD,CACD,OAAU,GACV,WAAc,yCACd,GAAM,kBACN,KAAQ,SACR,WAAc,IAAM,uCAAiD,IAAI,CAACF,AAAAA,GAAeC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAkBD,EAAa,oBAAoB,KAAK,CAACE,EAAAA,EAAsBA,EACxK,UAAaE,EAAAA,OAAUA,AACzB,EACC,CDhCDC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAiB,CACfC,UAFgB,WAGhBV,UAAAA,EACAG,OAAMA,EACNJ,QAAAA,EACAD,UAAAA,CACF,G,qCEVMa,EAAgB,AAAyB,YAAzB,OAAOC,EAAAA,CAAaA,CAAkBA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAcC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,KAAyBD,EAAAA,CAAaA,CAE1GE,EAAU,EAAE,CAIlBA,EAAQ,IAAI,CAACC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAY,CAAC,WAAa,CAAC,qBAAqB,EAAIL,AAAAA,CAAAA,GAAiB,CAAC,GAAG,MAAS,CAAG,AAACA,CAAAA,CAAAA,GAAiB,CAAC,GAAG,eAAkB,EAAI,CAAC,EAAE,CAAC,WAAW,CAAGM,AAAAA,CAAAA,GAAAA,EAAAA,EAAAA,AAAAA,KAAwB,CAAC,GAAG,MAAS,IAElNC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAeJ,EAASH,G,0BCAlBQ,EAAaC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,IAEnBC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAO,UAACF,EAAAA,CAAAA,GAAe,O,+ECZR,SAASG,IACpB,MACI,UAAC,O,SACG,UAACC,EAAAA,EAAMA,CAAAA,CAAAA,E,EAGnB,C,wBCNA,IAAeC,AAAAA,GAAAA,A,SAAAA,EAAAA,AAAAA,EAAoB,CAAC,E,wrFCFpCtB,OAAO,eAAe,CAAG,W"}