{"version": 3, "file": "static/js/reply-admin.59331f0b.js", "sources": ["webpack://web-dashboard/./src/modern.runtime.ts", "webpack://web-dashboard/./src/reply-admin/routes/layout.tsx", "webpack://web-dashboard/"], "sourcesContent": ["import { defineRuntimeConfig } from '@modern-js/runtime';\r\n\r\nexport default defineRuntimeConfig({});\r\n", "import { useState } from 'react'\r\nimport { Outlet, useNavigate } from '@modern-js/runtime/router';\r\nimport { ProLayout, } from '@ant-design/pro-components';\r\nimport './index.css';\r\nimport logo from '@/reply-admin/static/imags/logo.png';\r\n\r\nexport default function Layout() {\r\n\r\n  const [pathname, setPathname] = useState<string>(document.location.pathname)\r\n\r\n  const navigate = useNavigate();\r\n\r\n  const currentPath = '/frontend/reply-admin';\r\n\r\n  const handleMenuClick = (path: string) => {\r\n    navigate(path.replace(currentPath, ''));\r\n    setPathname(path)\r\n  }\r\n\r\n\r\n  return (\r\n    <div\r\n      id=\"test-pro-layout\"\r\n      style={{\r\n        height: '100vh',\r\n      }}\r\n    >\r\n      <ProLayout\r\n        title=\"妙真谷AI自动回复\"\r\n        logo={logo}\r\n        siderWidth={216}\r\n        location={{ pathname: pathname }}\r\n        route={{\r\n          path: '/',\r\n          routes: [\r\n            {\r\n              path: currentPath + '/account-admin',\r\n              name: '账号管理',\r\n            },\r\n            {\r\n              path: currentPath + '/reply-params-admin',\r\n              name: '回评论参数管理',\r\n            },\r\n            {\r\n              path: currentPath + '/reply-record-admin',\r\n              name: '回复记录管理',\r\n            }\r\n          ],\r\n        }}\r\n        menuItemRender={(item, dom) => (\r\n          <div\r\n            onClick={() => {\r\n              handleMenuClick(item.path || currentPath);\r\n            }}\r\n          >\r\n            {dom}\r\n          </div>\r\n        )}\r\n      >\r\n        <Outlet />\r\n      </ProLayout>\r\n    </div>\r\n  );\r\n};\r\n", "import \"core-js\";"], "names": ["defineRuntimeConfig", "Layout", "pathname", "setPathname", "useState", "document", "navigate", "useNavigate", "currentPath", "handleMenuClick", "path", "ProLayout", "logo", "item", "dom", "Outlet", "window"], "mappings": "sHAEA,IAAeA,AAAAA,GAAAA,A,SAAAA,EAAAA,AAAAA,EAAoB,CAAC,E,4ICIrB,SAASC,IAEtB,GAAM,CAACC,EAAUC,EAAY,CAAGC,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAiBC,SAAS,QAAQ,CAAC,QAAQ,EAErEC,EAAWC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAEXC,EAAc,wBAEdC,EAAkB,AAACC,IACvBJ,EAASI,EAAK,OAAO,CAACF,EAAa,KACnCL,EAAYO,EACd,EAGA,MACE,UAAC,OACC,GAAG,kBACH,MAAO,CACL,OAAQ,OACV,E,SAEA,UAACC,EAAAA,CAASA,CAAAA,CACR,MAAM,+CACN,KAAMC,EACN,WAAY,IACZ,SAAU,CAAE,SAAUV,CAAS,EAC/B,MAAO,CACL,KAAM,IACN,OAAQ,CACN,CACE,KAAMM,EAAc,iBACpB,KAAM,0BACR,EACA,CACE,KAAMA,EAAc,sBACpB,KAAM,4CACR,EACA,CACE,KAAMA,EAAc,sBACpB,KAAM,sCACR,EACD,AACH,EACA,eAAgB,CAACK,EAAMC,IACrB,UAAC,OACC,QAAS,KACPL,EAAgBI,EAAK,IAAI,EAAIL,EAC/B,E,SAECM,C,YAIL,UAACC,EAAAA,EAAMA,CAAAA,CAAAA,E,IAIf,C,wrFC/DAC,OAAO,eAAe,CAAG,W"}