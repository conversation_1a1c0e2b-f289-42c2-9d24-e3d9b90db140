{"version": 3, "file": "static/js/async/reply-admin_reply-params-admin/page.22709960.js", "sources": ["webpack://web-dashboard/../../node_modules/.pnpm/@ant-design+pro-form@2.31.7_2adea46d42129a2ef318a7e4099cdea4/node_modules/@ant-design/pro-form/es/components/Select/index.js", "webpack://web-dashboard/../../node_modules/.pnpm/@ant-design+pro-form@2.31.7_2adea46d42129a2ef318a7e4099cdea4/node_modules/@ant-design/pro-form/es/components/Text/index.js", "webpack://web-dashboard/../../node_modules/.pnpm/@ant-design+pro-form@2.31.7_2adea46d42129a2ef318a7e4099cdea4/node_modules/@ant-design/pro-form/es/components/TextArea/index.js", "webpack://web-dashboard/./src/reply-admin/components/ReplyParamsModalForm/index.tsx", "webpack://web-dashboard/./src/reply-admin/containers/ReplyParamsAdmin.tsx", "webpack://web-dashboard/./src/reply-admin/routes/reply-params-admin/page.tsx"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fieldProps\", \"children\", \"params\", \"proFieldProps\", \"mode\", \"valueEnum\", \"request\", \"showSearch\", \"options\"],\n  _excluded2 = [\"fieldProps\", \"children\", \"params\", \"proFieldProps\", \"mode\", \"valueEnum\", \"request\", \"options\"];\nimport { runFunction } from '@ant-design/pro-utils';\nimport React, { useContext } from 'react';\nimport FieldContext from \"../../FieldContext\";\nimport ProFormField from \"../Field\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * 选择框\n *\n * @param\n */\nvar ProFormSelectComponents = function ProFormSelectComponents(_ref, ref) {\n  var fieldProps = _ref.fieldProps,\n    children = _ref.children,\n    params = _ref.params,\n    proFieldProps = _ref.proFieldProps,\n    mode = _ref.mode,\n    valueEnum = _ref.valueEnum,\n    request = _ref.request,\n    showSearch = _ref.showSearch,\n    options = _ref.options,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var context = useContext(FieldContext);\n  return /*#__PURE__*/_jsx(ProFormField, _objectSpread(_objectSpread({\n    valueEnum: runFunction(valueEnum),\n    request: request,\n    params: params,\n    valueType: \"select\",\n    filedConfig: {\n      customLightMode: true\n    },\n    fieldProps: _objectSpread({\n      options: options,\n      mode: mode,\n      showSearch: showSearch,\n      getPopupContainer: context.getPopupContainer\n    }, fieldProps),\n    ref: ref,\n    proFieldProps: proFieldProps\n  }, rest), {}, {\n    children: children\n  }));\n};\nvar SearchSelect = /*#__PURE__*/React.forwardRef(function (_ref2, ref) {\n  var fieldProps = _ref2.fieldProps,\n    children = _ref2.children,\n    params = _ref2.params,\n    proFieldProps = _ref2.proFieldProps,\n    mode = _ref2.mode,\n    valueEnum = _ref2.valueEnum,\n    request = _ref2.request,\n    options = _ref2.options,\n    rest = _objectWithoutProperties(_ref2, _excluded2);\n  var props = _objectSpread({\n    options: options,\n    mode: mode || 'multiple',\n    labelInValue: true,\n    showSearch: true,\n    suffixIcon: null,\n    autoClearSearchValue: true,\n    optionLabelProp: 'label'\n  }, fieldProps);\n  var context = useContext(FieldContext);\n  return /*#__PURE__*/_jsx(ProFormField, _objectSpread(_objectSpread({\n    valueEnum: runFunction(valueEnum),\n    request: request,\n    params: params,\n    valueType: \"select\",\n    filedConfig: {\n      customLightMode: true\n    },\n    fieldProps: _objectSpread({\n      getPopupContainer: context.getPopupContainer\n    }, props),\n    ref: ref,\n    proFieldProps: proFieldProps\n  }, rest), {}, {\n    children: children\n  }));\n});\nvar ProFormSelect = /*#__PURE__*/React.forwardRef(ProFormSelectComponents);\nvar ProFormSearchSelect = SearchSelect;\nvar WrappedProFormSelect = ProFormSelect;\nWrappedProFormSelect.SearchSelect = ProFormSearchSelect;\n\n// @ts-ignore\n// eslint-disable-next-line no-param-reassign\nWrappedProFormSelect.displayName = 'ProFormComponent';\nexport default WrappedProFormSelect;", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fieldProps\", \"proFieldProps\"],\n  _excluded2 = [\"fieldProps\", \"proFieldProps\"];\nimport { useMountMergeState } from '@ant-design/pro-utils';\nimport { Form, Popover } from 'antd';\nimport omit from \"rc-util/es/omit\";\nimport React, { useState } from 'react';\nimport ProField from \"../Field\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar valueType = 'text';\n/**\n * 文本组件\n *\n * @param\n */\nvar ProFormText = function ProFormText(_ref) {\n  var fieldProps = _ref.fieldProps,\n    proFieldProps = _ref.proFieldProps,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/_jsx(ProField, _objectSpread({\n    valueType: valueType,\n    fieldProps: fieldProps,\n    filedConfig: {\n      valueType: valueType\n    },\n    proFieldProps: proFieldProps\n  }, rest));\n};\nvar PassWordStrength = function PassWordStrength(props) {\n  var _useMountMergeState = useMountMergeState(props.open || false, {\n      value: props.open,\n      onChange: props.onOpenChange\n    }),\n    _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2),\n    open = _useMountMergeState2[0],\n    setOpen = _useMountMergeState2[1];\n  return /*#__PURE__*/_jsx(Form.Item, {\n    shouldUpdate: true,\n    noStyle: true,\n    children: function children(form) {\n      var _props$statusRender;\n      var value = form.getFieldValue(props.name || []);\n      return /*#__PURE__*/_jsx(Popover, _objectSpread(_objectSpread({\n        getPopupContainer: function getPopupContainer(node) {\n          if (node && node.parentNode) {\n            return node.parentNode;\n          }\n          return node;\n        },\n        onOpenChange: function onOpenChange(e) {\n          return setOpen(e);\n        },\n        content: /*#__PURE__*/_jsxs(\"div\", {\n          style: {\n            padding: '4px 0'\n          },\n          children: [(_props$statusRender = props.statusRender) === null || _props$statusRender === void 0 ? void 0 : _props$statusRender.call(props, value), props.strengthText ? /*#__PURE__*/_jsx(\"div\", {\n            style: {\n              marginTop: 10\n            },\n            children: /*#__PURE__*/_jsx(\"span\", {\n              children: props.strengthText\n            })\n          }) : null]\n        }),\n        overlayStyle: {\n          width: 240\n        },\n        placement: \"rightTop\"\n      }, props.popoverProps), {}, {\n        open: open,\n        children: props.children\n      }));\n    }\n  });\n};\nvar Password = function Password(_ref2) {\n  var fieldProps = _ref2.fieldProps,\n    proFieldProps = _ref2.proFieldProps,\n    rest = _objectWithoutProperties(_ref2, _excluded2);\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  if (fieldProps !== null && fieldProps !== void 0 && fieldProps.statusRender && rest.name) {\n    return /*#__PURE__*/_jsx(PassWordStrength, {\n      name: rest.name,\n      statusRender: fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.statusRender,\n      popoverProps: fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.popoverProps,\n      strengthText: fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.strengthText,\n      open: open,\n      onOpenChange: setOpen,\n      children: /*#__PURE__*/_jsx(\"div\", {\n        children: /*#__PURE__*/_jsx(ProField, _objectSpread({\n          valueType: \"password\",\n          fieldProps: _objectSpread(_objectSpread({}, omit(fieldProps, ['statusRender', 'popoverProps', 'strengthText'])), {}, {\n            onBlur: function onBlur(e) {\n              var _fieldProps$onBlur;\n              fieldProps === null || fieldProps === void 0 || (_fieldProps$onBlur = fieldProps.onBlur) === null || _fieldProps$onBlur === void 0 || _fieldProps$onBlur.call(fieldProps, e);\n              setOpen(false);\n            },\n            onClick: function onClick(e) {\n              var _fieldProps$onClick;\n              fieldProps === null || fieldProps === void 0 || (_fieldProps$onClick = fieldProps.onClick) === null || _fieldProps$onClick === void 0 || _fieldProps$onClick.call(fieldProps, e);\n              setOpen(true);\n            }\n          }),\n          proFieldProps: proFieldProps,\n          filedConfig: {\n            valueType: valueType\n          }\n        }, rest))\n      })\n    });\n  }\n  return /*#__PURE__*/_jsx(ProField, _objectSpread({\n    valueType: \"password\",\n    fieldProps: fieldProps,\n    proFieldProps: proFieldProps,\n    filedConfig: {\n      valueType: valueType\n    }\n  }, rest));\n};\nvar WrappedProFormText = ProFormText;\nWrappedProFormText.Password = Password;\n\n// @ts-ignore\n// eslint-disable-next-line no-param-reassign\nWrappedProFormText.displayName = 'ProFormComponent';\nexport default WrappedProFormText;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fieldProps\", \"proFieldProps\"];\nimport React from 'react';\nimport <PERSON>Field from \"../Field\";\n\n/**\n * 文本选择组件\n *\n * @param\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar ProFormTextArea = function ProFormTextArea(_ref, ref) {\n  var fieldProps = _ref.fieldProps,\n    proFieldProps = _ref.proFieldProps,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/_jsx(ProField, _objectSpread({\n    ref: ref,\n    valueType: \"textarea\",\n    fieldProps: fieldProps,\n    proFieldProps: proFieldProps\n  }, rest));\n};\nexport default /*#__PURE__*/React.forwardRef(ProFormTextArea);", "import type { ModalFormProps } from '@ant-design/pro-components';\r\nimport {\r\n    ModalForm,\r\n    ProFormSelect,\r\n    ProFormText,\r\n    ProForm,\r\n    ProFormTextArea,\r\n    ProFormList\r\n} from '@ant-design/pro-components';\r\nimport { Form, Col, Row } from 'antd';\r\n\r\n\r\nconst fetchAllUser = async (): Promise<Array<{\r\n    username: string;\r\n    id: number;\r\n    user_id: string;\r\n}>> => {\r\n    const url = '/api/v1/users/all/'\r\n    const res = await fetch(url, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n    })\r\n    return res.json()\r\n};\r\n\r\nexport type PromotedProducts = {\r\n    name: string;\r\n    reason: string;\r\n}\r\n\r\nexport type DataItem = {\r\n    platform: string;\r\n    user_id: number;\r\n    title: string;\r\n    video_url: string;\r\n    category: string;\r\n    video_script: string;\r\n    video_summary: string;\r\n    reply_setting: string;\r\n    comment_control_plan: string;\r\n    promoted_products: PromotedProducts[];\r\n    common_questions: string;\r\n};\r\n\r\nexport type ReplyParamsModalFormProps = ModalFormProps<DataItem>\r\n\r\nexport default (props: ReplyParamsModalFormProps) => {\r\n    const [form] = Form.useForm<DataItem>();\r\n    const { initialValues, ...restProps } = props;\r\n    // // 给表单赋值\r\n    // form.resetFields();\r\n    // if (initialValues) {\r\n    //     form.setFieldsValue(initialValues);\r\n    // } else {\r\n\r\n    //     form\r\n    // }\r\n    return (\r\n        <ModalForm<DataItem>\r\n            initialValues={initialValues}\r\n            grid={true}\r\n            disabled={true}\r\n            rowProps={{\r\n                gutter: 24,\r\n            }}\r\n            labelCol={{ flex: '100px' }}\r\n            labelAlign=\"left\"\r\n            labelWrap\r\n            wrapperCol={{ flex: 1 }}\r\n            layout='horizontal'\r\n            {...restProps}\r\n            form={form}\r\n        >\r\n            <ProForm.Group\r\n                title=\"绑定回评视频\">\r\n                <ProFormSelect\r\n                    name=\"platform\"\r\n                    label=\"所属平台\"\r\n                    valueEnum={{\r\n                        bz: 'B站',\r\n                    }}\r\n                    colProps={{\r\n                        span: 11,\r\n                    }}\r\n                    placeholder=\"请选择所属平台\"\r\n                    rules={[\r\n                        {\r\n                            required: true,\r\n                            message: '请选择所属平台',\r\n                            type: 'string',\r\n                        },\r\n                    ]}\r\n                />\r\n                <ProFormSelect\r\n                    label=\"关联账号\"\r\n                    name=\"user_id\"\r\n                    colProps={{\r\n                        span: 11,\r\n                        offset: 2,\r\n                    }}\r\n                    request={async () => {\r\n\r\n                        return (await fetchAllUser()).map((user) => {\r\n                            return {\r\n                                label: user.username,\r\n                                value: parseInt(user.user_id),\r\n                            }\r\n                        });\r\n                    }}\r\n                    rules={[\r\n                        {\r\n                            required: true,\r\n                            message: '请选择关联账号',\r\n                            type: 'number',\r\n                        }\r\n                    ]} />\r\n                <ProFormText\r\n                    label=\"视频标题\"\r\n                    placeholder='请输入视频标题'\r\n                    name=\"title\"\r\n                    colProps={{\r\n                        span: 24,\r\n                    }}\r\n                    rules={[\r\n                        {\r\n                            required: true,\r\n                            message: '请输入视频标题',\r\n                            type: 'string',\r\n                        }\r\n                    ]} />\r\n                <ProFormText\r\n                    label=\"视频链接\"\r\n                    placeholder='请输入视频链接'\r\n                    name=\"video_url\"\r\n                    colProps={{\r\n                        span: 11,\r\n                    }}\r\n                    rules={[\r\n                        {\r\n                            required: true,\r\n                            message: '请输入视频链接',\r\n                            type: 'string',\r\n                        }\r\n                    ]} />\r\n                <ProFormText\r\n                    label=\"所属品类\"\r\n                    placeholder='请输入所属品类'\r\n                    name=\"category\"\r\n                    colProps={{\r\n                        span: 11,\r\n                        offset: 2,\r\n                    }}\r\n                    rules={[\r\n                        {\r\n                            required: true,\r\n                            message: '请输入所属品类',\r\n                            type: 'string',\r\n                        }\r\n                    ]} />\r\n                <ProFormTextArea\r\n                    label=\"视频文案\"\r\n                    placeholder='复制粘贴视频文案'\r\n                    name=\"video_script\"\r\n                    colProps={{\r\n                        span: 24,\r\n                    }}\r\n                    rules={[\r\n                        {\r\n                            required: true,\r\n                            message: '请输入视频文案',\r\n                            type: 'string',\r\n                        }\r\n                    ]} />\r\n                <ProFormTextArea\r\n                    label=\"视频总结\"\r\n                    placeholder='请输入视频内容总结'\r\n                    name=\"video_summary\"\r\n                    colProps={{\r\n                        span: 24,\r\n                    }}\r\n                    rules={[\r\n                        {\r\n                            required: true,\r\n                            message: '请输入视频内容总结',\r\n                            type: 'string',\r\n                        }\r\n                    ]} />\r\n                <ProFormText\r\n                    label=\"回复设定\"\r\n                    placeholder='请输入视频回复设定（账号人设、回复语气等）'\r\n                    name=\"reply_setting\"\r\n                    colProps={{\r\n                        span: 24,\r\n                    }}\r\n                    rules={[\r\n                        {\r\n                            required: true,\r\n                            message: '请输入视频回复设定',\r\n                            type: 'string',\r\n                        }\r\n                    ]} />\r\n                <ProFormTextArea\r\n                    label=\"控评方案\"\r\n                    placeholder='请输入视频的控评方案/参考回复，会作为评论回复的参考'\r\n                    name=\"comment_control_plan\"\r\n                    colProps={{\r\n                        span: 24,\r\n                    }} />\r\n            </ProForm.Group>\r\n            <ProForm.Group\r\n                title=\"产品推荐策略\"\r\n            >\r\n                <ProFormList\r\n                    min={1}\r\n                    name=\"promoted_products\"\r\n                    deleteIconProps={props.disabled ? false : undefined}\r\n                    copyIconProps={props.disabled ? false : undefined}\r\n                    alwaysShowItemLabel={false}\r\n                    creatorButtonProps={{\r\n                        creatorButtonText: '添加主推产品',\r\n                    }}\r\n                >\r\n                    {(\r\n                        // 当前行的基本信息 {name: number; key: number}\r\n                        _meta,\r\n                        // 当前的行号\r\n                        index,\r\n                        /**\r\n                         * action\r\n                         * @name 用于操作行的一些快捷方法\r\n                         * @example 给第二行增加数据 action.add?.({},1);\r\n                         * @example 删除第二行 action.remove?.(1);\r\n                         * @example 从 1 移到 2: action.move?.(2,1);\r\n                         * @example 获取当前行的数据: action.getCurrentRowData() -> {id:\"xxx\",name:'123',age:18}\r\n                         * @example 设置当前行的数据: {id:\"123\",name:'123'} -> action.setCurrentRowData({name:'xxx'}) -> {id:\"123\",name:'xxx'}\r\n                         * @example 清空当前行的数据：{id:\"123\",name:'123'} -> action.setCurrentRowData({name:undefined}) -> {id:\"123\"}\r\n                         */\r\n                        _action,\r\n                        // 总行数\r\n                        _count,\r\n                    ) => {\r\n                        return (\r\n                            <Row>\r\n                                <Col span={3} >\r\n                                    <label className='form-item-required'>主推产品{index + 1}</label></Col >\r\n                                <Col span={21}>\r\n                                    <ProFormText\r\n                                        label=\"\"\r\n                                        placeholder='请输入主推产品名称及型号'\r\n                                        key=\"name\"\r\n                                        name=\"name\"\r\n                                        rules={[\r\n                                            {\r\n                                                required: true,\r\n                                                message: '请输入主推产品名称及型号',\r\n                                                type: 'string',\r\n                                            }\r\n                                        ]} />\r\n                                    <ProFormTextArea\r\n                                        label=\"\"\r\n                                        placeholder='请输入推荐理由'\r\n                                        key=\"reason\"\r\n                                        name=\"reason\"\r\n                                        rules={[\r\n                                            {\r\n                                                required: true,\r\n                                                message: '请输入推荐理由',\r\n                                                type: 'string',\r\n                                            }\r\n                                        ]} />\r\n                                </Col>\r\n                            </Row>\r\n                        );\r\n                    }}\r\n                </ProFormList>\r\n            </ProForm.Group>\r\n            <ProForm.Group\r\n                title=\"产品常见问题Q&A\"\r\n            >\r\n                <ProFormTextArea\r\n                    label=\"场景问题\"\r\n                    placeholder='复制粘贴场景问题Q&A'\r\n                    name=\"common_questions\"\r\n                    colProps={{\r\n                        span: 24,\r\n                    }} />\r\n            </ProForm.Group>\r\n        </ModalForm>\r\n    );\r\n};\r\n", "import { useState, useRef } from 'react';\r\nimport { useImmer } from 'use-immer'\r\nimport { Helmet } from '@modern-js/runtime/head';\r\nimport { PageContainer, ProTable } from '@ant-design/pro-components';\r\nimport type { ProColumns, ActionType } from '@ant-design/pro-components';\r\nimport { Button, Popconfirm, Space, Table, Switch, message } from 'antd';\r\nimport ReplyParamsModalForm, { type ReplyParamsModalFormProps, DataItem as VideoData } from '@/reply-admin/components/ReplyParamsModalForm'\r\n\r\n\r\nexport type TableListItem = {\r\n    /**\r\n     * 记录id\r\n     */\r\n    id: string;\r\n    /**\r\n     * 关联账号\r\n     */\r\n    user: {\r\n        /**\r\n         * 账号名称\r\n         */\r\n        username: string;\r\n    },\r\n    /**\r\n     * 视频标题\r\n     */\r\n    title: string;\r\n    /**\r\n     * 所属品类\r\n     */\r\n    category: string;\r\n    /**\r\n     * 视频链接\r\n     */\r\n    video_url: string;\r\n    /**\r\n     * 回复设定\r\n     */\r\n    reply_setting: string;\r\n    promoted_products: Array<{\r\n        /**\r\n         * 商品名称\r\n         */\r\n        name: string;\r\n        reason: string;\r\n    }> | undefined;\r\n    /**\r\n     * 视频总结\r\n     */\r\n    video_summary: string;\r\n    /**\r\n     * 创建时间\r\n     */\r\n    created_at: string;\r\n    /**\r\n     * 是否禁用\r\n     */\r\n    is_disabled: boolean;\r\n}\r\n\r\nexport type TableListParams = {\r\n    page: number;\r\n    page_size: number;\r\n}\r\n\r\n/**\r\n * 获取视频列表\r\n * @param params \r\n * @returns \r\n */\r\nconst queryTableData = async (params: any): Promise<{\r\n    items: TableListItem[];\r\n    page: number;\r\n    page_size: number;\r\n    total: number;\r\n}> => {\r\n    console.log('queryTableData', params);\r\n    const searchParams = new URLSearchParams();\r\n    const skip = (params.current - 1) * params.pageSize\r\n    searchParams.append('skip', `${skip}`)\r\n    searchParams.append('limit', `${params.pageSize}`)\r\n    // searchParams.append('page', params.current)\r\n    // searchParams.append('page_size', params.pageSize)\r\n    const url = '/api/v1/videos/' + '?' + searchParams.toString()\r\n    const res = await fetch(url, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n    })\r\n    return res.json()\r\n}\r\n\r\n/**\r\n * 添加用户\r\n * @param data \r\n * @returns \r\n */\r\nconst addVideo = async (data: Omit<VideoData, 'platform'>): Promise<void> => {\r\n    const url = '/api/v1/videos/'\r\n    const res = await fetch(url, {\r\n        method: 'POST',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(data),\r\n    })\r\n    return res.json()\r\n}\r\n\r\n/**\r\n * 根据id获取用户信息\r\n * @param id \r\n * @returns \r\n */\r\nconst getVideoDataById = async (id: string): Promise<TableListItem> => {\r\n    const url = '/api/v1/videos/' + id\r\n    const res = await fetch(url, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n    })\r\n    return res.json()\r\n}\r\n\r\n/**\r\n * 根据id更新用户信息\r\n * @param id \r\n * @param data \r\n * @returns \r\n */\r\nconst putVideoDataById = async (id: string, data: Omit<VideoData, 'platform'>) => {\r\n    const url = '/api/v1/videos/' + id\r\n    const res = await fetch(url, {\r\n        method: 'PUT',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(data),\r\n    })\r\n    return res.json()\r\n}\r\n\r\nconst putBathchDisabledByIds = async (ids: string[], disabled: boolean) => {\r\n    const url = disabled ? '/api/v1/videos/batch/disable' : '/api/v1/videos/batch/enable'\r\n    const res = await fetch(url, {\r\n        method: 'PUT',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n            video_ids: ids\r\n        }),\r\n    })\r\n    return res.json()\r\n}\r\n\r\nconst Index = () => {\r\n    const tableRef = useRef<ActionType>();\r\n    const [messageApi, contextHolder] = message.useMessage();\r\n    // 当前被选中的项目\r\n    const [currentSelectedRows, setCurrentSelectedRows] = useState<Array<TableListItem>>([])\r\n    const [modalSchemaFormProps, updateModalSchemaFormProps] = useImmer<ReplyParamsModalFormProps>(() => {\r\n        return {\r\n            open: false,\r\n            modalProps: {\r\n                destroyOnHidden: true,\r\n                title: '添加回评参数',\r\n                width: '70%'\r\n            },\r\n            // disabledKeys: [],\r\n        }\r\n    })\r\n\r\n    const onEnableButton = async (record: TableListItem, checked: boolean) => {\r\n        try {\r\n            console.log(`点击启用/禁用【id:${record.id}】期望为 ${checked}`);\r\n            await putBathchDisabledByIds([record.id], !checked)\r\n            messageApi.open({\r\n                type: 'success',\r\n                content: '操作成功',\r\n            });\r\n            if (tableRef && tableRef.current) {\r\n                // 刷新列表\r\n                tableRef.current.reload();\r\n            }\r\n        } catch (error) {\r\n            messageApi.open({\r\n                type: 'error',\r\n                content: '操作失败',\r\n            })\r\n        }\r\n\r\n    };\r\n\r\n    const onModalOpenChange = (val: boolean) => {\r\n        updateModalSchemaFormProps(drart => {\r\n            drart.open = val\r\n        })\r\n    }\r\n\r\n    const onClickViewButton = async (record: TableListItem) => {\r\n        console.log(`点击查看【id:${record.id}】`);\r\n        const info = await getVideoDataById(record.id)\r\n        if (!info.id) {\r\n            messageApi.open({\r\n                type: 'error',\r\n                content: (info as any).detail ? (info as any).detail : '获取数据失败',\r\n            })\r\n            return\r\n        }\r\n        updateModalSchemaFormProps(drart => {\r\n            drart.modalProps && (drart.modalProps.title = '查看回评参数')\r\n            drart.disabled = true\r\n            drart.submitter = false\r\n            drart.initialValues = {\r\n                platform: 'bz',\r\n                ...info\r\n            }\r\n            drart.onFinish = async (val) => {\r\n                return false\r\n            }\r\n            drart.open = true\r\n        })\r\n    }\r\n\r\n    const onClickEditButton = async (record: TableListItem) => {\r\n        console.log(`点击编辑【id:${record.id}】`);\r\n        const info = await getVideoDataById(record.id)\r\n        if (!info.id) {\r\n            messageApi.open({\r\n                type: 'error',\r\n                content: (info as any).detail ? (info as any).detail : '获取数据失败',\r\n            })\r\n            return\r\n        }\r\n        updateModalSchemaFormProps(drart => {\r\n            drart.modalProps && (drart.modalProps.title = '编辑账号')\r\n            drart.disabled = false\r\n            drart.submitter = undefined\r\n            drart.initialValues = {\r\n                platform: 'bz',\r\n                ...info\r\n            }\r\n            drart.onFinish = async (val) => {\r\n                try {\r\n                    const { platform, ...restVal } = val\r\n                    await putVideoDataById(record.id, restVal)\r\n                    messageApi.open({\r\n                        type: 'success',\r\n                        content: '操作成功',\r\n                    })\r\n                } catch (error) {\r\n                    messageApi.open({\r\n                        type: 'error',\r\n                        content: '操作失败',\r\n                    })\r\n                }\r\n                if (tableRef && tableRef.current) {\r\n                    // 刷新列表\r\n                    tableRef.current.reload();\r\n                }\r\n                return true\r\n            }\r\n            drart.open = true\r\n        })\r\n    }\r\n\r\n    const onClickAddButton = () => {\r\n        console.log(`点击添加回评参数`);\r\n        updateModalSchemaFormProps(drart => {\r\n            drart.disabled = false\r\n            drart.modalProps && (drart.modalProps.title = '添加回评参数')\r\n            drart.initialValues = {\r\n                platform: 'bz',\r\n                promoted_products: [{\r\n                    name: '',\r\n                    reason: '',\r\n                }],\r\n            }\r\n            // drart.disabledKeys = []\r\n            drart.submitter = undefined\r\n            drart.onFinish = async (val) => {\r\n                console.log('新增val', val)\r\n                try {\r\n                    const { platform, ...restVal } = val\r\n                    await addVideo(restVal)\r\n                    messageApi.open({\r\n                        type: 'success',\r\n                        content: '操作成功',\r\n                    });\r\n                } catch (error) {\r\n                    messageApi.open({\r\n                        type: 'error',\r\n                        content: '操作失败',\r\n                    })\r\n                }\r\n                if (tableRef && tableRef.current) {\r\n                    // 刷新列表\r\n                    tableRef.current.reload();\r\n                }\r\n                return true\r\n            }\r\n            drart.open = true\r\n        })\r\n    }\r\n\r\n    const onClickBatchEnable = async () => {\r\n        const ids = currentSelectedRows.map(item => item.id)\r\n        console.log(`点击批量启用，数量${ids.length}, ids:${ids}`);\r\n        try {\r\n            await putBathchDisabledByIds(ids, false)\r\n            messageApi.open({\r\n                type: 'success',\r\n                content: '操作成功',\r\n            });\r\n            if (tableRef && tableRef.current) {\r\n                // 刷新列表\r\n                tableRef.current.reload();\r\n            }\r\n        } catch (error) {\r\n            messageApi.open({\r\n                type: 'error',\r\n                content: '操作失败',\r\n            })\r\n        }\r\n    }\r\n\r\n    const onClickBatchDisable = async () => {\r\n        const ids = currentSelectedRows.map(item => item.id)\r\n        console.log(`点击批量禁用，数量${ids.length}, ids:${ids}`);\r\n        try {\r\n            await putBathchDisabledByIds(ids, true)\r\n            messageApi.open({\r\n                type: 'success',\r\n                content: '操作成功',\r\n            });\r\n            if (tableRef && tableRef.current) {\r\n                // 刷新列表\r\n                tableRef.current.reload();\r\n            }\r\n        } catch (error) {\r\n            messageApi.open({\r\n                type: 'error',\r\n                content: '操作失败',\r\n            })\r\n        }\r\n    }\r\n\r\n\r\n    const columns: ProColumns<TableListItem>[] = [\r\n        {\r\n            title: '关联账号',\r\n            width: 120,\r\n            ellipsis: true,\r\n            dataIndex: ['user', 'username'],\r\n        },\r\n        {\r\n            title: '账号所属平台',\r\n            width: 100,\r\n            key: 'platform',\r\n            render: () => <span>B站</span>\r\n        },\r\n        {\r\n            title: '视频标题',\r\n            width: 240,\r\n            ellipsis: true,\r\n            dataIndex: 'title',\r\n            render: (value, record) => <a href={record.video_url} target='_blank'>{record.title}</a>\r\n        },\r\n        {\r\n            title: '所属品类',\r\n            width: 90,\r\n            ellipsis: true,\r\n            dataIndex: 'category',\r\n        },\r\n        {\r\n            title: '回复设定',\r\n            width: 90,\r\n            ellipsis: true,\r\n            dataIndex: 'reply_setting',\r\n        },\r\n\r\n        {\r\n            title: '产品推荐策略',\r\n            width: 200,\r\n            ellipsis: true,\r\n            dataIndex: 'promoted_products',\r\n            render: (value, record) => Array.isArray(record.promoted_products) && record.promoted_products.length ? record.promoted_products.map((item, index) => (\r\n                <p key={`promoted_products_${record.id}_${index}`}>\r\n                    <span>{item.name}</span>:\r\n                    <span>{item.reason}</span>\r\n                </p>\r\n            )) : <span>-</span>\r\n        },\r\n        {\r\n            title: '视频总结',\r\n            width: 120,\r\n            ellipsis: true,\r\n            dataIndex: 'video_summary',\r\n        },\r\n        {\r\n            title: '创建时间',\r\n            width: 150,\r\n            dataIndex: 'created_at',\r\n            valueType: 'dateTime',\r\n        },\r\n        {\r\n            title: '启用/禁用',\r\n            width: 80,\r\n            key: 'enable',\r\n            valueType: 'option',\r\n            fixed: 'right',\r\n            render: (_, record) => [<Switch key={record.id} value={!record.is_disabled} onClick={(checked) => onEnableButton(record, checked)}></Switch>],\r\n        },\r\n        {\r\n            title: '操作',\r\n            width: 80,\r\n            key: 'option',\r\n            valueType: 'option',\r\n            fixed: 'right',\r\n            render: (_, record) => [\r\n                <a key=\"view\" onClick={() => onClickViewButton(record)}>查看</a>,\r\n                <a key=\"edit\" onClick={() => onClickEditButton(record)}>编辑</a>],\r\n        },\r\n    ];\r\n\r\n    return (\r\n        <PageContainer\r\n            style={{\r\n                height: '100vh',\r\n                overflow: 'hidden'\r\n            }}>\r\n            <Helmet>\r\n                <title>账号管理</title>\r\n            </Helmet>\r\n            {contextHolder}\r\n\r\n            <ProTable<TableListItem, TableListParams>\r\n                actionRef={tableRef}\r\n                request={async (\r\n                    // 第一个参数 params 查询表单和 params 参数的结合\r\n                    // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范\r\n                    params: any,\r\n                    sort,\r\n                    filter,\r\n                ) => {\r\n                    // 这里需要返回一个 Promise,在返回之前你可以进行数据转化\r\n                    // 如果需要转化参数可以在这里进行修改\r\n                    const data = await queryTableData(params);\r\n                    console.log('data', data);\r\n                    return {\r\n                        data: data.items,\r\n                        success: true,\r\n                        total: data.total,\r\n                    }\r\n                }}\r\n                columns={columns}\r\n                columnsState={{\r\n                    persistenceType: 'localStorage',\r\n                    persistenceKey: 'reply-admin/account-admin'\r\n                }}\r\n                rowSelection={{\r\n                    // 自定义选择项参考: https://ant.design/components/table-cn/#components-table-demo-row-selection-custom\r\n                    // 注释该行则默认不显示下拉选项\r\n                    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],\r\n                    defaultSelectedRowKeys: [],\r\n                    alwaysShowAlert: true,\r\n                    onChange: (_1, selectedRows, _2) => setCurrentSelectedRows(selectedRows)\r\n                }}\r\n\r\n                tableAlertRender={({\r\n                    selectedRowKeys,\r\n                    onCleanSelected,\r\n                }) => {\r\n                    return (\r\n                        <Space size={24}>\r\n                            <span>\r\n                                已选 {selectedRowKeys.length} 项\r\n                                {selectedRowKeys.length > 0 && (\r\n                                    <Button type=\"link\" style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>\r\n                                        取消选择\r\n                                    </Button>\r\n                                )}\r\n                            </span>\r\n                        </Space>\r\n                    );\r\n                }}\r\n                tableAlertOptionRender={() => {\r\n                    return (\r\n                        <Space size={16}>\r\n                            <Popconfirm\r\n                                title=\"批量启用\"\r\n                                description=\"是否批量启用?\"\r\n                                onConfirm={onClickBatchEnable}\r\n                                disabled={currentSelectedRows.length === 0}\r\n                                okText=\"确认\"\r\n                                cancelText=\"取消\"\r\n                            >\r\n                                <Button disabled={currentSelectedRows.length === 0}>启用</Button>\r\n                            </Popconfirm>\r\n                            <Popconfirm\r\n                                title=\"批量禁用\"\r\n                                description=\"是否批量禁用?\"\r\n                                onConfirm={onClickBatchDisable}\r\n                                disabled={currentSelectedRows.length === 0}\r\n                                okText=\"确认\"\r\n                                cancelText=\"取消\"\r\n                            >\r\n                                <Button disabled={currentSelectedRows.length === 0}>禁用</Button>\r\n                            </Popconfirm>\r\n                        </Space>\r\n                    );\r\n                }}\r\n                options={{\r\n                    fullScreen: false,\r\n                    reload: false,\r\n                    density: true,\r\n                    setting: true\r\n                }}\r\n                search={false}\r\n                pagination={{\r\n                    pageSize: 50,\r\n                }}\r\n                scroll={\r\n                    {\r\n                        scrollToFirstRowOnChange: true,\r\n                        y: 'calc(100vh - 340px)',\r\n                        x: 'calc(100vw - 200px)'\r\n                    }\r\n                }\r\n                rowKey=\"id\"\r\n                toolBarRender={() => [<Button key=\"add-account\" type='primary' onClick={onClickAddButton}>添加回评参数</Button>]}\r\n            />\r\n            {\r\n                modalSchemaFormProps.open && <ReplyParamsModalForm\r\n                    {...{ ...modalSchemaFormProps }}\r\n                    onOpenChange={onModalOpenChange}\r\n                ></ReplyParamsModalForm>\r\n            }\r\n        </PageContainer>\r\n    );\r\n}\r\n\r\nexport default Index;\r\n", "import { Helmet } from '@modern-js/runtime/head';\r\nimport ReplyParamsAdmin from '@/reply-admin/containers/ReplyParamsAdmin'\r\nimport './index.css';\r\n\r\nconst Index = () => (\r\n  <>\r\n    <Helmet>\r\n      <title>回评论参数管理</title>\r\n    </Helmet>\r\n    <ReplyParamsAdmin></ReplyParamsAdmin>\r\n  </>\r\n);\r\n\r\nexport default Index;\r\n"], "names": ["_excluded", "_excluded2", "SearchSelect", "_ref2", "ref", "fieldProps", "children", "params", "proFieldProps", "mode", "valueEnum", "request", "options", "rest", "props", "context", "FieldContext", "runFunction", "ProFormSelect", "_ref", "showSearch", "WrappedProFormSelect", "valueType", "_useMountMergeState", "_useMountMergeState2", "open", "<PERSON><PERSON><PERSON>", "form", "_props$statusRender", "value", "node", "e", "WrappedProFormText", "_useState", "_useState2", "omit", "_fieldProps$onBlur", "_fieldProps$onClick", "fetchAllUser", "res", "fetch", "Form", "initialValues", "restProps", "ModalForm", "ProForm", "user", "parseInt", "ProFormText", "ProFormTextArea", "ProFormList", "undefined", "_meta", "index", "_action", "_count", "Row", "Col", "queryTableData", "console", "searchParams", "URLSearchParams", "skip", "url", "addVideo", "data", "JSON", "getVideoDataById", "id", "putVideoDataById", "putBathchDisabledByIds", "ids", "disabled", "tableRef", "useRef", "messageApi", "contextHolder", "message", "currentSelectedRows", "setCurrentSelectedRows", "useState", "modalSchemaFormProps", "updateModalSchemaFormProps", "useImmer", "onEnableButton", "record", "checked", "error", "onClickViewButton", "info", "drart", "val", "onClickEditButton", "platform", "restVal", "onClickAddButton", "onClickBatchEnable", "item", "onClickBatchDisable", "columns", "Array", "_", "Switch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ProTable", "sort", "filter", "Table", "_1", "selectedRows", "_2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onCleanSelected", "Space", "<PERSON><PERSON>", "Popconfirm", "ReplyParamsModalForm", "ReplyParamsAdmin"], "mappings": "4ZAEIA,EAAY,CAAC,aAAc,WAAY,SAAU,gBAAiB,OAAQ,YAAa,UAAW,aAAc,UAAU,CAC5HC,EAAa,CAAC,aAAc,WAAY,SAAU,gBAAiB,OAAQ,YAAa,UAAW,UAAU,CA2C3GC,EAA4B,YAAgB,CAAC,SAAUC,CAAK,CAAEC,CAAG,EACnE,IAAIC,EAAaF,EAAM,UAAU,CAC/BG,EAAWH,EAAM,QAAQ,CACzBI,EAASJ,EAAM,MAAM,CACrBK,EAAgBL,EAAM,aAAa,CACnCM,EAAON,EAAM,IAAI,CACjBO,EAAYP,EAAM,SAAS,CAC3BQ,EAAUR,EAAM,OAAO,CACvBS,EAAUT,EAAM,OAAO,CACvBU,EAAO,QAAyBV,EAAOF,GACrCa,EAAQ,QAAc,CACxB,QAASF,EACT,KAAMH,GAAQ,WACd,aAAc,GACd,WAAY,GACZ,WAAY,KACZ,qBAAsB,GACtB,gBAAiB,OACnB,EAAGJ,GACCU,EAAU,iBAAWC,EAAA,CAAY,EACrC,MAAoB,UAAK,GAAY,CAAE,QAAc,QAAc,CACjE,UAAW,GAAAC,EAAA,GAAYP,GACvB,QAASC,EACT,OAAQJ,EACR,UAAW,SACX,YAAa,CACX,gBAAiB,EACnB,EACA,WAAY,QAAc,CACxB,kBAAmBQ,EAAQ,iBAAiB,AAC9C,EAAGD,GACH,IAAKV,EACL,cAAeI,CACjB,EAAGK,GAAO,CAAC,EAAG,CACZ,SAAUP,CACZ,GACF,GACIY,EAA6B,YAAgB,CArEnB,SAAiCC,CAAI,CAAEf,CAAG,EACtE,IAAIC,EAAac,EAAK,UAAU,CAC9Bb,EAAWa,EAAK,QAAQ,CACxBZ,EAASY,EAAK,MAAM,CACpBX,EAAgBW,EAAK,aAAa,CAClCV,EAAOU,EAAK,IAAI,CAChBT,EAAYS,EAAK,SAAS,CAC1BR,EAAUQ,EAAK,OAAO,CACtBC,EAAaD,EAAK,UAAU,CAC5BP,EAAUO,EAAK,OAAO,CACtBN,EAAO,QAAyBM,EAAMnB,GACpCe,EAAU,iBAAWC,EAAA,CAAY,EACrC,MAAoB,UAAK,GAAY,CAAE,QAAc,QAAc,CACjE,UAAW,GAAAC,EAAA,GAAYP,GACvB,QAASC,EACT,OAAQJ,EACR,UAAW,SACX,YAAa,CACX,gBAAiB,EACnB,EACA,WAAY,QAAc,CACxB,QAASK,EACT,KAAMH,EACN,WAAYW,EACZ,kBAAmBL,EAAQ,iBAAiB,AAC9C,EAAGV,GACH,IAAKD,EACL,cAAeI,CACjB,EAAGK,GAAO,CAAC,EAAG,CACZ,SAAUP,CACZ,GACF,EAyCAe,CAD2BH,EACN,YAAY,CAFPhB,EAM1BmB,AAL2BH,EAKN,WAAW,CAAG,mB,2DCvF/B,EAAY,CAAC,aAAc,gBAAgB,CAC7C,EAAa,CAAC,aAAc,gBAAgB,CAQ1CI,EAAY,OAmBZ,EAAmB,SAA0BR,CAAK,EACpD,IAAIS,EAAsB,QAAmBT,EAAM,IAAI,EAAI,GAAO,CAC9D,MAAOA,EAAM,IAAI,CACjB,SAAUA,EAAM,YAAY,AAC9B,GACAU,EAAuB,QAAeD,EAAqB,GAC3DE,EAAOD,CAAoB,CAAC,EAAE,CAC9BE,EAAUF,CAAoB,CAAC,EAAE,CACnC,MAAoB,UAAK,QAAS,CAAE,CAClC,aAAc,GACd,QAAS,GACT,SAAU,SAAkBG,CAAI,EAE9B,IADIC,EACAC,EAAQF,EAAK,aAAa,CAACb,EAAM,IAAI,EAAI,EAAE,EAC/C,MAAoB,UAAK,GAAO,CAAE,QAAc,QAAc,CAC5D,kBAAmB,SAA2BgB,CAAI,SAChD,AAAIA,GAAQA,EAAK,UAAU,CAClBA,EAAK,UAAU,CAEjBA,CACT,EACA,aAAc,SAAsBC,CAAC,EACnC,OAAOL,EAAQK,EACjB,EACA,QAAsB,WAAM,MAAO,CACjC,MAAO,CACL,QAAS,OACX,EACA,SAAU,CAAC,MAACH,CAAAA,EAAsBd,EAAM,YAAY,AAAD,EAAgD,KAAK,EAAIc,EAAoB,IAAI,CAACd,EAAOe,GAAQf,EAAM,YAAY,CAAgB,UAAK,MAAO,CAChM,MAAO,CACL,UAAW,EACb,EACA,SAAuB,UAAK,OAAQ,CAClC,SAAUA,EAAM,YAAY,AAC9B,EACF,GAAK,KAAK,AACZ,GACA,aAAc,CACZ,MAAO,GACT,EACA,UAAW,UACb,EAAGA,EAAM,YAAY,EAAG,CAAC,EAAG,CAC1B,KAAMW,EACN,SAAUX,EAAM,QAAQ,AAC1B,GACF,CACF,EACF,EAiDIkB,EA7Gc,SAAqBb,CAAI,EACzC,IAAId,EAAac,EAAK,UAAU,CAC9BX,EAAgBW,EAAK,aAAa,CAClCN,EAAO,QAAyBM,EAAM,GACxC,MAAoB,UAAK,GAAQ,CAAE,QAAc,CAC/C,UAAWG,EACX,WAAYjB,EACZ,YAAa,CACX,UAAWiB,CACb,EACA,cAAed,CACjB,EAAGK,GACL,CAkGAmB,CAAAA,EAAmB,QAAQ,CAjDZ,SAAkB7B,CAAK,EACpC,IAAIE,EAAaF,EAAM,UAAU,CAC/BK,EAAgBL,EAAM,aAAa,CACnCU,EAAO,QAAyBV,EAAO,GACrC8B,EAAY,eAAS,IACvBC,EAAa,QAAeD,EAAW,GACvCR,EAAOS,CAAU,CAAC,EAAE,CACpBR,EAAUQ,CAAU,CAAC,EAAE,QACzB,AAAI7B,MAAAA,GAAgDA,EAAW,YAAY,EAAIQ,EAAK,IAAI,CAClE,UAAK,EAAkB,CACzC,KAAMA,EAAK,IAAI,CACf,aAAcR,MAAAA,EAA+C,KAAK,EAAIA,EAAW,YAAY,CAC7F,aAAcA,MAAAA,EAA+C,KAAK,EAAIA,EAAW,YAAY,CAC7F,aAAcA,MAAAA,EAA+C,KAAK,EAAIA,EAAW,YAAY,CAC7F,KAAMoB,EACN,aAAcC,EACd,SAAuB,UAAK,MAAO,CACjC,SAAuB,UAAK,GAAQ,CAAE,QAAc,CAClD,UAAW,WACX,WAAY,QAAc,QAAc,CAAC,EAAG,GAAAS,EAAA,GAAK9B,EAAY,CAAC,eAAgB,eAAgB,eAAe,GAAI,CAAC,EAAG,CACnH,OAAQ,SAAgB0B,CAAC,EACvB,IAAIK,CACJ/B,OAAAA,GAAqG+B,MAApDA,CAAAA,EAAqB/B,EAAW,MAAM,AAAD,GAAgD+B,EAAmB,IAAI,CAAC/B,EAAY0B,GAC1KL,EAAQ,GACV,EACA,QAAS,SAAiBK,CAAC,EACzB,IAAIM,CACJhC,OAAAA,GAAuGgC,MAAtDA,CAAAA,EAAsBhC,EAAW,OAAO,AAAD,GAAiDgC,EAAoB,IAAI,CAAChC,EAAY0B,GAC9KL,EAAQ,GACV,CACF,GACA,cAAelB,EACf,YAAa,CACX,UAAWc,CACb,CACF,EAAGT,GACL,EACF,GAEkB,UAAK,GAAQ,CAAE,QAAc,CAC/C,UAAW,WACX,WAAYR,EACZ,cAAeG,EACf,YAAa,CACX,UAAWc,CACb,CACF,EAAGT,GACL,EAMAmB,EAAmB,WAAW,CAAG,mBClIjC,IAAI,EAAY,CAAC,aAAc,gBAAgB,CAqB/C,EAA4B,YAAgB,CAXtB,SAAyBb,CAAI,CAAEf,CAAG,EACtD,IAAIC,EAAac,EAAK,UAAU,CAC9BX,EAAgBW,EAAK,aAAa,CAClCN,EAAO,QAAyBM,EAAM,GACxC,MAAoB,UAAK,GAAQ,CAAE,QAAc,CAC/C,IAAKf,EACL,UAAW,WACX,WAAYC,EACZ,cAAeG,CACjB,EAAGK,GACL,G,iCCVMyB,G,EAAe,oBAYjB,MAAOC,AANK,OAAMC,MADN,qBACiB,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,CACJ,EAAC,EACU,IAAI,EACnB,G,4CAuBA,EAAgB,AAAA1B,IACZ,GAAM,CAACa,EAAK,CAAGc,EAAAA,CAAAA,CAAAA,OAAY,GACrB,CAAEC,cAAAA,CAAa,CAAgB,CAAG5B,EAAd6B,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAc7B,EAAAA,CAAhC4B,gB,EASR,MACI,WAACE,EAAAA,CAASA,CAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CACN,cAAeF,EACf,KAAM,GACN,SAAU,GACV,SAAU,CACN,OAAQ,EACZ,EACA,SAAU,CAAE,KAAM,OAAQ,EAC1B,WAAW,OACX,UAAS,GACT,WAAY,CAAE,KAAM,CAAE,EACtB,OAAO,Y,EACHC,GAAAA,CACJ,KAAMhB,E,UAEN,WAACkB,EAAAA,CAAAA,CAAAA,KAAa,EACV,MAAM,uC,UACN,UHQW3B,EGRGA,CACV,KAAK,WACL,MAAM,2BACN,UAAW,CACP,GAAI,SACR,EACA,SAAU,CACN,KAAM,EACV,EACA,YAAY,6CACZ,MAAO,CACH,CACI,SAAU,GACV,QAAS,6CACT,KAAM,QACV,EACH,A,GAEL,UHVWA,EGUGA,CACV,MAAM,2BACN,KAAK,UACL,SAAU,CACN,KAAM,GACN,OAAQ,CACZ,EACA,QAAO,QAAE,YAEL,MAAQ,OAAMoB,GAAa,EAAG,GAAG,CAAC,AAACQ,GACxB,EACH,MAAOA,EAAK,QAAQ,CACpB,MAAOC,SAASD,EAAK,OAAO,CAChC,GAER,GACA,MAAO,CACH,CACI,SAAU,GACV,QAAS,6CACT,KAAM,QACV,EACH,A,GACL,UFeDd,EEfagB,CACR,MAAM,2BACN,YAAY,6CACZ,KAAK,QACL,SAAU,CACN,KAAM,EACV,EACA,MAAO,CACH,CACI,SAAU,GACV,QAAS,6CACT,KAAM,QACV,EACH,A,GACL,UFCDhB,EEDagB,CACR,MAAM,2BACN,YAAY,6CACZ,KAAK,YACL,SAAU,CACN,KAAM,EACV,EACA,MAAO,CACH,CACI,SAAU,GACV,QAAS,6CACT,KAAM,QACV,EACH,A,GACL,UFbDhB,EEaagB,CACR,MAAM,2BACN,YAAY,6CACZ,KAAK,WACL,SAAU,CACN,KAAM,GACN,OAAQ,CACZ,EACA,MAAO,CACH,CACI,SAAU,GACV,QAAS,6CACT,KAAM,QACV,EACH,A,GACL,UAACC,EAAeA,CACZ,MAAM,2BACN,YAAY,mDACZ,KAAK,eACL,SAAU,CACN,KAAM,EACV,EACA,MAAO,CACH,CACI,SAAU,GACV,QAAS,6CACT,KAAM,QACV,EACH,A,GACL,UAACA,EAAeA,CACZ,MAAM,2BACN,YAAY,yDACZ,KAAK,gBACL,SAAU,CACN,KAAM,EACV,EACA,MAAO,CACH,CACI,SAAU,GACV,QAAS,yDACT,KAAM,QACV,EACH,A,GACL,UFxDDjB,EEwDagB,CACR,MAAM,2BACN,YAAY,iIACZ,KAAK,gBACL,SAAU,CACN,KAAM,EACV,EACA,MAAO,CACH,CACI,SAAU,GACV,QAAS,yDACT,KAAM,QACV,EACH,A,GACL,UAACC,EAAeA,CACZ,MAAM,2BACN,YAAY,0JACZ,KAAK,uBACL,SAAU,CACN,KAAM,EACV,C,MAER,UAACJ,EAAAA,CAAAA,CAAAA,KAAa,EACV,MAAM,uC,SAEN,UAACK,EAAAA,CAAWA,CAAAA,CACR,IAAK,EACL,KAAK,oBACL,gBAAiBpC,CAAAA,EAAM,QAAQ,EAAWqC,KAAAA,EAC1C,cAAerC,CAAAA,EAAM,QAAQ,EAAWqC,KAAAA,EACxC,oBAAqB,GACrB,mBAAoB,CAChB,kBAAmB,sCACvB,E,SAEC,CAEGC,EAEAC,EAWAC,EAEAC,IAGI,WAACC,EAAAA,CAAGA,CAAAA,C,UACA,UAACC,EAAAA,CAAGA,CAAAA,CAAC,KAAM,E,SACP,WAAC,SAAM,UAAU,qB,UAAqB,2BAAKJ,EAAQ,E,KACvD,WAACI,EAAAA,CAAGA,CAAAA,CAAC,KAAM,G,UACP,UFnHrBzB,EEmHiCgB,CACR,MAAM,GACN,YAAY,2EAEZ,KAAK,OACL,MAAO,CACH,CACI,SAAU,GACV,QAAS,2EACT,KAAM,QACV,EACH,A,EARG,QASR,UAACC,EAAeA,CACZ,MAAM,GACN,YAAY,6CAEZ,KAAK,SACL,MAAO,CACH,CACI,SAAU,GACV,QAAS,6CACT,KAAM,QACV,EACH,A,EARG,U,UAehC,UAACJ,EAAAA,CAAAA,CAAAA,KAAa,EACV,MAAM,0C,SAEN,UAACI,EAAeA,CACZ,MAAM,2BACN,YAAY,sDACZ,KAAK,mBACL,SAAU,CACN,KAAM,EACV,C,QAIpB,EC7NMS,G,EAAiB,kBAAOnD,CAAM,EAMhCoD,QAAQ,GAAG,CAAC,iBAAkBpD,GAC9B,IAAMqD,EAAe,IAAIC,gBACnBC,EAAQvD,AAAAA,CAAAA,EAAO,OAAO,CAAG,GAAKA,EAAO,QAAQ,CACnDqD,EAAa,MAAM,CAAC,OAAS,GAAO,OAALE,IAC/BF,EAAa,MAAM,CAAC,QAAU,GAAkB,OAAhBrD,EAAO,QAAQ,GAG/C,IAAMwD,EAAM,mBAA0BH,EAAa,QAAQ,GAO3D,MAAOrB,AANK,OAAMC,MAAMuB,EAAK,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,CACJ,EAAC,EACU,IAAI,EACnB,G,SArB8BxD,CAAM,E,iCA4B9ByD,I,EAAW,kBAAOC,CAAI,EASxB,MAAO1B,AAPK,OAAMC,MADN,kBACiB,CACzB,OAAQ,OACR,QAAS,CACL,eAAgB,kBACpB,EACA,KAAM0B,KAAK,SAAS,CAACD,EACzB,EAAC,EACU,IAAI,EACnB,G,SAVwBA,CAAI,E,iCAiBtBE,I,EAAmB,kBAAOC,CAAE,EAQ9B,MAAO7B,AANK,OAAMC,MADN,kBAAoB4B,EACH,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,CACJ,EAAC,EACU,IAAI,EACnB,G,SATgCA,CAAE,E,iCAiB5BC,I,EAAmB,kBAAOD,CAAE,CAAUH,CAAI,EAS5C,MAAO1B,AAPK,OAAMC,MADN,kBAAoB4B,EACH,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,EACA,KAAMF,KAAK,SAAS,CAACD,EACzB,EAAC,EACU,IAAI,EACnB,G,SAVgCG,CAAE,CAAUH,CAAI,E,iCAY1CK,I,EAAyB,kBAAOC,CAAG,CAAYC,CAAQ,EAWzD,MAAOjC,AATK,OAAMC,MADNgC,EAAW,+BAAiC,8BAC3B,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,EACA,KAAMN,KAAK,SAAS,CAAC,CACjB,UAAWK,CACf,EACJ,EAAC,EACU,IAAI,EACnB,G,SAZsCA,CAAG,CAAYC,CAAQ,E,iCAiZ7D,GAnYc,KACV,I,YAAMC,EAAWC,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,IACX,CAACC,EAAYC,EAAc,CAAGC,EAAAA,EAAAA,CAAAA,UAAkB,GAEhD,CAACC,EAAqBC,EAAuB,CAAGC,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAA+B,EAAE,EACjF,CAACC,EAAsBC,EAA2B,CAAGC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAoC,IACpF,EACH,KAAM,GACN,WAAY,CACR,gBAAiB,GACjB,MAAO,uCACP,MAAO,KACX,CAEJ,IAGEC,G,EAAiB,kBAAOC,CAAM,CAAiBC,CAAO,EACxD,GAAI,CACA3B,QAAQ,GAAG,CAAE,iDAA8B2B,MAAAA,CAAjBD,EAAO,EAAE,CAAC,6BAAe,OAARC,IAC3C,MAAMhB,GAAuB,CAACe,EAAO,EAAE,CAAC,CAAE,CAACC,GAC3CX,EAAW,IAAI,CAAC,CACZ,KAAM,UACN,QAAS,0BACb,GACIF,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,EAE/B,CAAE,MAAOc,EAAO,CACZZ,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAS,0BACb,EACJ,CAEJ,G,SAnB8BU,CAAM,CAAiBC,CAAO,E,iCA2BtDE,G,EAAoB,kBAAOH,CAAM,EACnC1B,QAAQ,GAAG,CAAE,oCAAoB,OAAV0B,EAAO,EAAE,CAAC,WACjC,IAAMI,EAAO,MAAMtB,GAAiBkB,EAAO,EAAE,EAC7C,GAAI,CAACI,EAAK,EAAE,CAAE,YACVd,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAUc,EAAa,MAAM,CAAIA,EAAa,MAAM,CAAG,sCAC3D,GAGJP,EAA2BQ,AAAAA,I,KACvBA,CAAAA,EAAM,UAAU,EAAKA,CAAAA,EAAM,UAAU,CAAC,KAAK,CAAG,sCAAO,EACrDA,EAAM,QAAQ,CAAG,GACjBA,EAAM,SAAS,CAAG,GAClBA,EAAM,aAAa,CAAG,SAClB,SAAU,I,EACPD,G,EAEU,kBAAOE,CAAG,EACvB,MAAO,EACX,GAFAD,EAAM,QAAQ,C,SAAUC,CAAG,E,gCAG3BD,EAAM,IAAI,CAAG,EACjB,EACJ,G,SAvBiCL,CAAM,E,iCAyBjCO,G,EAAoB,kBAAOP,CAAM,EACnC1B,QAAQ,GAAG,CAAE,oCAAoB,OAAV0B,EAAO,EAAE,CAAC,WACjC,IAAMI,EAAO,MAAMtB,GAAiBkB,EAAO,EAAE,EAC7C,GAAI,CAACI,EAAK,EAAE,CAAE,YACVd,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAUc,EAAa,MAAM,CAAIA,EAAa,MAAM,CAAG,sCAC3D,GAGJP,EAA2BQ,AAAAA,I,KACvBA,CAAAA,EAAM,UAAU,EAAKA,CAAAA,EAAM,UAAU,CAAC,KAAK,CAAG,0BAAK,EACnDA,EAAM,QAAQ,CAAG,GACjBA,EAAM,SAAS,CAAGvC,KAAAA,EAClBuC,EAAM,aAAa,CAAG,SAClB,SAAU,I,EACPD,G,EAEU,kBAAOE,CAAG,EACvB,GAAI,CACA,GAAM,CAAEE,SAAAA,CAAQ,CAAc,CAAGF,EAAZG,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAYH,EAAAA,CAAzBE,W,CACR,OAAMxB,GAAiBgB,EAAO,EAAE,CAAES,GAClCnB,EAAW,IAAI,CAAC,CACZ,KAAM,UACN,QAAS,0BACb,EACJ,CAAE,MAAOY,EAAO,CACZZ,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAS,0BACb,EACJ,CAKA,OAJIF,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,GAEpB,EACX,GAnBAiB,EAAM,QAAQ,C,SAAUC,CAAG,E,gCAoB3BD,EAAM,IAAI,CAAG,EACjB,EACJ,G,SAxCiCL,CAAM,E,iCA0CjCU,EAAmB,KACrBpC,QAAQ,GAAG,CAAE,oDACbuB,EAA2BQ,AAAAA,I,KACvBA,CAAAA,EAAM,QAAQ,CAAG,GACjBA,EAAM,UAAU,EAAKA,CAAAA,EAAM,UAAU,CAAC,KAAK,CAAG,sCAAO,EACrDA,EAAM,aAAa,CAAG,CAClB,SAAU,KACV,kBAAmB,CAAC,CAChB,KAAM,GACN,OAAQ,EACZ,EAAE,AACN,EAEAA,EAAM,SAAS,CAAGvC,KAAAA,E,EACD,kBAAOwC,CAAG,EACvBhC,QAAQ,GAAG,CAAC,kBAASgC,GACrB,GAAI,CACA,GAAM,CAAEE,SAAAA,CAAQ,CAAc,CAAGF,EAAZG,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAYH,EAAAA,CAAzBE,W,CACR,OAAM7B,GAAS8B,GACfnB,EAAW,IAAI,CAAC,CACZ,KAAM,UACN,QAAS,0BACb,EACJ,CAAE,MAAOY,EAAO,CACZZ,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAS,0BACb,EACJ,CAKA,OAJIF,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,GAEpB,EACX,GApBAiB,EAAM,QAAQ,C,SAAUC,CAAG,E,gCAqB3BD,EAAM,IAAI,CAAG,EACjB,EACJ,EAEMM,G,EAAqB,oBACvB,IAAMzB,EAAMO,EAAoB,GAAG,CAACmB,AAAAA,GAAQA,EAAK,EAAE,EACnDtC,QAAQ,GAAG,CAAE,yDAA8BY,MAAAA,CAAnBA,EAAI,MAAM,CAAC,UAAY,OAAJA,IAC3C,GAAI,CACA,MAAMD,GAAuBC,EAAK,IAClCI,EAAW,IAAI,CAAC,CACZ,KAAM,UACN,QAAS,0BACb,GACIF,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,EAE/B,CAAE,MAAOc,EAAO,CACZZ,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAS,0BACb,EACJ,CACJ,G,4CAEMuB,G,EAAsB,oBACxB,IAAM3B,EAAMO,EAAoB,GAAG,CAACmB,AAAAA,GAAQA,EAAK,EAAE,EACnDtC,QAAQ,GAAG,CAAE,yDAA8BY,MAAAA,CAAnBA,EAAI,MAAM,CAAC,UAAY,OAAJA,IAC3C,GAAI,CACA,MAAMD,GAAuBC,EAAK,IAClCI,EAAW,IAAI,CAAC,CACZ,KAAM,UACN,QAAS,0BACb,GACIF,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,EAE/B,CAAE,MAAOc,EAAO,CACZZ,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAS,0BACb,EACJ,CACJ,G,4CAGMwB,EAAuC,CACzC,CACI,MAAO,2BACP,MAAO,IACP,SAAU,GACV,UAAW,CAAC,OAAQ,WAAW,AACnC,EACA,CACI,MAAO,uCACP,MAAO,IACP,IAAK,WACL,OAAQ,IAAM,UAAC,Q,SAAK,S,EACxB,EACA,CACI,MAAO,2BACP,MAAO,IACP,SAAU,GACV,UAAW,QACX,OAAQ,CAACtE,EAAOwD,IAAW,UAAC,KAAE,KAAMA,EAAO,SAAS,CAAE,OAAO,S,SAAUA,EAAO,KAAK,A,EACvF,EACA,CACI,MAAO,2BACP,MAAO,GACP,SAAU,GACV,UAAW,UACf,EACA,CACI,MAAO,2BACP,MAAO,GACP,SAAU,GACV,UAAW,eACf,EAEA,CACI,MAAO,uCACP,MAAO,IACP,SAAU,GACV,UAAW,oBACX,OAAQ,CAACxD,EAAOwD,IAAWe,MAAM,OAAO,CAACf,EAAO,iBAAiB,GAAKA,EAAO,iBAAiB,CAAC,MAAM,CAAGA,EAAO,iBAAiB,CAAC,GAAG,CAAC,CAACY,EAAM5C,IACxI,WAAC,K,UACG,UAAC,Q,SAAM4C,EAAK,IAAI,A,GAAQ,IACxB,UAAC,Q,SAAMA,EAAK,MAAM,A,KAFb,qBAAiC5C,MAAAA,CAAbgC,EAAO,EAAE,CAAC,KAAS,OAANhC,KAIzC,UAAC,Q,SAAK,G,EACf,EACA,CACI,MAAO,2BACP,MAAO,IACP,SAAU,GACV,UAAW,eACf,EACA,CACI,MAAO,2BACP,MAAO,IACP,UAAW,aACX,UAAW,UACf,EACA,CACI,MAAO,4BACP,MAAO,GACP,IAAK,SACL,UAAW,SACX,MAAO,QACP,OAAQ,CAACgD,EAAGhB,IAAW,CAAC,UAACiB,EAAAA,CAAMA,CAAAA,CAAiB,MAAO,CAACjB,EAAO,WAAW,CAAE,QAAS,AAACC,GAAYF,EAAeC,EAAQC,E,EAApFD,EAAO,EAAE,EAA+F,AACjJ,EACA,CACI,MAAO,eACP,MAAO,GACP,IAAK,SACL,UAAW,SACX,MAAO,QACP,OAAQ,CAACgB,EAAGhB,IAAW,CACnB,UAAC,KAAa,QAAS,IAAMG,EAAkBH,G,SAAS,c,EAAjD,QACP,UAAC,KAAa,QAAS,IAAMO,EAAkBP,G,SAAS,c,EAAjD,QAAwD,AACvE,EACH,CAED,MACI,WAACkB,EAAAA,EAAaA,CAAAA,CACV,MAAO,CACH,OAAQ,QACR,SAAU,QACd,E,UACA,UAACC,EAAAA,CAAMA,CAAAA,C,SACH,UAAC,S,SAAM,0B,KAEV5B,EAED,UAAC6B,EAAAA,CAAQA,CAAAA,CACL,UAAWhC,EACX,OAAO,E,EAAE,kBAGLlE,CAAM,CACNmG,CAAI,CACJC,CAAM,EAIN,IAAM1C,EAAO,MAAMP,EAAenD,GAElC,OADAoD,QAAQ,GAAG,CAAC,OAAQM,GACb,CACH,KAAMA,EAAK,KAAK,CAChB,QAAS,GACT,MAAOA,EAAK,KAAK,AACrB,CACJ,G,SAbI1D,CAAM,CACNmG,CAAI,CACJC,CAAM,E,iCAYV,QAASR,EACT,aAAc,CACV,gBAAiB,eACjB,eAAgB,2BACpB,EACA,aAAc,CAGV,WAAY,CAACS,EAAAA,CAAAA,CAAAA,aAAmB,CAAEA,EAAAA,CAAAA,CAAAA,gBAAsB,CAAC,CACzD,uBAAwB,EAAE,CAC1B,gBAAiB,GACjB,SAAU,CAACC,EAAIC,EAAcC,IAAOhC,EAAuB+B,EAC/D,EAEA,iBAAkB,AAAC,I,GAAA,CACfE,gBAAAA,CAAe,CACfC,gBAAAA,CAAe,CAClB,GACG,MACI,UAACC,EAAAA,CAAKA,CAAAA,CAAC,KAAM,G,SACT,WAAC,Q,UAAK,gBACEF,EAAgB,MAAM,CAAC,UAC1BA,EAAgB,MAAM,CAAG,GACtB,UAACG,EAAAA,EAAMA,CAAAA,CAAC,KAAK,OAAO,MAAO,CAAE,kBAAmB,CAAE,EAAG,QAASF,E,SAAiB,0B,OAOnG,EACA,uBAAwB,IAEhB,WAACC,EAAAA,CAAKA,CAAAA,CAAC,KAAM,G,UACT,UAACE,EAAAA,CAAUA,CAAAA,CACP,MAAM,2BACN,YAAY,wCACZ,UAAWpB,EACX,SAAUlB,AAA+B,IAA/BA,EAAoB,MAAM,CACpC,OAAO,eACP,WAAW,e,SAEX,UAACqC,EAAAA,EAAMA,CAAAA,CAAC,SAAUrC,AAA+B,IAA/BA,EAAoB,MAAM,C,SAAQ,c,KAExD,UAACsC,EAAAA,CAAUA,CAAAA,CACP,MAAM,2BACN,YAAY,wCACZ,UAAWlB,EACX,SAAUpB,AAA+B,IAA/BA,EAAoB,MAAM,CACpC,OAAO,eACP,WAAW,e,SAEX,UAACqC,EAAAA,EAAMA,CAAAA,CAAC,SAAUrC,AAA+B,IAA/BA,EAAoB,MAAM,C,SAAQ,c,QAKpE,QAAS,CACL,WAAY,GACZ,OAAQ,GACR,QAAS,GACT,QAAS,EACb,EACA,OAAQ,GACR,WAAY,CACR,SAAU,EACd,EACA,OACI,CACI,yBAA0B,GAC1B,EAAG,sBACH,EAAG,qBACP,EAEJ,OAAO,KACP,cAAe,IAAM,CAAC,UAACqC,EAAAA,EAAMA,CAAAA,CAAmB,KAAK,UAAU,QAASpB,E,SAAkB,sC,EAAxD,eAAwE,A,GAG1Gd,EAAqB,IAAI,EAAI,UAACoC,EAAoBA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CAAAA,EACrCpC,GAAAA,CACT,aAtVU,AAACU,IACvBT,EAA2BQ,AAAAA,IACvBA,EAAM,IAAI,CAAGC,CACjB,EACJ,C,MAuVJ,EClhBA,GATc,IACZ,uB,UACE,UAACa,EAAAA,CAAMA,CAAAA,C,SACL,UAAC,S,SAAM,4C,KAET,UAACc,GAAgBA,CAAAA,G"}