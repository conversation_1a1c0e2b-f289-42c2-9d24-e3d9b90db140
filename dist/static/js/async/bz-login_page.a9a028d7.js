"use strict";(self.webpackChunkweb_dashboard=self.webpackChunkweb_dashboard||[]).push([["0"],{2080:function(e,t,n){n.r(t),n.d(t,{default:()=>y});var r=n(59845),s=n(97458),a=n(94343),o=n(67509),i=n(62155),c=n(36920),l=n(68431),u=n(30113),d=n(52983),h=n(32053),{Timer:p}=o.Z,y=()=>{var e,t,n,[o]=(0,h.lr)(),[y,g]=(0,d.useState)(0),[j,x]=(0,d.useState)(""),[v,_]=(0,d.useState)(!1),[b,C]=(0,d.useState)({}),[S,P]=(0,d.useState)(!1),w=j,T=e=>{e&&(w=e),x(e)},k=o.get("machine_code");if(!k)return(0,s.jsx)(i.ZP,{status:"warning",title:"\u7F3A\u5931\u5FC5\u8981\u53C2\u6570"});var I=(e=(0,r._)(function*(){var e="/api/v1/users/login-status/by-machine-code/".concat(k),t=yield fetch(e,{method:"GET",headers:{"Content-Type":"application/json"}});return yield t.json()}),function(){return e.apply(this,arguments)}),Z=(t=(0,r._)(function*(){try{var e="/api/v1/users/qrcode/".concat(k),t=yield fetch(e,{method:"GET",headers:{"Content-Type":"application/json"}}),n=yield t.json();if(n.qrcode_base64)return n.qrcode_base64;return""}catch(e){return""}}),function(){return t.apply(this,arguments)}),N=()=>{I().then(e=>{console.log("checkLoginStatus",e),"online"===e.login_status?(_(!0),C(e)):(f(k),clearInterval(D),D=setInterval(()=>{Z().then(e=>{console.log("getErorCodeBase64",e),e&&T(e)})},3e3),clearInterval(E),E=setInterval(()=>{I().then(e=>{console.log("checkLoginStatus",e),"online"===e.login_status&&(_(!0),C(e),clearInterval(E))})},1e4))}).catch(e=>{console.log("checkLoginStatus",e),c.ZP.error("\u670D\u52A1\u5F02\u5E38"),P(!0)})},q=(n=(0,r._)(function*(){yield fetch("/api/v1/users/qrcode/upload",{method:"POST",body:JSON.stringify({machine_code:k,qrcode_base64:""}),headers:{"Content-Type":"application/json"}}),T(""),g(Date.now()),f(k),clearInterval(D),D=setInterval(()=>{Z().then(e=>{console.log("getErorCodeBase64",e),e!==w&&T(e)})},3e3)}),function(){return n.apply(this,arguments)}),D=null,E=null;(0,d.useEffect)(()=>{g(Date.now());try{N()}catch(e){console.log("error",e),c.ZP.error("\u670D\u52A1\u5F02\u5E38"),P(!0)}},[]);var L=j?(0,s.jsxs)("div",{className:"flex flex-col justify-start items-center gap-4",children:[(0,s.jsx)("p",{children:"\u8BF7\u4F7F\u7528\u624B\u673AB\u7AD9APP\u626B\u63CF\u4E8C\u7EF4\u7801\u767B\u5F55"}),(0,s.jsx)("img",{src:j,alt:""}),(0,s.jsx)("p",{children:"\u5982\u679C\u4E8C\u7EF4\u7801\u5931\u6548\uFF0C\u70B9\u51FB\u6309\u94AE"}),(0,s.jsx)(l.ZP,{type:"primary",onClick:q,children:"\u91CD\u65B0\u83B7\u53D6\u4E8C\u7EF4\u7801"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{className:"",children:"\u6B63\u5728\u83B7\u53D6\u8D26\u53F7\u767B\u5F55\u4E8C\u7EF4\u7801\u8BF7\u5728\u5F53\u524D\u9875\u9762\u7A0D\u7B491-3\u5206\u949F"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:["\u5DF2\u7B49\u5F85",(0,s.jsx)(p,{type:"countup",value:y,onChange:e=>{}})]}),(0,s.jsx)(u.Z,{size:"large"})]}),O=v?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.ZP,{status:"success",title:"\u767B\u5F55\u6210\u529F",subTitle:"\u5F53\u524D\u767B\u5F55\u8D26\u53F7\u4E3A\u3010".concat(b.username,"\u3011\uFF0Cuid\u4E3A\u3010").concat(b.user_id,"\u3011")}),(0,s.jsx)("div",{className:"flex flex-col justify-start items-center gap-4",children:(0,s.jsx)(l.ZP,{onClick:()=>{(function(e){m.apply(this,arguments)})(k),_(!1),T(""),g(Date.now()),clearInterval(D),clearInterval(E),setTimeout(()=>{N()},3e4)},children:"\u5C34\u5C2C\uFF0C\u767B\u9519\u8D26\u53F7\u4E86\uFF0C\u6211\u8981\u6362\u4E2A\u8D26\u53F7\u767B\u5F55"},"retry")})]}):(0,s.jsx)(s.Fragment,{children:L});return(0,s.jsxs)("div",{className:"container-box flex flex-col justify-start items-center gap-4",children:[(0,s.jsx)(a.q,{children:(0,s.jsx)("title",{children:"\u767B\u5F55\u8D26\u53F7"})}),S?(0,s.jsx)(i.ZP,{status:"error",title:"\u670D\u52A1\u5F02\u5E38"}):O]})};function f(e){return g.apply(this,arguments)}function g(){return(g=(0,r._)(function*(e){var t={ts:Date.now()};console.log("addLoginCommand",(yield j({machine_code:e,command_type:"login",ext_params:t,priority:0})))})).apply(this,arguments)}function m(){return(m=(0,r._)(function*(e){var t={ts:Date.now()};console.log("addLoginCommand",(yield j({machine_code:e,command_type:"relogin",ext_params:t,priority:0})))})).apply(this,arguments)}function j(e){return x.apply(this,arguments)}function x(){return(x=(0,r._)(function*(e){var t=yield fetch("/api/v1/commands/test/add",{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}});return yield t.json()})).apply(this,arguments)}}}]);