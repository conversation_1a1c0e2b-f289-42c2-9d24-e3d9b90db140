{"version": 3, "file": "static/js/async/reply-admin_reply-record-admin/page.417057b6.js", "sources": ["webpack://web-dashboard/./src/reply-admin/containers/ReplyRecordAdmin.tsx", "webpack://web-dashboard/./src/reply-admin/routes/reply-record-admin/page.tsx"], "sourcesContent": ["import { useState, useRef } from 'react';\r\nimport { useImmer } from 'use-immer'\r\nimport { Helmet } from '@modern-js/runtime/head';\r\nimport { PageContainer, ProTable } from '@ant-design/pro-components';\r\nimport type { ProColumns, ProFormColumnsType, ActionType } from '@ant-design/pro-components';\r\nimport { Button, Popconfirm, Space, Table, Switch, message, Modal } from 'antd';\r\nimport ModalSchemaForm, { type ModalSchemaFormProps } from '@/reply-admin/components/ModalSchemaForm'\r\n\r\n\r\nexport type TableListItem = {\r\n    /**\r\n     * 记录id\r\n     */\r\n    id: string;\r\n    /**\r\n     * 所属账号\r\n     */\r\n    user_info: {\r\n        username: string;\r\n    }\r\n    video_info: {\r\n        /**\r\n         * 所属视频标题\r\n         */\r\n        title: string;\r\n        /**\r\n        * 所属视频链接\r\n         */\r\n        video_url: string;\r\n    }\r\n    /**\r\n     * AI回复内容\r\n     */\r\n    reply_content: string;\r\n    /**\r\n     * 评论内容\r\n     */\r\n    comment_info: {\r\n        /**\r\n         * 评论内容\r\n         */\r\n        source_content: string;\r\n        /**\r\n         * 回复时间\r\n         */\r\n        reply_time: string;\r\n    };\r\n    /**\r\n     * 上下文\r\n     */\r\n    comment_context: string;\r\n\r\n    /**\r\n     * B站回复状态\r\n     */\r\n    status: string;\r\n    /**\r\n     * 满意标记\r\n     */\r\n    is_satisfied: boolean;\r\n}\r\n\r\nexport type TableListParams = {\r\n    page: number;\r\n    page_size: number;\r\n}\r\n\r\n/**\r\n * 获取用户列表\r\n * @param params \r\n * @returns \r\n */\r\nconst queryTableData = async (params: any): Promise<{\r\n    data: {\r\n        items: TableListItem[];\r\n        page: number;\r\n        page_size: number;\r\n        total: number;\r\n    }\r\n\r\n}> => {\r\n    console.log('queryTableData', params);\r\n    const searchParams = new URLSearchParams();\r\n    searchParams.append('page', params.current)\r\n    searchParams.append('page_size', params.pageSize)\r\n    if (params.video_id) {\r\n        searchParams.append('video_id', params.video_id)\r\n    }\r\n    const url = '/api/v1/comment-replies/list' + '?' + searchParams.toString()\r\n    const res = await fetch(url, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n    })\r\n    return res.json()\r\n}\r\n\r\nconst updateSatisfaction = async (id: string, is_satisfied: boolean) => {\r\n    const url = `/api/v1/comment-replies/${id}/update-satisfaction`\r\n    const body = {\r\n        is_satisfied: is_satisfied\r\n    }\r\n    const res = await fetch(url, {\r\n        method: 'POST',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(body)\r\n    })\r\n    return res.json()\r\n}\r\n\r\n\r\nconst Index = () => {\r\n    const tableRef = useRef<ActionType>();\r\n    const [isModalOpen, setIsModalOpen] = useState(false);\r\n    const [currentModalData, setCurrentModalData] = useState<TableListItem>({} as TableListItem);\r\n\r\n\r\n\r\n    const handleOk = async () => {\r\n        setIsModalOpen(false);\r\n        console.log('handleOk', currentModalData);\r\n        await updateSatisfaction(currentModalData.id, true);\r\n        setCurrentModalData({} as TableListItem);\r\n        if (tableRef && tableRef.current) {\r\n            // 刷新列表\r\n            tableRef.current.reload();\r\n        }\r\n    };\r\n\r\n    const handleCancel = async () => {\r\n        setIsModalOpen(false);\r\n        console.log('handleCancel', currentModalData);\r\n        await updateSatisfaction(currentModalData.id, false);\r\n        setCurrentModalData({} as TableListItem);\r\n        if (tableRef && tableRef.current) {\r\n            // 刷新列表\r\n            tableRef.current.reload();\r\n        }\r\n    };\r\n\r\n    const onClicksatisfy = (record: TableListItem) => {\r\n        console.log('onClicksatisfy', record);\r\n        setCurrentModalData(record);\r\n        setIsModalOpen(true);\r\n    }\r\n\r\n    const columns: ProColumns<TableListItem>[] = [\r\n        {\r\n            title: '所属账号',\r\n            width: 120,\r\n            ellipsis: true,\r\n            dataIndex: ['user_info', 'username'],\r\n        },\r\n        {\r\n            title: '所属平台',\r\n            width: 80,\r\n            key: 'platform',\r\n            render: () => <span>B站</span>\r\n        },\r\n        {\r\n            title: '所属视频',\r\n            width: 210,\r\n            ellipsis: true,\r\n            dataIndex: 'video_info',\r\n            render: (_, record) => <a href={record.video_info.video_url} target='_blank'>{record.video_info.title}</a>\r\n        },\r\n        {\r\n            title: 'AI回复内容',\r\n            width: 200,\r\n            ellipsis: true,\r\n            dataIndex: 'reply_content',\r\n        },\r\n        {\r\n            title: '评论',\r\n            width: 200,\r\n            ellipsis: true,\r\n            dataIndex: ['comment_info', 'source_content'],\r\n        },\r\n        {\r\n            title: '上下文(近10条)',\r\n            width: 200,\r\n            ellipsis: true,\r\n            dataIndex: 'comment_context',\r\n        },\r\n        {\r\n            title: '回复时间',\r\n            width: 180,\r\n            dataIndex: ['comment_info', 'reply_time'],\r\n            valueType: 'dateTime',\r\n        },\r\n        {\r\n            title: 'B站回复状态',\r\n            width: 120,\r\n            dataIndex: 'status',\r\n            fixed: 'right',\r\n            render: (_, record) => {\r\n                const statusMap: any = {\r\n                    'completed': '成功',\r\n                    'failed': '失败',\r\n                    'pending': '等待中',\r\n                    'processing': '处理中',\r\n                }\r\n                return <> {\r\n                    typeof statusMap[record.status] === 'string' ?\r\n                        <span>{statusMap[record.status]}</span> : _\r\n                } </>\r\n            }\r\n        },\r\n        {\r\n            title: '满意标记',\r\n            width: 80,\r\n            dataIndex: 'is_satisfied',\r\n            fixed: 'right',\r\n            render: (_, record) => {\r\n                return <> {\r\n                    typeof record.is_satisfied === 'boolean' ?\r\n                        record.is_satisfied ? <span>满意</span> : <span>不满意</span>\r\n                        : _\r\n                } </>\r\n            }\r\n        },\r\n        {\r\n            title: '操作',\r\n            width: 80,\r\n            key: 'option',\r\n            valueType: 'option',\r\n            fixed: 'right',\r\n            render: (_, record) => [\r\n                <a key=\"satisfy\" onClick={() => onClicksatisfy(record)}>标记</a>\r\n            ]\r\n        },\r\n    ];\r\n\r\n    return (\r\n        <PageContainer\r\n            style={{\r\n                height: '100vh',\r\n                overflow: 'hidden'\r\n            }}>\r\n            <Helmet>\r\n                <title>账号管理</title>\r\n            </Helmet>\r\n            {/* {contextHolder} */}\r\n\r\n            <ProTable<TableListItem, TableListParams>\r\n                actionRef={tableRef}\r\n                request={async (\r\n                    // 第一个参数 params 查询表单和 params 参数的结合\r\n                    // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范\r\n                    params: any,\r\n                    sort,\r\n                    filter,\r\n                ) => {\r\n                    // 这里需要返回一个 Promise,在返回之前你可以进行数据转化\r\n                    // 如果需要转化参数可以在这里进行修改\r\n                    const res = await queryTableData(params);\r\n                    console.log('data', res.data);\r\n                    return {\r\n                        data: res.data.items,\r\n                        success: true,\r\n                        total: res.data.total,\r\n                    }\r\n                }}\r\n                columns={columns}\r\n                columnsState={{\r\n                    persistenceType: 'localStorage',\r\n                    persistenceKey: 'reply-admin/account-admin'\r\n                }}\r\n                // rowSelection={{\r\n                //     // 自定义选择项参考: https://ant.design/components/table-cn/#components-table-demo-row-selection-custom\r\n                //     // 注释该行则默认不显示下拉选项\r\n                //     selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],\r\n                //     defaultSelectedRowKeys: [],\r\n                //     alwaysShowAlert: true,\r\n                //     onChange: (_1, selectedRows, _2) => setCurrentSelectedRows(selectedRows)\r\n                // }}\r\n\r\n                // tableAlertRender={({\r\n                //     selectedRowKeys,\r\n                //     onCleanSelected,\r\n                // }) => {\r\n                //     return (\r\n                //         <Space size={24}>\r\n                //             <span>\r\n                //                 已选 {selectedRowKeys.length} 项\r\n                //                 {selectedRowKeys.length > 0 && (\r\n                //                     <Button type=\"link\" style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>\r\n                //                         取消选择\r\n                //                     </Button>\r\n                //                 )}\r\n                //             </span>\r\n                //         </Space>\r\n                //     );\r\n                // }}\r\n                // tableAlertOptionRender={() => {\r\n                //     return (\r\n                //         <Space size={16}>\r\n                //             <Popconfirm\r\n                //                 title=\"批量启用\"\r\n                //                 description=\"是否批量启用?\"\r\n                //                 onConfirm={onClickBatchEnable}\r\n                //                 disabled={currentSelectedRows.length === 0}\r\n                //                 okText=\"确认\"\r\n                //                 cancelText=\"取消\"\r\n                //             >\r\n                //                 <Button disabled={currentSelectedRows.length === 0}>启用</Button>\r\n                //             </Popconfirm>\r\n                //             <Popconfirm\r\n                //                 title=\"批量禁用\"\r\n                //                 description=\"是否批量禁用?\"\r\n                //                 onConfirm={onClickBatchDisable}\r\n                //                 disabled={currentSelectedRows.length === 0}\r\n                //                 okText=\"确认\"\r\n                //                 cancelText=\"取消\"\r\n                //             >\r\n                //                 <Button disabled={currentSelectedRows.length === 0}>禁用</Button>\r\n                //             </Popconfirm>\r\n                //         </Space>\r\n                //     );\r\n                // }}\r\n                options={{\r\n                    fullScreen: false,\r\n                    reload: false,\r\n                    density: true,\r\n                    setting: true\r\n                }}\r\n                search={false}\r\n                pagination={{\r\n                    pageSize: 50,\r\n                }}\r\n                scroll={\r\n                    {\r\n                        scrollToFirstRowOnChange: true,\r\n                        y: 'calc(100vh - 340px)',\r\n                    }\r\n                }\r\n                rowKey=\"id\"\r\n            />\r\n            <Modal\r\n                title=\"标记回复\"\r\n                open={isModalOpen}\r\n                onOk={handleOk}\r\n                onCancel={handleCancel}\r\n                footer={[\r\n                    <Button key=\"back\" onClick={handleCancel}>\r\n                        不满意\r\n                    </Button>,\r\n                    <Button key=\"submit\" type=\"primary\" onClick={handleOk}>\r\n                        满意\r\n                    </Button>\r\n                ]}\r\n            >\r\n                <p>标记回复是否满意</p>\r\n            </Modal>\r\n        </PageContainer>\r\n    );\r\n}\r\n\r\nexport default Index;\r\n", "import { Helmet } from '@modern-js/runtime/head';\r\nimport ReplyRecordAdmin from '@/reply-admin/containers/ReplyRecordAdmin'\r\nimport './index.css';\r\n\r\nconst Index = () => (\r\n  <>\r\n    <Helmet>\r\n      <title>回复记录管理</title>\r\n    </Helmet>\r\n    <ReplyRecordAdmin></ReplyRecordAdmin>\r\n  </>\r\n);\r\n\r\nexport default Index;\r\n"], "names": ["queryTableData", "params", "console", "searchParams", "URLSearchParams", "url", "res", "fetch", "updateSatisfaction", "id", "is_satisfied", "JSON", "tableRef", "useRef", "isModalOpen", "setIsModalOpen", "useState", "currentModalData", "setCurrentModalData", "handleOk", "handleCancel", "onClicksatisfy", "record", "columns", "_", "statusMap", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ProTable", "sort", "filter", "Modal", "<PERSON><PERSON>", "ReplyRecordAdmin"], "mappings": "oPAwEMA,G,EAAiB,kBAAOC,CAAM,EAShCC,QAAQ,GAAG,CAAC,iBAAkBD,GAC9B,IAAME,EAAe,IAAIC,gBACzBD,EAAa,MAAM,CAAC,OAAQF,EAAO,OAAO,EAC1CE,EAAa,MAAM,CAAC,YAAaF,EAAO,QAAQ,EAC5CA,EAAO,QAAQ,EACfE,EAAa,MAAM,CAAC,WAAYF,EAAO,QAAQ,EAEnD,IAAMI,EAAM,gCAAuCF,EAAa,QAAQ,GAOxE,MAAOG,AANK,OAAMC,MAAMF,EAAK,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,CACJ,EAAC,EACU,IAAI,EACnB,G,SAxB8BJ,CAAM,E,iCA0B9BO,G,EAAqB,kBAAOC,CAAE,CAAUC,CAAY,EAYtD,MAAOJ,AAPK,OAAMC,MAJL,2BAA6B,OAAHE,EAAG,wBAIb,CACzB,OAAQ,OACR,QAAS,CACL,eAAgB,kBACpB,EACA,KAAME,KAAK,SAAS,CARX,CACT,aAAcD,CAClB,EAOA,EAAC,EACU,IAAI,EACnB,G,SAbkCD,CAAE,CAAUC,CAAY,E,iCAuQ1D,EAvPc,KACV,I,MAAME,EAAWC,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,IACX,CAACC,EAAaC,EAAe,CAAGC,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IACzC,CAACC,EAAkBC,EAAoB,CAAGF,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAwB,CAAC,GAInEG,G,EAAW,oBACbJ,EAAe,IACfb,QAAQ,GAAG,CAAC,WAAYe,GACxB,MAAMT,EAAmBS,EAAiB,EAAE,CAAE,IAC9CC,EAAoB,CAAC,GACjBN,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,EAE/B,G,4CAEMQ,G,EAAe,oBACjBL,EAAe,IACfb,QAAQ,GAAG,CAAC,eAAgBe,GAC5B,MAAMT,EAAmBS,EAAiB,EAAE,CAAE,IAC9CC,EAAoB,CAAC,GACjBN,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,EAE/B,G,4CAEMS,EAAiB,AAACC,IACpBpB,QAAQ,GAAG,CAAC,iBAAkBoB,GAC9BJ,EAAoBI,GACpBP,EAAe,GACnB,EAEMQ,EAAuC,CACzC,CACI,MAAO,2BACP,MAAO,IACP,SAAU,GACV,UAAW,CAAC,YAAa,WAAW,AACxC,EACA,CACI,MAAO,2BACP,MAAO,GACP,IAAK,WACL,OAAQ,IAAM,UAAC,Q,SAAK,S,EACxB,EACA,CACI,MAAO,2BACP,MAAO,IACP,SAAU,GACV,UAAW,aACX,OAAQ,CAACC,EAAGF,IAAW,UAAC,KAAE,KAAMA,EAAO,UAAU,CAAC,SAAS,CAAE,OAAO,S,SAAUA,EAAO,UAAU,CAAC,KAAK,A,EACzG,EACA,CACI,MAAO,6BACP,MAAO,IACP,SAAU,GACV,UAAW,eACf,EACA,CACI,MAAO,eACP,MAAO,IACP,SAAU,GACV,UAAW,CAAC,eAAgB,iBAAiB,AACjD,EACA,CACI,MAAO,qCACP,MAAO,IACP,SAAU,GACV,UAAW,iBACf,EACA,CACI,MAAO,2BACP,MAAO,IACP,UAAW,CAAC,eAAgB,aAAa,CACzC,UAAW,UACf,EACA,CACI,MAAO,kCACP,MAAO,IACP,UAAW,SACX,MAAO,QACP,OAAQ,CAACE,EAAGF,KACR,IAAMG,EAAiB,CACnB,UAAa,eACb,OAAU,eACV,QAAW,qBACX,WAAc,oBAClB,EACA,MAAO,uB,UAAE,IACL,AAAoC,UAApC,OAAOA,CAAS,CAACH,EAAO,MAAM,CAAC,CAC3B,UAAC,Q,SAAMG,CAAS,CAACH,EAAO,MAAM,CAAC,A,GAAWE,EACjD,I,EACL,CACJ,EACA,CACI,MAAO,2BACP,MAAO,GACP,UAAW,eACX,MAAO,QACP,OAAQ,CAACA,EAAGF,IACD,uB,UAAE,IACL,AAA+B,WAA/B,OAAOA,EAAO,YAAY,CACtBA,EAAO,YAAY,CAAG,UAAC,Q,SAAK,c,GAAY,UAAC,Q,SAAK,oB,GAC5CE,EACT,I,EAET,EACA,CACI,MAAO,eACP,MAAO,GACP,IAAK,SACL,UAAW,SACX,MAAO,QACP,OAAQ,CAACA,EAAGF,IAAW,CACnB,UAAC,KAAgB,QAAS,IAAMD,EAAeC,G,SAAS,c,EAAjD,WACV,AACL,EACH,CAED,MACI,WAACI,EAAAA,EAAaA,CAAAA,CACV,MAAO,CACH,OAAQ,QACR,SAAU,QACd,E,UACA,UAACC,EAAAA,CAAMA,CAAAA,C,SACH,UAAC,S,SAAM,0B,KAIX,UAACC,EAAAA,CAAQA,CAAAA,CACL,UAAWhB,EACX,OAAO,E,EAAE,kBAGLX,CAAM,CACN4B,CAAI,CACJC,CAAM,EAIN,IAAMxB,EAAM,MAAMN,EAAeC,GAEjC,OADAC,QAAQ,GAAG,CAAC,OAAQI,EAAI,IAAI,EACrB,CACH,KAAMA,EAAI,IAAI,CAAC,KAAK,CACpB,QAAS,GACT,MAAOA,EAAI,IAAI,CAAC,KAAK,AACzB,CACJ,G,SAbIL,CAAM,CACN4B,CAAI,CACJC,CAAM,E,iCAYV,QAASP,EACT,aAAc,CACV,gBAAiB,eACjB,eAAgB,2BACpB,EAqDA,QAAS,CACL,WAAY,GACZ,OAAQ,GACR,QAAS,GACT,QAAS,EACb,EACA,OAAQ,GACR,WAAY,CACR,SAAU,EACd,EACA,OACI,CACI,yBAA0B,GAC1B,EAAG,qBACP,EAEJ,OAAO,I,GAEX,UAACQ,EAAAA,CAAKA,CAAAA,CACF,MAAM,2BACN,KAAMjB,EACN,KAAMK,EACN,SAAUC,EACV,OAAQ,CACJ,UAACY,EAAAA,EAAMA,CAAAA,CAAY,QAASZ,E,SAAc,oB,EAA9B,QAGZ,UAACY,EAAAA,EAAMA,CAAAA,CAAc,KAAK,UAAU,QAASb,E,SAAU,c,EAA3C,UAGf,C,SAED,UAAC,K,SAAE,kD,OAInB,EC1VA,EATc,IACZ,uB,UACE,UAACQ,EAAAA,CAAMA,CAAAA,C,SACL,UAAC,S,SAAM,sC,KAET,UAACM,EAAgBA,CAAAA,G"}