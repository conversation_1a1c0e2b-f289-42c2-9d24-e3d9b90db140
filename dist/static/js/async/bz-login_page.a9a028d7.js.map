{"version": 3, "file": "static/js/async/bz-login_page.a9a028d7.js", "sources": ["webpack://web-dashboard/./src/bz-login/routes/page.tsx"], "sourcesContent": ["import { Helmet } from '@modern-js/runtime/head';\r\nimport { <PERSON><PERSON><PERSON>, Spin, Button, Result, message } from 'antd';\r\nimport { useEffect, useState } from 'react'\r\nimport { useSearchParams } from '@modern-js/runtime/router';\r\nimport './index.css';\r\nconst { Timer } = Statistic;\r\n\r\ntype CommandType = 'login' | \"relogin\"\r\n\r\ntype CommandParams = {\r\n    machine_code: string;\r\n    command_type: CommandType;\r\n    ext_params: Record<string, any>;\r\n    priority: number;\r\n}\r\n\r\nconst Index = () => {\r\n    const [searchParams] = useSearchParams();\r\n\r\n    const [before, setBefore] = useState(0);\r\n    const [erCode, setErCode] = useState('');\r\n    const [loginStatus, setLoginStatus] = useState(false);\r\n    const [userInfo, setUserInfo] = useState({} as any)\r\n    const [isError, setIsError] = useState(false);\r\n    let oldErCode: string = erCode;\r\n\r\n    const setErCodeBase64 = (base64: string) => {\r\n        if (base64) {\r\n            oldErCode = base64;\r\n        }\r\n        setErCode(base64)\r\n\r\n    }\r\n\r\n    // 机器码\r\n    const machine_code = searchParams.get('machine_code');\r\n\r\n    if (!machine_code) {\r\n        return (\r\n            <Result\r\n                status=\"warning\"\r\n                title=\"缺失必要参数\"\r\n            />\r\n        )\r\n    }\r\n\r\n    const onChange = (value: any) => {\r\n\r\n    };\r\n\r\n    const checkLoginStatus = async () => {\r\n        const url = `/api/v1/users/login-status/by-machine-code/${machine_code}`\r\n        const method = 'GET'\r\n        const res = await fetch(url, {\r\n            method,\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            }\r\n        })\r\n        const result = await res.json()\r\n        return result\r\n    }\r\n\r\n    const getErorCodeBase64 = async () => {\r\n        try {\r\n            const url = `/api/v1/users/qrcode/${machine_code}`\r\n            const method = 'GET'\r\n            const res = await fetch(url, {\r\n                method,\r\n                headers: {\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            })\r\n            const result = await res.json()\r\n            if (result.qrcode_base64) {\r\n                return result.qrcode_base64\r\n            } else {\r\n                return ''\r\n            }\r\n        } catch (error) {\r\n            return ''\r\n        }\r\n    }\r\n\r\n    const init = () => {\r\n        checkLoginStatus().then(res => {\r\n            console.log('checkLoginStatus', res)\r\n            if (res.login_status === \"online\") {\r\n                setLoginStatus(true)\r\n                setUserInfo(res)\r\n            } else {\r\n                addLoginCommand(machine_code)\r\n                clearInterval(timer)\r\n                timer = setInterval(() => {\r\n                    getErorCodeBase64().then(res => {\r\n                        console.log('getErorCodeBase64', res)\r\n                        if (res) {\r\n                            setErCodeBase64(res)\r\n                        }\r\n                    })\r\n                }, 3000)\r\n                clearInterval(loginTimer)\r\n                loginTimer = setInterval(() => {\r\n                    checkLoginStatus().then(res => {\r\n                        console.log('checkLoginStatus', res)\r\n                        if (res.login_status === \"online\") {\r\n                            setLoginStatus(true)\r\n                            setUserInfo(res)\r\n                            clearInterval(loginTimer)\r\n                        }\r\n                    })\r\n                }, 10000)\r\n            }\r\n\r\n        }).catch(err => {\r\n            console.log('checkLoginStatus', err)\r\n            message.error('服务异常')\r\n            setIsError(true)\r\n        })\r\n    }\r\n\r\n    const onClickRelogin = () => {\r\n        addReLoginCommand(machine_code)\r\n        setLoginStatus(false)\r\n        setErCodeBase64('')\r\n        setBefore(Date.now())\r\n        clearInterval(timer)\r\n        clearInterval(loginTimer)\r\n        setTimeout(() => {\r\n            init()\r\n        }, 30 * 1000);\r\n    }\r\n\r\n    const onClickRefresh = async () => {\r\n        await fetch('/api/v1/users/qrcode/upload', {\r\n            method: 'POST',\r\n            body: JSON.stringify({\r\n                machine_code,\r\n                qrcode_base64: ''\r\n            }),\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            }\r\n        })\r\n\r\n        setErCodeBase64('')\r\n        setBefore(Date.now())\r\n        addLoginCommand(machine_code)\r\n        clearInterval(timer)\r\n        timer = setInterval(() => {\r\n            getErorCodeBase64().then(res => {\r\n                console.log('getErorCodeBase64', res)\r\n                if (res !== oldErCode) {\r\n                    setErCodeBase64(res)\r\n                    // clearInterval(timer)\r\n                }\r\n            })\r\n        }, 3000)\r\n    }\r\n\r\n    let timer: any = null;\r\n    let loginTimer: any = null;\r\n\r\n\r\n\r\n    useEffect(() => {\r\n        setBefore(Date.now()) // 开始计时\r\n        try {\r\n            init()\r\n        } catch (error) {\r\n            console.log('error', error)\r\n            message.error('服务异常')\r\n            setIsError(true)\r\n        }\r\n\r\n    }, [])\r\n\r\n    const ErCodeDispplay = erCode ? (\r\n        <div className='flex flex-col justify-start items-center gap-4'>\r\n            <p>请使用手机B站APP扫描二维码登录</p>\r\n            <img src={erCode} alt=\"\" />\r\n\r\n            {/* <p>注意，请使用【账户名】进行登录</p> */}\r\n            <p>如果二维码失效，点击按钮</p>\r\n            <Button type=\"primary\" onClick={onClickRefresh}>重新获取二维码</Button>\r\n        </div>\r\n    ) : (\r\n        <>\r\n            <p className=''>正在获取账号登录二维码请在当前页面稍等1-3分钟</p>\r\n            <div className=\"flex items-center gap-4\">已等待<Timer type=\"countup\" value={before} onChange={onChange} /></div>\r\n            <Spin size=\"large\" />\r\n        </>\r\n\r\n    )\r\n\r\n    const LoginStatusDispplay = loginStatus ? (\r\n        <>\r\n            <Result\r\n                status=\"success\"\r\n                title=\"登录成功\"\r\n                subTitle={`当前登录账号为【${userInfo.username}】，uid为【${userInfo.user_id}】`}\r\n            />\r\n            <div className='flex flex-col justify-start items-center gap-4'>\r\n                {/* <Button type=\"primary\" key=\"close\" onClick={onClickClose}>关闭当前页面（30秒后自动关闭）</Button> */}\r\n                <Button key=\"retry\" onClick={onClickRelogin}>尴尬，登错账号了，我要换个账号登录</Button>\r\n            </div>\r\n        </>\r\n    ) : (\r\n        <>\r\n            {ErCodeDispplay}\r\n        </>\r\n    )\r\n\r\n    return (\r\n        <div className=\"container-box flex flex-col justify-start items-center gap-4\" >\r\n            <Helmet>\r\n                <title>登录账号</title>\r\n            </Helmet>\r\n            {isError ? (\r\n                <Result\r\n                    status=\"error\"\r\n                    title=\"服务异常\"\r\n                />)\r\n                : LoginStatusDispplay}\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default Index;\r\n\r\n\r\nasync function addLoginCommand(machine_code: string) {\r\n\r\n    const command_type = \"login\"\r\n    const ext_params = {\r\n        ts: Date.now(),\r\n    }\r\n    const result = await addCommand({\r\n        machine_code: machine_code,\r\n        command_type,\r\n        ext_params,\r\n        priority: 0\r\n    })\r\n    console.log('addLoginCommand', result)\r\n}\r\n\r\nasync function addReLoginCommand(machine_code: string) {\r\n\r\n    const command_type = \"relogin\"\r\n    const ext_params = {\r\n        ts: Date.now(),\r\n    }\r\n    const result = await addCommand({\r\n        machine_code: machine_code,\r\n        command_type,\r\n        ext_params,\r\n        priority: 0\r\n    })\r\n    console.log('addLoginCommand', result)\r\n}\r\n\r\nasync function addCommand(data: CommandParams) {\r\n    const url = '/api/v1/commands/test/add'\r\n    const res = await fetch(url, {\r\n        method: 'POST',\r\n        body: JSON.stringify(data),\r\n        headers: {\r\n            'Content-Type': 'application/json'\r\n        }\r\n    })\r\n    return await res.json()\r\n}"], "names": ["Timer", "Statistic", "searchParams", "useSearchParams", "before", "setBefore", "useState", "erCode", "setErCode", "loginStatus", "setLoginStatus", "userInfo", "setUserInfo", "isError", "setIsError", "oldErCode", "setErCodeBase64", "base64", "machine_code", "Result", "checkLoginStatus", "url", "res", "fetch", "method", "getErorCodeBase64", "result", "error", "init", "console", "addLoginCommand", "clearInterval", "timer", "setInterval", "loginTimer", "err", "message", "onClickRefresh", "JSON", "Date", "useEffect", "ErCodeDispplay", "<PERSON><PERSON>", "value", "Spin", "LoginStatusDispplay", "addReLoginCommand", "_addReLoginCommand", "setTimeout", "<PERSON><PERSON><PERSON>", "_addLoginCommand", "ext_params", "addCommand", "command_type", "_addCommand", "data"], "mappings": "mQAKM,CAAEA,MAAAA,CAAK,CAAE,CAAGC,EAAAA,CAASA,CA+N3B,EApNc,KACV,I,MAAM,CAACC,EAAa,CAAGC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAEjB,CAACC,EAAQC,EAAU,CAAGC,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,GAC/B,CAACC,EAAQC,EAAU,CAAGF,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IAC/B,CAACG,EAAaC,EAAe,CAAGJ,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IACzC,CAACK,EAAUC,EAAY,CAAGN,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,CAAC,GACpC,CAACO,EAASC,EAAW,CAAGR,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IACnCS,EAAoBR,EAElBS,EAAkB,AAACC,IACjBA,GACAF,CAAAA,EAAYE,CAAK,EAErBT,EAAUS,EAEd,EAGMC,EAAehB,EAAa,GAAG,CAAC,gBAEtC,GAAI,CAACgB,EACD,MACI,UAACC,EAAAA,EAAMA,CAAAA,CACH,OAAO,UACP,MAAM,sC,GASlB,IAAMC,G,EAAmB,oBACrB,IAAMC,EAAO,8CAA0D,OAAbH,GAEpDI,EAAM,MAAMC,MAAMF,EAAK,CACzBG,OAFW,MAGX,QAAS,CACL,eAAgB,kBACpB,CACJ,GAEA,OADe,MAAMF,EAAI,IAAI,EAEjC,G,4CAEMG,G,EAAoB,oBACtB,GAAI,CACA,IAAMJ,EAAO,wBAAoC,OAAbH,GAE9BI,EAAM,MAAMC,MAAMF,EAAK,CACzBG,OAFW,MAGX,QAAS,CACL,eAAgB,kBACpB,CACJ,GACME,EAAS,MAAMJ,EAAI,IAAI,GAC7B,GAAII,EAAO,aAAa,CACpB,OAAOA,EAAO,aAAa,CAE3B,MAAO,EAEf,CAAE,MAAOC,EAAO,CACZ,MAAO,EACX,CACJ,G,4CAEMC,EAAO,KACTR,IAAmB,IAAI,CAACE,AAAAA,IACpBO,QAAQ,GAAG,CAAC,mBAAoBP,GAC5BA,AAAqB,WAArBA,EAAI,YAAY,EAChBZ,EAAe,IACfE,EAAYU,KAEZQ,EAAgBZ,GAChBa,cAAcC,GACdA,EAAQC,YAAY,KAChBR,IAAoB,IAAI,CAACH,AAAAA,IACrBO,QAAQ,GAAG,CAAC,oBAAqBP,GAC7BA,GACAN,EAAgBM,EAExB,EACJ,EAAG,KACHS,cAAcG,GACdA,EAAaD,YAAY,KACrBb,IAAmB,IAAI,CAACE,AAAAA,IACpBO,QAAQ,GAAG,CAAC,mBAAoBP,GACP,WAArBA,EAAI,YAAY,GAChBZ,EAAe,IACfE,EAAYU,GACZS,cAAcG,GAEtB,EACJ,EAAG,KAGX,GAAG,KAAK,CAACC,AAAAA,IACLN,QAAQ,GAAG,CAAC,mBAAoBM,GAChCC,EAAAA,EAAAA,CAAAA,KAAa,CAAC,4BACdtB,EAAW,GACf,EACJ,EAcMuB,G,EAAiB,oBACnB,MAAMd,MAAM,8BAA+B,CACvC,OAAQ,OACR,KAAMe,KAAK,SAAS,CAAC,CACjBpB,aAAAA,EACA,cAAe,EACnB,GACA,QAAS,CACL,eAAgB,kBACpB,CACJ,GAEAF,EAAgB,IAChBX,EAAUkC,KAAK,GAAG,IAClBT,EAAgBZ,GAChBa,cAAcC,GACdA,EAAQC,YAAY,KAChBR,IAAoB,IAAI,CAACH,AAAAA,IACrBO,QAAQ,GAAG,CAAC,oBAAqBP,GAC7BA,IAAQP,GACRC,EAAgBM,EAGxB,EACJ,EAAG,IACP,G,4CAEIU,EAAa,KACbE,EAAkB,KAItBM,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACNnC,EAAUkC,KAAK,GAAG,IAClB,GAAI,CACAX,GACJ,CAAE,MAAOD,EAAO,CACZE,QAAQ,GAAG,CAAC,QAASF,GACrBS,EAAAA,EAAAA,CAAAA,KAAa,CAAC,4BACdtB,EAAW,GACf,CAEJ,EAAG,EAAE,EAEL,IAAM2B,EAAiBlC,EACnB,WAAC,OAAI,UAAU,iD,UACX,UAAC,K,SAAE,oF,GACH,UAAC,OAAI,IAAKA,EAAQ,IAAI,E,GAGtB,UAAC,K,SAAE,0E,GACH,UAACmC,EAAAA,EAAMA,CAAAA,CAAC,KAAK,UAAU,QAASL,E,SAAgB,4C,MAGpD,uB,UACI,UAAC,KAAE,UAAU,G,SAAG,mI,GAChB,WAAC,OAAI,UAAU,0B,UAA0B,qBAAG,UAACrC,EAAAA,CAAM,KAAK,UAAU,MAAOI,EAAQ,SA/IxE,AAACuC,IAElB,C,MA8IQ,UAACC,EAAAA,CAAIA,CAAAA,CAAC,KAAK,O,MAKbC,EAAsBpC,EACxB,uB,UACI,UAACU,EAAAA,EAAMA,CAAAA,CACH,OAAO,UACP,MAAM,2BACN,SAAW,mDAAqCR,MAAAA,CAA3BA,EAAS,QAAQ,CAAC,+BAA0B,OAAjBA,EAAS,OAAO,CAAC,S,GAErE,UAAC,OAAI,UAAU,iD,SAEX,UAAC+B,EAAAA,EAAMA,CAAAA,CAAa,QAnFT,KACnBI,A,UA4HyB5B,CAAoB,EAAtC6B,EAAAA,KAAAA,CAAAA,IAAAA,CAAAA,U,GA5HW7B,GAClBR,EAAe,IACfM,EAAgB,IAChBX,EAAUkC,KAAK,GAAG,IAClBR,cAAcC,GACdD,cAAcG,GACdc,WAAW,KACPpB,GACJ,EAAG,IACP,E,SAyEyD,wG,EAAjC,Q,MAIpB,sB,SACKa,C,GAIT,MACI,WAAC,OAAI,UAAU,+D,UACX,UAACQ,EAAAA,CAAMA,CAAAA,C,SACH,UAAC,S,SAAM,0B,KAEVpC,EACG,UAACM,EAAAA,EAAMA,CAAAA,CACH,OAAO,QACP,MAAM,0B,GAER0B,E,EAGlB,E,SAKeK,EAAgBhC,CAAoB,E,OAApCgC,EAAAA,KAAAA,CAAAA,IAAAA,CAAAA,U,UAAAA,I,MAAAA,AAAAA,CAAAA,EAAf,kBAA+BhC,CAAoB,EAG/C,IAAMiC,EAAa,CACf,GAAIZ,KAAK,GAAG,EAChB,EAOAV,QAAQ,GAAG,CAAC,kBANG,OAAMuB,EAAW,CAC5B,aAAclC,EACdmC,aANiB,QAOjBF,WAAAA,EACA,SAAU,CACd,EAAC,EAEL,EAAC,EAbcD,KAAAA,CAAAA,IAAAA,CAAAA,U,UAeAH,I,MAAAA,AAAAA,CAAAA,EAAf,kBAAiC7B,CAAoB,EAGjD,IAAMiC,EAAa,CACf,GAAIZ,KAAK,GAAG,EAChB,EAOAV,QAAQ,GAAG,CAAC,kBANG,OAAMuB,EAAW,CAC5B,aAAclC,EACdmC,aANiB,UAOjBF,WAAAA,EACA,SAAU,CACd,EAAC,EAEL,EAAC,EAbcJ,KAAAA,CAAAA,IAAAA,CAAAA,U,UAeAO,EAAWC,CAAmB,E,OAA9BD,EAAAA,KAAAA,CAAAA,IAAAA,CAAAA,U,UAAAA,I,MAAAA,AAAAA,CAAAA,EAAf,kBAA0BC,CAAmB,EAEzC,IAAMjC,EAAM,MAAMC,MADN,4BACiB,CACzB,OAAQ,OACR,KAAMe,KAAK,SAAS,CAACiB,GACrB,QAAS,CACL,eAAgB,kBACpB,CACJ,GACA,OAAO,MAAMjC,EAAI,IAAI,EACzB,EAAC,EAVcgC,KAAAA,CAAAA,IAAAA,CAAAA,U"}