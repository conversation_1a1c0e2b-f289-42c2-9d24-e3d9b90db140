{"version": 3, "file": "static/js/async/reply-admin_account-admin/page.b347f5a9.js", "sources": ["webpack://web-dashboard/./src/reply-admin/components/ModalSchemaForm/index.tsx", "webpack://web-dashboard/./src/reply-admin/containers/AccountAdmin.tsx", "webpack://web-dashboard/./src/reply-admin/routes/account-admin/page.tsx"], "sourcesContent": ["import { BetaSchemaForm } from '@ant-design/pro-components';\r\nimport type { ProFormColumnsType, ModalFormProps, ProFormProps } from '@ant-design/pro-components';\r\nimport { Form } from 'antd';\r\n\r\nexport type ModalSchemaFormProps<T> = ModalFormProps & {\r\n    request?: ProFormProps['request'];\r\n    /**\r\n     * 表单列定义\r\n     */\r\n    columns: ProFormColumnsType<T>[];\r\n    /**\r\n     * 表单初始值\r\n     */\r\n    initialValues?: Partial<T>;\r\n    disabledKeys?: string[];\r\n}\r\n\r\nconst ModalSchemaForm = <T extends Record<string, any>>(props: ModalSchemaFormProps<T>) => {\r\n    const { trigger, modalProps, columns: originalColumns, initialValues, submitter, open, disabledKeys, onFinish, onOpenChange } = props;\r\n    const [form] = Form.useForm<T>();\r\n\r\n    // 创建一个新的columns数组\r\n    const columns = originalColumns.map(column => {\r\n        if (disabledKeys?.includes(column.dataIndex as string)) {\r\n            return {\r\n                ...column,\r\n                fieldProps: {\r\n                    ...(column.fieldProps || {}),\r\n                    disabled: true\r\n                }\r\n            };\r\n        }\r\n        return column;\r\n    });\r\n\r\n    return (\r\n        <BetaSchemaForm<T>\r\n            open={open}\r\n            onOpenChange={onOpenChange}\r\n            form={form}\r\n            trigger={trigger}\r\n            layoutType='ModalForm'\r\n            onFinish={onFinish}\r\n            modalProps={modalProps}\r\n            initialValues={initialValues}\r\n            columns={columns}\r\n            submitter={submitter}\r\n        />\r\n    );\r\n}\r\n\r\nexport default ModalSchemaForm;", "import { useState, useRef } from 'react';\r\nimport { useImmer } from 'use-immer'\r\nimport { Helmet } from '@modern-js/runtime/head';\r\nimport { PageContainer, ProTable } from '@ant-design/pro-components';\r\nimport type { ProColumns, ProFormColumnsType, ActionType } from '@ant-design/pro-components';\r\nimport { Button, Popconfirm, Space, Table, Switch, message } from 'antd';\r\nimport ModalSchemaForm, { type ModalSchemaFormProps } from '@/reply-admin/components/ModalSchemaForm'\r\n\r\n\r\nexport type TableListItem = {\r\n    /**\r\n     * 记录id\r\n     */\r\n    id: string;\r\n    /**\r\n     * 账号名称\r\n     */\r\n    username: string;\r\n    /**\r\n     * uid\r\n     */\r\n    user_id: string;\r\n    /**\r\n     * 创建时间\r\n     */\r\n    created_at: string;\r\n    /**\r\n     * 账号状态\r\n     */\r\n    is_disabled: boolean;\r\n    /**\r\n     * 关联视频\r\n     */\r\n    video_count: number;\r\n}\r\n\r\nexport type TableListParams = {\r\n    page: number;\r\n    page_size: number;\r\n}\r\n\r\ntype DataItem = {\r\n    platform: string;\r\n    username: string;\r\n    user_id: string;\r\n};\r\n\r\nconst modalColumns: ProFormColumnsType<DataItem>[] = [\r\n    {\r\n        title: '所属平台',\r\n        dataIndex: 'platform',\r\n        valueType: 'select',\r\n        valueEnum: {\r\n            bz: {\r\n                text: 'B站',\r\n                status: 'bz'\r\n            }\r\n        },\r\n        fieldProps: {\r\n            allowClear: false\r\n        },\r\n        formItemProps: {\r\n            rules: [\r\n                {\r\n                    required: true,\r\n                    message: '此项为必填项',\r\n                },\r\n            ],\r\n        },\r\n        width: 'md',\r\n    },\r\n    {\r\n        title: '账号名称',\r\n        dataIndex: 'username',\r\n        formItemProps: {\r\n            rules: [\r\n                {\r\n                    required: true,\r\n                    message: '此项为必填项',\r\n                },\r\n            ],\r\n        },\r\n        width: 'md',\r\n    },\r\n    {\r\n        title: 'UID',\r\n        dataIndex: 'user_id',\r\n        formItemProps: {\r\n            rules: [\r\n                {\r\n                    required: true,\r\n                    message: '此项为必填项',\r\n                },\r\n            ],\r\n        },\r\n        width: 'md',\r\n    },\r\n]\r\n\r\n/**\r\n * 获取用户列表\r\n * @param params \r\n * @returns \r\n */\r\nconst queryTableData = async (params: any): Promise<{\r\n    items: TableListItem[];\r\n    page: number;\r\n    page_size: number;\r\n    total: number;\r\n}> => {\r\n    console.log('queryTableData', params);\r\n    const searchParams = new URLSearchParams();\r\n    searchParams.append('page', params.current)\r\n    searchParams.append('page_size', params.pageSize)\r\n    const url = '/api/v1/users/' + '?' + searchParams.toString()\r\n    const res = await fetch(url, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n    })\r\n    return res.json()\r\n}\r\n\r\n/**\r\n * 添加用户\r\n * @param data \r\n * @returns \r\n */\r\nconst addUserData = async (data: {\r\n    username: string;\r\n    user_id: string;\r\n}): Promise<void> => {\r\n    const url = '/api/v1/users/'\r\n    const res = await fetch(url, {\r\n        method: 'POST',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(data),\r\n    })\r\n    return res.json()\r\n}\r\n\r\n/**\r\n * 根据id获取用户信息\r\n * @param id \r\n * @returns \r\n */\r\nconst getUserDataById = async (id: string): Promise<TableListItem> => {\r\n    try {\r\n        const url = '/api/v1/users/' + id\r\n        const res = await fetch(url, {\r\n            method: 'GET',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        })\r\n        return res.json()\r\n    } catch (error) {\r\n        console.log(error)\r\n        message.error('获取数据失败')\r\n        throw error\r\n    }\r\n\r\n}\r\n\r\n/**\r\n * 根据id更新用户信息\r\n * @param id \r\n * @param data \r\n * @returns \r\n */\r\nconst putUserDataById = async (id: string, data: {\r\n    username: string;\r\n    user_id: string;\r\n}) => {\r\n    const url = '/api/v1/users/' + id\r\n    const res = await fetch(url, {\r\n        method: 'PUT',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(data),\r\n    })\r\n    return res.json()\r\n}\r\n\r\nconst putUserDisabledById = async (id: string, disabled: boolean) => {\r\n    const url = '/api/v1/users/' + id + '/disabled/' + '?is_disabled=' + disabled\r\n    const res = await fetch(url, {\r\n        method: 'PUT',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        }\r\n    })\r\n    return res.json()\r\n}\r\n\r\nconst putBathchDisabledByIds = async (ids: string[], disabled: boolean) => {\r\n    const url = '/api/v1/users/batch/disable'\r\n    const res = await fetch(url, {\r\n        method: 'PUT',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n            ids: ids,\r\n            is_disabled: disabled\r\n        }),\r\n    })\r\n    return res.json()\r\n}\r\n\r\nconst Index = () => {\r\n    const tableRef = useRef<ActionType>();\r\n    const [messageApi, contextHolder] = message.useMessage();\r\n    // 当前被选中的项目\r\n    const [currentSelectedRows, setCurrentSelectedRows] = useState<Array<TableListItem>>([])\r\n    const [modalSchemaFormProps, updateModalSchemaFormProps] = useImmer<ModalSchemaFormProps<DataItem>>(() => {\r\n        return {\r\n            open: false,\r\n            columns: modalColumns,\r\n\r\n            modalProps: {\r\n                destroyOnHidden: true,\r\n                title: '新增账号',\r\n                width: '300px'\r\n            },\r\n            disabledKeys: ['platform', 'username'],\r\n        }\r\n    })\r\n\r\n    const onEnableButton = async (record: TableListItem, checked: boolean) => {\r\n        try {\r\n            console.log(`点击启用/禁用【id:${record.id}】期望为 ${checked}`);\r\n            await putUserDisabledById(record.id, !checked)\r\n            messageApi.open({\r\n                type: 'success',\r\n                content: '操作成功',\r\n            });\r\n            if (tableRef && tableRef.current) {\r\n                // 刷新列表\r\n                tableRef.current.reload();\r\n            }\r\n        } catch (error) {\r\n            messageApi.open({\r\n                type: 'error',\r\n                content: '操作失败',\r\n            })\r\n        }\r\n\r\n    };\r\n\r\n    const onModalOpenChange = (val: boolean) => {\r\n        updateModalSchemaFormProps(drart => {\r\n            drart.open = val\r\n        })\r\n    }\r\n\r\n    const onClickViewButton = async (record: TableListItem) => {\r\n        console.log(`点击查看【id:${record.id}】`);\r\n        const info = await getUserDataById(record.id)\r\n        if (!info.id) {\r\n            messageApi.open({\r\n                type: 'error',\r\n                content: (info as any).detail ? (info as any).detail : '获取数据失败',\r\n            })\r\n            return\r\n        }\r\n        updateModalSchemaFormProps(drart => {\r\n            drart.id = record.id\r\n            drart.modalProps && (drart.modalProps.title = '查看账号')\r\n            drart.disabledKeys = ['platform', 'username', 'user_id']\r\n            drart.submitter = false\r\n            drart.initialValues = {\r\n                platform: 'bz',\r\n                username: info.username,\r\n                user_id: info.user_id,\r\n            }\r\n            drart.onFinish = async (val) => {\r\n                return false\r\n            }\r\n            drart.open = true\r\n        })\r\n    }\r\n\r\n    const onClickEditButton = async (record: TableListItem) => {\r\n        console.log(`点击编辑【id:${record.id}】`);\r\n        const info = await getUserDataById(record.id)\r\n        if (!info.id) {\r\n            messageApi.open({\r\n                type: 'error',\r\n                content: (info as any).detail ? (info as any).detail : '获取数据失败',\r\n            })\r\n            return\r\n        }\r\n        updateModalSchemaFormProps(drart => {\r\n            drart.modalProps && (drart.modalProps.title = '编辑账号')\r\n            drart.disabledKeys = []\r\n            drart.submitter = undefined\r\n            drart.initialValues = {\r\n                platform: 'bz',\r\n                username: info.username,\r\n                user_id: info.user_id,\r\n            }\r\n            drart.onFinish = async (val) => {\r\n                try {\r\n                    await putUserDataById(record.id, {\r\n                        username: val.username,\r\n                        user_id: val.user_id,\r\n                    })\r\n                    messageApi.open({\r\n                        type: 'success',\r\n                        content: '操作成功',\r\n                    })\r\n                } catch (error) {\r\n                    messageApi.open({\r\n                        type: 'error',\r\n                        content: '操作失败',\r\n                    })\r\n                }\r\n                if (tableRef && tableRef.current) {\r\n                    // 刷新列表\r\n                    tableRef.current.reload();\r\n                }\r\n                return true\r\n            }\r\n            drart.open = true\r\n        })\r\n    }\r\n\r\n    const onClickAddButton = () => {\r\n        console.log(`点击新增账号`);\r\n        updateModalSchemaFormProps(drart => {\r\n            drart.modalProps && (drart.modalProps.title = '新增账号')\r\n            drart.initialValues = {\r\n                platform: 'bz',\r\n                username: '',\r\n                user_id: '',\r\n            }\r\n            drart.disabledKeys = []\r\n            drart.submitter = undefined\r\n            drart.onFinish = async (val) => {\r\n                console.log('新增val', val)\r\n                try {\r\n                    await addUserData({\r\n                        username: val.username,\r\n                        user_id: val.user_id,\r\n                    })\r\n                    messageApi.open({\r\n                        type: 'success',\r\n                        content: '操作成功',\r\n                    });\r\n                } catch (error) {\r\n                    messageApi.open({\r\n                        type: 'error',\r\n                        content: '操作失败',\r\n                    })\r\n                }\r\n                if (tableRef && tableRef.current) {\r\n                    // 刷新列表\r\n                    tableRef.current.reload();\r\n                }\r\n                return true\r\n            }\r\n            drart.open = true\r\n        })\r\n    }\r\n\r\n    const onClickBatchEnable = async () => {\r\n        const ids = currentSelectedRows.map(item => item.id)\r\n        console.log(`点击批量启用，数量${ids.length}, ids:${ids}`);\r\n        try {\r\n            await putBathchDisabledByIds(ids, false)\r\n            messageApi.open({\r\n                type: 'success',\r\n                content: '操作成功',\r\n            });\r\n            if (tableRef && tableRef.current) {\r\n                // 刷新列表\r\n                tableRef.current.reload();\r\n            }\r\n        } catch (error) {\r\n            messageApi.open({\r\n                type: 'error',\r\n                content: '操作失败',\r\n            })\r\n        }\r\n    }\r\n\r\n    const onClickBatchDisable = async () => {\r\n        const ids = currentSelectedRows.map(item => item.id)\r\n        console.log(`点击批量禁用，数量${ids.length}, ids:${ids}`);\r\n        try {\r\n            await putBathchDisabledByIds(ids, true)\r\n            messageApi.open({\r\n                type: 'success',\r\n                content: '操作成功',\r\n            });\r\n            if (tableRef && tableRef.current) {\r\n                // 刷新列表\r\n                tableRef.current.reload();\r\n            }\r\n        } catch (error) {\r\n            messageApi.open({\r\n                type: 'error',\r\n                content: '操作失败',\r\n            })\r\n        }\r\n    }\r\n\r\n\r\n    const columns: ProColumns<TableListItem>[] = [\r\n        {\r\n            title: '账号名称',\r\n            width: 120,\r\n            ellipsis: true,\r\n            dataIndex: 'username',\r\n        },\r\n        {\r\n            title: '所属平台',\r\n            width: 80,\r\n            key: 'platform',\r\n            render: () => <span>B站</span>\r\n        },\r\n        {\r\n            title: 'UID',\r\n            width: 90,\r\n            ellipsis: true,\r\n            dataIndex: 'user_id',\r\n        },\r\n        {\r\n            title: '关联视频',\r\n            width: 90,\r\n            ellipsis: true,\r\n            dataIndex: 'video_count',\r\n        },\r\n        {\r\n            title: '创建时间',\r\n            width: 120,\r\n            dataIndex: 'created_at',\r\n            valueType: 'dateTime',\r\n        },\r\n        {\r\n            title: '账号状态',\r\n            width: 80,\r\n            dataIndex: 'is_disabled',\r\n            fixed: 'right',\r\n            render: (_, record) => <> {record.is_disabled ? <div className='account-expired'>过期</div> : <div className='account-normal'>正常</div>} </>\r\n        },\r\n        {\r\n            title: '启用/禁用',\r\n            width: 80,\r\n            key: 'enable',\r\n            valueType: 'option',\r\n            fixed: 'right',\r\n            render: (_, record) => [<Switch key={record.id} value={!record.is_disabled} onClick={(checked) => onEnableButton(record, checked)}></Switch>],\r\n        },\r\n        {\r\n            title: '操作',\r\n            width: 80,\r\n            key: 'option',\r\n            valueType: 'option',\r\n            fixed: 'right',\r\n            render: (_, record) => [\r\n                <a key=\"view\" onClick={() => onClickViewButton(record)}>查看</a>,\r\n                <a key=\"edit\" onClick={() => onClickEditButton(record)}>编辑</a>],\r\n        },\r\n    ];\r\n\r\n    return (\r\n        <PageContainer\r\n            style={{\r\n                height: '100vh',\r\n                overflow: 'hidden'\r\n            }}>\r\n            <Helmet>\r\n                <title>账号管理</title>\r\n            </Helmet>\r\n            {contextHolder}\r\n\r\n            <ProTable<TableListItem, TableListParams>\r\n                actionRef={tableRef}\r\n                request={async (\r\n                    // 第一个参数 params 查询表单和 params 参数的结合\r\n                    // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范\r\n                    params: any,\r\n                    sort,\r\n                    filter,\r\n                ) => {\r\n                    // 这里需要返回一个 Promise,在返回之前你可以进行数据转化\r\n                    // 如果需要转化参数可以在这里进行修改\r\n                    const data = await queryTableData(params);\r\n                    console.log('data', data);\r\n                    return {\r\n                        data: data.items,\r\n                        success: true,\r\n                        total: data.total,\r\n                    }\r\n                }}\r\n                columns={columns}\r\n                columnsState={{\r\n                    persistenceType: 'localStorage',\r\n                    persistenceKey: 'reply-admin/account-admin'\r\n                }}\r\n                rowSelection={{\r\n                    // 自定义选择项参考: https://ant.design/components/table-cn/#components-table-demo-row-selection-custom\r\n                    // 注释该行则默认不显示下拉选项\r\n                    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],\r\n                    defaultSelectedRowKeys: [],\r\n                    alwaysShowAlert: true,\r\n                    onChange: (_1, selectedRows, _2) => setCurrentSelectedRows(selectedRows)\r\n                }}\r\n\r\n                tableAlertRender={({\r\n                    selectedRowKeys,\r\n                    onCleanSelected,\r\n                }) => {\r\n                    return (\r\n                        <Space size={24}>\r\n                            <span>\r\n                                已选 {selectedRowKeys.length} 项\r\n                                {selectedRowKeys.length > 0 && (\r\n                                    <Button type=\"link\" style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>\r\n                                        取消选择\r\n                                    </Button>\r\n                                )}\r\n                            </span>\r\n                        </Space>\r\n                    );\r\n                }}\r\n                tableAlertOptionRender={() => {\r\n                    return (\r\n                        <Space size={16}>\r\n                            <Popconfirm\r\n                                title=\"批量启用\"\r\n                                description=\"是否批量启用?\"\r\n                                onConfirm={onClickBatchEnable}\r\n                                disabled={currentSelectedRows.length === 0}\r\n                                okText=\"确认\"\r\n                                cancelText=\"取消\"\r\n                            >\r\n                                <Button disabled={currentSelectedRows.length === 0}>启用</Button>\r\n                            </Popconfirm>\r\n                            <Popconfirm\r\n                                title=\"批量禁用\"\r\n                                description=\"是否批量禁用?\"\r\n                                onConfirm={onClickBatchDisable}\r\n                                disabled={currentSelectedRows.length === 0}\r\n                                okText=\"确认\"\r\n                                cancelText=\"取消\"\r\n                            >\r\n                                <Button disabled={currentSelectedRows.length === 0}>禁用</Button>\r\n                            </Popconfirm>\r\n                        </Space>\r\n                    );\r\n                }}\r\n                options={{\r\n                    fullScreen: false,\r\n                    reload: false,\r\n                    density: true,\r\n                    setting: true\r\n                }}\r\n                search={false}\r\n                pagination={{\r\n                    pageSize: 50,\r\n                }}\r\n                scroll={\r\n                    {\r\n                        scrollToFirstRowOnChange: true,\r\n                        y: 'calc(100vh - 340px)',\r\n                    }\r\n                }\r\n                rowKey=\"id\"\r\n                toolBarRender={() => [<Button key=\"add-account\" type='primary' onClick={onClickAddButton}>新增账号</Button>]}\r\n            />\r\n            {\r\n                modalSchemaFormProps.open && <ModalSchemaForm<DataItem>\r\n                    {...{ ...modalSchemaFormProps }}\r\n                    onOpenChange={onModalOpenChange}\r\n                ></ModalSchemaForm>\r\n            }\r\n        </PageContainer>\r\n    );\r\n}\r\n\r\nexport default Index;\r\n", "import { Helmet } from '@modern-js/runtime/head';\r\nimport AccountAdmin from '@/reply-admin/containers/AccountAdmin';\r\nimport './index.css';\r\n\r\nconst Index = () => {\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>账号管理</title>\r\n      </Helmet>\r\n      <AccountAdmin></AccountAdmin>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Index;\r\n"], "names": ["props", "trigger", "modalProps", "originalColumns", "initialValues", "submitter", "open", "disabled<PERSON><PERSON>s", "onFinish", "onOpenChange", "form", "Form", "columns", "column", "BetaSchemaForm", "modalColumns", "queryTableData", "params", "console", "searchParams", "URLSearchParams", "url", "res", "fetch", "addUserData", "data", "JSON", "getUserDataById", "id", "error", "message", "putUserDataById", "putUserDisabledById", "disabled", "putBathchDisabledByIds", "ids", "tableRef", "useRef", "messageApi", "contextHolder", "currentSelectedRows", "setCurrentSelectedRows", "useState", "modalSchemaFormProps", "updateModalSchemaFormProps", "useImmer", "onEnableButton", "record", "checked", "onClickViewButton", "info", "drart", "val", "onClickEditButton", "undefined", "onClickAddButton", "onClickBatchEnable", "item", "onClickBatchDisable", "_", "Switch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ProTable", "sort", "filter", "Table", "_1", "selectedRows", "_2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onCleanSelected", "Space", "<PERSON><PERSON>", "Popconfirm", "ModalSchemaForm", "Account<PERSON><PERSON><PERSON>"], "mappings": "2VAmDA,EAlCwB,AAAgCA,IACpD,GAAM,CAAEC,QAAAA,CAAO,CAAEC,WAAAA,CAAU,CAAE,QAASC,CAAe,CAAEC,cAAAA,CAAa,CAAEC,UAAAA,CAAS,CAAEC,KAAAA,CAAI,CAAEC,aAAAA,CAAY,CAAEC,SAAAA,CAAQ,CAAEC,aAAAA,CAAY,CAAE,CAAGT,EAC1H,CAACU,EAAK,CAAGC,EAAAA,CAAAA,CAAAA,OAAY,GAGrBC,EAAUT,EAAgB,GAAG,CAACU,AAAAA,GAChC,AAAIN,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAc,QAAQ,CAACM,EAAO,SAAS,GAChC,mBACAA,GAAAA,CACH,WAAY,mBACJA,EAAO,UAAU,EAAI,CAAC,IAC1B,SAAU,E,KAIfA,GAGX,MACI,UAACC,EAAAA,CAAcA,CAAAA,CACX,KAAMR,EACN,aAAcG,EACd,KAAMC,EACN,QAAST,EACT,WAAW,YACX,SAAUO,EACV,WAAYN,EACZ,cAAeE,EACf,QAASQ,EACT,UAAWP,C,EAGvB,ECFMU,EAA+C,CACjD,CACI,MAAO,2BACP,UAAW,WACX,UAAW,SACX,UAAW,CACP,GAAI,CACA,KAAM,UACN,OAAQ,IACZ,CACJ,EACA,WAAY,CACR,WAAY,EAChB,EACA,cAAe,CACX,MAAO,CACH,CACI,SAAU,GACV,QAAS,sCACb,EACH,AACL,EACA,MAAO,IACX,EACA,CACI,MAAO,2BACP,UAAW,WACX,cAAe,CACX,MAAO,CACH,CACI,SAAU,GACV,QAAS,sCACb,EACH,AACL,EACA,MAAO,IACX,EACA,CACI,MAAO,MACP,UAAW,UACX,cAAe,CACX,MAAO,CACH,CACI,SAAU,GACV,QAAS,sCACb,EACH,AACL,EACA,MAAO,IACX,EACH,CAOKC,G,EAAiB,kBAAOC,CAAM,EAMhCC,QAAQ,GAAG,CAAC,iBAAkBD,GAC9B,IAAME,EAAe,IAAIC,gBACzBD,EAAa,MAAM,CAAC,OAAQF,EAAO,OAAO,EAC1CE,EAAa,MAAM,CAAC,YAAaF,EAAO,QAAQ,EAChD,IAAMI,EAAM,kBAAyBF,EAAa,QAAQ,GAO1D,MAAOG,AANK,OAAMC,MAAMF,EAAK,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,CACJ,EAAC,EACU,IAAI,EACnB,G,SAlB8BJ,CAAM,E,iCAyB9BO,G,EAAc,kBAAOC,CAAI,EAY3B,MAAOH,AAPK,OAAMC,MADN,iBACiB,CACzB,OAAQ,OACR,QAAS,CACL,eAAgB,kBACpB,EACA,KAAMG,KAAK,SAAS,CAACD,EACzB,EAAC,EACU,IAAI,EACnB,G,SAb2BA,CAAI,E,iCAoBzBE,G,EAAkB,kBAAOC,CAAE,EAC7B,GAAI,CAQA,MAAON,AANK,OAAMC,MADN,iBAAmBK,EACF,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,CACJ,EAAC,EACU,IAAI,EACnB,CAAE,MAAOC,EAAO,CAGZ,MAFAX,QAAQ,GAAG,CAACW,GACZC,EAAAA,EAAAA,CAAAA,KAAa,CAAC,wCACRD,CACV,CAEJ,G,SAhB+BD,CAAE,E,iCAwB3BG,G,EAAkB,kBAAOH,CAAE,CAAUH,CAAI,EAY3C,MAAOH,AAPK,OAAMC,MADN,iBAAmBK,EACF,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,EACA,KAAMF,KAAK,SAAS,CAACD,EACzB,EAAC,EACU,IAAI,EACnB,G,SAb+BG,CAAE,CAAUH,CAAI,E,iCAezCO,G,EAAsB,kBAAOJ,CAAE,CAAUK,CAAQ,EAQnD,MAAOX,AANK,OAAMC,MADN,iBAAmBK,EAAnB,0BAAyDK,EACxC,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,CACJ,EAAC,EACU,IAAI,EACnB,G,SATmCL,CAAE,CAAUK,CAAQ,E,iCAWjDC,G,EAAyB,kBAAOC,CAAG,CAAYF,CAAQ,EAYzD,MAAOX,AAVK,OAAMC,MADN,8BACiB,CACzB,OAAQ,MACR,QAAS,CACL,eAAgB,kBACpB,EACA,KAAMG,KAAK,SAAS,CAAC,CACjB,IAAKS,EACL,YAAaF,CACjB,EACJ,EAAC,EACU,IAAI,EACnB,G,SAbsCE,CAAG,CAAYF,CAAQ,E,iCAoY7D,EArXc,KACV,I,YAAMG,EAAWC,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,IACX,CAACC,EAAYC,EAAc,CAAGT,EAAAA,EAAAA,CAAAA,UAAkB,GAEhD,CAACU,EAAqBC,EAAuB,CAAGC,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAA+B,EAAE,EACjF,CAACC,EAAsBC,EAA2B,CAAGC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAyC,IACzF,EACH,KAAM,GACN,QAAS9B,EAET,WAAY,CACR,gBAAiB,GACjB,MAAO,2BACP,MAAO,OACX,EACA,aAAc,CAAC,WAAY,WAAW,AAC1C,IAGE+B,G,EAAiB,kBAAOC,CAAM,CAAiBC,CAAO,EACxD,GAAI,CACA9B,QAAQ,GAAG,CAAE,iDAA8B8B,MAAAA,CAAjBD,EAAO,EAAE,CAAC,6BAAe,OAARC,IAC3C,MAAMhB,EAAoBe,EAAO,EAAE,CAAE,CAACC,GACtCV,EAAW,IAAI,CAAC,CACZ,KAAM,UACN,QAAS,0BACb,GACIF,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,EAE/B,CAAE,MAAOP,EAAO,CACZS,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAS,0BACb,EACJ,CAEJ,G,SAnB8BS,CAAM,CAAiBC,CAAO,E,iCA2BtDC,G,EAAoB,kBAAOF,CAAM,EACnC7B,QAAQ,GAAG,CAAE,oCAAoB,OAAV6B,EAAO,EAAE,CAAC,WACjC,IAAMG,EAAO,MAAMvB,EAAgBoB,EAAO,EAAE,EAC5C,GAAI,CAACG,EAAK,EAAE,CAAE,YACVZ,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAUY,EAAa,MAAM,CAAIA,EAAa,MAAM,CAAG,sCAC3D,GAGJN,EAA2BO,AAAAA,I,KACvBA,CAAAA,EAAM,EAAE,CAAGJ,EAAO,EAAE,CACpBI,EAAM,UAAU,EAAKA,CAAAA,EAAM,UAAU,CAAC,KAAK,CAAG,0BAAK,EACnDA,EAAM,YAAY,CAAG,CAAC,WAAY,WAAY,UAAU,CACxDA,EAAM,SAAS,CAAG,GAClBA,EAAM,aAAa,CAAG,CAClB,SAAU,KACV,SAAUD,EAAK,QAAQ,CACvB,QAASA,EAAK,OAAO,AACzB,E,EACiB,kBAAOE,CAAG,EACvB,MAAO,EACX,GAFAD,EAAM,QAAQ,C,SAAUC,CAAG,E,gCAG3BD,EAAM,IAAI,CAAG,EACjB,EACJ,G,SAzBiCJ,CAAM,E,iCA2BjCM,G,EAAoB,kBAAON,CAAM,EACnC7B,QAAQ,GAAG,CAAE,oCAAoB,OAAV6B,EAAO,EAAE,CAAC,WACjC,IAAMG,EAAO,MAAMvB,EAAgBoB,EAAO,EAAE,EAC5C,GAAI,CAACG,EAAK,EAAE,CAAE,YACVZ,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAUY,EAAa,MAAM,CAAIA,EAAa,MAAM,CAAG,sCAC3D,GAGJN,EAA2BO,AAAAA,I,KACvBA,CAAAA,EAAM,UAAU,EAAKA,CAAAA,EAAM,UAAU,CAAC,KAAK,CAAG,0BAAK,EACnDA,EAAM,YAAY,CAAG,EAAE,CACvBA,EAAM,SAAS,CAAGG,KAAAA,EAClBH,EAAM,aAAa,CAAG,CAClB,SAAU,KACV,SAAUD,EAAK,QAAQ,CACvB,QAASA,EAAK,OAAO,AACzB,E,EACiB,kBAAOE,CAAG,EACvB,GAAI,CACA,MAAMrB,EAAgBgB,EAAO,EAAE,CAAE,CAC7B,SAAUK,EAAI,QAAQ,CACtB,QAASA,EAAI,OAAO,AACxB,GACAd,EAAW,IAAI,CAAC,CACZ,KAAM,UACN,QAAS,0BACb,EACJ,CAAE,MAAOT,EAAO,CACZS,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAS,0BACb,EACJ,CAKA,OAJIF,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,GAEpB,EACX,GArBAe,EAAM,QAAQ,C,SAAUC,CAAG,E,gCAsB3BD,EAAM,IAAI,CAAG,EACjB,EACJ,G,SA3CiCJ,CAAM,E,iCA6CjCQ,EAAmB,KACrBrC,QAAQ,GAAG,CAAE,wCACb0B,EAA2BO,AAAAA,I,KACvBA,CAAAA,EAAM,UAAU,EAAKA,CAAAA,EAAM,UAAU,CAAC,KAAK,CAAG,0BAAK,EACnDA,EAAM,aAAa,CAAG,CAClB,SAAU,KACV,SAAU,GACV,QAAS,EACb,EACAA,EAAM,YAAY,CAAG,EAAE,CACvBA,EAAM,SAAS,CAAGG,KAAAA,E,EACD,kBAAOF,CAAG,EACvBlC,QAAQ,GAAG,CAAC,kBAASkC,GACrB,GAAI,CACA,MAAM5B,EAAY,CACd,SAAU4B,EAAI,QAAQ,CACtB,QAASA,EAAI,OAAO,AACxB,GACAd,EAAW,IAAI,CAAC,CACZ,KAAM,UACN,QAAS,0BACb,EACJ,CAAE,MAAOT,EAAO,CACZS,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAS,0BACb,EACJ,CAKA,OAJIF,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,GAEpB,EACX,GAtBAe,EAAM,QAAQ,C,SAAUC,CAAG,E,gCAuB3BD,EAAM,IAAI,CAAG,EACjB,EACJ,EAEMK,G,EAAqB,oBACvB,IAAMrB,EAAMK,EAAoB,GAAG,CAACiB,AAAAA,GAAQA,EAAK,EAAE,EACnDvC,QAAQ,GAAG,CAAE,yDAA8BiB,MAAAA,CAAnBA,EAAI,MAAM,CAAC,UAAY,OAAJA,IAC3C,GAAI,CACA,MAAMD,EAAuBC,EAAK,IAClCG,EAAW,IAAI,CAAC,CACZ,KAAM,UACN,QAAS,0BACb,GACIF,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,EAE/B,CAAE,MAAOP,EAAO,CACZS,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAS,0BACb,EACJ,CACJ,G,4CAEMoB,G,EAAsB,oBACxB,IAAMvB,EAAMK,EAAoB,GAAG,CAACiB,AAAAA,GAAQA,EAAK,EAAE,EACnDvC,QAAQ,GAAG,CAAE,yDAA8BiB,MAAAA,CAAnBA,EAAI,MAAM,CAAC,UAAY,OAAJA,IAC3C,GAAI,CACA,MAAMD,EAAuBC,EAAK,IAClCG,EAAW,IAAI,CAAC,CACZ,KAAM,UACN,QAAS,0BACb,GACIF,GAAYA,EAAS,OAAO,EAE5BA,EAAS,OAAO,CAAC,MAAM,EAE/B,CAAE,MAAOP,EAAO,CACZS,EAAW,IAAI,CAAC,CACZ,KAAM,QACN,QAAS,0BACb,EACJ,CACJ,G,4CAGM1B,EAAuC,CACzC,CACI,MAAO,2BACP,MAAO,IACP,SAAU,GACV,UAAW,UACf,EACA,CACI,MAAO,2BACP,MAAO,GACP,IAAK,WACL,OAAQ,IAAM,UAAC,Q,SAAK,S,EACxB,EACA,CACI,MAAO,MACP,MAAO,GACP,SAAU,GACV,UAAW,SACf,EACA,CACI,MAAO,2BACP,MAAO,GACP,SAAU,GACV,UAAW,aACf,EACA,CACI,MAAO,2BACP,MAAO,IACP,UAAW,aACX,UAAW,UACf,EACA,CACI,MAAO,2BACP,MAAO,GACP,UAAW,cACX,MAAO,QACP,OAAQ,CAAC+C,EAAGZ,IAAW,uB,UAAE,IAAEA,EAAO,WAAW,CAAG,UAAC,OAAI,UAAU,kB,SAAkB,c,GAAW,UAAC,OAAI,UAAU,iB,SAAiB,c,GAAS,I,EACzI,EACA,CACI,MAAO,4BACP,MAAO,GACP,IAAK,SACL,UAAW,SACX,MAAO,QACP,OAAQ,CAACY,EAAGZ,IAAW,CAAC,UAACa,EAAAA,CAAMA,CAAAA,CAAiB,MAAO,CAACb,EAAO,WAAW,CAAE,QAAS,AAACC,GAAYF,EAAeC,EAAQC,E,EAApFD,EAAO,EAAE,EAA+F,AACjJ,EACA,CACI,MAAO,eACP,MAAO,GACP,IAAK,SACL,UAAW,SACX,MAAO,QACP,OAAQ,CAACY,EAAGZ,IAAW,CACnB,UAAC,KAAa,QAAS,IAAME,EAAkBF,G,SAAS,c,EAAjD,QACP,UAAC,KAAa,QAAS,IAAMM,EAAkBN,G,SAAS,c,EAAjD,QAAwD,AACvE,EACH,CAED,MACI,WAACc,EAAAA,EAAaA,CAAAA,CACV,MAAO,CACH,OAAQ,QACR,SAAU,QACd,E,UACA,UAACC,EAAAA,CAAMA,CAAAA,C,SACH,UAAC,S,SAAM,0B,KAEVvB,EAED,UAACwB,EAAAA,CAAQA,CAAAA,CACL,UAAW3B,EACX,OAAO,E,EAAE,kBAGLnB,CAAM,CACN+C,CAAI,CACJC,CAAM,EAIN,IAAMxC,EAAO,MAAMT,EAAeC,GAElC,OADAC,QAAQ,GAAG,CAAC,OAAQO,GACb,CACH,KAAMA,EAAK,KAAK,CAChB,QAAS,GACT,MAAOA,EAAK,KAAK,AACrB,CACJ,G,SAbIR,CAAM,CACN+C,CAAI,CACJC,CAAM,E,iCAYV,QAASrD,EACT,aAAc,CACV,gBAAiB,eACjB,eAAgB,2BACpB,EACA,aAAc,CAGV,WAAY,CAACsD,EAAAA,CAAAA,CAAAA,aAAmB,CAAEA,EAAAA,CAAAA,CAAAA,gBAAsB,CAAC,CACzD,uBAAwB,EAAE,CAC1B,gBAAiB,GACjB,SAAU,CAACC,EAAIC,EAAcC,IAAO5B,EAAuB2B,EAC/D,EAEA,iBAAkB,AAAC,I,GAAA,CACfE,gBAAAA,CAAe,CACfC,gBAAAA,CAAe,CAClB,GACG,MACI,UAACC,EAAAA,CAAKA,CAAAA,CAAC,KAAM,G,SACT,WAAC,Q,UAAK,gBACEF,EAAgB,MAAM,CAAC,UAC1BA,EAAgB,MAAM,CAAG,GACtB,UAACG,EAAAA,EAAMA,CAAAA,CAAC,KAAK,OAAO,MAAO,CAAE,kBAAmB,CAAE,EAAG,QAASF,E,SAAiB,0B,OAOnG,EACA,uBAAwB,IAEhB,WAACC,EAAAA,CAAKA,CAAAA,CAAC,KAAM,G,UACT,UAACE,EAAAA,CAAUA,CAAAA,CACP,MAAM,2BACN,YAAY,wCACZ,UAAWlB,EACX,SAAUhB,AAA+B,IAA/BA,EAAoB,MAAM,CACpC,OAAO,eACP,WAAW,e,SAEX,UAACiC,EAAAA,EAAMA,CAAAA,CAAC,SAAUjC,AAA+B,IAA/BA,EAAoB,MAAM,C,SAAQ,c,KAExD,UAACkC,EAAAA,CAAUA,CAAAA,CACP,MAAM,2BACN,YAAY,wCACZ,UAAWhB,EACX,SAAUlB,AAA+B,IAA/BA,EAAoB,MAAM,CACpC,OAAO,eACP,WAAW,e,SAEX,UAACiC,EAAAA,EAAMA,CAAAA,CAAC,SAAUjC,AAA+B,IAA/BA,EAAoB,MAAM,C,SAAQ,c,QAKpE,QAAS,CACL,WAAY,GACZ,OAAQ,GACR,QAAS,GACT,QAAS,EACb,EACA,OAAQ,GACR,WAAY,CACR,SAAU,EACd,EACA,OACI,CACI,yBAA0B,GAC1B,EAAG,qBACP,EAEJ,OAAO,KACP,cAAe,IAAM,CAAC,UAACiC,EAAAA,EAAMA,CAAAA,CAAmB,KAAK,UAAU,QAASlB,E,SAAkB,0B,EAAxD,eAAsE,A,GAGxGZ,EAAqB,IAAI,EAAI,UAACgC,EAAeA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CAAAA,EAChChC,GAAAA,CACT,aAtUU,AAACS,IACvBR,EAA2BO,AAAAA,IACvBA,EAAM,IAAI,CAAGC,CACjB,EACJ,C,MAuUJ,EC1jBA,EAXc,IAEV,uB,UACE,UAACU,EAAAA,CAAMA,CAAAA,C,SACL,UAAC,S,SAAM,0B,KAET,UAACc,EAAYA,CAAAA,G"}