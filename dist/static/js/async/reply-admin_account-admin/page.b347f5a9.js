"use strict";(self.webpackChunkweb_dashboard=self.webpackChunkweb_dashboard||[]).push([["62"],{41218:function(e,t,n){n.r(t),n.d(t,{default:()=>E});var i,r,o,s,a,l,d=n(97458),c=n(94343),u=n(59845),p=n(10),h=n(22347),y=n(52983),m=n(41596),f=n(79395),x=n(38416),g=n(36920),_=n(91370),b=n(64538),v=n(20633),j=n(68431),T=n(84759),w=n(29831),P=n(11153),C=e=>{var{trigger:t,modalProps:n,columns:i,initialValues:r,submitter:o,open:s,disabledKeys:a,onFinish:l,onOpenChange:c}=e,[u]=P.Z.useForm(),y=i.map(e=>(null==a?void 0:a.includes(e.dataIndex))?(0,h._)((0,p._)({},e),{fieldProps:(0,h._)((0,p._)({},e.fieldProps||{}),{disabled:!0})}):e);return(0,d.jsx)(w.Z,{open:s,onOpenChange:c,form:u,trigger:t,layoutType:"ModalForm",onFinish:l,modalProps:n,initialValues:r,columns:y,submitter:o})},I=[{title:"\u6240\u5C5E\u5E73\u53F0",dataIndex:"platform",valueType:"select",valueEnum:{bz:{text:"B\u7AD9",status:"bz"}},fieldProps:{allowClear:!1},formItemProps:{rules:[{required:!0,message:"\u6B64\u9879\u4E3A\u5FC5\u586B\u9879"}]},width:"md"},{title:"\u8D26\u53F7\u540D\u79F0",dataIndex:"username",formItemProps:{rules:[{required:!0,message:"\u6B64\u9879\u4E3A\u5FC5\u586B\u9879"}]},width:"md"},{title:"UID",dataIndex:"user_id",formItemProps:{rules:[{required:!0,message:"\u6B64\u9879\u4E3A\u5FC5\u586B\u9879"}]},width:"md"}],S=(i=(0,u._)(function*(e){console.log("queryTableData",e);var t=new URLSearchParams;t.append("page",e.current),t.append("page_size",e.pageSize);var n="/api/v1/users/?"+t.toString();return(yield fetch(n,{method:"GET",headers:{"Content-Type":"application/json"}})).json()}),function(e){return i.apply(this,arguments)}),Z=(r=(0,u._)(function*(e){return(yield fetch("/api/v1/users/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json()}),function(e){return r.apply(this,arguments)}),k=(o=(0,u._)(function*(e){try{return(yield fetch("/api/v1/users/"+e,{method:"GET",headers:{"Content-Type":"application/json"}})).json()}catch(e){throw console.log(e),g.ZP.error("\u83B7\u53D6\u6570\u636E\u5931\u8D25"),e}}),function(e){return o.apply(this,arguments)}),z=(s=(0,u._)(function*(e,t){return(yield fetch("/api/v1/users/"+e,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).json()}),function(e,t){return s.apply(this,arguments)}),O=(a=(0,u._)(function*(e,t){return(yield fetch("/api/v1/users/"+e+"/disabled/?is_disabled="+t,{method:"PUT",headers:{"Content-Type":"application/json"}})).json()}),function(e,t){return a.apply(this,arguments)}),F=(l=(0,u._)(function*(e,t){return(yield fetch("/api/v1/users/batch/disable",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({ids:e,is_disabled:t})})).json()}),function(e,t){return l.apply(this,arguments)}),R=()=>{var e,t,n,i,r,o,s=(0,y.useRef)(),[a,l]=g.ZP.useMessage(),[w,P]=(0,y.useState)([]),[R,E]=(0,m.x)(()=>({open:!1,columns:I,modalProps:{destroyOnHidden:!0,title:"\u65B0\u589E\u8D26\u53F7",width:"300px"},disabledKeys:["platform","username"]})),N=(e=(0,u._)(function*(e,t){try{console.log("\u70B9\u51FB\u542F\u7528/\u7981\u7528\u3010id:".concat(e.id,"\u3011\u671F\u671B\u4E3A ").concat(t)),yield O(e.id,!t),a.open({type:"success",content:"\u64CD\u4F5C\u6210\u529F"}),s&&s.current&&s.current.reload()}catch(e){a.open({type:"error",content:"\u64CD\u4F5C\u5931\u8D25"})}}),function(t,n){return e.apply(this,arguments)}),q=(t=(0,u._)(function*(e){console.log("\u70B9\u51FB\u67E5\u770B\u3010id:".concat(e.id,"\u3011"));var t=yield k(e.id);if(!t.id)return void a.open({type:"error",content:t.detail?t.detail:"\u83B7\u53D6\u6570\u636E\u5931\u8D25"});E(n=>{var i;n.id=e.id,n.modalProps&&(n.modalProps.title="\u67E5\u770B\u8D26\u53F7"),n.disabledKeys=["platform","username","user_id"],n.submitter=!1,n.initialValues={platform:"bz",username:t.username,user_id:t.user_id},i=(0,u._)(function*(e){return!1}),n.onFinish=function(e){return i.apply(this,arguments)},n.open=!0})}),function(e){return t.apply(this,arguments)}),K=(n=(0,u._)(function*(e){console.log("\u70B9\u51FB\u7F16\u8F91\u3010id:".concat(e.id,"\u3011"));var t=yield k(e.id);if(!t.id)return void a.open({type:"error",content:t.detail?t.detail:"\u83B7\u53D6\u6570\u636E\u5931\u8D25"});E(n=>{var i;n.modalProps&&(n.modalProps.title="\u7F16\u8F91\u8D26\u53F7"),n.disabledKeys=[],n.submitter=void 0,n.initialValues={platform:"bz",username:t.username,user_id:t.user_id},i=(0,u._)(function*(t){try{yield z(e.id,{username:t.username,user_id:t.user_id}),a.open({type:"success",content:"\u64CD\u4F5C\u6210\u529F"})}catch(e){a.open({type:"error",content:"\u64CD\u4F5C\u5931\u8D25"})}return s&&s.current&&s.current.reload(),!0}),n.onFinish=function(e){return i.apply(this,arguments)},n.open=!0})}),function(e){return n.apply(this,arguments)}),U=()=>{console.log("\u70B9\u51FB\u65B0\u589E\u8D26\u53F7"),E(e=>{var t;e.modalProps&&(e.modalProps.title="\u65B0\u589E\u8D26\u53F7"),e.initialValues={platform:"bz",username:"",user_id:""},e.disabledKeys=[],e.submitter=void 0,t=(0,u._)(function*(e){console.log("\u65B0\u589Eval",e);try{yield Z({username:e.username,user_id:e.user_id}),a.open({type:"success",content:"\u64CD\u4F5C\u6210\u529F"})}catch(e){a.open({type:"error",content:"\u64CD\u4F5C\u5931\u8D25"})}return s&&s.current&&s.current.reload(),!0}),e.onFinish=function(e){return t.apply(this,arguments)},e.open=!0})},L=(i=(0,u._)(function*(){var e=w.map(e=>e.id);console.log("\u70B9\u51FB\u6279\u91CF\u542F\u7528\uFF0C\u6570\u91CF".concat(e.length,", ids:").concat(e));try{yield F(e,!1),a.open({type:"success",content:"\u64CD\u4F5C\u6210\u529F"}),s&&s.current&&s.current.reload()}catch(e){a.open({type:"error",content:"\u64CD\u4F5C\u5931\u8D25"})}}),function(){return i.apply(this,arguments)}),V=(r=(0,u._)(function*(){var e=w.map(e=>e.id);console.log("\u70B9\u51FB\u6279\u91CF\u7981\u7528\uFF0C\u6570\u91CF".concat(e.length,", ids:").concat(e));try{yield F(e,!0),a.open({type:"success",content:"\u64CD\u4F5C\u6210\u529F"}),s&&s.current&&s.current.reload()}catch(e){a.open({type:"error",content:"\u64CD\u4F5C\u5931\u8D25"})}}),function(){return r.apply(this,arguments)}),A=[{title:"\u8D26\u53F7\u540D\u79F0",width:120,ellipsis:!0,dataIndex:"username"},{title:"\u6240\u5C5E\u5E73\u53F0",width:80,key:"platform",render:()=>(0,d.jsx)("span",{children:"B\u7AD9"})},{title:"UID",width:90,ellipsis:!0,dataIndex:"user_id"},{title:"\u5173\u8054\u89C6\u9891",width:90,ellipsis:!0,dataIndex:"video_count"},{title:"\u521B\u5EFA\u65F6\u95F4",width:120,dataIndex:"created_at",valueType:"dateTime"},{title:"\u8D26\u53F7\u72B6\u6001",width:80,dataIndex:"is_disabled",fixed:"right",render:(e,t)=>(0,d.jsxs)(d.Fragment,{children:[" ",t.is_disabled?(0,d.jsx)("div",{className:"account-expired",children:"\u8FC7\u671F"}):(0,d.jsx)("div",{className:"account-normal",children:"\u6B63\u5E38"})," "]})},{title:"\u542F\u7528/\u7981\u7528",width:80,key:"enable",valueType:"option",fixed:"right",render:(e,t)=>[(0,d.jsx)(_.Z,{value:!t.is_disabled,onClick:e=>N(t,e)},t.id)]},{title:"\u64CD\u4F5C",width:80,key:"option",valueType:"option",fixed:"right",render:(e,t)=>[(0,d.jsx)("a",{onClick:()=>q(t),children:"\u67E5\u770B"},"view"),(0,d.jsx)("a",{onClick:()=>K(t),children:"\u7F16\u8F91"},"edit")]}];return(0,d.jsxs)(f._z,{style:{height:"100vh",overflow:"hidden"},children:[(0,d.jsx)(c.q,{children:(0,d.jsx)("title",{children:"\u8D26\u53F7\u7BA1\u7406"})}),l,(0,d.jsx)(x.Z,{actionRef:s,request:(o=(0,u._)(function*(e,t,n){var i=yield S(e);return console.log("data",i),{data:i.items,success:!0,total:i.total}}),function(e,t,n){return o.apply(this,arguments)}),columns:A,columnsState:{persistenceType:"localStorage",persistenceKey:"reply-admin/account-admin"},rowSelection:{selections:[b.Z.SELECTION_ALL,b.Z.SELECTION_INVERT],defaultSelectedRowKeys:[],alwaysShowAlert:!0,onChange:(e,t,n)=>P(t)},tableAlertRender:e=>{var{selectedRowKeys:t,onCleanSelected:n}=e;return(0,d.jsx)(v.Z,{size:24,children:(0,d.jsxs)("span",{children:["\u5DF2\u9009 ",t.length," \u9879",t.length>0&&(0,d.jsx)(j.ZP,{type:"link",style:{marginInlineStart:8},onClick:n,children:"\u53D6\u6D88\u9009\u62E9"})]})})},tableAlertOptionRender:()=>(0,d.jsxs)(v.Z,{size:16,children:[(0,d.jsx)(T.Z,{title:"\u6279\u91CF\u542F\u7528",description:"\u662F\u5426\u6279\u91CF\u542F\u7528?",onConfirm:L,disabled:0===w.length,okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",children:(0,d.jsx)(j.ZP,{disabled:0===w.length,children:"\u542F\u7528"})}),(0,d.jsx)(T.Z,{title:"\u6279\u91CF\u7981\u7528",description:"\u662F\u5426\u6279\u91CF\u7981\u7528?",onConfirm:V,disabled:0===w.length,okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",children:(0,d.jsx)(j.ZP,{disabled:0===w.length,children:"\u7981\u7528"})})]}),options:{fullScreen:!1,reload:!1,density:!0,setting:!0},search:!1,pagination:{pageSize:50},scroll:{scrollToFirstRowOnChange:!0,y:"calc(100vh - 340px)"},rowKey:"id",toolBarRender:()=>[(0,d.jsx)(j.ZP,{type:"primary",onClick:U,children:"\u65B0\u589E\u8D26\u53F7"},"add-account")]}),R.open&&(0,d.jsx)(C,(0,h._)((0,p._)({},R),{onOpenChange:e=>{E(t=>{t.open=e})}}))]})},E=()=>(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(c.q,{children:(0,d.jsx)("title",{children:"\u8D26\u53F7\u7BA1\u7406"})}),(0,d.jsx)(R,{})]})}}]);