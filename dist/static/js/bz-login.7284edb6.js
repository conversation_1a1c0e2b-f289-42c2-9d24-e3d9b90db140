"use strict";(self.webpackChunkweb_dashboard=self.webpackChunkweb_dashboard||[]).push([["721"],{3421:function(e,n,o){var t,r,i,u=o(97458),s=o(9465),a=o(52983),c=o(30154),d=o(4984);"undefined"!=typeof document&&(window._routeModules={});var l=[{path:"/",children:[{_component:"@_modern_js_src/bz-login/routes/page",index:!0,id:"bz-login_page",type:"nested",lazyImport:()=>Promise.all([o.e("563"),o.e("204"),o.e("393"),o.e("873"),o.e("0")]).then(o.bind(o,2080)).then(e=>(0,c.u3)(e,"bz-login_page")).catch(c.UC),component:(0,a.lazy)(()=>Promise.all([o.e("563"),o.e("204"),o.e("393"),o.e("873"),o.e("0")]).then(o.bind(o,2080)).then(e=>(0,c.u3)(e,"bz-login_page")).catch(c.UC))}],isRoot:!0,_component:"@_modern_js_src/bz-login/routes/layout",id:"bz-login_layout",type:"nested",lazyImport:()=>Promise.resolve().then(o.bind(o,4984)).then(e=>(0,c.u3)(e,"bz-login_layout")).catch(c.UC),component:d.default}];(0,s.cE)({entryName:"bz-login",layoutApp:i,routes:l,appInit:r,appConfig:t});var f=o(96712),_=o(57597),b=o(51568),p="function"==typeof _.Z?(0,_.Z)((0,s.te)()):_.Z,h=[];h.push((0,b.NA)((0,f.f)({serverBase:["/frontend/bz-login"]},(p||{}).router,((p||{}).routerByEntries||{})["bz-login"],((0,s.nB)()||{}).router))),(0,f.v)(h,p);var g=o(33229),m=o(55241),z=(0,g.s)();(0,m.sY)((0,u.jsx)(z,{}),"root")},4984:function(e,n,o){o.r(n),o.d(n,{default:()=>i});var t=o(97458),r=o(83696);function i(){return(0,t.jsx)("div",{children:(0,t.jsx)(r.j3,{})})}},57597:function(e,n,o){n.Z=(0,o(65435).mc)({})},76762:function(e,n,o){o(38450),o(39821),o(35290),o(79853),o(65889),o(50038),o(32458),o(92037),o(88240),o(59464),o(4491),o(6984),o(30930),o(26),o(41572),o(28419),o(95467),o(79876),o(93008),o(68388),o(34458),o(14976),o(67190),o(33022),o(53607),o(56711),o(80798),o(838),o(42867),o(15519),o(73452),o(54357),o(86775),o(6763),o(20447),o(16213),o(48736),o(93285),o(5063),o(15434),o(87173),o(74414),o(92574),o(98737),o(95633),o(42457),o(64416),o(25019),o(32793),o(73971),o(36484),o(68804),o(30813),o(66704),o(17009),o(98245),o(50725),o(70332),o(53221),o(38062),o(35746),o(5806),o(47975),o(80194),o(25800),o(56113),o(50887),o(12604),o(12875),o(13189),o(63277),o(82230),o(27899),o(37389),o(27968),o(46299),o(18777),o(17144),o(57493),o(80216),o(91378),o(99881),o(64542),o(89224),o(87945),o(24019),o(34494),o(64715),o(51633),o(58275),o(41038),o(10363),o(83541),o(17237),o(75495),o(9861),o(35191),o(2376),o(73588),o(71561),o(55843),o(12745),o(75184),o(12100),o(29646),o(67581),o(33392),o(41970),o(63782),o(14311),o(26044),o(72547),o(86936),o(20889),o(1506),o(89923),o(9525),o(29611),o(47573),o(37242),o(42580),o(6788),o(12116),o(81289),o(25314),o(68001),o(76950),o(20686),o(29003),o(63727),o(8579),o(40275),o(4626),o(48213),o(18312),o(79981),o(45197),o(18211),o(63992),o(62912),o(59251),o(41777),o(60375),o(43254),o(92843),o(12608),o(46661),o(4457),o(32439),o(11430),o(32476),o(74263),o(96305),o(19375),o(73133),o(61764),o(36683),o(51370),o(9139),o(12803),o(13143),o(40609),o(59864),o(28210),o(6970),o(42181),o(64410),o(49773),o(13490),o(34200),o(20682),o(27673),o(31918),o(6145),o(7735),o(38950),o(95974),o(64863),o(37797),o(34449),o(47146),o(14735),o(81914),o(92851),o(79956),o(80755),o(13139),o(73029),o(22908),o(20849),o(63325),o(58559),o(49431),o(97427),o(16212),o(64784),o(15002),o(47324),o(32109),o(49806),o(96043),o(8099),o(52986),o(61760),o(3172),o(30174),o(10561),o(57155),o(11138),o(261),o(55823),o(88815),o(21203),o(40622),o(618),o(38204),o(66263),o(24818),o(4395),o(20269),o(30480),o(50687),o(61503),o(55847),o(56208),o(11788),o(63053),o(22791),o(39635),o(10793),o(42684),o(84281),o(37209),o(62705),o(24629),o(17875),o(58899),o(96051),o(30235),o(2138),o(87649),o(66802),o(74028),o(63707),o(60685),o(42616),o(89534),o(48630),o(91351),o(47569),o(83426),o(29970),o(63808),o(26595),o(47067),o(77119),o(8403),o(36358),o(38741),o(58982),o(60397),o(84370),o(91413),o(64535),o(98524),o(78034),o(74998),o(93350),o(32777),o(14088),o(99643),o(59387),o(11626),o(43602),o(15511),o(43600),o(64349),o(11690),o(23452),o(14893),o(36043),o(92906),o(57303),o(24235),o(14667),o(35388),o(56384),o(5402),o(65223),o(2847),o(50886),o(6690),o(67229),o(13151),o(64728),o(88592),o(91634),o(72467),o(33891),o(98383),o(45261),o(70957),o(24551),o(22349)},74404:function(){window.__assetPrefix__="/frontend"}},function(e){var n=function(n){return e(e.s=n)};e.O(0,["118","126","361","960"],function(){return n(74404),n(76762),n(3421)}),e.O()}]);