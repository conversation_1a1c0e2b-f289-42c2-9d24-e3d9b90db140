/*! For license information please see 592.665f81f5.js.LICENSE.txt */
(self.webpackChunkweb_dashboard=self.webpackChunkweb_dashboard||[]).push([["592"],{93942:function(e,t,n){"use strict";n.d(t,{Z:()=>l});var r=n(25833),i=n(52983),o={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},a=n(61727),l=i.forwardRef(function(e,t){return i.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:o}))})},76349:function(e,t,n){"use strict";n.d(t,{Z:()=>l});var r=n(25833),i=n(52983),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"},a=n(61727),l=i.forwardRef(function(e,t){return i.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:o}))})},79340:function(e,t,n){"use strict";n.d(t,{Z:()=>l});var r=n(25833),i=n(52983),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},a=n(61727),l=i.forwardRef(function(e,t){return i.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:o}))})},8427:function(e,t,n){"use strict";n.d(t,{f:()=>t8});var r,i,o,a=n(50793),l=n(30104),s=n(63587),c=n(95504),u=n(57987),d=n(19741),f=n(28800),p=n(29202),h=n(52983);function m(e){var t="undefined"==typeof window,n=(0,h.useState)(function(){return!t&&window.matchMedia(e).matches}),r=(0,u.Z)(n,2),i=r[0],o=r[1];return(0,h.useLayoutEffect)(function(){if(!t){var n=window.matchMedia(e),r=function(e){return o(e.matches)};return n.addListener(r),function(){return n.removeListener(r)}}},[e]),i}var g={xs:{maxWidth:575,matchMedia:"(max-width: 575px)"},sm:{minWidth:576,maxWidth:767,matchMedia:"(min-width: 576px) and (max-width: 767px)"},md:{minWidth:768,maxWidth:991,matchMedia:"(min-width: 768px) and (max-width: 991px)"},lg:{minWidth:992,maxWidth:1199,matchMedia:"(min-width: 992px) and (max-width: 1199px)"},xl:{minWidth:1200,maxWidth:1599,matchMedia:"(min-width: 1200px) and (max-width: 1599px)"},xxl:{minWidth:1600,matchMedia:"(min-width: 1600px)"}},v=function(){var e=void 0;return"undefined"==typeof window?e:e=Object.keys(g).find(function(e){var t=g[e].matchMedia;return!!window.matchMedia(t).matches})},y=function(){var e=m(g.md.matchMedia),t=m(g.lg.matchMedia),n=m(g.xxl.matchMedia),r=m(g.xl.matchMedia),i=m(g.sm.matchMedia),o=m(g.xs.matchMedia),a=(0,h.useState)(v()),l=(0,u.Z)(a,2),s=l[0],c=l[1];return(0,h.useEffect)(function(){return n?void c("xxl"):r?void c("xl"):t?void c("lg"):e?void c("md"):i?void c("sm"):o?void c("xs"):void c("md")},[e,t,n,r,i,o]),s},b=n(49130),x=n(24288),Z=n(43902);function C(e){if(0>(0,x.n)((0,Z.b)(),"5.6.0"))return e;var t={colorGroupTitle:"groupTitleColor",radiusItem:"itemBorderRadius",radiusSubMenuItem:"subMenuItemBorderRadius",colorItemText:"itemColor",colorItemTextHover:"itemHoverColor",colorItemTextHoverHorizontal:"horizontalItemHoverColor",colorItemTextSelected:"itemSelectedColor",colorItemTextSelectedHorizontal:"horizontalItemSelectedColor",colorItemTextDisabled:"itemDisabledColor",colorDangerItemText:"dangerItemColor",colorDangerItemTextHover:"dangerItemHoverColor",colorDangerItemTextSelected:"dangerItemSelectedColor",colorDangerItemBgActive:"dangerItemActiveBg",colorDangerItemBgSelected:"dangerItemSelectedBg",colorItemBg:"itemBg",colorItemBgHover:"itemHoverBg",colorSubItemBg:"subMenuItemBg",colorItemBgActive:"itemActiveBg",colorItemBgSelected:"itemSelectedBg",colorItemBgSelectedHorizontal:"horizontalItemSelectedBg",colorActiveBarWidth:"activeBarWidth",colorActiveBarHeight:"activeBarHeight",colorActiveBarBorderSize:"activeBarBorderWidth"},n=(0,d.Z)({},e);return Object.keys(t).forEach(function(e){void 0!==n[e]&&(n[t[e]]=n[e],delete n[e])}),n}var S=n(41029);function w(e,t){return t>>>e|t<<32-e}var k=[0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2];function M(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function F(){var e,t,n,i,a,l,s,c,u,d,f,p,h,m,g,v,y=Array(16);s=r[0],c=r[1],u=r[2],d=r[3],f=r[4],p=r[5],h=r[6],m=r[7];for(var b=0;b<16;b++)y[b]=o[(b<<2)+3]|o[(b<<2)+2]<<8|o[(b<<2)+1]<<16|o[b<<2]<<24;for(var x=0;x<64;x++)g=m+(w(6,e=f)^w(11,e)^w(25,e))+((t=f)&p^~t&h)+k[x],x<16?g+=y[x]:g+=function(e,t){var n,r;return e[15&t]+=(w(17,n=e[t+14&15])^w(19,n)^n>>>10)+e[t+9&15]+(w(7,r=e[t+1&15])^w(18,r)^r>>>3)}(y,x),v=(w(2,n=s)^w(13,n)^w(22,n))+((i=s)&(a=c)^i&(l=u)^a&l),m=h,h=p,p=f,f=M(d,g),d=u,u=c,c=s,s=M(g,v);r[0]+=s,r[1]+=c,r[2]+=u,r[3]+=d,r[4]+=f,r[5]+=p,r[6]+=h,r[7]+=m}var T=function(e){r=Array(8),i=[,,],o=Array(64),i[0]=i[1]=0,r[0]=0x6a09e667,r[1]=0xbb67ae85,r[2]=0x3c6ef372,r[3]=0xa54ff53a,r[4]=0x510e527f,r[5]=0x9b05688c,r[6]=0x1f83d9ab,r[7]=0x5be0cd19,function(e,t){var n,r,a=0;r=i[0]>>3&63;var l=63&t;for((i[0]+=t<<3)<t<<3&&i[1]++,i[1]+=t>>29,n=0;n+63<t;n+=64){for(var s=r;s<64;s++)o[s]=e.charCodeAt(a++);F(),r=0}for(var c=0;c<l;c++)o[c]=e.charCodeAt(a++)}(e,e.length);var t=i[0]>>3&63;if(o[t++]=128,t<=56)for(var n=t;n<56;n++)o[n]=0;else{for(var a=t;a<64;a++)o[a]=0;F();for(var l=0;l<56;l++)o[l]=0}o[56]=i[1]>>>24&255,o[57]=i[1]>>>16&255,o[58]=i[1]>>>8&255,o[59]=255&i[1],o[60]=i[0]>>>24&255,o[61]=i[0]>>>16&255,o[62]=i[0]>>>8&255,o[63]=255&i[0],F();for(var s=new String,c=0;c<8;c++)for(var u=28;u>=0;u-=4)s+="0123456789abcdef".charAt(r[c]>>>u&15);return s};function P(e){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var E=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function R(e){var t="function"==typeof Map?new Map:void 0;return(R=function(e){var n;if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return I(e,arguments,A(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),L(r,e)})(e)}function I(e,t,n){return(I=j()?Reflect.construct.bind():function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&L(i,n.prototype),i}).apply(null,arguments)}function j(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function L(e,t){return(L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function A(e){return(A=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function O(e){return function(e){if(Array.isArray(e))return D(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||N(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(e,t){if(e){if("string"==typeof e)return D(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return D(e,t)}}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function B(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?B(Object(n),!0).forEach(function(t){var r,i,o;r=e,i=t,o=n[t],i in r?Object.defineProperty(r,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):B(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var H="routes";function z(e){return e.split("?")[0].split("#")[0]}var V=function(e){if(!e.startsWith("http"))return!1;try{return new URL(e),!0}catch(e){return!1}},W=function(e){var t=e.path;if(!t||"/"===t)try{return"/".concat(T(JSON.stringify(e)))}catch(e){}return t?z(t):t},$=function(e,t){var n=e.name,r=e.locale;return(!("locale"in e)||!1!==r)&&!!n&&(e.locale||"".concat(t,".").concat(n))},K=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/";return e.endsWith("/*")?e.replace("/*","/"):(e||t).startsWith("/")||V(e)?e:"/".concat(t,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/")},U=function(e,t){var n=e.menu,r=void 0===n?{}:n,i=e.indexRoute,o=e.path,a=e.children||[],l=r.name,s=void 0===l?e.name:l,c=r.icon,u=void 0===c?e.icon:c,d=r.hideChildren,f=void 0===d?e.hideChildren:d,p=r.flatMenu,h=void 0===p?e.flatMenu:p,m=i&&"redirect"!==Object.keys(i).join(",")?[_({path:void 0===o?"":o,menu:r},i)].concat(a||[]):a,g=_({},e);if(s&&(g.name=s),u&&(g.icon=u),m&&m.length){if(f)return delete g.children,g;var v=Y(_(_({},t),{},{data:m}),e);if(h)return v;delete g[H]}return g},q=function(e){return Array.isArray(e)&&e.length>0};function Y(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{path:"/"},n=e.data,r=e.formatMessage,i=e.parentName,o=e.locale;return n&&Array.isArray(n)?n.filter(function(e){return!!e&&(!!q(e.children)||!!e.path||!!e.originPath||!!e.layout||!e.redirect&&(e.unaccessible,!1))}).filter(function(e){var t,n;return null!=e&&null!=(t=e.menu)&&!!t.name||null!=e&&!!e.flatMenu||null!=e&&null!=(n=e.menu)&&!!n.flatMenu||!1!==e.menu}).map(function(e){var t=_(_({},e),{},{path:e.path||e.originPath});return!t.children&&t[H]&&(t.children=t[H],delete t[H]),t.unaccessible&&delete t.name,"*"===t.path&&(t.path="."),"/*"===t.path&&(t.path="."),!t.path&&t.originPath&&(t.path=t.originPath),t}).map(function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{path:"/"},a=n.children||n[H]||[],l=K(n.path,t?t.path:"/"),s=n.name,c=$(n,i||"menu"),u=!1!==c&&!1!==o&&r&&c?r({id:c,defaultMessage:s}):s,d=t.pro_layout_parentKeys,f=(t.children,t.icon,t.flatMenu,t.indexRoute,t.routes,function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(t,E)),p=new Set([].concat(O(void 0===d?[]:d),O(n.parentKeys||[])));t.key&&p.add(t.key);var h=_(_(_({},f),{},{menu:void 0},n),{},{path:l,locale:c,key:n.key||W(_(_({},n),{},{path:l})),pro_layout_parentKeys:Array.from(p).filter(function(e){return e&&"/"!==e})});if(u?h.name=u:delete h.name,void 0===h.menu&&delete h.menu,q(a)){var m=Y(_(_({},e),{},{data:a,parentName:c||""}),h);q(m)&&(h.children=m)}return U(h,e)}).flat(1):[]}var X=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(function(e){return e&&(e.name||q(e.children))&&!e.hideInMenu&&!e.redirect}).map(function(t){var n=_({},t),r=n.children||t[H]||[];if(delete n[H],q(r)&&!n.hideChildrenInMenu&&r.some(function(e){return e&&!!e.name})){var i=e(r);if(i.length)return _(_({},n),{},{children:i})}return _({},t)}).filter(function(e){return e})},G=function(e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");i.prototype=Object.create(e&&e.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),e&&L(i,e);var t,n,r=(t=j(),function(){var e,n=A(i);return e=t?Reflect.construct(n,arguments,A(this).constructor):n.apply(this,arguments),function(e,t){if(t&&("object"===P(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var n=e;if(void 0===n)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return n}(this,e)});function i(){if(!(this instanceof i))throw TypeError("Cannot call a class as a function");return r.apply(this,arguments)}return n=[{key:"get",value:function(e){var t;try{var n,r=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=N(e))){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){l=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(l)throw o}}}}(this.entries());try{for(r.s();!(n=r.n()).done;){var i,o=(i=n.value,function(e){if(Array.isArray(e))return e}(i)||function(e,t){var n,r,i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var o=[],a=!0,l=!1;try{for(i=i.call(e);!(a=(n=i.next()).done)&&(o.push(n.value),o.length!==t);a=!0);}catch(e){l=!0,r=e}finally{try{a||null==i.return||i.return()}finally{if(l)throw r}}return o}}(i,2)||N(i,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),a=o[0],l=o[1],s=z(a);if(!V(a)&&(0,S.pathToRegexp)(s,[]).test(e)){t=l;break}}}catch(e){r.e(e)}finally{r.f()}}catch(e){t=void 0}return t}}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(i.prototype,n),Object.defineProperty(i,"prototype",{writable:!1}),i}(R(Map)),Q=function(e){var t=new G;return!function e(n,r){n.forEach(function(n){var i=n.children||n[H]||[];q(i)&&e(i,n);var o=K(n.path,r?r.path:"/");t.set(z(o),n)})}(e),t},J=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.map(function(t){var n=t.children||t[H];if(q(n)&&e(n).length)return _({},t);var r=_({},t);return delete r[H],delete r.children,r}).filter(function(e){return e})},ee=function(e,t,n,r){var i=Y({data:e,formatMessage:n,locale:t}),o=r?J(i):X(i);return{breadcrumb:Q(i),menuData:o}};function et(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function en(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?et(Object(n),!0).forEach(function(t){var r,i,o;r=e,i=t,o=n[t],i in r?Object.defineProperty(r,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):et(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var er=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n={};return t.forEach(function(t){var r=en({},t);if(r&&r.key){!r.children&&r[H]&&(r.children=r[H],delete r[H]);var i=r.children||[];n[z(r.path||r.key||"/")]=en({},r),n[r.key||r.path||"/"]=en({},r),i&&(n=en(en({},n),e(i)))}}),n},ei=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;return e.filter(function(e){if("/"===e&&"/"===t)return!0;if("/"!==e&&"/*"!==e&&e&&!V(e)){var r=z(e);try{if(n&&(0,S.pathToRegexp)("".concat(r)).test(t)||(0,S.pathToRegexp)("".concat(r),[]).test(t)||(0,S.pathToRegexp)("".concat(r,"/(.*)")).test(t))return!0}catch(e){}}return!1}).sort(function(e,n){return e===t?10:n===t?-10:e.substr(1).split("/").length-n.substr(1).split("/").length})},eo=function(e,t,n,r){var i=er(t),o=ei(Object.keys(i),e||"/",r);return!o||o.length<1?[]:(n||(o=[o[o.length-1]]),o.map(function(e){var t=i[e]||{pro_layout_parentKeys:"",key:""},n=new Map,r=(t.pro_layout_parentKeys||[]).map(function(e){return n.has(e)?null:(n.set(e,!0),i[e])}).filter(function(e){return e});return t.key&&r.push(t),r}).flat(1))},ea=n(35376),el=n(90738),es=n(41229),ec=n.n(es),eu=n(63851),ed=n(37326),ef=n(65327),ep=n(52008),eh=n(35591),em=n(97458),eg=function(e){var t=(0,h.useContext)(f.L_).hashId,n=e.style,r=e.prefixCls,i=e.children,o=e.hasPageContainer,l=ec()("".concat(r,"-content"),t,(0,a.Z)((0,a.Z)({},"".concat(r,"-has-header"),e.hasHeader),"".concat(r,"-content-has-page-container"),(void 0===o?0:o)>0)),s=e.ErrorBoundary||eh.S;return!1===e.ErrorBoundary?(0,em.jsx)(el.Z.Content,{className:l,style:n,children:i}):(0,em.jsx)(s,{children:(0,em.jsx)(el.Z.Content,{className:l,style:n,children:i})})},ev=function(){return(0,em.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 200 200",children:[(0,em.jsxs)("defs",{children:[(0,em.jsxs)("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1",children:[(0,em.jsx)("stop",{stopColor:"#4285EB",offset:"0%"}),(0,em.jsx)("stop",{stopColor:"#2EC7FF",offset:"100%"})]}),(0,em.jsxs)("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2",children:[(0,em.jsx)("stop",{stopColor:"#29CDFF",offset:"0%"}),(0,em.jsx)("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),(0,em.jsx)("stop",{stopColor:"#0A60FF",offset:"100%"})]}),(0,em.jsxs)("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3",children:[(0,em.jsx)("stop",{stopColor:"#FA816E",offset:"0%"}),(0,em.jsx)("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),(0,em.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]}),(0,em.jsxs)("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4",children:[(0,em.jsx)("stop",{stopColor:"#FA8E7D",offset:"0%"}),(0,em.jsx)("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),(0,em.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]})]}),(0,em.jsx)("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd",children:(0,em.jsx)("g",{transform:"translate(-20.000000, -20.000000)",children:(0,em.jsx)("g",{transform:"translate(20.000000, 20.000000)",children:(0,em.jsxs)("g",{children:[(0,em.jsxs)("g",{fillRule:"nonzero",children:[(0,em.jsxs)("g",{children:[(0,em.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),(0,em.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})]}),(0,em.jsx)("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})]}),(0,em.jsx)("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"})]})})})})]})},ey=n(25833),eb={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"}}]},name:"copyright",theme:"outlined"},ex=n(61727),eZ=h.forwardRef(function(e,t){return h.createElement(ex.Z,(0,ey.Z)({},e,{ref:t,icon:eb}))}),eC=n(41628),eS=function(e){var t=e.className,n=e.prefixCls,r=e.links,i=e.copyright,o=e.style,l=(0,h.useContext)(ea.ZP.ConfigContext).getPrefixCls(n||"pro-global-footer"),s=(0,eC.Xj)("ProLayoutFooter",function(e){var t;return[(t=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(l)}),(0,a.Z)({},t.componentCls,{marginBlock:0,marginBlockStart:48,marginBlockEnd:24,marginInline:0,paddingBlock:0,paddingInline:16,textAlign:"center","&-list":{marginBlockEnd:8,color:t.colorTextSecondary,"&-link":{color:t.colorTextSecondary,textDecoration:t.linkDecoration},"*:not(:last-child)":{marginInlineEnd:8},"&:hover":{color:t.colorPrimary}},"&-copyright":{fontSize:"14px",color:t.colorText}}))]}),c=s.wrapSSR,u=s.hashId;return(null==r||!1===r||Array.isArray(r)&&0===r.length)&&(null==i||!1===i)?null:c((0,em.jsxs)("div",{className:ec()(l,u,t),style:o,children:[r&&(0,em.jsx)("div",{className:"".concat(l,"-list ").concat(u).trim(),children:r.map(function(e){return(0,em.jsx)("a",{className:"".concat(l,"-list-link ").concat(u).trim(),title:e.key,target:e.blankTarget?"_blank":"_self",href:e.href,rel:"noreferrer",children:e.title},e.key)})}),i&&(0,em.jsx)("div",{className:"".concat(l,"-copyright ").concat(u).trim(),children:i})]}))},ew=el.Z.Footer,ek=function(e){var t=e.links,n=e.copyright,r=e.style,i=e.className,o=e.prefixCls;return(0,em.jsx)(ew,{className:i,style:(0,d.Z)({padding:0},r),children:(0,em.jsx)(eS,{links:t,prefixCls:o,copyright:!1===n?null:(0,em.jsxs)(h.Fragment,{children:[(0,em.jsx)(eZ,{})," ",n]})})})},eM=function e(t){return(t||[]).reduce(function(t,n){return(n.key&&t.push(n.key),n.children||n.routes)?t.concat(e(n.children||n.routes)||[]):t},[])};function eF(e){return e.map(function(e){var t=e.children||[],n=(0,d.Z)({},e);if(!n.children&&n.routes&&(n.children=n.routes),!n.name||n.hideInMenu)return null;if(n&&null!=n&&n.children){if(!n.hideChildrenInMenu&&t.some(function(e){return e&&e.name&&!e.hideInMenu}))return(0,d.Z)((0,d.Z)({},e),{},{children:eF(t)});delete n.children}return delete n.routes,n}).filter(function(e){return e})}var eT={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},eP=h.forwardRef(function(e,t){return h.createElement(ex.Z,(0,ey.Z)({},e,{ref:t,icon:eT}))}),eE=n(93340),eR=function(){return(0,em.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,em.jsx)("path",{d:"M0 0h3v3H0V0zm4.5 0h3v3h-3V0zM9 0h3v3H9V0zM0 4.5h3v3H0v-3zm4.503 0h3v3h-3v-3zM9 4.5h3v3H9v-3zM0 9h3v3H0V9zm4.503 0h3v3h-3V9zM9 9h3v3H9V9z"})})},eI=function e(t){var n=t.appList,r=t.baseClassName,i=t.hashId,o=t.itemClick;return(0,em.jsx)("div",{className:"".concat(r,"-content ").concat(i).trim(),children:(0,em.jsx)("ul",{className:"".concat(r,"-content-list ").concat(i).trim(),children:null==n?void 0:n.map(function(t,n){var a;return null!=t&&null!=(a=t.children)&&a.length?(0,em.jsxs)("div",{className:"".concat(r,"-content-list-item-group ").concat(i).trim(),children:[(0,em.jsx)("div",{className:"".concat(r,"-content-list-item-group-title ").concat(i).trim(),children:t.title}),(0,em.jsx)(e,{hashId:i,itemClick:o,appList:null==t?void 0:t.children,baseClassName:r})]},n):(0,em.jsx)("li",{className:"".concat(r,"-content-list-item ").concat(i).trim(),onClick:function(e){e.stopPropagation(),null==o||o(t)},children:(0,em.jsxs)("a",{href:o?void 0:t.url,target:t.target,rel:"noreferrer",children:[eN(t.icon),(0,em.jsxs)("div",{children:[(0,em.jsx)("div",{children:t.title}),t.desc?(0,em.jsx)("span",{children:t.desc}):null]})]})},n)})})})},ej=function(e){if(!e||!e.startsWith("http"))return!1;try{return new URL(e),!0}catch(e){return!1}},eL=function(e,t){if(e&&"string"==typeof e&&ej(e))return(0,em.jsx)("img",{src:e,alt:"logo"});if("function"==typeof e)return e();if(e&&"string"==typeof e)return(0,em.jsx)("div",{id:"avatarLogo",children:e});if(!e&&t&&"string"==typeof t){var n=t.substring(0,1);return(0,em.jsx)("div",{id:"avatarLogo",children:n})}return e},eA=function e(t){var n=t.appList,r=t.baseClassName,i=t.hashId,o=t.itemClick;return(0,em.jsx)("div",{className:"".concat(r,"-content ").concat(i).trim(),children:(0,em.jsx)("ul",{className:"".concat(r,"-content-list ").concat(i).trim(),children:null==n?void 0:n.map(function(t,n){var a;return null!=t&&null!=(a=t.children)&&a.length?(0,em.jsxs)("div",{className:"".concat(r,"-content-list-item-group ").concat(i).trim(),children:[(0,em.jsx)("div",{className:"".concat(r,"-content-list-item-group-title ").concat(i).trim(),children:t.title}),(0,em.jsx)(e,{hashId:i,itemClick:o,appList:null==t?void 0:t.children,baseClassName:r})]},n):(0,em.jsx)("li",{className:"".concat(r,"-content-list-item ").concat(i).trim(),onClick:function(e){e.stopPropagation(),null==o||o(t)},children:(0,em.jsxs)("a",{href:o?"javascript:;":t.url,target:t.target,rel:"noreferrer",children:[eL(t.icon,t.title),(0,em.jsx)("div",{children:(0,em.jsx)("div",{children:t.title})})]})},n)})})})},eO=function(e){var t,n,r,i,o;return(0,a.Z)({},e.componentCls,{"&-icon":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInline:4,paddingBlock:0,fontSize:14,lineHeight:"14px",height:28,width:28,cursor:"pointer",color:null==(t=e.layout)?void 0:t.colorTextAppListIcon,borderRadius:e.borderRadius,"&:hover":{color:null==(n=e.layout)?void 0:n.colorTextAppListIconHover,backgroundColor:null==(r=e.layout)?void 0:r.colorBgAppListIconHover},"&-active":{color:null==(i=e.layout)?void 0:i.colorTextAppListIconHover,backgroundColor:null==(o=e.layout)?void 0:o.colorBgAppListIconHover}},"&-item-title":{marginInlineStart:"16px",marginInlineEnd:"8px",marginBlockStart:0,marginBlockEnd:"12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginBlockStart:12}},"&-popover":(0,a.Z)({},"".concat(e.antCls,"-popover-arrow"),{display:"none"}),"&-simple":{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"border-box",maxWidth:376,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:104,height:104,marginBlock:8,marginInline:8,paddingInline:24,paddingBlock:24,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},a:{display:"flex",flexDirection:"column",alignItems:"center",height:"100%",fontSize:12,textDecoration:"none","& > #avatarLogo":{width:40,height:40,margin:"0 auto",color:e.colorPrimary,fontSize:22,lineHeight:"40px",textAlign:"center",backgroundImage:"linear-gradient(180deg, #E8F0FB 0%, #F6F8FC 100%)",borderRadius:e.borderRadius},"& > img":{width:40,height:40},"& > div":{marginBlockStart:5,marginInlineStart:0,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}},"&-default":{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"content-box",maxWidth:656,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:328,height:72,paddingInline:16,paddingBlock:16,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},"* div":null===eC.Wf||void 0===eC.Wf?void 0:(0,eC.Wf)(e),a:{display:"flex",height:"100%",fontSize:12,textDecoration:"none","& > img":{width:40,height:40},"& > div":{marginInlineStart:14,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}})},eN=function(e){return"string"==typeof e?(0,em.jsx)("img",{width:"auto",height:22,src:e,alt:"logo"}):"function"==typeof e?e():e},eD=function(e){var t,n=e.appList,r=e.appListRender,i=e.prefixCls,o=e.onItemClick,l=h.useRef(null),s=h.useRef(null),c="".concat(void 0===i?"ant-pro":i,"-layout-apps"),f=(0,eC.Xj)("AppsLogoComponents",function(e){return[eO((0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(c)}))]}),p=f.wrapSSR,m=f.hashId,g=(0,h.useState)(!1),v=(0,u.Z)(g,2),y=v[0],b=v[1],x=function(e){null==o||o(e,s)},C=(0,h.useMemo)(function(){return(null==n?void 0:n.some(function(e){return!(null!=e&&e.desc)}))?(0,em.jsx)(eA,{hashId:m,appList:n,itemClick:o?x:void 0,baseClassName:"".concat(c,"-simple")}):(0,em.jsx)(eI,{hashId:m,appList:n,itemClick:o?x:void 0,baseClassName:"".concat(c,"-default")})},[n,c,m]);if(!(null!=e&&null!=(t=e.appList)&&t.length))return null;var S=r?r(null==e?void 0:e.appList,C):C,w=(0,Z.X)(void 0,function(e){return b(e)});return p((0,em.jsxs)(em.Fragment,{children:[(0,em.jsx)("div",{ref:l,onClick:function(e){e.stopPropagation(),e.preventDefault()}}),(0,em.jsx)(eE.Z,(0,d.Z)((0,d.Z)({placement:"bottomRight",trigger:["click"],zIndex:9999,arrow:!1},w),{},{overlayClassName:"".concat(c,"-popover ").concat(m).trim(),content:S,getPopupContainer:function(){return l.current||document.body},children:(0,em.jsx)("span",{ref:s,onClick:function(e){e.stopPropagation()},className:ec()("".concat(c,"-icon"),m,(0,a.Z)({},"".concat(c,"-icon-active"),y)),children:(0,em.jsx)(eR,{})})}))]}))},eB=n(84222),e_=n(20633),eH=n(70876);function ez(){return(0,em.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,em.jsx)("path",{d:"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"})})}var eV=function(e){var t,n,r;return(0,a.Z)({},e.componentCls,{position:"absolute",insetBlockStart:"18px",zIndex:"101",width:"24px",height:"24px",fontSize:["14px","16px"],textAlign:"center",borderRadius:"40px",insetInlineEnd:"-13px",transition:"transform 0.3s",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:null==(t=e.layout)||null==(t=t.sider)?void 0:t.colorTextCollapsedButton,backgroundColor:null==(n=e.layout)||null==(n=n.sider)?void 0:n.colorBgCollapsedButton,boxShadow:"0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)","&:hover":{color:null==(r=e.layout)||null==(r=r.sider)?void 0:r.colorTextCollapsedButtonHover,boxShadow:"0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)"},".anticon":{fontSize:"14px"},"& > svg":{transition:"transform  0.3s",transform:"rotate(90deg)"},"&-collapsed":{"& > svg":{transform:"rotate(-90deg)"}}})},eW=["isMobile","collapsed"],e$=function(e){var t,n=e.isMobile,r=e.collapsed,i=(0,c.Z)(e,eW),o=(t=e.className,(0,eC.Xj)("SiderMenuCollapsedIcon",function(e){return[eV((0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(t)}))]})),l=o.wrapSSR,s=o.hashId;return n&&r?null:l((0,em.jsx)("div",(0,d.Z)((0,d.Z)({},i),{},{className:ec()(e.className,s,(0,a.Z)((0,a.Z)({},"".concat(e.className,"-collapsed"),r),"".concat(e.className,"-is-mobile"),n)),children:(0,em.jsx)(ez,{})})))},eK=n(81632),eU=n(83739),eq=n(71123),eY=n(22878),eX=n(67911),eG=n(45648),eQ=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],eJ=h.forwardRef(function(e,t){var n=e.className,r=e.component,i=e.viewBox,o=e.spin,l=e.rotate,s=e.tabIndex,u=e.onClick,f=e.children,p=(0,c.Z)(e,eQ),m=h.useRef(),g=(0,eY.x1)(m,t);(0,eG.Kp)(!!(r||f),"Should have `component` prop or `children`."),(0,eG.C3)(m);var v=h.useContext(eX.Z),y=v.prefixCls,b=void 0===y?"anticon":y,x=v.rootClassName,Z=ec()(x,b,(0,a.Z)({},"".concat(b,"-spin"),!!o&&!!r),n),C=ec()((0,a.Z)({},"".concat(b,"-spin"),!!o)),S=(0,d.Z)((0,d.Z)({},eG.vD),{},{className:C,style:l?{msTransform:"rotate(".concat(l,"deg)"),transform:"rotate(".concat(l,"deg)")}:void 0,viewBox:i});i||delete S.viewBox;var w=s;return void 0===w&&u&&(w=-1),h.createElement("span",(0,ey.Z)({role:"img"},p,{ref:g,tabIndex:w,onClick:u,className:Z}),r?h.createElement(r,S,f):f?((0,eG.Kp)(!!i||1===h.Children.count(f)&&h.isValidElement(f)&&"use"===h.Children.only(f).type,"Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),h.createElement("svg",(0,ey.Z)({},S,{viewBox:i}),f)):null)});eJ.displayName="AntdIcon";var e0=["type","children"],e1=new Set;function e2(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=e[t];if("string"==typeof n&&n.length&&!e1.has(n)){var r=document.createElement("script");r.setAttribute("src",n),r.setAttribute("data-namespace",n),e.length>t+1&&(r.onload=function(){e2(e,t+1)},r.onerror=function(){e2(e,t+1)}),e1.add(n),document.body.appendChild(r)}}function e5(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.scriptUrl,n=e.extraCommonProps,r=void 0===n?{}:n;t&&"undefined"!=typeof document&&"undefined"!=typeof window&&"function"==typeof document.createElement&&(Array.isArray(t)?e2(t.reverse()):e2([t]));var i=h.forwardRef(function(e,t){var n=e.type,i=e.children,o=(0,c.Z)(e,e0),a=null;return e.type&&(a=h.createElement("use",{xlinkHref:"#".concat(n)})),i&&(a=i),h.createElement(eJ,(0,ey.Z)({},r,o,{ref:t}),a)});return i.displayName="Iconfont",i}var e6=n(92939),e8=n(7102),e3={navTheme:"light",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!0,iconfontUrl:"",colorPrimary:"#1677FF",splitMenus:!1},e4=function(e,t){var n,r,i=t.includes("horizontal")?null==(n=e.layout)?void 0:n.header:null==(r=e.layout)?void 0:r.sider;return(0,d.Z)((0,d.Z)((0,a.Z)({},"".concat(e.componentCls),(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({background:"transparent",color:null==i?void 0:i.colorTextMenu,border:"none"},"".concat(e.componentCls,"-menu-item"),{transition:"none !important"}),"".concat(e.componentCls,"-submenu-has-icon"),(0,a.Z)({},"> ".concat(e.antCls,"-menu-sub"),{paddingInlineStart:10})),"".concat(e.antCls,"-menu-title-content"),{width:"100%",height:"100%",display:"inline-flex"}),"".concat(e.antCls,"-menu-title-content"),{"&:first-child":{width:"100%"}}),"".concat(e.componentCls,"-item-icon"),{display:"flex",alignItems:"center"}),"&&-collapsed",(0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(e.antCls,"-menu-item, \n        ").concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-item, \n        ").concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title, \n        ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title"),{paddingInline:"0 !important",marginBlock:"4px !important"}),"".concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title, \n        ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title"),{backgroundColor:null==i?void 0:i.colorBgMenuItemSelected,borderRadius:e.borderRadiusLG}),"".concat(e.componentCls,"-group"),(0,a.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{paddingInline:0}))),"&-item-title",(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({display:"flex",flexDirection:"row",alignItems:"center",gap:e.marginXS},"".concat(e.componentCls,"-item-text"),{maxWidth:"100%",textOverflow:"ellipsis",overflow:"hidden",wordBreak:"break-all",whiteSpace:"nowrap"}),"&-collapsed",(0,a.Z)((0,a.Z)({minWidth:40,height:40},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px !important",height:"16px"}}),"".concat(e.componentCls,"-item-text-has-icon"),{display:"none !important"})),"&-collapsed-level-0",{flexDirection:"column",justifyContent:"center"}),"&".concat(e.componentCls,"-group-item-title"),{gap:e.marginXS,height:18,overflow:"hidden"}),"&".concat(e.componentCls,"-item-collapsed-show-title"),(0,a.Z)({lineHeight:"16px",gap:0},"&".concat(e.componentCls,"-item-title-collapsed"),(0,a.Z)((0,a.Z)({display:"flex"},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px!important",height:"16px"}}),"".concat(e.componentCls,"-item-text"),{opacity:"1 !important",display:"inline !important",textAlign:"center",fontSize:12,height:12,lineHeight:"12px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",margin:0,padding:0,marginBlockStart:4})))),"&-group",(0,a.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:12,color:e.colorTextLabel,".anticon":{marginInlineEnd:8}})),"&-group-divider",{color:e.colorTextSecondary,fontSize:12,lineHeight:20})),t.includes("horizontal")?{}:(0,a.Z)({},"".concat(e.antCls,"-menu-submenu").concat(e.antCls,"-menu-submenu-popup"),(0,a.Z)({},"".concat(e.componentCls,"-item-title"),{alignItems:"flex-start"}))),{},(0,a.Z)({},"".concat(e.antCls,"-menu-submenu-popup"),{backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"}))},e9=function(e){var t=(0,h.useState)(e.collapsed),n=(0,u.Z)(t,2),r=n[0],i=n[1],o=(0,h.useState)(!1),a=(0,u.Z)(o,2),l=a[0],s=a[1];return((0,h.useEffect)(function(){s(!1),setTimeout(function(){i(e.collapsed)},400)},[e.collapsed]),e.disable)?e.children:(0,em.jsx)(e6.Z,{title:e.title,open:!!r&&!!e.collapsed&&l,placement:"right",onOpenChange:s,children:e.children})},e7=e5({scriptUrl:e3.iconfontUrl}),te=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"icon-",n=arguments.length>2?arguments[2]:void 0;if("string"==typeof e&&""!==e){if(ej(e)||/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(e))return(0,em.jsx)("img",{width:16,src:e,alt:"icon",className:n},e);if(e.startsWith(t))return(0,em.jsx)(e7,{type:e})}return e},tt=function(e){return e&&"string"==typeof e?e.substring(0,1).toUpperCase():null},tn=(0,eU.Z)(function e(t){var n=this;(0,eq.Z)(this,e),(0,a.Z)(this,"props",void 0),(0,a.Z)(this,"getNavMenuItems",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0;return e.map(function(e){return n.getSubMenuOrItem(e,t,r)}).filter(function(e){return e}).flat(1)}),(0,a.Z)(this,"getSubMenuOrItem",function(e,t,r){var i=n.props,o=i.subMenuItemRender,l=i.baseClassName,s=i.prefixCls,c=i.collapsed,u=i.menu,f=i.iconPrefixes,p=i.layout,h=(null==u?void 0:u.type)==="group"&&"top"!==p,m=n.props.token,g=n.getIntlName(e),v=(null==e?void 0:e.children)||(null==e?void 0:e.routes),y=h&&0===t?"group":void 0;if(Array.isArray(v)&&v.length>0){var b,x,Z,C,S,w=0===t||h&&1===t,k=te(e.icon,f,"".concat(l,"-icon ").concat(null==(b=n.props)?void 0:b.hashId)),M=c&&w?tt(g):null,F=(0,em.jsxs)("div",{className:ec()("".concat(l,"-item-title"),null==(x=n.props)?void 0:x.hashId,(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(l,"-item-title-collapsed"),c),"".concat(l,"-item-title-collapsed-level-").concat(r),c),"".concat(l,"-group-item-title"),"group"===y),"".concat(l,"-item-collapsed-show-title"),(null==u?void 0:u.collapsedShowTitle)&&c)),children:["group"===y&&c?null:w&&k?(0,em.jsx)("span",{className:"".concat(l,"-item-icon ").concat(null==(Z=n.props)?void 0:Z.hashId).trim(),children:k}):M,(0,em.jsx)("span",{className:ec()("".concat(l,"-item-text"),null==(C=n.props)?void 0:C.hashId,(0,a.Z)({},"".concat(l,"-item-text-has-icon"),"group"!==y&&w&&(k||M))),children:g})]}),T=o?o((0,d.Z)((0,d.Z)({},e),{},{isUrl:!1}),F,n.props):F;if(h&&0===t&&n.props.collapsed&&!u.collapsedShowGroupTitle)return n.getNavMenuItems(v,t+1,t);var P=n.getNavMenuItems(v,t+1,h&&0===t&&n.props.collapsed?t:t+1);return[{type:y,key:e.key||e.path,label:T,onClick:h?void 0:e.onTitleClick,children:P,className:ec()((0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(l,"-group"),"group"===y),"".concat(l,"-submenu"),"group"!==y),"".concat(l,"-submenu-has-icon"),"group"!==y&&w&&k))},h&&0===t?{type:"divider",prefixCls:s,className:"".concat(l,"-divider"),key:(e.key||e.path)+"-group-divider",style:{padding:0,borderBlockEnd:0,margin:n.props.collapsed?"4px":"6px 16px",marginBlockStart:n.props.collapsed?4:8,borderColor:null==m||null==(S=m.layout)||null==(S=S.sider)?void 0:S.colorMenuItemDivider}}:void 0].filter(Boolean)}return{className:"".concat(l,"-menu-item"),disabled:e.disabled,key:e.key||e.path,onClick:e.onTitleClick,label:n.getMenuItemPath(e,t,r)}}),(0,a.Z)(this,"getIntlName",function(e){var t=e.name,r=e.locale,i=n.props,o=i.menu,a=i.formatMessage,l=t;return(r&&(null==o?void 0:o.locale)!==!1&&(l=null==a?void 0:a({id:r,defaultMessage:t})),n.props.menuTextRender)?n.props.menuTextRender(e,l,n.props):l}),(0,a.Z)(this,"getMenuItemPath",function(e,t,r){var i,o,l,s,c,u,f,p=n.conversionPath(e.path||"/"),h=n.props,m=h.location,g=h.isMobile,v=h.onCollapse,y=h.menuItemRender,b=h.iconPrefixes,x=n.getIntlName(e),Z=n.props,C=Z.baseClassName,S=Z.menu,w=Z.collapsed,k=(null==S?void 0:S.type)==="group",M=0===t||k&&1===t,F=M?te(e.icon,b,"".concat(C,"-icon ").concat(null==(i=n.props)?void 0:i.hashId)):null,T=w&&M?tt(x):null,P=(0,em.jsxs)("div",{className:ec()("".concat(C,"-item-title"),null==(o=n.props)?void 0:o.hashId,(0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(C,"-item-title-collapsed"),w),"".concat(C,"-item-title-collapsed-level-").concat(r),w),"".concat(C,"-item-collapsed-show-title"),(null==S?void 0:S.collapsedShowTitle)&&w)),children:[(0,em.jsx)("span",{className:"".concat(C,"-item-icon ").concat(null==(l=n.props)?void 0:l.hashId).trim(),style:{display:null!==T||F?"":"none"},children:F||(0,em.jsx)("span",{className:"anticon",children:T})}),(0,em.jsx)("span",{className:ec()("".concat(C,"-item-text"),null==(s=n.props)?void 0:s.hashId,(0,a.Z)({},"".concat(C,"-item-text-has-icon"),M&&(F||T))),children:x})]},p),E=ej(p);if(E&&(P=(0,em.jsxs)("span",{onClick:function(){var e,t;null==(e=window)||null==(t=e.open)||t.call(e,p,"_blank")},className:ec()("".concat(C,"-item-title"),null==(c=n.props)?void 0:c.hashId,(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(C,"-item-title-collapsed"),w),"".concat(C,"-item-title-collapsed-level-").concat(r),w),"".concat(C,"-item-link"),!0),"".concat(C,"-item-collapsed-show-title"),(null==S?void 0:S.collapsedShowTitle)&&w)),children:[(0,em.jsx)("span",{className:"".concat(C,"-item-icon ").concat(null==(u=n.props)?void 0:u.hashId).trim(),style:{display:null!==T||F?"":"none"},children:F||(0,em.jsx)("span",{className:"anticon",children:T})}),(0,em.jsx)("span",{className:ec()("".concat(C,"-item-text"),null==(f=n.props)?void 0:f.hashId,(0,a.Z)({},"".concat(C,"-item-text-has-icon"),M&&(F||T))),children:x})]},p)),y){var R=(0,d.Z)((0,d.Z)({},e),{},{isUrl:E,itemPath:p,isMobile:g,replace:p===(void 0===m?{pathname:"/"}:m).pathname,onClick:function(){return v&&v(!0)},children:void 0});return 0===t?(0,em.jsx)(e9,{collapsed:w,title:x,disable:e.disabledTooltip,children:y(R,P,n.props)}):y(R,P,n.props)}return 0===t?(0,em.jsx)(e9,{collapsed:w,title:x,disable:e.disabledTooltip,children:P}):P}),(0,a.Z)(this,"conversionPath",function(e){return e&&0===e.indexOf("http")?e:"/".concat(e||"").replace(/\/+/g,"/")}),this.props=t}),tr=function(e,t){var n=t.layout,r=t.collapsed,i={};return e&&!r&&["side","mix"].includes(n||"mix")&&(i={openKeys:e}),i},ti=function(e){var t=e.mode,n=e.className,r=e.handleOpenChange,i=e.style,o=e.menuData,l=e.prefixCls,s=e.menu,c=e.matchMenuKeys,m=e.iconfontUrl,g=e.selectedKeys,v=e.onSelect,y=e.menuRenderType,b=e.openKeys,x=(0,h.useContext)(f.L_),Z=x.dark,C=x.token,S="".concat(l,"-base-menu-").concat(t),w=(0,h.useRef)([]),k=(0,p.Z)(null==s?void 0:s.defaultOpenAll),M=(0,u.Z)(k,2),F=M[0],T=M[1],P=(0,p.Z)(function(){return null!=s&&s.defaultOpenAll?eM(o)||[]:!1!==b&&[]},{value:!1===b?void 0:b,onChange:r}),E=(0,u.Z)(P,2),R=E[0],I=E[1],j=(0,p.Z)([],{value:g,onChange:v?function(e){v&&e&&v(e)}:void 0}),L=(0,u.Z)(j,2),A=L[0],O=L[1];(0,h.useEffect)(function(){(null==s||!s.defaultOpenAll)&&!1!==b&&c&&(I(c),O(c))},[c.join("-")]),(0,h.useEffect)(function(){m&&(e7=e5({scriptUrl:m}))},[m]),(0,h.useEffect)(function(){if(c.join("-")!==(A||[]).join("-")&&O(c),F||!1===b||c.join("-")===(R||[]).join("-"))null!=s&&s.ignoreFlatMenu&&F?I(eM(o)):T(!1);else{var e=c;(null==s?void 0:s.autoClose)===!1&&(e=Array.from(new Set([].concat((0,eK.Z)(c),(0,eK.Z)(R||[]))))),I(e)}},[c.join("-")]);var N=(0,h.useMemo)(function(){return tr(R,e)},[R&&R.join(","),e.layout,e.collapsed]),D=(0,eC.Xj)("ProLayoutBaseMenu"+t,function(e){return[e4((0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(S)}),t||"inline")]}),B=D.wrapSSR,_=D.hashId,H=(0,h.useMemo)(function(){return new tn((0,d.Z)((0,d.Z)({},e),{},{token:C,menuRenderType:y,baseClassName:S,hashId:_}))},[e,C,y,S,_]);if(null!=s&&s.loading)return(0,em.jsx)("div",{style:null!=t&&t.includes("inline")?{padding:24}:{marginBlockStart:16},children:(0,em.jsx)(e8.Z,{active:!0,title:!1,paragraph:{rows:null!=t&&t.includes("inline")?6:1}})});!1!==e.openKeys||e.handleOpenChange||(w.current=c);var z=e.postMenuData?e.postMenuData(o):o;return z&&(null==z?void 0:z.length)<1?null:B((0,h.createElement)(eH.Z,(0,d.Z)((0,d.Z)({},N),{},{_internalDisableMenuItemTitleTooltip:!0,key:"Menu",mode:t,inlineIndent:16,defaultOpenKeys:w.current,theme:Z?"dark":"light",selectedKeys:A,style:(0,d.Z)({backgroundColor:"transparent",border:"none"},i),className:ec()(n,_,S,(0,a.Z)((0,a.Z)({},"".concat(S,"-horizontal"),"horizontal"===t),"".concat(S,"-collapsed"),e.collapsed)),items:H.getNavMenuItems(z,0,0),onOpenChange:function(t){e.collapsed||I(t)}},e.menuProps)))},to=["title","render"],ta=h.memo(function(e){return(0,em.jsx)(em.Fragment,{children:e.children})}),tl=el.Z.Sider,ts=el.Z._InternalSiderContext,tc=void 0===ts?{Provider:ta}:ts,tu=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"menuHeaderRender",n=e.logo,r=e.title,i=e.layout,o=e[t];if(!1===o)return null;var a=eN(n),l=(0,em.jsx)("h1",{children:null!=r?r:"Ant Design Pro"});return o?o(a,e.collapsed?null:l,e):e.isMobile?null:("mix"!==i||"menuHeaderRender"!==t)&&(e.collapsed?(0,em.jsx)("a",{children:a},"title"):(0,em.jsxs)("a",{children:[a,l]},"title"))},td=function(e){var t,n,r,i,o,l=e.collapsed,s=e.originCollapsed,u=e.fixSiderbar,p=e.menuFooterRender,m=e.onCollapse,g=e.theme,v=e.siderWidth,y=e.isMobile,b=e.onMenuHeaderClick,x=e.breakpoint,Z=void 0===x?"lg":x,C=e.style,S=e.layout,w=e.menuExtraRender,k=void 0!==w&&w,M=e.links,F=e.menuContentRender,T=e.collapsedButtonRender,P=e.prefixCls,E=e.avatarProps,R=e.rightContentRender,I=e.actionsRender,j=e.onOpenChange,L=e.stylish,A=e.logoStyle,O=(0,h.useContext)(f.L_).hashId,N=(0,h.useMemo)(function(){return!y&&"mix"!==S},[y,S]),D="".concat(P,"-sider"),B=(t="".concat(D,".").concat(D,"-stylish"),r=(n={stylish:L,proLayoutCollapsedWidth:64}).stylish,i=n.proLayoutCollapsedWidth,(0,eC.Xj)("ProLayoutSiderMenuStylish",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(t),proLayoutCollapsedWidth:i});return r?[(0,a.Z)({},"div".concat(e.proComponentsCls,"-layout"),(0,a.Z)({},"".concat(n.componentCls),null==r?void 0:r(n)))]:[]})),_=ec()("".concat(D),O,(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(D,"-fixed"),u),"".concat(D,"-fixed-mix"),"mix"===S&&!y&&u),"".concat(D,"-collapsed"),e.collapsed),"".concat(D,"-layout-").concat(S),S&&!y),"".concat(D,"-light"),"dark"!==g),"".concat(D,"-mix"),"mix"===S&&!y),"".concat(D,"-stylish"),!!L)),H=tu(e),z=k&&k(e),V=(0,h.useMemo)(function(){return!1!==F&&(0,h.createElement)(ti,(0,d.Z)((0,d.Z)({},e),{},{key:"base-menu",mode:l&&!y?"vertical":"inline",handleOpenChange:j,style:{width:"100%"},className:"".concat(D,"-menu ").concat(O).trim()}))},[D,O,F,j,e]),W=(M||[]).map(function(e,t){return{className:"".concat(D,"-link"),label:e,key:t}}),$=(0,h.useMemo)(function(){return F?F(e,V):V},[F,V,e]),K=(0,h.useMemo)(function(){if(!E)return null;var t=E.title,n=E.render,r=(0,c.Z)(E,to),i=(0,em.jsxs)("div",{className:"".concat(D,"-actions-avatar"),children:[null!=r&&r.src||null!=r&&r.srcSet||r.icon||r.children?(0,em.jsx)(eB.Z,(0,d.Z)({size:28},r)):null,E.title&&!l&&(0,em.jsx)("span",{children:t})]});return n?n(E,i,e):i},[E,D,l]),U=(0,h.useMemo)(function(){return I?(0,em.jsx)(e_.Z,{align:"center",size:4,direction:l?"vertical":"horizontal",className:ec()(["".concat(D,"-actions-list"),l&&"".concat(D,"-actions-list-collapsed"),O]),children:[null==I?void 0:I(e)].flat(1).map(function(e,t){return(0,em.jsx)("div",{className:"".concat(D,"-actions-list-item ").concat(O).trim(),children:e},t)})}):null},[I,D,l]),q=(0,h.useMemo)(function(){return(0,em.jsx)(eD,{onItemClick:e.itemClick,appListRender:e.appListRender,appList:e.appList,prefixCls:e.prefixCls})},[e.appList,e.appListRender,e.prefixCls]),Y=(0,h.useMemo)(function(){if(!1===T)return null;var e=(0,em.jsx)(e$,{isMobile:y,collapsed:s,className:"".concat(D,"-collapsed-button"),onClick:function(){null==m||m(!s)}});return T?T(l,e):e},[T,y,s,D,l,m]),X=(0,h.useMemo)(function(){return K||U?(0,em.jsxs)("div",{className:ec()("".concat(D,"-actions"),O,l&&"".concat(D,"-actions-collapsed")),children:[K,U]}):null},[U,K,D,l,O]),G=(0,h.useMemo)(function(){var t;return null!=e&&null!=(t=e.menu)&&t.hideMenuWhenCollapsed&&l?"".concat(D,"-hide-menu-collapsed"):null},[D,l,null==e||null==(o=e.menu)?void 0:o.hideMenuWhenCollapsed]),Q=p&&(null==p?void 0:p(e)),J=(0,em.jsxs)(em.Fragment,{children:[H&&(0,em.jsxs)("div",{className:ec()([ec()("".concat(D,"-logo"),O,(0,a.Z)({},"".concat(D,"-logo-collapsed"),l))]),onClick:N?b:void 0,id:"logo",style:A,children:[H,q]}),z&&(0,em.jsx)("div",{className:ec()(["".concat(D,"-extra"),!H&&"".concat(D,"-extra-no-logo"),O]),children:z}),(0,em.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:$}),(0,em.jsxs)(tc.Provider,{value:{},children:[M?(0,em.jsx)("div",{className:"".concat(D,"-links ").concat(O).trim(),children:(0,em.jsx)(eH.Z,{inlineIndent:16,className:"".concat(D,"-link-menu ").concat(O).trim(),selectedKeys:[],openKeys:[],theme:g,mode:"inline",items:W})}):null,N&&(0,em.jsxs)(em.Fragment,{children:[X,!U&&R?(0,em.jsx)("div",{className:ec()("".concat(D,"-actions"),O,(0,a.Z)({},"".concat(D,"-actions-collapsed"),l)),children:null==R?void 0:R(e)}):null]}),Q&&(0,em.jsx)("div",{className:ec()(["".concat(D,"-footer"),O,(0,a.Z)({},"".concat(D,"-footer-collapsed"),l)]),children:Q})]})]});return B.wrapSSR((0,em.jsxs)(em.Fragment,{children:[u&&!y&&!G&&(0,em.jsx)("div",{style:(0,d.Z)({width:l?64:v,overflow:"hidden",flex:"0 0 ".concat(l?64:v,"px"),maxWidth:l?64:v,minWidth:l?64:v,transition:"all 0.2s ease 0s"},C)}),(0,em.jsxs)(tl,{collapsible:!0,trigger:null,collapsed:l,breakpoint:!1===Z?void 0:Z,onCollapse:function(e){y||null==m||m(e)},collapsedWidth:64,style:C,theme:g,width:v,className:ec()(_,O,G),children:[G?(0,em.jsx)("div",{className:"".concat(D,"-hide-when-collapsed ").concat(O).trim(),style:{height:"100%",width:"100%",opacity:+!G},children:J}):J,Y]})]}))},tf=n(67144),tp=n(86708),th=function(e){var t,n,r,i,o;return(0,a.Z)({},e.componentCls,{"&-header-actions":{display:"flex",height:"100%",alignItems:"center","&-item":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingBlock:0,paddingInline:2,color:null==(t=e.layout)||null==(t=t.header)?void 0:t.colorTextRightActionsItem,fontSize:"16px",cursor:"pointer",borderRadius:e.borderRadius,"> *":{paddingInline:6,paddingBlock:6,borderRadius:e.borderRadius,"&:hover":{backgroundColor:null==(n=e.layout)||null==(n=n.header)?void 0:n.colorBgRightActionsItemHover}}},"&-avatar":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInlineStart:e.padding,paddingInlineEnd:e.padding,cursor:"pointer",color:null==(r=e.layout)||null==(r=r.header)?void 0:r.colorTextRightActionsItem,"> div":{height:"44px",color:null==(i=e.layout)||null==(i=i.header)?void 0:i.colorTextRightActionsItem,paddingInline:8,paddingBlock:8,cursor:"pointer",display:"flex",alignItems:"center",lineHeight:"44px",borderRadius:e.borderRadius,"&:hover":{backgroundColor:null==(o=e.layout)||null==(o=o.header)?void 0:o.colorBgRightActionsItemHover}}}}})},tm=["rightContentRender","avatarProps","actionsRender","headerContentRender"],tg=["title","render"],tv=function(e){var t,n=e.rightContentRender,r=e.avatarProps,i=e.actionsRender,o=(e.headerContentRender,(0,c.Z)(e,tm)),f=(0,h.useContext)(ea.ZP.ConfigContext).getPrefixCls,p="".concat(f(),"-pro-global-header"),m=(0,eC.Xj)("ProLayoutRightContent",function(e){return[th((0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(p)}))]}),g=m.wrapSSR,v=m.hashId,y=(0,h.useState)("auto"),b=(0,u.Z)(y,2),x=b[0],Z=b[1],C=(0,h.useMemo)(function(){if(!r)return null;var e=r.title,t=r.render,n=(0,c.Z)(r,tg),i=[null!=n&&n.src||null!=n&&n.srcSet||n.icon||n.children?(0,h.createElement)(eB.Z,(0,d.Z)((0,d.Z)({},n),{},{size:28,key:"avatar"})):null,e?(0,em.jsx)("span",{style:{marginInlineStart:8},children:e},"name"):void 0];return t?t(r,(0,em.jsx)("div",{children:i}),o):(0,em.jsx)("div",{children:i})},[r]),S=i||C?function(e){var t=i&&(null==i?void 0:i(e));return t||C?Array.isArray(t)?g((0,em.jsxs)("div",{className:"".concat(p,"-header-actions ").concat(v).trim(),children:[t.filter(Boolean).map(function(e,t){var n,r=!1;return h.isValidElement(e)&&(r=!!(null!=e&&null!=(n=e.props)&&n["aria-hidden"])),(0,em.jsx)("div",{className:ec()("".concat(p,"-header-actions-item ").concat(v),(0,a.Z)({},"".concat(p,"-header-actions-hover"),!r)),children:e},t)}),C&&(0,em.jsx)("span",{className:"".concat(p,"-header-actions-avatar ").concat(v).trim(),children:C})]})):g((0,em.jsxs)("div",{className:"".concat(p,"-header-actions ").concat(v).trim(),children:[t,C&&(0,em.jsx)("span",{className:"".concat(p,"-header-actions-avatar ").concat(v).trim(),children:C})]})):null}:void 0,w=(0,tf.D)((t=(0,s.Z)((0,l.Z)().mark(function e(t){return(0,l.Z)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:Z(t);case 1:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)}),160),k=S||n;return(0,em.jsx)("div",{className:"".concat(p,"-right-content ").concat(v).trim(),style:{minWidth:x,height:"100%"},children:(0,em.jsx)("div",{style:{height:"100%"},children:(0,em.jsx)(tp.Z,{onResize:function(e){var t=e.width;w.run(t)},children:k?(0,em.jsx)("div",{style:{display:"flex",alignItems:"center",height:"100%",justifyContent:"flex-end"},children:k((0,d.Z)((0,d.Z)({},o),{},{rightContentSize:x}))}):null})})})},ty=function(e){var t,n;return(0,a.Z)({},e.componentCls,{position:"relative",width:"100%",height:"100%",backgroundColor:"transparent",".anticon":{color:"inherit"},"&-main":{display:"flex",height:"100%",paddingInlineStart:"16px","&-left":(0,a.Z)({display:"flex",alignItems:"center"},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16,marginInlineStart:-8})},"&-wide":{maxWidth:1152,margin:"0 auto"},"&-logo":{position:"relative",display:"flex",height:"100%",alignItems:"center",overflow:"hidden","> *:first-child":{display:"flex",alignItems:"center",minHeight:"22px",fontSize:"22px"},"> *:first-child > img":{display:"inline-block",height:"32px",verticalAlign:"middle"},"> *:first-child > h1":{display:"inline-block",marginBlock:0,marginInline:0,lineHeight:"24px",marginInlineStart:6,fontWeight:"600",fontSize:"16px",color:null==(t=e.layout)||null==(t=t.header)?void 0:t.colorHeaderTitle,verticalAlign:"top"}},"&-menu":{minWidth:0,display:"flex",alignItems:"center",paddingInline:6,paddingBlock:6,lineHeight:"".concat(Math.max(((null==(n=e.layout)||null==(n=n.header)?void 0:n.heightLayoutHeader)||56)-12,40),"px")}})},tb=function(e){var t,n,r,i,o,l,s,c=(0,h.useRef)(null),u=e.onMenuHeaderClick,p=e.contentWidth,m=e.rightContentRender,g=e.className,v=e.style,y=e.headerContentRender,b=e.layout,x=e.actionsRender,Z=(0,h.useContext)(ea.ZP.ConfigContext).getPrefixCls,S=(0,h.useContext)(f.L_).dark,w="".concat(e.prefixCls||Z("pro"),"-top-nav-header"),k=(0,eC.Xj)("ProLayoutTopNavHeader",function(e){return[ty((0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(w)}))]}),M=k.wrapSSR,F=k.hashId,T=void 0;void 0!==e.menuHeaderRender?T="menuHeaderRender":("mix"===b||"top"===b)&&(T="headerTitleRender");var P=tu((0,d.Z)((0,d.Z)({},e),{},{collapsed:!1}),T),E=(0,h.useContext)(f.L_).token,R=(0,h.useMemo)(function(){var t,n,r,i,o,a,l,s,c,u,p,h,m,g=(0,em.jsx)(ea.ZP,{theme:{hashed:(0,f.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"},Menu:(0,d.Z)({},C({colorItemBg:(null==(t=E.layout)||null==(t=t.header)?void 0:t.colorBgHeader)||"transparent",colorSubItemBg:(null==(n=E.layout)||null==(n=n.header)?void 0:n.colorBgHeader)||"transparent",radiusItem:E.borderRadius,colorItemBgSelected:(null==(r=E.layout)||null==(r=r.header)?void 0:r.colorBgMenuItemSelected)||(null==E?void 0:E.colorBgTextHover),itemHoverBg:(null==(i=E.layout)||null==(i=i.header)?void 0:i.colorBgMenuItemHover)||(null==E?void 0:E.colorBgTextHover),colorItemBgSelectedHorizontal:(null==(o=E.layout)||null==(o=o.header)?void 0:o.colorBgMenuItemSelected)||(null==E?void 0:E.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:(null==(a=E.layout)||null==(a=a.header)?void 0:a.colorTextMenu)||(null==E?void 0:E.colorTextSecondary),colorItemTextHoverHorizontal:(null==(l=E.layout)||null==(l=l.header)?void 0:l.colorTextMenuActive)||(null==E?void 0:E.colorText),colorItemTextSelectedHorizontal:(null==(s=E.layout)||null==(s=s.header)?void 0:s.colorTextMenuSelected)||(null==E?void 0:E.colorTextBase),horizontalItemBorderRadius:4,colorItemTextHover:(null==(c=E.layout)||null==(c=c.header)?void 0:c.colorTextMenuActive)||"rgba(0, 0, 0, 0.85)",horizontalItemHoverBg:(null==(u=E.layout)||null==(u=u.header)?void 0:u.colorBgMenuItemHover)||"rgba(0, 0, 0, 0.04)",colorItemTextSelected:(null==(p=E.layout)||null==(p=p.header)?void 0:p.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:null==E?void 0:E.colorBgElevated,subMenuItemBg:null==E?void 0:E.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:null==E?void 0:E.colorBgElevated}))},token:{colorBgElevated:(null==(h=E.layout)||null==(h=h.header)?void 0:h.colorBgHeader)||"transparent"}},children:(0,em.jsx)(ti,(0,d.Z)((0,d.Z)((0,d.Z)({theme:S?"dark":"light"},e),{},{className:"".concat(w,"-base-menu ").concat(F).trim()},e.menuProps),{},{style:(0,d.Z)({width:"100%"},null==(m=e.menuProps)?void 0:m.style),collapsed:!1,menuRenderType:"header",mode:"horizontal"}))});return y?y(e,g):g},[null==(t=E.layout)||null==(t=t.header)?void 0:t.colorBgHeader,null==(n=E.layout)||null==(n=n.header)?void 0:n.colorBgMenuItemSelected,null==(r=E.layout)||null==(r=r.header)?void 0:r.colorBgMenuItemHover,null==(i=E.layout)||null==(i=i.header)?void 0:i.colorTextMenu,null==(o=E.layout)||null==(o=o.header)?void 0:o.colorTextMenuActive,null==(l=E.layout)||null==(l=l.header)?void 0:l.colorTextMenuSelected,null==(s=E.layout)||null==(s=s.header)?void 0:s.colorBgMenuElevated,E.borderRadius,null==E?void 0:E.colorBgTextHover,null==E?void 0:E.colorTextSecondary,null==E?void 0:E.colorText,null==E?void 0:E.colorTextBase,E.colorBgElevated,S,e,w,F,y]);return M((0,em.jsx)("div",{className:ec()(w,F,g,(0,a.Z)({},"".concat(w,"-light"),!0)),style:v,children:(0,em.jsxs)("div",{ref:c,className:ec()("".concat(w,"-main"),F,(0,a.Z)({},"".concat(w,"-wide"),"Fixed"===p&&"top"===b)),children:[P&&(0,em.jsxs)("div",{className:ec()("".concat(w,"-main-left ").concat(F)),onClick:u,children:[(0,em.jsx)(eD,(0,d.Z)({},e)),(0,em.jsx)("div",{className:"".concat(w,"-logo ").concat(F).trim(),id:"logo",children:P},"logo")]}),(0,em.jsx)("div",{style:{flex:1},className:"".concat(w,"-menu ").concat(F).trim(),children:R}),(m||x||e.avatarProps)&&(0,em.jsx)(tv,(0,d.Z)((0,d.Z)({rightContentRender:m},e),{},{prefixCls:w}))]})}))},tx=function(e){var t,n,r;return(0,a.Z)({},e.componentCls,(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({position:"relative",background:"transparent",display:"flex",alignItems:"center",marginBlock:0,marginInline:16,height:(null==(t=e.layout)||null==(t=t.header)?void 0:t.heightLayoutHeader)||56,boxSizing:"border-box","> a":{height:"100%"}},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16}),"&-collapsed-button",{minHeight:"22px",color:null==(n=e.layout)||null==(n=n.header)?void 0:n.colorHeaderTitle,fontSize:"18px",marginInlineEnd:"16px"}),"&-logo",{position:"relative",marginInlineEnd:"16px",a:{display:"flex",alignItems:"center",height:"100%",minHeight:"22px",fontSize:"20px"},img:{height:"28px"},h1:{height:"32px",marginBlock:0,marginInline:0,marginInlineStart:8,fontWeight:"600",color:(null==(r=e.layout)||null==(r=r.header)?void 0:r.colorHeaderTitle)||e.colorTextHeading,fontSize:"18px",lineHeight:"32px"},"&-mix":{display:"flex",alignItems:"center"}}),"&-logo-mobile",{minWidth:"24px",marginInlineEnd:0}))},tZ=function(e){var t=e.isMobile,n=e.logo,r=e.collapsed,i=e.onCollapse,o=e.rightContentRender,l=e.menuHeaderRender,s=e.onMenuHeaderClick,c=e.className,u=e.style,f=e.layout,p=e.children,m=e.splitMenus,g=e.menuData,v=e.prefixCls,y=(0,h.useContext)(ea.ZP.ConfigContext),b=y.getPrefixCls,x=y.direction,Z="".concat(v||b("pro"),"-global-header"),C=(0,eC.Xj)("ProLayoutGlobalHeader",function(e){return[tx((0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(Z)}))]}),S=C.wrapSSR,w=C.hashId,k=ec()(c,Z,w);if("mix"===f&&!t&&m){var M=eF((g||[]).map(function(e){return(0,d.Z)((0,d.Z)({},e),{},{children:void 0,routes:void 0})}));return(0,em.jsx)(tb,(0,d.Z)((0,d.Z)({mode:"horizontal"},e),{},{splitMenus:!1,menuData:M}))}var F=ec()("".concat(Z,"-logo"),w,(0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(Z,"-logo-rtl"),"rtl"===x),"".concat(Z,"-logo-mix"),"mix"===f),"".concat(Z,"-logo-mobile"),t)),T=(0,em.jsx)("span",{className:F,children:(0,em.jsx)("a",{children:eN(n)})},"logo");return S((0,em.jsxs)("div",{className:k,style:(0,d.Z)({},u),children:[t&&(0,em.jsx)("span",{className:"".concat(Z,"-collapsed-button ").concat(w).trim(),onClick:function(){null==i||i(!r)},children:(0,em.jsx)(eP,{})}),t&&(!1===l?null:l?l(T,null):T),"mix"===f&&!t&&(0,em.jsxs)(em.Fragment,{children:[(0,em.jsx)(eD,(0,d.Z)({},e)),(0,em.jsx)("div",{className:F,onClick:s,children:tu((0,d.Z)((0,d.Z)({},e),{},{collapsed:!1}),"headerTitleRender")})]}),(0,em.jsx)("div",{style:{flex:1},children:p}),(o||e.actionsRender||e.avatarProps)&&(0,em.jsx)(tv,(0,d.Z)({rightContentRender:o},e))]}))},tC=function(e){var t,n,r,i;return(0,a.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,a.Z)({},"".concat(e.antCls,"-layout-header").concat(e.componentCls),{height:(null==(t=e.layout)||null==(t=t.header)?void 0:t.heightLayoutHeader)||56,lineHeight:"".concat((null==(n=e.layout)||null==(n=n.header)?void 0:n.heightLayoutHeader)||56,"px"),zIndex:19,width:"100%",paddingBlock:0,paddingInline:0,borderBlockEnd:"1px solid ".concat(e.colorSplit),backgroundColor:(null==(r=e.layout)||null==(r=r.header)?void 0:r.colorBgHeader)||"rgba(255, 255, 255, 0.4)",WebkitBackdropFilter:"blur(8px)",backdropFilter:"blur(8px)",transition:"background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)","&-fixed-header":{position:"fixed",insetBlockStart:0,width:"100%",zIndex:100,insetInlineEnd:0},"&-fixed-header-scroll":{backgroundColor:(null==(i=e.layout)||null==(i=i.header)?void 0:i.colorBgScrollHeader)||"rgba(255, 255, 255, 0.8)"},"&-header-actions":{display:"flex",alignItems:"center",fontSize:"16",cursor:"pointer","& &-item":{paddingBlock:0,paddingInline:8,"&:hover":{color:e.colorText}}},"&-header-realDark":{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"},"&-header-actions-header-action":{transition:"width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"}}))},tS=el.Z.Header,tw=function(e){var t,n,r,i,o,l,s,c=e.isMobile,p=e.fixedHeader,m=e.className,g=e.style,v=e.collapsed,y=e.prefixCls,b=e.onCollapse,x=e.layout,Z=e.headerRender,C=e.headerContentRender,S=(0,h.useContext)(f.L_).token,w=(0,h.useContext)(ea.ZP.ConfigContext),k=(0,h.useState)(!1),M=(0,u.Z)(k,2),F=M[0],T=M[1],P=p||"mix"===x,E=(0,h.useCallback)(function(){var t="top"===x,n=eF(e.menuData||[]),r=(0,em.jsx)(tZ,(0,d.Z)((0,d.Z)({onCollapse:b},e),{},{menuData:n,children:C&&C(e,null)}));return(t&&!c&&(r=(0,em.jsx)(tb,(0,d.Z)((0,d.Z)({mode:"horizontal",onCollapse:b},e),{},{menuData:n}))),Z&&"function"==typeof Z)?Z(e,r):r},[C,Z,c,x,b,e]);(0,h.useEffect)(function(){var e,t=(null==w||null==(e=w.getTargetContainer)?void 0:e.call(w))||document.body,n=function(){var e;return t.scrollTop>((null==(e=S.layout)||null==(e=e.header)?void 0:e.heightLayoutHeader)||56)&&!F?(T(!0),!0):(F&&T(!1),!1)};if(P&&"undefined"!=typeof window)return t.addEventListener("scroll",n,{passive:!0}),function(){t.removeEventListener("scroll",n)}},[null==(o=S.layout)||null==(o=o.header)?void 0:o.heightLayoutHeader,P,F]);var R="".concat(y,"-layout-header"),I=(0,eC.Xj)("ProLayoutHeader",function(e){return[tC((0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(R)}))]}),j=I.wrapSSR,L=I.hashId,A=(t="".concat(R,".").concat(R,"-stylish"),r=(n={proLayoutCollapsedWidth:64,stylish:e.stylish}).stylish,i=n.proLayoutCollapsedWidth,(0,eC.Xj)("ProLayoutHeaderStylish",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(t),proLayoutCollapsedWidth:i});return r?[(0,a.Z)({},"div".concat(e.proComponentsCls,"-layout"),(0,a.Z)({},"".concat(n.componentCls),null==r?void 0:r(n)))]:[]})),O=ec()(m,L,R,(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(R,"-fixed-header"),P),"".concat(R,"-fixed-header-scroll"),F),"".concat(R,"-mix"),"mix"===x),"".concat(R,"-fixed-header-action"),!v),"".concat(R,"-top-menu"),"top"===x),"".concat(R,"-header"),!0),"".concat(R,"-stylish"),!!e.stylish));return"side"!==x||c?A.wrapSSR(j((0,em.jsx)(em.Fragment,{children:(0,em.jsxs)(ea.ZP,{theme:{hashed:(0,f.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"}}},children:[P&&(0,em.jsx)(tS,{style:(0,d.Z)({height:(null==(l=S.layout)||null==(l=l.header)?void 0:l.heightLayoutHeader)||56,lineHeight:"".concat((null==(s=S.layout)||null==(s=s.header)?void 0:s.heightLayoutHeader)||56,"px"),backgroundColor:"transparent",zIndex:19},g)}),(0,em.jsx)(tS,{className:O,style:g,children:E()})]})}))):null},tk=n(88418),tM=n(85181),tF=new(n(88122)).E4("antBadgeLoadingCircle",{"0%":{display:"none",opacity:0,overflow:"hidden"},"80%":{overflow:"hidden"},"100%":{display:"unset",opacity:1}}),tT=function(e){var t,n,r,i,o,l,s,c,u,d,f,p;return(0,a.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(e.antCls,"-layout-sider").concat(e.componentCls),{background:(null==(t=e.layout)||null==(t=t.sider)?void 0:t.colorMenuBackground)||"transparent"}),e.componentCls,(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({position:"relative",boxSizing:"border-box","&-menu":{position:"relative",zIndex:10,minHeight:"100%"}},"& ".concat(e.antCls,"-layout-sider-children"),{position:"relative",display:"flex",flexDirection:"column",height:"100%",paddingInline:null==(n=e.layout)||null==(n=n.sider)?void 0:n.paddingInlineLayoutMenu,paddingBlock:null==(r=e.layout)||null==(r=r.sider)?void 0:r.paddingBlockLayoutMenu,borderInlineEnd:"1px solid ".concat(e.colorSplit),marginInlineEnd:-1}),"".concat(e.antCls,"-menu"),(0,a.Z)((0,a.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:e.fontSizeSM,paddingBottom:4}),"".concat(e.antCls,"-menu-item:not(").concat(e.antCls,"-menu-item-selected):hover"),{color:null==(i=e.layout)||null==(i=i.sider)?void 0:i.colorTextMenuItemHover})),"&-logo",{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:12,paddingBlock:16,color:null==(o=e.layout)||null==(o=o.sider)?void 0:o.colorTextMenu,cursor:"pointer",borderBlockEnd:"1px solid ".concat(null==(l=e.layout)||null==(l=l.sider)?void 0:l.colorMenuItemDivider),"> a":{display:"flex",alignItems:"center",justifyContent:"center",minHeight:22,fontSize:22,"> img":{display:"inline-block",height:22,verticalAlign:"middle"},"> h1":{display:"inline-block",height:22,marginBlock:0,marginInlineEnd:0,marginInlineStart:6,color:null==(s=e.layout)||null==(s=s.sider)?void 0:s.colorTextMenuTitle,animationName:tF,animationDuration:".4s",animationTimingFunction:"ease",fontWeight:600,fontSize:16,lineHeight:"22px",verticalAlign:"middle"}},"&-collapsed":(0,a.Z)({flexDirection:"column-reverse",margin:0,padding:12},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginBlockEnd:8,fontSize:16,transition:"font-size 0.2s ease-in-out,color 0.2s ease-in-out"})}),"&-actions",{display:"flex",alignItems:"center",justifyContent:"space-between",marginBlock:4,marginInline:0,color:null==(c=e.layout)||null==(c=c.sider)?void 0:c.colorTextMenu,"&-collapsed":{flexDirection:"column-reverse",paddingBlock:0,paddingInline:8,fontSize:16,transition:"font-size 0.3s ease-in-out"},"&-list":{color:null==(u=e.layout)||null==(u=u.sider)?void 0:u.colorTextMenuSecondary,"&-collapsed":{marginBlockEnd:8,animationName:"none"},"&-item":{paddingInline:6,paddingBlock:6,lineHeight:"16px",fontSize:16,cursor:"pointer",borderRadius:e.borderRadius,"&:hover":{background:e.colorBgTextHover}}},"&-avatar":{fontSize:14,paddingInline:8,paddingBlock:8,display:"flex",alignItems:"center",gap:e.marginXS,borderRadius:e.borderRadius,"& *":{cursor:"pointer"},"&:hover":{background:e.colorBgTextHover}}}),"&-hide-menu-collapsed",{insetInlineStart:"-".concat(e.proLayoutCollapsedWidth-12,"px"),position:"absolute"}),"&-extra",{marginBlockEnd:16,marginBlock:0,marginInline:16,"&-no-logo":{marginBlockStart:16}}),"&-links",{width:"100%",ul:{height:"auto"}}),"&-link-menu",{border:"none",boxShadow:"none",background:"transparent"}),"&-footer",{color:null==(d=e.layout)||null==(d=d.sider)?void 0:d.colorTextMenuSecondary,paddingBlockEnd:16,fontSize:e.fontSize,animationName:tF,animationDuration:".4s",animationTimingFunction:"ease"})),"".concat(e.componentCls).concat(e.componentCls,"-fixed"),{position:"fixed",insetBlockStart:0,insetInlineStart:0,zIndex:"100",height:"100%","&-mix":{height:"calc(100% - ".concat((null==(f=e.layout)||null==(f=f.header)?void 0:f.heightLayoutHeader)||56,"px)"),insetBlockStart:"".concat((null==(p=e.layout)||null==(p=p.header)?void 0:p.heightLayoutHeader)||56,"px")}}))},tP=function(e){var t,n,r=e.isMobile,i=e.siderWidth,o=e.collapsed,a=e.onCollapse,l=e.style,s=e.className,c=e.hide,u=e.prefixCls,p=e.getContainer,m=(0,h.useContext)(f.L_).token;(0,h.useEffect)(function(){!0===r&&(null==a||a(!0))},[r]);var g=(0,eu.Z)(e,["className","style"]),v=h.useContext(ea.ZP.ConfigContext).direction,y=(t="".concat(u,"-sider"),(0,eC.Xj)("ProLayoutSiderMenu",function(e){return[tT((0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(t),proLayoutCollapsedWidth:64}))]})),b=y.wrapSSR,x=y.hashId,C=ec()("".concat(u,"-sider"),s,x);if(c)return null;var S=(0,Z.X)(!o,function(){return null==a?void 0:a(!0)});return b(r?(0,em.jsx)(tM.Z,(0,d.Z)((0,d.Z)({placement:"rtl"===v?"right":"left",className:ec()("".concat(u,"-drawer-sider"),s)},S),{},{style:(0,d.Z)({padding:0,height:"100vh"},l),onClose:function(){null==a||a(!0)},maskClosable:!0,closable:!1,getContainer:p||!1,width:i,styles:{body:{height:"100vh",padding:0,display:"flex",flexDirection:"row",backgroundColor:null==(n=m.layout)||null==(n=n.sider)?void 0:n.colorMenuBackground}},children:(0,em.jsx)(td,(0,d.Z)((0,d.Z)({},g),{},{isMobile:!0,className:C,collapsed:!r&&o,splitMenus:!1,originCollapsed:o}))})):(0,em.jsx)(td,(0,d.Z)((0,d.Z)({className:C,originCollapsed:o},g),{},{style:l})))},tE=n(45414),tR=n(85827),tI=function(e,t,n){if(n){var r=(0,eK.Z)(n.keys()).find(function(t){try{if(t.startsWith("http"))return!1;return(0,tR.match)(t)(e)}catch(e){return console.log("key",t,e),!1}});if(r)return n.get(r)}if(t){var i=Object.keys(t).find(function(t){try{if(null!=t&&t.startsWith("http"))return!1;return(0,tR.match)(t)(e)}catch(e){return console.log("key",t,e),!1}});if(i)return t[i]}return{path:""}},tj=function(e,t){var n=e.pathname,r=e.breadcrumb,i=e.breadcrumbMap,o=e.formatMessage,a=e.title,l=e.menu,s=t?"":a||"",c=tI(void 0===n?"/":n,r,i);if(!c)return{title:s,id:"",pageName:s};var u=c.name;return(!1!==(void 0===l?{locale:!1}:l).locale&&c.locale&&o&&(u=o({id:c.locale||"",defaultMessage:c.name})),u)?t||!a?{title:u,id:c.locale||"",pageName:u}:{title:"".concat(u," - ").concat(a),id:c.locale||"",pageName:u}:{title:s,id:c.locale||"",pageName:s}},tL=(0,d.Z)({},{"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.dark":"Dark Menu style","app.setting.pagestyle.light":"Light Menu style","app.setting.pagestyle.realdark":"Dark style (Beta)","app.setting.content-width":"Content Width","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.themecolor":"Theme Color","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blue (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.sidermenutype":"SideMenu Type","app.setting.sidermenutype-sub":"Classic","app.setting.sidermenutype-group":"Grouping","app.setting.navigationmode":"Navigation Mode","app.setting.regionalsettings":"Regional Settings","app.setting.regionalsettings.header":"Header","app.setting.regionalsettings.menu":"Menu","app.setting.regionalsettings.footer":"Footer","app.setting.regionalsettings.menuHeader":"Menu Header","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.mixmenu":"Mix Menu Layout","app.setting.splitMenus":"Split Menus","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.fixedsidebar.hint":"Works on Side Menu Layout","app.setting.hideheader":"Hidden Header when scrolling","app.setting.hideheader.hint":"Works when Hidden Header is enabled","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success\uFF0Cplease replace defaultSettings in src/models/setting.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify"}),tA=(0,d.Z)({},{"app.setting.pagestyle":"Impostazioni di stile","app.setting.pagestyle.dark":"Tema scuro","app.setting.pagestyle.light":"Tema chiaro","app.setting.content-width":"Largezza contenuto","app.setting.content-width.fixed":"Fissa","app.setting.content-width.fluid":"Fluida","app.setting.themecolor":"Colore del tema","app.setting.themecolor.dust":"Rosso polvere","app.setting.themecolor.volcano":"Vulcano","app.setting.themecolor.sunset":"Arancione tramonto","app.setting.themecolor.cyan":"Ciano","app.setting.themecolor.green":"Verde polare","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Blu cielo mattutino","app.setting.themecolor.geekblue":"Blu geek","app.setting.themecolor.purple":"Viola dorato","app.setting.navigationmode":"Modalit\xe0 di navigazione","app.setting.sidemenu":"Menu laterale","app.setting.topmenu":"Menu in testata","app.setting.mixmenu":"Menu misto","app.setting.splitMenus":"Menu divisi","app.setting.fixedheader":"Testata fissa","app.setting.fixedsidebar":"Menu laterale fisso","app.setting.fixedsidebar.hint":"Solo se selezionato Menu laterale","app.setting.hideheader":"Nascondi testata durante lo scorrimento","app.setting.hideheader.hint":"Solo se abilitato Nascondi testata durante lo scorrimento","app.setting.othersettings":"Altre impostazioni","app.setting.weakmode":"Inverti colori","app.setting.copy":"Copia impostazioni","app.setting.loading":"Carico tema...","app.setting.copyinfo":"Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js","app.setting.production.hint":"Questo pannello \xe8 visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"}),tO=(0,d.Z)({},{"app.setting.pagestyle":"\uC2A4\uD0C0\uC77C \uC124\uC815","app.setting.pagestyle.dark":"\uB2E4\uD06C \uBAA8\uB4DC","app.setting.pagestyle.light":"\uB77C\uC774\uD2B8 \uBAA8\uB4DC","app.setting.content-width":"\uCEE8\uD150\uCE20 \uB108\uBE44","app.setting.content-width.fixed":"\uACE0\uC815","app.setting.content-width.fluid":"\uD750\uB984","app.setting.themecolor":"\uD14C\uB9C8 \uC0C9\uC0C1","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"\uB124\uBE44\uAC8C\uC774\uC158 \uBAA8\uB4DC","app.setting.regionalsettings":"\uC601\uC5ED\uBCC4 \uC124\uC815","app.setting.regionalsettings.header":"\uD5E4\uB354","app.setting.regionalsettings.menu":"\uBA54\uB274","app.setting.regionalsettings.footer":"\uBC14\uB2E5\uAE00","app.setting.regionalsettings.menuHeader":"\uBA54\uB274 \uD5E4\uB354","app.setting.sidemenu":"\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58","app.setting.topmenu":"\uBA54\uB274 \uC0C1\uB2E8 \uBC30\uCE58","app.setting.mixmenu":"\uD63C\uD569\uD615 \uBC30\uCE58","app.setting.splitMenus":"\uBA54\uB274 \uBD84\uB9AC","app.setting.fixedheader":"\uD5E4\uB354 \uACE0\uC815","app.setting.fixedsidebar":"\uC0AC\uC774\uB4DC\uBC14 \uACE0\uC815","app.setting.fixedsidebar.hint":"'\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58'\uB97C \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.hideheader":"\uC2A4\uD06C\uB864 \uC911 \uD5E4\uB354 \uAC10\uCD94\uAE30","app.setting.hideheader.hint":"'\uD5E4\uB354 \uAC10\uCD94\uAE30 \uC635\uC158'\uC744 \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.othersettings":"\uB2E4\uB978 \uC124\uC815","app.setting.weakmode":"\uACE0\uB300\uBE44 \uBAA8\uB4DC","app.setting.copy":"\uC124\uC815\uAC12 \uBCF5\uC0AC","app.setting.loading":"\uD14C\uB9C8 \uB85C\uB529 \uC911","app.setting.copyinfo":"\uBCF5\uC0AC \uC131\uACF5. src/models/settings.js\uC5D0 \uC788\uB294 defaultSettings\uB97C \uAD50\uCCB4\uD574 \uC8FC\uC138\uC694.","app.setting.production.hint":"\uC124\uC815 \uD310\uB12C\uC740 \uAC1C\uBC1C \uD658\uACBD\uC5D0\uC11C\uB9CC \uBCF4\uC5EC\uC9D1\uB2C8\uB2E4. \uC9C1\uC811 \uC218\uB3D9\uC73C\uB85C \uBCC0\uACBD\uBC14\uB78D\uB2C8\uB2E4."}),tN={"zh-CN":(0,d.Z)({},{"app.setting.pagestyle":"\u6574\u4F53\u98CE\u683C\u8BBE\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98CE\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.content-width":"\u5185\u5BB9\u533A\u57DF\u5BBD\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BBD","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u9898\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6781\u5149\u7EFF","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8BA4\uFF09","app.setting.themecolor.daybreak":"\u62C2\u6653","app.setting.themecolor.geekblue":"\u6781\u5BA2\u84DD","app.setting.themecolor.purple":"\u9171\u7D2B","app.setting.navigationmode":"\u5BFC\u822A\u6A21\u5F0F","app.setting.sidermenutype":"\u4FA7\u8FB9\u83DC\u5355\u7C7B\u578B","app.setting.sidermenutype-sub":"\u7ECF\u5178\u6A21\u5F0F","app.setting.sidermenutype-group":"\u5206\u7EC4\u6A21\u5F0F","app.setting.regionalsettings":"\u5185\u5BB9\u533A\u57DF","app.setting.regionalsettings.header":"\u9876\u680F","app.setting.regionalsettings.menu":"\u83DC\u5355","app.setting.regionalsettings.footer":"\u9875\u811A","app.setting.regionalsettings.menuHeader":"\u83DC\u5355\u5934","app.setting.sidemenu":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40","app.setting.topmenu":"\u9876\u90E8\u83DC\u5355\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u5355\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u4FA7\u8FB9\u83DC\u5355","app.setting.fixedsidebar.hint":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40\u65F6\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u65F6\u9690\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u65F6\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8BBE\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8D1D\u8BBE\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F7D\u4E3B\u9898","app.setting.copyinfo":"\u62F7\u8D1D\u6210\u529F\uFF0C\u8BF7\u5230 src/defaultSettings.js \u4E2D\u66FF\u6362\u9ED8\u8BA4\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u680F\u53EA\u5728\u5F00\u53D1\u73AF\u5883\u7528\u4E8E\u9884\u89C8\uFF0C\u751F\u4EA7\u73AF\u5883\u4E0D\u4F1A\u5C55\u73B0\uFF0C\u8BF7\u62F7\u8D1D\u540E\u624B\u52A8\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"}),"zh-TW":(0,d.Z)({},{"app.setting.pagestyle":"\u6574\u9AD4\u98A8\u683C\u8A2D\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u55AE\u98A8\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98A8\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u55AE\u98A8\u683C","app.setting.content-width":"\u5167\u5BB9\u5340\u57DF\u5BEC\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BEC","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u984C\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6975\u5149\u7DA0","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8A8D\uFF09","app.setting.themecolor.daybreak":"\u62C2\u66C9\u85CD","app.setting.themecolor.geekblue":"\u6975\u5BA2\u85CD","app.setting.themecolor.purple":"\u91AC\u7D2B","app.setting.navigationmode":"\u5C0E\u822A\u6A21\u5F0F","app.setting.sidemenu":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40","app.setting.topmenu":"\u9802\u90E8\u83DC\u55AE\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u55AE\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u5074\u908A\u83DC\u55AE","app.setting.fixedsidebar.hint":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40\u6642\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u6642\u96B1\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u6642\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8A2D\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8C9D\u8A2D\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F09\u4E3B\u984C","app.setting.copyinfo":"\u62F7\u8C9D\u6210\u529F\uFF0C\u8ACB\u5230 src/defaultSettings.js \u4E2D\u66FF\u63DB\u9ED8\u8A8D\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u6B04\u53EA\u5728\u958B\u767C\u74B0\u5883\u7528\u65BC\u9810\u89BD\uFF0C\u751F\u7522\u74B0\u5883\u4E0D\u6703\u5C55\u73FE\uFF0C\u8ACB\u62F7\u8C9D\u5F8C\u624B\u52D5\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"}),"en-US":tL,"it-IT":tA,"ko-KR":tO},tD=n(17697),tB=function(){var e;return"undefined"==typeof process?tD.Z:(null==(e=process)||null==(e=e.env)?void 0:e.ANTD_VERSION)||tD.Z},t_=function(e){var t,n,r,i,o,l,s,c,u,d,f,p,h,m,g,v,y,b,x,Z,C,S,w,k,M,F,T,P,E,R,I,j;return null!=(t=tB())&&t.startsWith("5")?{}:(0,a.Z)((0,a.Z)((0,a.Z)({},e.componentCls,(0,a.Z)((0,a.Z)({width:"100%",height:"100%"},"".concat(e.proComponentsCls,"-base-menu"),(C={color:null==(n=e.layout)||null==(n=n.sider)?void 0:n.colorTextMenu},(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)(C,"".concat(e.antCls,"-menu-sub"),{backgroundColor:"transparent!important",color:null==(r=e.layout)||null==(r=r.sider)?void 0:r.colorTextMenu}),"& ".concat(e.antCls,"-layout"),{backgroundColor:"transparent",width:"100%"}),"".concat(e.antCls,"-menu-submenu-expand-icon, ").concat(e.antCls,"-menu-submenu-arrow"),{color:"inherit"}),"&".concat(e.antCls,"-menu"),(0,a.Z)((0,a.Z)({color:null==(i=e.layout)||null==(i=i.sider)?void 0:i.colorTextMenu},"".concat(e.antCls,"-menu-item"),{"*":{transition:"none !important"}}),"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-inline"),(0,a.Z)({},"".concat(e.antCls,"-menu-selected::after,").concat(e.antCls,"-menu-item-selected::after"),{display:"none"})),"".concat(e.antCls,"-menu-sub ").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-item:active, \n        ").concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"}),"&".concat(e.antCls,"-menu-light"),(0,a.Z)({},"".concat(e.antCls,"-menu-item:hover, \n            ").concat(e.antCls,"-menu-item-active,\n            ").concat(e.antCls,"-menu-submenu-active, \n            ").concat(e.antCls,"-menu-submenu-title:hover"),(0,a.Z)({color:null==(o=e.layout)||null==(o=o.sider)?void 0:o.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null==(l=e.layout)||null==(l=l.sider)?void 0:l.colorTextMenuActive}))),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,a.Z)((0,a.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:null==(s=e.layout)||null==(s=s.sider)?void 0:s.colorBgMenuItemSelected,borderRadius:e.borderRadius}),"".concat(e.antCls,"-menu-item:hover, \n            ").concat(e.antCls,"-menu-item-active,\n            ").concat(e.antCls,"-menu-submenu-title:hover"),(0,a.Z)({color:null==(c=e.layout)||null==(c=c.sider)?void 0:c.colorTextMenuActive,borderRadius:e.borderRadius,backgroundColor:"".concat(null==(u=e.layout)||null==(u=u.header)?void 0:u.colorBgMenuItemHover," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null==(d=e.layout)||null==(d=d.sider)?void 0:d.colorTextMenuActive}))),"".concat(e.antCls,"-menu-item-selected"),{color:null==(f=e.layout)||null==(f=f.sider)?void 0:f.colorTextMenuSelected}),(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)(C,"".concat(e.antCls,"-menu-submenu-selected"),{color:null==(p=e.layout)||null==(p=p.sider)?void 0:p.colorTextMenuSelected}),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-inline) ").concat(e.antCls,"-menu-submenu-open"),{color:null==(h=e.layout)||null==(h=h.sider)?void 0:h.colorTextMenuSelected}),"&".concat(e.antCls,"-menu-vertical"),(0,a.Z)({},"".concat(e.antCls,"-menu-submenu-selected"),{borderRadius:e.borderRadius,color:null==(m=e.layout)||null==(m=m.sider)?void 0:m.colorTextMenuSelected})),"".concat(e.antCls,"-menu-submenu:hover > ").concat(e.antCls,"-menu-submenu-title > ").concat(e.antCls,"-menu-submenu-arrow"),{color:null==(g=e.layout)||null==(g=g.sider)?void 0:g.colorTextMenuActive}),"&".concat(e.antCls,"-menu-horizontal"),(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({},"".concat(e.antCls,"-menu-item:hover,\n          ").concat(e.antCls,"-menu-submenu:hover,\n          ").concat(e.antCls,"-menu-item-active,\n          ").concat(e.antCls,"-menu-submenu-active"),{borderRadius:4,transition:"none",color:null==(v=e.layout)||null==(v=v.header)?void 0:v.colorTextMenuActive,backgroundColor:"".concat(null==(y=e.layout)||null==(y=y.header)?void 0:y.colorBgMenuItemHover," !important")}),"".concat(e.antCls,"-menu-item-open,\n          ").concat(e.antCls,"-menu-submenu-open,\n          ").concat(e.antCls,"-menu-item-selected,\n          ").concat(e.antCls,"-menu-submenu-selected"),(0,a.Z)({backgroundColor:null==(b=e.layout)||null==(b=b.header)?void 0:b.colorBgMenuItemSelected,borderRadius:e.borderRadius,transition:"none",color:"".concat(null==(x=e.layout)||null==(x=x.header)?void 0:x.colorTextMenuSelected," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:"".concat(null==(Z=e.layout)||null==(Z=Z.header)?void 0:Z.colorTextMenuSelected," !important")})),"> ".concat(e.antCls,"-menu-item, > ").concat(e.antCls,"-menu-submenu"),{paddingInline:16,marginInline:4}),"> ".concat(e.antCls,"-menu-item::after, > ").concat(e.antCls,"-menu-submenu::after"),{display:"none"})))),"".concat(e.proComponentsCls,"-top-nav-header-base-menu"),(0,a.Z)((0,a.Z)({},"&".concat(e.antCls,"-menu"),(0,a.Z)({color:null==(S=e.layout)||null==(S=S.header)?void 0:S.colorTextMenu},"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-light"),(0,a.Z)((0,a.Z)({},"".concat(e.antCls,"-menu-item:hover, \n            ").concat(e.antCls,"-menu-item-active,\n            ").concat(e.antCls,"-menu-submenu-active, \n            ").concat(e.antCls,"-menu-submenu-title:hover"),(0,a.Z)({color:null==(w=e.layout)||null==(w=w.header)?void 0:w.colorTextMenuActive,borderRadius:e.borderRadius,transition:"none",backgroundColor:null==(k=e.layout)||null==(k=k.header)?void 0:k.colorBgMenuItemSelected},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null==(M=e.layout)||null==(M=M.header)?void 0:M.colorTextMenuActive})),"".concat(e.antCls,"-menu-item-selected"),{color:null==(F=e.layout)||null==(F=F.header)?void 0:F.colorTextMenuSelected,borderRadius:e.borderRadius,backgroundColor:null==(T=e.layout)||null==(T=T.header)?void 0:T.colorBgMenuItemSelected})))),"".concat(e.antCls,"-menu-sub").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-submenu-popup"),(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"},"".concat(e.antCls,"-menu"),(0,a.Z)({background:"transparent !important",backgroundColor:"transparent !important"},"".concat(e.antCls,"-menu-item:active, \n        ").concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"})),"".concat(e.antCls,"-menu-item-selected"),{color:null==(P=e.layout)||null==(P=P.sider)?void 0:P.colorTextMenuSelected}),"".concat(e.antCls,"-menu-submenu-selected"),{color:null==(E=e.layout)||null==(E=E.sider)?void 0:E.colorTextMenuSelected}),"".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,a.Z)((0,a.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:"rgba(0, 0, 0, 0.04)",borderRadius:e.borderRadius,color:null==(R=e.layout)||null==(R=R.sider)?void 0:R.colorTextMenuSelected}),"".concat(e.antCls,"-menu-item:hover, \n          ").concat(e.antCls,"-menu-item-active,\n          ").concat(e.antCls,"-menu-submenu-title:hover"),(0,a.Z)({color:null==(I=e.layout)||null==(I=I.sider)?void 0:I.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null==(j=e.layout)||null==(j=j.sider)?void 0:j.colorTextMenuActive}))))},tH=function(e){var t,n,r,i;return(0,a.Z)((0,a.Z)({},"".concat(e.antCls,"-layout"),{backgroundColor:"transparent !important"}),e.componentCls,(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({},"& ".concat(e.antCls,"-layout"),{display:"flex",backgroundColor:"transparent",width:"100%"}),"".concat(e.componentCls,"-content"),{display:"flex",flexDirection:"column",width:"100%",backgroundColor:(null==(t=e.layout)||null==(t=t.pageContainer)?void 0:t.colorBgPageContainer)||"transparent",position:"relative",paddingBlock:null==(n=e.layout)||null==(n=n.pageContainer)?void 0:n.paddingBlockPageContainerContent,paddingInline:null==(r=e.layout)||null==(r=r.pageContainer)?void 0:r.paddingInlinePageContainerContent,"&-has-page-container":{padding:0}}),"".concat(e.componentCls,"-container"),{width:"100%",display:"flex",flexDirection:"column",minWidth:0,minHeight:0,backgroundColor:"transparent"}),"".concat(e.componentCls,"-bg-list"),{pointerEvents:"none",position:"fixed",overflow:"hidden",insetBlockStart:0,insetInlineStart:0,zIndex:0,height:"100%",width:"100%",background:null==(i=e.layout)?void 0:i.bgLayout}))},tz=function(){var e;return"undefined"==typeof process?tD.Z:(null==(e=process)||null==(e=e.env)?void 0:e.ANTD_VERSION)||tD.Z},tV=function(e,t,n){var r=e.breadcrumbName,i=e.title,o=e.path;return n.findIndex(function(t){return t.linkPath===e.path})===n.length-1?(0,em.jsx)("span",{children:i||r}):(0,em.jsx)("span",{onClick:o?function(){return location.href=o}:void 0,children:i||r})},tW=function(e,t){var n=t.formatMessage,r=t.menu;return e.locale&&n&&(null==r?void 0:r.locale)!==!1?n({id:e.locale,defaultMessage:e.name}):e.name},t$=function(e,t){var n=e.get(t);if(!n){var r=(Array.from(e.keys())||[]).find(function(e){try{if(null!=e&&e.startsWith("http"))return!1;return(0,tR.match)(e.replace("?",""))(t)}catch(t){return console.log("path",e,t),!1}});r&&(n=e.get(r))}return n||{path:""}},tK=function(e){var t={location:e.location,breadcrumbMap:e.breadcrumbMap},n=t.location,r=t.breadcrumbMap;return n&&n.pathname&&r?(function(e){if(!e||"/"===e)return["/"];var t=e.split("/").filter(function(e){return e});return t.map(function(e,n){return"/".concat(t.slice(0,n+1).join("/"))})})(null==n?void 0:n.pathname).map(function(t){var n=t$(r,t),i=tW(n,e),o=n.hideInBreadcrumb;return i&&!o?{linkPath:t,breadcrumbName:i,title:i,component:n.component}:{linkPath:"",breadcrumbName:"",title:""}}).filter(function(e){return e&&e.linkPath}):[]},tU=function(e,t){var n=e.breadcrumbRender,r=e.itemRender,i=(t.breadcrumbProps||{}).minLength,o=tK(e),a=function(e){for(var t=r||tV,n=arguments.length,i=Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];return null==t?void 0:t.apply(void 0,[(0,d.Z)((0,d.Z)({},e),{},{path:e.linkPath||e.path})].concat(i))},l=o;return n&&(l=n(l||[])||void 0),(l&&l.length<(void 0===i?2:i)||!1===n)&&(l=void 0),(0,x.n)(tz(),"5.3.0")>-1?{items:l,itemRender:a}:{routes:l,itemRender:a}},tq=function e(t,n,r,i){var o=ee(t,(null==n?void 0:n.locale)||!1,r,!0),a=o.menuData,l=o.breadcrumb;return i?e(i(a),n,r,void 0):{breadcrumb:(0,eK.Z)(l).reduce(function(e,t){var n=(0,u.Z)(t,2),r=n[0],i=n[1];return e[r]=i,e},{}),breadcrumbMap:l,menuData:a}},tY=n(52655),tX=n(30945),tG=function(e){var t=(0,h.useState)({}),n=(0,u.Z)(t,2),r=n[0],i=n[1];return(0,h.useEffect)(function(){i((0,tX.Y)({layout:"object"!==(0,tY.Z)(e.layout)?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar}))},[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar]),r},tQ=["id","defaultMessage"],tJ=["fixSiderbar","navTheme","layout"],t0=0,t1=function(e,t){var n;return!1===e.headerRender||e.pure?null:(0,em.jsx)(tw,(0,d.Z)((0,d.Z)({matchMenuKeys:t},e),{},{stylish:null==(n=e.stylish)?void 0:n.header}))},t2=function(e,t){var n,r=e.layout,i=e.isMobile,o=e.selectedKeys,a=e.openKeys,l=e.splitMenus,s=e.suppressSiderWhenMenuEmpty,c=e.menuRender;if(!1===e.menuRender||e.pure)return null;var f=e.menuData;if(l&&(!1!==a||"mix"===r)&&!i){var p,h,m=o||t,g=(0,u.Z)(m,1)[0];f=g&&(null==(h=e.menuData)||null==(h=h.find(function(e){return e.key===g}))?void 0:h.children)||[]}var v=eF(f||[]);if(v&&(null==v?void 0:v.length)<1&&(l||s))return null;if("top"===r&&!i)return(0,em.jsx)(tP,(0,d.Z)((0,d.Z)({matchMenuKeys:t},e),{},{hide:!0,stylish:null==(n=e.stylish)?void 0:n.sider}));var y=(0,em.jsx)(tP,(0,d.Z)((0,d.Z)({matchMenuKeys:t},e),{},{menuData:v,stylish:null==(p=e.stylish)?void 0:p.sider}));return c?c(e,y):y},t5=function(e,t){var n=t.pageTitleRender,r=tj(e);if(!1===n)return{title:t.title||"",id:"",pageName:""};if(n){var i=n(e,r.title,r);if("string"==typeof i)return tj((0,d.Z)((0,d.Z)({},r),{},{title:i}));(0,ed.ZP)("string"==typeof i,"pro-layout: renderPageTitle return value should be a string")}return r},t6=function(e){var t,n,r,i,o,m,g,v,x,Z,S,w,k,M,F,T,P,E,R=e||{},I=R.children,j=R.onCollapse,L=R.location,A=void 0===L?{pathname:"/"}:L,O=R.contentStyle,N=R.route,D=R.defaultCollapsed,B=R.style,_=R.siderWidth,H=R.menu,z=R.siderMenuType,V=R.isChildrenLayout,W=R.menuDataRender,$=R.actionRef,K=R.bgLayoutImgList,U=R.formatMessage,q=R.loading,Y=(0,h.useMemo)(function(){return _||("mix"===e.layout?215:256)},[e.layout,_]),X=(0,h.useContext)(ea.ZP.ConfigContext),G=null!=(o=e.prefixCls)?o:X.getPrefixCls("pro"),Q=(0,p.Z)(!1,{value:null==H?void 0:H.loading,onChange:null==H?void 0:H.onLoadingChange}),J=(0,u.Z)(Q,2),ee=J[0],et=J[1],en=(0,h.useState)(function(){return t0+=1,"pro-layout-".concat(t0)}),er=(0,u.Z)(en,1)[0],ei=(0,h.useCallback)(function(e){var t=e.id,n=e.defaultMessage,r=(0,c.Z)(e,tQ);if(U)return U((0,d.Z)({id:t,defaultMessage:n},r));var i=tN[!(0,b.j)()?"zh-CN":window.localStorage.getItem("umi_locale")||window.g_locale||navigator.language]||tN["zh-CN"];return i[t]?i[t]:n},[U]),es=(0,ef.ZP)([er,null==H?void 0:H.params],(t=(0,s.Z)((0,l.Z)().mark(function e(t){var n,r,i;return(0,l.Z)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=(0,u.Z)(t,2)[1],et(!0),e.next=4,null==H||null==(n=H.request)?void 0:n.call(H,r||{},(null==N?void 0:N.children)||(null==N?void 0:N.routes)||[]);case 4:return i=e.sent,et(!1),e.abrupt("return",i);case 7:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)}),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),ed=es.data,eh=es.mutate,ev=es.isLoading;(0,h.useEffect)(function(){et(ev)},[ev]);var ey=(0,ep.kY)().cache;(0,h.useEffect)(function(){return function(){ey instanceof Map&&ey.delete(er)}},[]);var eb=(0,h.useMemo)(function(){return tq(ed||(null==N?void 0:N.children)||(null==N?void 0:N.routes)||[],H,ei,W)},[ei,H,W,ed,null==N?void 0:N.children,null==N?void 0:N.routes])||{},ex=eb.breadcrumb,eZ=eb.breadcrumbMap,eS=eb.menuData,ew=void 0===eS?[]:eS;$&&null!=H&&H.request&&($.current={reload:function(){eh()}});var eM=(0,h.useMemo)(function(){return eo(A.pathname||"/",ew||[],!0)},[A.pathname,ew]),eF=(0,h.useMemo)(function(){return Array.from(new Set(eM.map(function(e){return e.key||e.path||""})))},[eM]),eT=eM[eM.length-1]||{},eP=tG(eT),eE=(0,d.Z)((0,d.Z)({},e),eP),eR=eE.fixSiderbar,eI=(eE.navTheme,eE.layout),ej=(0,c.Z)(eE,tJ),eL=y(),eA=(0,h.useMemo)(function(){return("sm"===eL||"xs"===eL)&&!e.disableMobile},[eL,e.disableMobile]),eO="top"!==eI&&!eA,eN=(0,p.Z)(function(){return void 0!==D?D:!!eA||"md"===eL},{value:e.collapsed,onChange:j}),eD=(0,u.Z)(eN,2),eB=eD[0],e_=eD[1],eH=(0,eu.Z)((0,d.Z)((0,d.Z)((0,d.Z)({prefixCls:G},e),{},{siderWidth:Y},eP),{},{formatMessage:ei,breadcrumb:ex,menu:(0,d.Z)((0,d.Z)({},H),{},{type:z||(null==H?void 0:H.type),loading:ee}),layout:eI}),["className","style","breadcrumbRender"]),ez=t5((0,d.Z)((0,d.Z)({pathname:A.pathname},eH),{},{breadcrumbMap:eZ}),e),eV=tU((0,d.Z)((0,d.Z)({},eH),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:eZ}),e),eW=t2((0,d.Z)((0,d.Z)({},eH),{},{menuData:ew,onCollapse:e_,isMobile:eA,collapsed:eB}),eF),e$=t1((0,d.Z)((0,d.Z)({},eH),{},{children:null,hasSiderMenu:!!eW,menuData:ew,isMobile:eA,collapsed:eB,onCollapse:e_}),eF),eK=!1===(n=(0,d.Z)({isMobile:eA,collapsed:eB},eH)).footerRender||n.pure?null:n.footerRender?n.footerRender((0,d.Z)({},n),(0,em.jsx)(ek,{})):null,eU=(0,h.useContext)(tE.X).isChildrenLayout,eq=void 0!==V?V:eU,eY="".concat(G,"-layout"),eX=(0,eC.Xj)("ProLayout",function(e){var t=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(eY)});return[tH(t),t_(t)]}),eG=eX.wrapSSR,eQ=eX.hashId,eJ=ec()(e.className,eQ,"ant-design-pro",eY,(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({},"screen-".concat(eL),eL),"".concat(eY,"-top-menu"),"top"===eI),"".concat(eY,"-is-children"),eq),"".concat(eY,"-fix-siderbar"),eR),"".concat(eY,"-").concat(eI),eI)),e0=eO?eB?64:Y:0,e1={position:"relative"};(eq||O&&O.minHeight)&&(e1.minHeight=0),(0,h.useEffect)(function(){var t;null==(t=e.onPageChange)||t.call(e,e.location)},[A.pathname,null==(m=A.pathname)?void 0:m.search]);var e2=(0,h.useState)(!1),e5=(0,u.Z)(e2,2),e6=e5[0],e8=e5[1],e3=(0,h.useState)(0),e4=(0,u.Z)(e3,2),e9=e4[0],e7=e4[1];r=e.title||!1,i="string"==typeof ez.pageName?ez.title:r,(0,h.useEffect)(function(){(0,b.j)()&&i&&(document.title=i)},[ez.title,i]);var te=(0,h.useContext)(f.L_).token,tt=(0,h.useMemo)(function(){return K&&K.length>0?null==K?void 0:K.map(function(e,t){return(0,em.jsx)("img",{src:e.src,style:(0,d.Z)({position:"absolute"},e)},t)}):null},[K]);return eG((0,em.jsx)(tE.X.Provider,{value:(0,d.Z)((0,d.Z)({},eH),{},{breadcrumb:eV,menuData:ew,isMobile:eA,collapsed:eB,hasPageContainer:e9,setHasPageContainer:e7,isChildrenLayout:!0,title:ez.pageName,hasSiderMenu:!!eW,hasHeader:!!e$,siderWidth:e0,hasFooter:!!eK,hasFooterToolbar:e6,setHasFooterToolbar:e8,pageTitleInfo:ez,matchMenus:eM,matchMenuKeys:eF,currentMenu:eT}),children:e.pure?(0,em.jsx)(em.Fragment,{children:I}):(0,em.jsxs)("div",{className:eJ,children:[tt||null!=(g=te.layout)&&g.bgLayout?(0,em.jsx)("div",{className:ec()("".concat(eY,"-bg-list"),eQ),children:tt}):null,(0,em.jsxs)(el.Z,{style:(0,d.Z)({minHeight:"100%",flexDirection:eW?"row":void 0},B),children:[(0,em.jsx)(ea.ZP,{theme:{hashed:(0,f.nu)(),token:{controlHeightLG:(null==(v=te.layout)||null==(v=v.sider)?void 0:v.menuHeight)||(null==te?void 0:te.controlHeightLG)},components:{Menu:C({colorItemBg:(null==(x=te.layout)||null==(x=x.sider)?void 0:x.colorMenuBackground)||"transparent",colorSubItemBg:(null==(Z=te.layout)||null==(Z=Z.sider)?void 0:Z.colorMenuBackground)||"transparent",radiusItem:te.borderRadius,colorItemBgSelected:(null==(S=te.layout)||null==(S=S.sider)?void 0:S.colorBgMenuItemSelected)||(null==te?void 0:te.colorBgTextHover),colorItemBgHover:(null==(w=te.layout)||null==(w=w.sider)?void 0:w.colorBgMenuItemHover)||(null==te?void 0:te.colorBgTextHover),colorItemBgActive:(null==(k=te.layout)||null==(k=k.sider)?void 0:k.colorBgMenuItemActive)||(null==te?void 0:te.colorBgTextActive),colorItemBgSelectedHorizontal:(null==(M=te.layout)||null==(M=M.sider)?void 0:M.colorBgMenuItemSelected)||(null==te?void 0:te.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:(null==(F=te.layout)||null==(F=F.sider)?void 0:F.colorTextMenu)||(null==te?void 0:te.colorTextSecondary),colorItemTextHover:(null==(T=te.layout)||null==(T=T.sider)?void 0:T.colorTextMenuItemHover)||"rgba(0, 0, 0, 0.85)",colorItemTextSelected:(null==(P=te.layout)||null==(P=P.sider)?void 0:P.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:null==te?void 0:te.colorBgElevated,subMenuItemBg:null==te?void 0:te.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:null==te?void 0:te.colorBgElevated})}},children:eW}),(0,em.jsxs)("div",{style:e1,className:"".concat(eY,"-container ").concat(eQ).trim(),children:[e$,(0,em.jsx)(eg,(0,d.Z)((0,d.Z)({hasPageContainer:e9,isChildrenLayout:eq},ej),{},{hasHeader:!!e$,prefixCls:eY,style:O,children:q?(0,em.jsx)(tk.S,{}):I})),eK,e6&&(0,em.jsx)("div",{className:"".concat(eY,"-has-footer"),style:{height:64,marginBlockStart:null==(E=te.layout)||null==(E=E.pageContainer)?void 0:E.paddingBlockPageContainerContent}})]})]})]})}))},t8=function(e){var t=e.colorPrimary,n=void 0!==e.navTheme?{dark:"realDark"===e.navTheme}:{};return(0,em.jsx)(ea.ZP,{theme:t?{token:{colorPrimary:t}}:void 0,children:(0,em.jsx)(f._Y,(0,d.Z)((0,d.Z)({},n),{},{token:e.token,prefixCls:e.prefixCls,children:(0,em.jsx)(t6,(0,d.Z)((0,d.Z)({logo:(0,em.jsx)(ev,{})},e3),{},{location:(0,b.j)()?window.location:void 0},e))}))})}},88418:function(e,t,n){"use strict";n.d(t,{S:()=>s});var r=n(19741),i=n(95504),o=n(30113);n(52983);var a=n(97458),l=["isLoading","pastDelay","timedOut","error","retry"],s=function(e){e.isLoading,e.pastDelay,e.timedOut,e.error,e.retry;var t=(0,i.Z)(e,l);return(0,a.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,a.jsx)(o.Z,(0,r.Z)({size:"large"},t))})}},45414:function(e,t,n){"use strict";n.d(t,{X:()=>r});var r=(0,n(52983).createContext)({})},28800:function(e,t,n){"use strict";n.d(t,{L_:()=>R,ZP:()=>I,nu:()=>k,YB:()=>E,_Y:()=>P});var r=n(81632),i=n(57987),o=n(95504),a=n(19741),l=n(88122),s=n(35376),c=n(3542),u=n(52983),d=n(52008),f=n(65327),p=n(24459),h=n(28977),m=n.n(h),g=n(41628),v=function(e,t){var n,r,i,o,l,s=(0,a.Z)({},e);return(0,a.Z)((0,a.Z)({bgLayout:"linear-gradient(".concat(t.colorBgContainer,", ").concat(t.colorBgLayout," 28%)"),colorTextAppListIcon:t.colorTextSecondary,appListIconHoverBgColor:null==s||null==(n=s.sider)?void 0:n.colorBgMenuItemSelected,colorBgAppListIconHover:(0,g.uK)(t.colorTextBase,.04),colorTextAppListIconHover:t.colorTextBase},s),{},{header:(0,a.Z)({colorBgHeader:(0,g.uK)(t.colorBgElevated,.6),colorBgScrollHeader:(0,g.uK)(t.colorBgElevated,.8),colorHeaderTitle:t.colorText,colorBgMenuItemHover:(0,g.uK)(t.colorTextBase,.03),colorBgMenuItemSelected:"transparent",colorBgMenuElevated:(null==s||null==(r=s.header)?void 0:r.colorBgHeader)!=="rgba(255, 255, 255, 0.6)"?null==(i=s.header)?void 0:i.colorBgHeader:t.colorBgElevated,colorTextMenuSelected:(0,g.uK)(t.colorTextBase,.95),colorBgRightActionsItemHover:(0,g.uK)(t.colorTextBase,.03),colorTextRightActionsItem:t.colorTextTertiary,heightLayoutHeader:56,colorTextMenu:t.colorTextSecondary,colorTextMenuSecondary:t.colorTextTertiary,colorTextMenuTitle:t.colorText,colorTextMenuActive:t.colorText},s.header),sider:(0,a.Z)({paddingInlineLayoutMenu:8,paddingBlockLayoutMenu:0,colorBgCollapsedButton:t.colorBgElevated,colorTextCollapsedButtonHover:t.colorTextSecondary,colorTextCollapsedButton:(0,g.uK)(t.colorTextBase,.25),colorMenuBackground:"transparent",colorMenuItemDivider:(0,g.uK)(t.colorTextBase,.06),colorBgMenuItemHover:(0,g.uK)(t.colorTextBase,.03),colorBgMenuItemSelected:(0,g.uK)(t.colorTextBase,.04),colorTextMenuItemHover:t.colorText,colorTextMenuSelected:(0,g.uK)(t.colorTextBase,.95),colorTextMenuActive:t.colorText,colorTextMenu:t.colorTextSecondary,colorTextMenuSecondary:t.colorTextTertiary,colorTextMenuTitle:t.colorText,colorTextSubMenuSelected:(0,g.uK)(t.colorTextBase,.95)},s.sider),pageContainer:(0,a.Z)({colorBgPageContainer:"transparent",paddingInlinePageContainerContent:(null==(o=s.pageContainer)?void 0:o.marginInlinePageContainerContent)||40,paddingBlockPageContainerContent:(null==(l=s.pageContainer)?void 0:l.marginBlockPageContainerContent)||32,colorBgPageContainerFixed:t.colorBgElevated},s.pageContainer)})},y=n(35460),b=n(52655),x=function(){for(var e,t={},n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];for(var o=r.length,l=0;l<o;l+=1)for(e in r[l])r[l].hasOwnProperty(e)&&("object"!==(0,b.Z)(t[e])||"object"!==(0,b.Z)(r[l][e])||void 0===t[e]||null===t[e]||Array.isArray(t[e])||Array.isArray(r[l][e])?t[e]=r[l][e]:t[e]=(0,a.Z)((0,a.Z)({},t[e]),r[l][e]));return t};n(58070);var Z=n(97458),C=["locale","getPrefixCls"],S=["locale","theme"],w=function(e){var t={};if(Object.keys(e||{}).forEach(function(n){void 0!==e[n]&&(t[n]=e[n])}),!(Object.keys(t).length<1))return t},k=function(){return"undefined"==typeof process||true},M=u.createContext({intl:(0,a.Z)((0,a.Z)({},p.Hi),{},{locale:"default"}),valueTypeMap:{},theme:y.emptyTheme,hashed:!0,dark:!1,token:y.defaultToken});M.Consumer;var F=function(){var e=(0,d.kY)().cache;return(0,u.useEffect)(function(){return function(){e.clear()}},[]),null},T=function(e){var t,n=e.children,r=e.dark,c=e.valueTypeMap,d=e.autoClearCache,h=void 0!==d&&d,b=e.token,S=e.prefixCls,w=e.intl,T=(0,u.useContext)(s.ZP.ConfigContext),P=T.locale,E=T.getPrefixCls,R=(0,o.Z)(T,C),I=null==(t=g.Ow.useToken)?void 0:t.call(g.Ow),j=(0,u.useContext)(M),L=S?".".concat(S):".".concat(E(),"-pro"),A="."+E(),O="".concat(L),N=(0,u.useMemo)(function(){return v(b||{},I.token||y.defaultToken)},[b,I.token]),D=(0,u.useMemo)(function(){var e,t=null==P?void 0:P.locale,n=(0,p.Vy)(t),i=null!=w?w:t&&(null==(e=j.intl)?void 0:e.locale)==="default"?p.Go[n]:j.intl||p.Go[n];return(0,a.Z)((0,a.Z)({},j),{},{dark:null!=r?r:j.dark,token:x(j.token,I.token,{proComponentsCls:L,antCls:A,themeId:I.theme.id,layout:N}),intl:i||p.Hi})},[null==P?void 0:P.locale,j,r,I.token,I.theme.id,L,A,N,w]),B=(0,a.Z)((0,a.Z)({},D.token||{}),{},{proComponentsCls:L}),_=(0,l.fp)(I.theme,[I.token,null!=B?B:{}],{salt:O,override:B}),H=(0,i.Z)(_,2),z=H[0],V=H[1],W=(0,u.useMemo)(function(){return!1!==e.hashed&&!1!==j.hashed},[j.hashed,e.hashed]),$=(0,u.useMemo)(function(){return!1===e.hashed||!1===j.hashed||!1===k()?"":I.hashId?I.hashId:V},[V,j.hashed,e.hashed]);(0,u.useEffect)(function(){m().locale((null==P?void 0:P.locale)||"zh-cn")},[null==P?void 0:P.locale]);var K=(0,u.useMemo)(function(){return(0,a.Z)((0,a.Z)({},R.theme),{},{hashId:$,hashed:W&&k()})},[R.theme,$,W,k()]),U=(0,u.useMemo)(function(){return(0,a.Z)((0,a.Z)({},D),{},{valueTypeMap:c||(null==D?void 0:D.valueTypeMap),token:z,theme:I.theme,hashed:W,hashId:$})},[D,c,z,I.theme,W,$]),q=(0,u.useMemo)(function(){return(0,Z.jsx)(s.ZP,(0,a.Z)((0,a.Z)({},R),{},{theme:K,children:(0,Z.jsx)(M.Provider,{value:U,children:(0,Z.jsxs)(Z.Fragment,{children:[h&&(0,Z.jsx)(F,{}),n]})})}))},[R,K,U,h,n]);return h?(0,Z.jsx)(f.J$,{value:{provider:function(){return new Map}},children:q}):q},P=function(e){var t,n=e.needDeps,i=e.dark,l=e.token,d=(0,u.useContext)(M),f=(0,u.useContext)(s.ZP.ConfigContext),p=f.locale,h=f.theme,m=(0,o.Z)(f,S);if(n&&void 0!==d.hashId&&"children-needDeps"===Object.keys(e).sort().join("-"))return(0,Z.jsx)(Z.Fragment,{children:e.children});var v=(0,a.Z)((0,a.Z)({},m),{},{locale:p||c.Z,theme:w((0,a.Z)((0,a.Z)({},h),{},{algorithm:(t=null!=i?i:d.dark)&&!Array.isArray(null==h?void 0:h.algorithm)?[null==h?void 0:h.algorithm,g.Ow.darkAlgorithm].filter(Boolean):t&&Array.isArray(null==h?void 0:h.algorithm)?[].concat((0,r.Z)((null==h?void 0:h.algorithm)||[]),[g.Ow.darkAlgorithm]).filter(Boolean):null==h?void 0:h.algorithm}))});return(0,Z.jsx)(s.ZP,(0,a.Z)((0,a.Z)({},v),{},{children:(0,Z.jsx)(T,(0,a.Z)((0,a.Z)({},e),{},{token:l}))}))};function E(){var e=(0,u.useContext)(s.ZP.ConfigContext).locale,t=(0,u.useContext)(M).intl;return t&&"default"!==t.locale?t||p.Hi:null!=e&&e.locale&&p.Go[(0,p.Vy)(e.locale)]||p.Hi}M.displayName="ProProvider";var R=M,I=M},24459:function(e,t,n){"use strict";n.d(t,{Hi:()=>l,Go:()=>s,Vy:()=>u});var r=n(71553),i=function(e,t){return{getMessage:function(n,i){var o=(0,r.U2)(t,n.replace(/\[(\d+)\]/g,".$1").split("."))||"";if(o)return o;if("zh-CN"===e.replace("_","-"))return i;var a=s["zh-CN"];return a?a.getMessage(n,i):i},locale:e}},o=i("mn_MN",{moneySymbol:"\u20AE",form:{lightFilter:{more:"\u0418\u043B\u04AF\u04AF",clear:"\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445",confirm:"\u0411\u0430\u0442\u0430\u043B\u0433\u0430\u0430\u0436\u0443\u0443\u043B\u0430\u0445",itemUnit:"\u041D\u044D\u0433\u0436\u04AF\u04AF\u0434"}},tableForm:{search:"\u0425\u0430\u0439\u0445",reset:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",submit:"\u0418\u043B\u0433\u044D\u044D\u0445",collapsed:"\u04E8\u0440\u0433\u04E9\u0442\u0433\u04E9\u0445",expand:"\u0425\u0443\u0440\u0430\u0430\u0445",inputPlaceholder:"\u0423\u0442\u0433\u0430 \u043E\u0440\u0443\u0443\u043B\u043D\u0430 \u0443\u0443",selectPlaceholder:"\u0423\u0442\u0433\u0430 \u0441\u043E\u043D\u0433\u043E\u043D\u043E \u0443\u0443"},alert:{clear:"\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445",selected:"\u0421\u043E\u043D\u0433\u043E\u0433\u0434\u0441\u043E\u043D",item:"\u041D\u044D\u0433\u0436"},pagination:{total:{range:" ",total:"\u041D\u0438\u0439\u0442",item:"\u043C\u04E9\u0440"}},tableToolBar:{leftPin:"\u0417\u04AF\u04AF\u043D \u0442\u0438\u0439\u0448 \u0431\u044D\u0445\u043B\u044D\u0445",rightPin:"\u0411\u0430\u0440\u0443\u0443\u043D \u0442\u0438\u0439\u0448 \u0431\u044D\u0445\u043B\u044D\u0445",noPin:"\u0411\u044D\u0445\u043B\u044D\u0445\u0433\u04AF\u0439",leftFixedTitle:"\u0417\u04AF\u04AF\u043D \u0437\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445",rightFixedTitle:"\u0411\u0430\u0440\u0443\u0443\u043D \u0437\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445",noFixedTitle:"\u0417\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445\u0433\u04AF\u0439",reset:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",columnDisplay:"\u0411\u0430\u0433\u0430\u043D\u0430\u0430\u0440 \u0445\u0430\u0440\u0443\u0443\u043B\u0430\u0445",columnSetting:"\u0422\u043E\u0445\u0438\u0440\u0433\u043E\u043E",fullScreen:"\u0411\u04AF\u0442\u044D\u043D \u0434\u044D\u043B\u0433\u044D\u0446\u044D\u044D\u0440",exitFullScreen:"\u0411\u04AF\u0442\u044D\u043D \u0434\u044D\u043B\u0433\u044D\u0446 \u0446\u0443\u0446\u043B\u0430\u0445",reload:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",density:"\u0425\u044D\u043C\u0436\u044D\u044D",densityDefault:"\u0425\u044D\u0432\u0438\u0439\u043D",densityLarger:"\u0422\u043E\u043C",densityMiddle:"\u0414\u0443\u043D\u0434",densitySmall:"\u0416\u0438\u0436\u0438\u0433"},stepsForm:{next:"\u0414\u0430\u0440\u0430\u0430\u0445",prev:"\u04E8\u043C\u043D\u04E9\u0445",submit:"\u0414\u0443\u0443\u0441\u0433\u0430\u0445"},loginForm:{submitText:"\u041D\u044D\u0432\u0442\u0440\u044D\u0445"},editableTable:{action:{save:"\u0425\u0430\u0434\u0433\u0430\u043B\u0430\u0445",cancel:"\u0426\u0443\u0446\u043B\u0430\u0445",delete:"\u0423\u0441\u0442\u0433\u0430\u0445",add:"\u041C\u04E9\u0440 \u043D\u044D\u043C\u044D\u0445"}},switch:{open:"\u041D\u044D\u044D\u0445",close:"\u0425\u0430\u0430\u0445"}}),a=i("ar_EG",{moneySymbol:"$",form:{lightFilter:{more:"\u0627\u0644\u0645\u0632\u064A\u062F",clear:"\u0646\u0638\u0641",confirm:"\u062A\u0623\u0643\u064A\u062F",itemUnit:"\u0639\u0646\u0627\u0635\u0631"}},tableForm:{search:"\u0627\u0628\u062D\u062B",reset:"\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646",submit:"\u0627\u0631\u0633\u0627\u0644",collapsed:"\u0645\u064F\u0642\u0644\u0635",expand:"\u0645\u064F\u0648\u0633\u0639",inputPlaceholder:"\u0627\u0644\u0631\u062C\u0627\u0621 \u0627\u0644\u0625\u062F\u062E\u0627\u0644",selectPlaceholder:"\u0627\u0644\u0631\u062C\u0627\u0621 \u0627\u0644\u0625\u062E\u062A\u064A\u0627\u0631"},alert:{clear:"\u0646\u0638\u0641",selected:"\u0645\u062D\u062F\u062F",item:"\u0639\u0646\u0635\u0631"},pagination:{total:{range:" ",total:"\u0645\u0646",item:"\u0639\u0646\u0627\u0635\u0631"}},tableToolBar:{leftPin:"\u062B\u0628\u062A \u0639\u0644\u0649 \u0627\u0644\u064A\u0633\u0627\u0631",rightPin:"\u062B\u0628\u062A \u0639\u0644\u0649 \u0627\u0644\u064A\u0645\u064A\u0646",noPin:"\u0627\u0644\u063A\u0627\u0621 \u0627\u0644\u062A\u062B\u0628\u064A\u062A",leftFixedTitle:"\u0644\u0635\u0642 \u0639\u0644\u0649 \u0627\u0644\u064A\u0633\u0627\u0631",rightFixedTitle:"\u0644\u0635\u0642 \u0639\u0644\u0649 \u0627\u0644\u064A\u0645\u064A\u0646",noFixedTitle:"\u0625\u0644\u063A\u0627\u0621 \u0627\u0644\u0625\u0644\u0635\u0627\u0642",reset:"\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646",columnDisplay:"\u0627\u0644\u0623\u0639\u0645\u062F\u0629 \u0627\u0644\u0645\u0639\u0631\u0648\u0636\u0629",columnSetting:"\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A",fullScreen:"\u0648\u0636\u0639 \u0643\u0627\u0645\u0644 \u0627\u0644\u0634\u0627\u0634\u0629",exitFullScreen:"\u0627\u0644\u062E\u0631\u0648\u062C \u0645\u0646 \u0648\u0636\u0639 \u0643\u0627\u0645\u0644 \u0627\u0644\u0634\u0627\u0634\u0629",reload:"\u062A\u062D\u062F\u064A\u062B",density:"\u0627\u0644\u0643\u062B\u0627\u0641\u0629",densityDefault:"\u0627\u0641\u062A\u0631\u0627\u0636\u064A",densityLarger:"\u0623\u0643\u0628\u0631",densityMiddle:"\u0648\u0633\u0637",densitySmall:"\u0645\u062F\u0645\u062C"},stepsForm:{next:"\u0627\u0644\u062A\u0627\u0644\u064A",prev:"\u0627\u0644\u0633\u0627\u0628\u0642",submit:"\u0623\u0646\u0647\u0649"},loginForm:{submitText:"\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644"},editableTable:{action:{save:"\u0623\u0646\u0642\u0630",cancel:"\u0625\u0644\u063A\u0627\u0621 \u0627\u0644\u0623\u0645\u0631",delete:"\u062D\u0630\u0641",add:"\u0625\u0636\u0627\u0641\u0629 \u0635\u0641 \u0645\u0646 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A"}},switch:{open:"\u0645\u0641\u062A\u0648\u062D",close:"\u063A\u0644\u0642"}}),l=i("zh_CN",{moneySymbol:"\xa5",deleteThisLine:"\u5220\u9664\u6B64\u9879",copyThisLine:"\u590D\u5236\u6B64\u9879",form:{lightFilter:{more:"\u66F4\u591A\u7B5B\u9009",clear:"\u6E05\u9664",confirm:"\u786E\u8BA4",itemUnit:"\u9879"}},tableForm:{search:"\u67E5\u8BE2",reset:"\u91CD\u7F6E",submit:"\u63D0\u4EA4",collapsed:"\u5C55\u5F00",expand:"\u6536\u8D77",inputPlaceholder:"\u8BF7\u8F93\u5165",selectPlaceholder:"\u8BF7\u9009\u62E9"},alert:{clear:"\u53D6\u6D88\u9009\u62E9",selected:"\u5DF2\u9009\u62E9",item:"\u9879"},pagination:{total:{range:"\u7B2C",total:"\u6761/\u603B\u5171",item:"\u6761"}},tableToolBar:{leftPin:"\u56FA\u5B9A\u5728\u5217\u9996",rightPin:"\u56FA\u5B9A\u5728\u5217\u5C3E",noPin:"\u4E0D\u56FA\u5B9A",leftFixedTitle:"\u56FA\u5B9A\u5728\u5DE6\u4FA7",rightFixedTitle:"\u56FA\u5B9A\u5728\u53F3\u4FA7",noFixedTitle:"\u4E0D\u56FA\u5B9A",reset:"\u91CD\u7F6E",columnDisplay:"\u5217\u5C55\u793A",columnSetting:"\u5217\u8BBE\u7F6E",fullScreen:"\u5168\u5C4F",exitFullScreen:"\u9000\u51FA\u5168\u5C4F",reload:"\u5237\u65B0",density:"\u5BC6\u5EA6",densityDefault:"\u6B63\u5E38",densityLarger:"\u5BBD\u677E",densityMiddle:"\u4E2D\u7B49",densitySmall:"\u7D27\u51D1"},stepsForm:{next:"\u4E0B\u4E00\u6B65",prev:"\u4E0A\u4E00\u6B65",submit:"\u63D0\u4EA4"},loginForm:{submitText:"\u767B\u5F55"},editableTable:{onlyOneLineEditor:"\u53EA\u80FD\u540C\u65F6\u7F16\u8F91\u4E00\u884C",action:{save:"\u4FDD\u5B58",cancel:"\u53D6\u6D88",delete:"\u5220\u9664",add:"\u6DFB\u52A0\u4E00\u884C\u6570\u636E"}},switch:{open:"\u6253\u5F00",close:"\u5173\u95ED"}}),s={"mn-MN":o,"ar-EG":a,"zh-CN":l,"en-US":i("en_US",{moneySymbol:"$",deleteThisLine:"Delete this line",copyThisLine:"Copy this line",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed to the left",rightFixedTitle:"Fixed to the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Table Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",onlyAddOneLine:"Only one line can be added",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}}),"en-GB":i("en_GB",{moneySymbol:"\xa3",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed to the left",rightFixedTitle:"Fixed to the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Table Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",onlyAddOneLine:"Only one line can be added",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}}),"vi-VN":i("vi_VN",{moneySymbol:"\u20AB",form:{lightFilter:{more:"Nhi\u1EC1u h\u01A1n",clear:"Trong",confirm:"X\xe1c nh\u1EADn",itemUnit:"M\u1EE5c"}},tableForm:{search:"T\xecm ki\u1EBFm",reset:"L\xe0m l\u1EA1i",submit:"G\u1EEDi \u0111i",collapsed:"M\u1EDF r\u1ED9ng",expand:"Thu g\u1ECDn",inputPlaceholder:"nh\u1EADp d\u1EEF li\u1EC7u",selectPlaceholder:"Vui l\xf2ng ch\u1ECDn"},alert:{clear:"X\xf3a",selected:"\u0111\xe3 ch\u1ECDn",item:"m\u1EE5c"},pagination:{total:{range:" ",total:"tr\xean",item:"m\u1EB7t h\xe0ng"}},tableToolBar:{leftPin:"Ghim tr\xe1i",rightPin:"Ghim ph\u1EA3i",noPin:"B\u1ECF ghim",leftFixedTitle:"C\u1ED1 \u0111\u1ECBnh tr\xe1i",rightFixedTitle:"C\u1ED1 \u0111\u1ECBnh ph\u1EA3i",noFixedTitle:"Ch\u01B0a c\u1ED1 \u0111\u1ECBnh",reset:"L\xe0m l\u1EA1i",columnDisplay:"C\u1ED9t hi\u1EC3n th\u1ECB",columnSetting:"C\u1EA5u h\xecnh",fullScreen:"Ch\u1EBF \u0111\u1ED9 to\xe0n m\xe0n h\xecnh",exitFullScreen:"Tho\xe1t ch\u1EBF \u0111\u1ED9 to\xe0n m\xe0n h\xecnh",reload:"L\xe0m m\u1EDBi",density:"M\u1EADt \u0111\u1ED9 hi\u1EC3n th\u1ECB",densityDefault:"M\u1EB7c \u0111\u1ECBnh",densityLarger:"M\u1EB7c \u0111\u1ECBnh",densityMiddle:"Trung b\xecnh",densitySmall:"Ch\u1EADt"},stepsForm:{next:"Sau",prev:"Tr\u01B0\u1EDBc",submit:"K\u1EBFt th\xfac"},loginForm:{submitText:"\u0110\u0103ng nh\u1EADp"},editableTable:{action:{save:"C\u1EE9u",cancel:"H\u1EE7y",delete:"X\xf3a",add:"th\xeam m\u1ED9t h\xe0ng d\u1EEF li\u1EC7u"}},switch:{open:"m\u1EDF",close:"\u0111\xf3ng"}}),"it-IT":i("it_IT",{moneySymbol:"\u20AC",form:{lightFilter:{more:"pi\xf9",clear:"pulisci",confirm:"conferma",itemUnit:"elementi"}},tableForm:{search:"Filtra",reset:"Pulisci",submit:"Invia",collapsed:"Espandi",expand:"Contrai",inputPlaceholder:"Digita",selectPlaceholder:"Seleziona"},alert:{clear:"Rimuovi",selected:"Selezionati",item:"elementi"},pagination:{total:{range:" ",total:"di",item:"elementi"}},tableToolBar:{leftPin:"Fissa a sinistra",rightPin:"Fissa a destra",noPin:"Ripristina posizione",leftFixedTitle:"Fissato a sinistra",rightFixedTitle:"Fissato a destra",noFixedTitle:"Non fissato",reset:"Ripristina",columnDisplay:"Disposizione colonne",columnSetting:"Impostazioni",fullScreen:"Modalit\xe0 schermo intero",exitFullScreen:"Esci da modalit\xe0 schermo intero",reload:"Ricarica",density:"Grandezza tabella",densityDefault:"predefinito",densityLarger:"Grande",densityMiddle:"Media",densitySmall:"Compatta"},stepsForm:{next:"successivo",prev:"precedente",submit:"finisci"},loginForm:{submitText:"Accedi"},editableTable:{action:{save:"salva",cancel:"annulla",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"chiudi"}}),"ja-JP":i("ja_JP",{moneySymbol:"\xa5",form:{lightFilter:{more:"\u66F4\u306B",clear:"\u30AF\u30EA\u30A2",confirm:"\u78BA\u8A8D",itemUnit:"\u30A2\u30A4\u30C6\u30E0"}},tableForm:{search:"\u691C\u7D22",reset:"\u30EA\u30BB\u30C3\u30C8",submit:"\u9001\u4FE1",collapsed:"\u62E1\u5927",expand:"\u6298\u7573",inputPlaceholder:"\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",selectPlaceholder:"\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044"},alert:{clear:"\u30AF\u30EA\u30A2",selected:"\u9078\u629E\u3057\u305F",item:"\u30A2\u30A4\u30C6\u30E0"},pagination:{total:{range:"\u30EC\u30B3\u30FC\u30C9",total:"/\u5408\u8A08",item:" "}},tableToolBar:{leftPin:"\u5DE6\u306B\u56FA\u5B9A",rightPin:"\u53F3\u306B\u56FA\u5B9A",noPin:"\u30AD\u30E3\u30F3\u30BB\u30EB",leftFixedTitle:"\u5DE6\u306B\u56FA\u5B9A\u3055\u308C\u305F\u9805\u76EE",rightFixedTitle:"\u53F3\u306B\u56FA\u5B9A\u3055\u308C\u305F\u9805\u76EE",noFixedTitle:"\u56FA\u5B9A\u3055\u308C\u3066\u306A\u3044\u9805\u76EE",reset:"\u30EA\u30BB\u30C3\u30C8",columnDisplay:"\u8868\u793A\u5217",columnSetting:"\u5217\u8868\u793A\u8A2D\u5B9A",fullScreen:"\u30D5\u30EB\u30B9\u30AF\u30EA\u30FC\u30F3",exitFullScreen:"\u7D42\u4E86",reload:"\u66F4\u65B0",density:"\u884C\u9AD8",densityDefault:"\u30C7\u30D5\u30A9\u30EB\u30C8",densityLarger:"\u5927",densityMiddle:"\u4E2D",densitySmall:"\u5C0F"},stepsForm:{next:"\u6B21\u3078",prev:"\u524D\u3078",submit:"\u9001\u4FE1"},loginForm:{submitText:"\u30ED\u30B0\u30A4\u30F3"},editableTable:{action:{save:"\u4FDD\u5B58",cancel:"\u30AD\u30E3\u30F3\u30BB\u30EB",delete:"\u524A\u9664",add:"\u8FFD\u52A0"}},switch:{open:"\u958B\u304F",close:"\u9589\u3058\u308B"}}),"es-ES":i("es_ES",{moneySymbol:"\u20AC",form:{lightFilter:{more:"M\xe1s",clear:"Limpiar",confirm:"Confirmar",itemUnit:"art\xedculos"}},tableForm:{search:"Buscar",reset:"Limpiar",submit:"Submit",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Ingrese valor",selectPlaceholder:"Seleccione valor"},alert:{clear:"Limpiar",selected:"Seleccionado",item:"Articulo"},pagination:{total:{range:" ",total:"de",item:"art\xedculos"}},tableToolBar:{leftPin:"Pin a la izquierda",rightPin:"Pin a la derecha",noPin:"Sin Pin",leftFixedTitle:"Fijado a la izquierda",rightFixedTitle:"Fijado a la derecha",noFixedTitle:"Sin Fijar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuraci\xf3n",fullScreen:"Pantalla Completa",exitFullScreen:"Salir Pantalla Completa",reload:"Refrescar",density:"Densidad",densityDefault:"Por Defecto",densityLarger:"Largo",densityMiddle:"Medio",densitySmall:"Compacto"},stepsForm:{next:"Siguiente",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Descartar",delete:"Borrar",add:"a\xf1adir una fila de datos"}},switch:{open:"abrir",close:"cerrar"}}),"ca-ES":i("ca_ES",{moneySymbol:"\u20AC",form:{lightFilter:{more:"M\xe9s",clear:"Netejar",confirm:"Confirmar",itemUnit:"Elements"}},tableForm:{search:"Cercar",reset:"Netejar",submit:"Enviar",collapsed:"Expandir",expand:"Col\xb7lapsar",inputPlaceholder:"Introdu\xefu valor",selectPlaceholder:"Seleccioneu valor"},alert:{clear:"Netejar",selected:"Seleccionat",item:"Article"},pagination:{total:{range:" ",total:"de",item:"articles"}},tableToolBar:{leftPin:"Pin a l'esquerra",rightPin:"Pin a la dreta",noPin:"Sense Pin",leftFixedTitle:"Fixat a l'esquerra",rightFixedTitle:"Fixat a la dreta",noFixedTitle:"Sense fixar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuraci\xf3",fullScreen:"Pantalla Completa",exitFullScreen:"Sortir Pantalla Completa",reload:"Refrescar",density:"Densitat",densityDefault:"Per Defecte",densityLarger:"Llarg",densityMiddle:"Mitj\xe0",densitySmall:"Compacte"},stepsForm:{next:"Seg\xfcent",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Cancel\xb7lar",delete:"Eliminar",add:"afegir una fila de dades"}},switch:{open:"obert",close:"tancat"}}),"ru-RU":i("ru_RU",{moneySymbol:"\u20BD",form:{lightFilter:{more:"\u0415\u0449\u0435",clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",confirm:"\u041E\u041A",itemUnit:"\u041F\u043E\u0437\u0438\u0446\u0438\u0438"}},tableForm:{search:"\u041D\u0430\u0439\u0442\u0438",reset:"\u0421\u0431\u0440\u043E\u0441",submit:"\u041E\u0442\u043F\u0440\u0430\u0432\u0438\u0442\u044C",collapsed:"\u0420\u0430\u0437\u0432\u0435\u0440\u043D\u0443\u0442\u044C",expand:"\u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C",inputPlaceholder:"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435",selectPlaceholder:"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435"},alert:{clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",selected:"\u0412\u044B\u0431\u0440\u0430\u043D\u043E",item:"\u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432"},pagination:{total:{range:" ",total:"\u0438\u0437",item:"\u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432"}},tableToolBar:{leftPin:"\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0441\u043B\u0435\u0432\u0430",rightPin:"\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0441\u043F\u0440\u0430\u0432\u0430",noPin:"\u041E\u0442\u043A\u0440\u0435\u043F\u0438\u0442\u044C",leftFixedTitle:"\u0417\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E \u0441\u043B\u0435\u0432\u0430",rightFixedTitle:"\u0417\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E \u0441\u043F\u0440\u0430\u0432\u0430",noFixedTitle:"\u041D\u0435 \u0437\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E",reset:"\u0421\u0431\u0440\u043E\u0441",columnDisplay:"\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u0441\u0442\u043E\u043B\u0431\u0446\u0430",columnSetting:"\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438",fullScreen:"\u041F\u043E\u043B\u043D\u044B\u0439 \u044D\u043A\u0440\u0430\u043D",exitFullScreen:"\u0412\u044B\u0439\u0442\u0438 \u0438\u0437 \u043F\u043E\u043B\u043D\u043E\u044D\u043A\u0440\u0430\u043D\u043D\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0430",reload:"\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C",density:"\u0420\u0430\u0437\u043C\u0435\u0440",densityDefault:"\u041F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",densityLarger:"\u0411\u043E\u043B\u044C\u0448\u043E\u0439",densityMiddle:"\u0421\u0440\u0435\u0434\u043D\u0438\u0439",densitySmall:"\u0421\u0436\u0430\u0442\u044B\u0439"},stepsForm:{next:"\u0421\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439",prev:"\u041F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0438\u0439",submit:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044C"},loginForm:{submitText:"\u0412\u0445\u043E\u0434"},editableTable:{action:{save:"\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",cancel:"\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C",delete:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C",add:"\u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0440\u044F\u0434 \u0434\u0430\u043D\u043D\u044B\u0445"}},switch:{open:"\u041E\u0442\u043A\u0440\u044B\u0442\u044B\u0439 \u0447\u0435\u043C\u043F\u0438\u043E\u043D\u0430\u0442 \u043C\u0438\u0440\u0430 \u043F\u043E \u0442\u0435\u043D\u043D\u0438\u0441\u0443",close:"\u041F\u043E \u0430\u0434\u0440\u0435\u0441\u0443:"}}),"sr-RS":i("sr_RS",{moneySymbol:"RSD",form:{lightFilter:{more:"Vi\u0161e",clear:"O\u010Disti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Prona\u0111i",reset:"Resetuj",submit:"Po\u0161alji",collapsed:"Pro\u0161iri",expand:"Skupi",inputPlaceholder:"Molimo unesite",selectPlaceholder:"Molimo odaberite"},alert:{clear:"O\u010Disti",selected:"Odabrano",item:"Stavka"},pagination:{total:{range:" ",total:"od",item:"stavki"}},tableToolBar:{leftPin:"Zaka\u010Di levo",rightPin:"Zaka\u010Di desno",noPin:"Nije zaka\u010Deno",leftFixedTitle:"Fiksirano levo",rightFixedTitle:"Fiksirano desno",noFixedTitle:"Nije fiksirano",reset:"Resetuj",columnDisplay:"Prikaz kolona",columnSetting:"Pode\u0161avanja",fullScreen:"Pun ekran",exitFullScreen:"Zatvori pun ekran",reload:"Osve\u017Ei",density:"Veli\u010Dina",densityDefault:"Podrazumevana",densityLarger:"Ve\u0107a",densityMiddle:"Srednja",densitySmall:"Kompaktna"},stepsForm:{next:"Dalje",prev:"Nazad",submit:"Gotovo"},loginForm:{submitText:"Prijavi se"},editableTable:{action:{save:"Sa\u010Duvaj",cancel:"Poni\u0161ti",delete:"Obri\u0161i",add:"dodajte red podataka"}},switch:{open:"\u041E\u0442\u0432\u043E\u0440\u0438\u0442\u0435",close:"\u0417\u0430\u0442\u0432\u043E\u0440\u0438\u0442\u0435"}}),"ms-MY":i("ms_MY",{moneySymbol:"RM",form:{lightFilter:{more:"Lebih banyak",clear:"Jelas",confirm:"Mengesahkan",itemUnit:"Item"}},tableForm:{search:"Cari",reset:"Menetapkan semula",submit:"Hantar",collapsed:"Kembang",expand:"Kuncup",inputPlaceholder:"Sila masuk",selectPlaceholder:"Sila pilih"},alert:{clear:"Padam",selected:"Dipilih",item:"Item"},pagination:{total:{range:" ",total:"daripada",item:"item"}},tableToolBar:{leftPin:"Pin ke kiri",rightPin:"Pin ke kanan",noPin:"Tidak pin",leftFixedTitle:"Tetap ke kiri",rightFixedTitle:"Tetap ke kanan",noFixedTitle:"Tidak Tetap",reset:"Menetapkan semula",columnDisplay:"Lajur",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Keluar Full Screen",reload:"Muat Semula",density:"Densiti",densityDefault:"Biasa",densityLarger:"Besar",densityMiddle:"Tengah",densitySmall:"Kecil"},stepsForm:{next:"Seterusnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Log Masuk"},editableTable:{action:{save:"Simpan",cancel:"Membatalkan",delete:"Menghapuskan",add:"tambah baris data"}},switch:{open:"Terbuka",close:"Tutup"}}),"zh-TW":i("zh_TW",{moneySymbol:"NT$",deleteThisLine:"\u522A\u9664\u6B64\u9879",copyThisLine:"\u8907\u88FD\u6B64\u9879",form:{lightFilter:{more:"\u66F4\u591A\u7BE9\u9078",clear:"\u6E05\u9664",confirm:"\u78BA\u8A8D",itemUnit:"\u9805"}},tableForm:{search:"\u67E5\u8A62",reset:"\u91CD\u7F6E",submit:"\u63D0\u4EA4",collapsed:"\u5C55\u958B",expand:"\u6536\u8D77",inputPlaceholder:"\u8ACB\u8F38\u5165",selectPlaceholder:"\u8ACB\u9078\u64C7"},alert:{clear:"\u53D6\u6D88\u9078\u64C7",selected:"\u5DF2\u9078\u64C7",item:"\u9805"},pagination:{total:{range:"\u7B2C",total:"\u689D/\u7E3D\u5171",item:"\u689D"}},tableToolBar:{leftPin:"\u56FA\u5B9A\u5230\u5DE6\u908A",rightPin:"\u56FA\u5B9A\u5230\u53F3\u908A",noPin:"\u4E0D\u56FA\u5B9A",leftFixedTitle:"\u56FA\u5B9A\u5728\u5DE6\u5074",rightFixedTitle:"\u56FA\u5B9A\u5728\u53F3\u5074",noFixedTitle:"\u4E0D\u56FA\u5B9A",reset:"\u91CD\u7F6E",columnDisplay:"\u5217\u5C55\u793A",columnSetting:"\u5217\u8A2D\u7F6E",fullScreen:"\u5168\u5C4F",exitFullScreen:"\u9000\u51FA\u5168\u5C4F",reload:"\u5237\u65B0",density:"\u5BC6\u5EA6",densityDefault:"\u6B63\u5E38",densityLarger:"\u5BEC\u9B06",densityMiddle:"\u4E2D\u7B49",densitySmall:"\u7DCA\u6E4A"},stepsForm:{next:"\u4E0B\u4E00\u6B65",prev:"\u4E0A\u4E00\u6B65",submit:"\u5B8C\u6210"},loginForm:{submitText:"\u767B\u5165"},editableTable:{onlyOneLineEditor:"\u53EA\u80FD\u540C\u6642\u7DE8\u8F2F\u4E00\u884C",action:{save:"\u4FDD\u5B58",cancel:"\u53D6\u6D88",delete:"\u522A\u9664",add:"\u65B0\u589E\u4E00\u884C\u8CC7\u6599"}},switch:{open:"\u6253\u958B",close:"\u95DC\u9589"}}),"fr-FR":i("fr_FR",{moneySymbol:"\u20AC",form:{lightFilter:{more:"Plus",clear:"Effacer",confirm:"Confirmer",itemUnit:"Items"}},tableForm:{search:"Rechercher",reset:"R\xe9initialiser",submit:"Envoyer",collapsed:"Agrandir",expand:"R\xe9duire",inputPlaceholder:"Entrer une valeur",selectPlaceholder:"S\xe9lectionner une valeur"},alert:{clear:"R\xe9initialiser",selected:"S\xe9lectionn\xe9",item:"Item"},pagination:{total:{range:" ",total:"sur",item:"\xe9l\xe9ments"}},tableToolBar:{leftPin:"\xc9pingler \xe0 gauche",rightPin:"\xc9pingler \xe0 gauche",noPin:"Sans \xe9pingle",leftFixedTitle:"Fixer \xe0 gauche",rightFixedTitle:"Fixer \xe0 droite",noFixedTitle:"Non fix\xe9",reset:"R\xe9initialiser",columnDisplay:"Affichage colonne",columnSetting:"R\xe9glages",fullScreen:"Plein \xe9cran",exitFullScreen:"Quitter Plein \xe9cran",reload:"Rafraichir",density:"Densit\xe9",densityDefault:"Par d\xe9faut",densityLarger:"Larger",densityMiddle:"Moyenne",densitySmall:"Compacte"},stepsForm:{next:"Suivante",prev:"Pr\xe9c\xe9dente",submit:"Finaliser"},loginForm:{submitText:"Se connecter"},editableTable:{action:{save:"Sauvegarder",cancel:"Annuler",delete:"Supprimer",add:"ajouter une ligne de donn\xe9es"}},switch:{open:"ouvert",close:"pr\xe8s"}}),"pt-BR":i("pt_BR",{moneySymbol:"R$",form:{lightFilter:{more:"Mais",clear:"Limpar",confirm:"Confirmar",itemUnit:"Itens"}},tableForm:{search:"Filtrar",reset:"Limpar",submit:"Confirmar",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Por favor insira",selectPlaceholder:"Por favor selecione"},alert:{clear:"Limpar",selected:"Selecionado(s)",item:"Item(s)"},pagination:{total:{range:" ",total:"de",item:"itens"}},tableToolBar:{leftPin:"Fixar \xe0 esquerda",rightPin:"Fixar \xe0 direita",noPin:"Desfixado",leftFixedTitle:"Fixado \xe0 esquerda",rightFixedTitle:"Fixado \xe0 direita",noFixedTitle:"N\xe3o fixado",reset:"Limpar",columnDisplay:"Mostrar Coluna",columnSetting:"Configura\xe7\xf5es",fullScreen:"Tela Cheia",exitFullScreen:"Sair da Tela Cheia",reload:"Atualizar",density:"Densidade",densityDefault:"Padr\xe3o",densityLarger:"Largo",densityMiddle:"M\xe9dio",densitySmall:"Compacto"},stepsForm:{next:"Pr\xf3ximo",prev:"Anterior",submit:"Enviar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Salvar",cancel:"Cancelar",delete:"Apagar",add:"adicionar uma linha de dados"}},switch:{open:"abrir",close:"fechar"}}),"ko-KR":i("ko_KR",{moneySymbol:"\u20A9",form:{lightFilter:{more:"\uB354\uBCF4\uAE30",clear:"\uCD08\uAE30\uD654",confirm:"\uD655\uC778",itemUnit:"\uAC74\uC218"}},tableForm:{search:"\uC870\uD68C",reset:"\uCD08\uAE30\uD654",submit:"\uC81C\uCD9C",collapsed:"\uD655\uC7A5",expand:"\uB2EB\uAE30",inputPlaceholder:"\uC785\uB825\uD574 \uC8FC\uC138\uC694",selectPlaceholder:"\uC120\uD0DD\uD574 \uC8FC\uC138\uC694"},alert:{clear:"\uCDE8\uC18C",selected:"\uC120\uD0DD",item:"\uAC74"},pagination:{total:{range:" ",total:"/ \uCD1D",item:"\uAC74"}},tableToolBar:{leftPin:"\uC67C\uCABD\uC73C\uB85C \uD540",rightPin:"\uC624\uB978\uCABD\uC73C\uB85C \uD540",noPin:"\uD540 \uC81C\uAC70",leftFixedTitle:"\uC67C\uCABD\uC73C\uB85C \uACE0\uC815",rightFixedTitle:"\uC624\uB978\uCABD\uC73C\uB85C \uACE0\uC815",noFixedTitle:"\uBE44\uACE0\uC815",reset:"\uCD08\uAE30\uD654",columnDisplay:"\uCEEC\uB7FC \uD45C\uC2DC",columnSetting:"\uC124\uC815",fullScreen:"\uC804\uCCB4 \uD654\uBA74",exitFullScreen:"\uC804\uCCB4 \uD654\uBA74 \uCDE8\uC18C",reload:"\uC0C8\uB85C \uACE0\uCE68",density:"\uC5EC\uBC31",densityDefault:"\uAE30\uBCF8",densityLarger:"\uB9CE\uC740 \uC5EC\uBC31",densityMiddle:"\uC911\uAC04 \uC5EC\uBC31",densitySmall:"\uC881\uC740 \uC5EC\uBC31"},stepsForm:{next:"\uB2E4\uC74C",prev:"\uC774\uC804",submit:"\uC885\uB8CC"},loginForm:{submitText:"\uB85C\uADF8\uC778"},editableTable:{action:{save:"\uC800\uC7A5",cancel:"\uCDE8\uC18C",delete:"\uC0AD\uC81C",add:"\uB370\uC774\uD130 \uD589 \uCD94\uAC00"}},switch:{open:"\uC5F4",close:"\uAC00\uAE4C \uC6B4"}}),"id-ID":i("id_ID",{moneySymbol:"RP",form:{lightFilter:{more:"Lebih",clear:"Hapus",confirm:"Konfirmasi",itemUnit:"Unit"}},tableForm:{search:"Cari",reset:"Atur ulang",submit:"Kirim",collapsed:"Lebih sedikit",expand:"Lebih banyak",inputPlaceholder:"Masukkan pencarian",selectPlaceholder:"Pilih"},alert:{clear:"Hapus",selected:"Dipilih",item:"Butir"},pagination:{total:{range:" ",total:"Dari",item:"Butir"}},tableToolBar:{leftPin:"Pin kiri",rightPin:"Pin kanan",noPin:"Tidak ada pin",leftFixedTitle:"Rata kiri",rightFixedTitle:"Rata kanan",noFixedTitle:"Tidak tetap",reset:"Atur ulang",columnDisplay:"Tampilan kolom",columnSetting:"Pengaturan",fullScreen:"Layar penuh",exitFullScreen:"Keluar layar penuh",reload:"Atur ulang",density:"Kerapatan",densityDefault:"Standar",densityLarger:"Lebih besar",densityMiddle:"Sedang",densitySmall:"Rapat"},stepsForm:{next:"Selanjutnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Login"},editableTable:{action:{save:"simpan",cancel:"batal",delete:"hapus",add:"Tambahkan baris data"}},switch:{open:"buka",close:"tutup"}}),"de-DE":i("de_DE",{moneySymbol:"\u20AC",form:{lightFilter:{more:"Mehr",clear:"Zur\xfccksetzen",confirm:"Best\xe4tigen",itemUnit:"Eintr\xe4ge"}},tableForm:{search:"Suchen",reset:"Zur\xfccksetzen",submit:"Absenden",collapsed:"Zeige mehr",expand:"Zeige weniger",inputPlaceholder:"Bitte eingeben",selectPlaceholder:"Bitte ausw\xe4hlen"},alert:{clear:"Zur\xfccksetzen",selected:"Ausgew\xe4hlt",item:"Eintrag"},pagination:{total:{range:" ",total:"von",item:"Eintr\xe4gen"}},tableToolBar:{leftPin:"Links anheften",rightPin:"Rechts anheften",noPin:"Nicht angeheftet",leftFixedTitle:"Links fixiert",rightFixedTitle:"Rechts fixiert",noFixedTitle:"Nicht fixiert",reset:"Zur\xfccksetzen",columnDisplay:"Angezeigte Reihen",columnSetting:"Einstellungen",fullScreen:"Vollbild",exitFullScreen:"Vollbild verlassen",reload:"Aktualisieren",density:"Abstand",densityDefault:"Standard",densityLarger:"Gr\xf6\xdfer",densityMiddle:"Mittel",densitySmall:"Kompakt"},stepsForm:{next:"Weiter",prev:"Zur\xfcck",submit:"Abschlie\xdfen"},loginForm:{submitText:"Anmelden"},editableTable:{action:{save:"Retten",cancel:"Abbrechen",delete:"L\xf6schen",add:"Hinzuf\xfcgen einer Datenzeile"}},switch:{open:"offen",close:"schlie\xdfen"}}),"fa-IR":i("fa_IR",{moneySymbol:"\u062A\u0648\u0645\u0627\u0646",form:{lightFilter:{more:"\u0628\u06CC\u0634\u062A\u0631",clear:"\u067E\u0627\u06A9 \u06A9\u0631\u062F\u0646",confirm:"\u062A\u0627\u06CC\u06CC\u062F",itemUnit:"\u0645\u0648\u0631\u062F"}},tableForm:{search:"\u062C\u0633\u062A\u062C\u0648",reset:"\u0628\u0627\u0632\u0646\u0634\u0627\u0646\u06CC",submit:"\u062A\u0627\u06CC\u06CC\u062F",collapsed:"\u0646\u0645\u0627\u06CC\u0634 \u0628\u06CC\u0634\u062A\u0631",expand:"\u0646\u0645\u0627\u06CC\u0634 \u06A9\u0645\u062A\u0631",inputPlaceholder:"\u067E\u06CC\u062F\u0627 \u06A9\u0646\u06CC\u062F",selectPlaceholder:"\u0627\u0646\u062A\u062E\u0627\u0628 \u06A9\u0646\u06CC\u062F"},alert:{clear:"\u067E\u0627\u06A9 \u0633\u0627\u0632\u06CC",selected:"\u0627\u0646\u062A\u062E\u0627\u0628",item:"\u0645\u0648\u0631\u062F"},pagination:{total:{range:" ",total:"\u0627\u0632",item:"\u0645\u0648\u0631\u062F"}},tableToolBar:{leftPin:"\u0633\u0646\u062C\u0627\u0642 \u0628\u0647 \u0686\u067E",rightPin:"\u0633\u0646\u062C\u0627\u0642 \u0628\u0647 \u0631\u0627\u0633\u062A",noPin:"\u0633\u0646\u062C\u0627\u0642 \u0646\u0634\u062F\u0647",leftFixedTitle:"\u062B\u0627\u0628\u062A \u0634\u062F\u0647 \u062F\u0631 \u0686\u067E",rightFixedTitle:"\u062B\u0627\u0628\u062A \u0634\u062F\u0647 \u062F\u0631 \u0631\u0627\u0633\u062A",noFixedTitle:"\u0634\u0646\u0627\u0648\u0631",reset:"\u0628\u0627\u0632\u0646\u0634\u0627\u0646\u06CC",columnDisplay:"\u0646\u0645\u0627\u06CC\u0634 \u0647\u0645\u0647",columnSetting:"\u062A\u0646\u0638\u06CC\u0645\u0627\u062A",fullScreen:"\u062A\u0645\u0627\u0645 \u0635\u0641\u062D\u0647",exitFullScreen:"\u062E\u0631\u0648\u062C \u0627\u0632 \u062D\u0627\u0644\u062A \u062A\u0645\u0627\u0645 \u0635\u0641\u062D\u0647",reload:"\u062A\u0627\u0632\u0647 \u0633\u0627\u0632\u06CC",density:"\u062A\u0631\u0627\u06A9\u0645",densityDefault:"\u067E\u06CC\u0634 \u0641\u0631\u0636",densityLarger:"\u0628\u0632\u0631\u06AF",densityMiddle:"\u0645\u062A\u0648\u0633\u0637",densitySmall:"\u06A9\u0648\u0686\u06A9"},stepsForm:{next:"\u0628\u0639\u062F\u06CC",prev:"\u0642\u0628\u0644\u06CC",submit:"\u0627\u062A\u0645\u0627\u0645"},loginForm:{submitText:"\u0648\u0631\u0648\u062F"},editableTable:{action:{save:"\u0630\u062E\u06CC\u0631\u0647",cancel:"\u0644\u063A\u0648",delete:"\u062D\u0630\u0641",add:"\u06CC\u06A9 \u0631\u062F\u06CC\u0641 \u062F\u0627\u062F\u0647 \u0627\u0636\u0627\u0641\u0647 \u06A9\u0646\u06CC\u062F"}},switch:{open:"\u0628\u0627\u0632",close:"\u0646\u0632\u062F\u06CC\u06A9"}}),"tr-TR":i("tr_TR",{moneySymbol:"\u20BA",form:{lightFilter:{more:"Daha Fazla",clear:"Temizle",confirm:"Onayla",itemUnit:"\xd6\u011Feler"}},tableForm:{search:"Filtrele",reset:"S\u0131f\u0131rla",submit:"G\xf6nder",collapsed:"Daha fazla",expand:"Daha az",inputPlaceholder:"Filtrelemek i\xe7in bir de\u011Fer girin",selectPlaceholder:"Filtrelemek i\xe7in bir de\u011Fer se\xe7in"},alert:{clear:"Temizle",selected:"Se\xe7ili",item:"\xd6\u011Fe"},pagination:{total:{range:" ",total:"Toplam",item:"\xd6\u011Fe"}},tableToolBar:{leftPin:"Sola sabitle",rightPin:"Sa\u011Fa sabitle",noPin:"Sabitlemeyi kald\u0131r",leftFixedTitle:"Sola sabitlendi",rightFixedTitle:"Sa\u011Fa sabitlendi",noFixedTitle:"Sabitlenmedi",reset:"S\u0131f\u0131rla",columnDisplay:"Kolon G\xf6r\xfcn\xfcm\xfc",columnSetting:"Ayarlar",fullScreen:"Tam Ekran",exitFullScreen:"Tam Ekrandan \xc7\u0131k",reload:"Yenile",density:"Kal\u0131nl\u0131k",densityDefault:"Varsay\u0131lan",densityLarger:"B\xfcy\xfck",densityMiddle:"Orta",densitySmall:"K\xfc\xe7\xfck"},stepsForm:{next:"S\u0131radaki",prev:"\xd6nceki",submit:"G\xf6nder"},loginForm:{submitText:"Giri\u015F Yap"},editableTable:{action:{save:"Kaydet",cancel:"Vazge\xe7",delete:"Sil",add:"foegje in rige gegevens ta"}},switch:{open:"a\xe7\u0131k",close:"kapatmak"}}),"pl-PL":i("pl_PL",{moneySymbol:"z\u0142",form:{lightFilter:{more:"Wi\u0119cej",clear:"Wyczy\u015B\u0107",confirm:"Potwierd\u017A",itemUnit:"Ilo\u015B\u0107"}},tableForm:{search:"Szukaj",reset:"Reset",submit:"Zatwierd\u017A",collapsed:"Poka\u017C wiecej",expand:"Poka\u017C mniej",inputPlaceholder:"Prosz\u0119 poda\u0107",selectPlaceholder:"Prosz\u0119 wybra\u0107"},alert:{clear:"Wyczy\u015B\u0107",selected:"Wybrane",item:"Wpis"},pagination:{total:{range:" ",total:"z",item:"Wpis\xf3w"}},tableToolBar:{leftPin:"Przypnij do lewej",rightPin:"Przypnij do prawej",noPin:"Odepnij",leftFixedTitle:"Przypi\u0119te do lewej",rightFixedTitle:"Przypi\u0119te do prawej",noFixedTitle:"Nieprzypi\u0119te",reset:"Reset",columnDisplay:"Wy\u015Bwietlane wiersze",columnSetting:"Ustawienia",fullScreen:"Pe\u0142en ekran",exitFullScreen:"Zamknij pe\u0142en ekran",reload:"Od\u015Bwie\u017C",density:"Odst\u0119p",densityDefault:"Standard",densityLarger:"Wiekszy",densityMiddle:"Sredni",densitySmall:"Kompaktowy"},stepsForm:{next:"Weiter",prev:"Zur\xfcck",submit:"Abschlie\xdfen"},loginForm:{submitText:"Zaloguj si\u0119"},editableTable:{action:{save:"Zapisa\u0107",cancel:"Anuluj",delete:"Usun\u0105\u0107",add:"dodawanie wiersza danych"}},switch:{open:"otwiera\u0107",close:"zamyka\u0107"}}),"hr-HR":i("hr_",{moneySymbol:"kn",form:{lightFilter:{more:"Vi\u0161e",clear:"O\u010Disti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Pretra\u017Ei",reset:"Poni\u0161ti",submit:"Potvrdi",collapsed:"Ra\u0161iri",expand:"Skupi",inputPlaceholder:"Unesite",selectPlaceholder:"Odaberite"},alert:{clear:"O\u010Disti",selected:"Odaberi",item:"stavke"},pagination:{total:{range:" ",total:"od",item:"stavke"}},tableToolBar:{leftPin:"Prika\u010Di lijevo",rightPin:"Prika\u010Di desno",noPin:"Bez prika\u010Denja",leftFixedTitle:"Fiksiraj lijevo",rightFixedTitle:"Fiksiraj desno",noFixedTitle:"Bez fiksiranja",reset:"Resetiraj",columnDisplay:"Prikaz stupaca",columnSetting:"Postavke",fullScreen:"Puni zaslon",exitFullScreen:"Iza\u0111i iz punog zaslona",reload:"Ponovno u\u010Ditaj",density:"Veli\u010Dina",densityDefault:"Zadano",densityLarger:"Veliko",densityMiddle:"Srednje",densitySmall:"Malo"},stepsForm:{next:"Sljede\u0107i",prev:"Prethodni",submit:"Kraj"},loginForm:{submitText:"Prijava"},editableTable:{action:{save:"Spremi",cancel:"Odustani",delete:"Obri\u0161i",add:"dodajte red podataka"}},switch:{open:"otvori",close:"zatvori"}}),"th-TH":i("th_TH",{moneySymbol:"\u0E3F",deleteThisLine:"\u0E25\u0E1A\u0E1A\u0E23\u0E23\u0E17\u0E31\u0E14\u0E19\u0E35\u0E49",copyThisLine:"\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E1A\u0E23\u0E23\u0E17\u0E31\u0E14\u0E19\u0E35\u0E49",form:{lightFilter:{more:"\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32",clear:"\u0E0A\u0E31\u0E14\u0E40\u0E08\u0E19",confirm:"\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19",itemUnit:"\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23"}},tableForm:{search:"\u0E2A\u0E2D\u0E1A\u0E16\u0E32\u0E21",reset:"\u0E23\u0E35\u0E40\u0E0B\u0E47\u0E15",submit:"\u0E2A\u0E48\u0E07",collapsed:"\u0E02\u0E22\u0E32\u0E22",expand:"\u0E17\u0E23\u0E38\u0E14",inputPlaceholder:"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E1B\u0E49\u0E2D\u0E19",selectPlaceholder:"\u0E42\u0E1B\u0E23\u0E14\u0E40\u0E25\u0E37\u0E2D\u0E01"},alert:{clear:"\u0E0A\u0E31\u0E14\u0E40\u0E08\u0E19",selected:"\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E41\u0E25\u0E49\u0E27",item:"\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23"},pagination:{total:{range:" ",total:"\u0E02\u0E2D\u0E07",item:"\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23"}},tableToolBar:{leftPin:"\u0E1B\u0E31\u0E01\u0E2B\u0E21\u0E38\u0E14\u0E44\u0E1B\u0E17\u0E32\u0E07\u0E0B\u0E49\u0E32\u0E22",rightPin:"\u0E1B\u0E31\u0E01\u0E2B\u0E21\u0E38\u0E14\u0E44\u0E1B\u0E17\u0E32\u0E07\u0E02\u0E27\u0E32",noPin:"\u0E40\u0E25\u0E34\u0E01\u0E15\u0E23\u0E36\u0E07\u0E41\u0E25\u0E49\u0E27",leftFixedTitle:"\u0E41\u0E01\u0E49\u0E44\u0E02\u0E14\u0E49\u0E32\u0E19\u0E0B\u0E49\u0E32\u0E22",rightFixedTitle:"\u0E41\u0E01\u0E49\u0E44\u0E02\u0E14\u0E49\u0E32\u0E19\u0E02\u0E27\u0E32",noFixedTitle:"\u0E44\u0E21\u0E48\u0E04\u0E07\u0E17\u0E35\u0E48",reset:"\u0E23\u0E35\u0E40\u0E0B\u0E47\u0E15",columnDisplay:"\u0E01\u0E32\u0E23\u0E41\u0E2A\u0E14\u0E07\u0E04\u0E2D\u0E25\u0E31\u0E21\u0E19\u0E4C",columnSetting:"\u0E01\u0E32\u0E23\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32",fullScreen:"\u0E40\u0E15\u0E47\u0E21\u0E08\u0E2D",exitFullScreen:"\u0E2D\u0E2D\u0E01\u0E08\u0E32\u0E01\u0E42\u0E2B\u0E21\u0E14\u0E40\u0E15\u0E47\u0E21\u0E2B\u0E19\u0E49\u0E32\u0E08\u0E2D",reload:"\u0E23\u0E35\u0E40\u0E1F\u0E23\u0E0A",density:"\u0E04\u0E27\u0E32\u0E21\u0E2B\u0E19\u0E32\u0E41\u0E19\u0E48\u0E19",densityDefault:"\u0E04\u0E48\u0E32\u0E40\u0E23\u0E34\u0E48\u0E21\u0E15\u0E49\u0E19",densityLarger:"\u0E02\u0E19\u0E32\u0E14\u0E43\u0E2B\u0E0D\u0E48\u0E02\u0E36\u0E49\u0E19",densityMiddle:"\u0E01\u0E25\u0E32\u0E07",densitySmall:"\u0E01\u0E30\u0E17\u0E31\u0E14\u0E23\u0E31\u0E14"},stepsForm:{next:"\u0E16\u0E31\u0E14\u0E44\u0E1B",prev:"\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",submit:"\u0E40\u0E2A\u0E23\u0E47\u0E08"},loginForm:{submitText:"\u0E40\u0E02\u0E49\u0E32\u0E2A\u0E39\u0E48\u0E23\u0E30\u0E1A\u0E1A"},editableTable:{onlyOneLineEditor:"\u0E41\u0E01\u0E49\u0E44\u0E02\u0E44\u0E14\u0E49\u0E40\u0E1E\u0E35\u0E22\u0E07\u0E1A\u0E23\u0E23\u0E17\u0E31\u0E14\u0E40\u0E14\u0E35\u0E22\u0E27\u0E40\u0E17\u0E48\u0E32\u0E19\u0E31\u0E49\u0E19",action:{save:"\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01",cancel:"\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01",delete:"\u0E25\u0E1A",add:"\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E41\u0E16\u0E27\u0E02\u0E2D\u0E07\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25"}},switch:{open:"\u0E40\u0E1B\u0E34\u0E14",close:"\u0E1B\u0E34\u0E14"}}),"cs-CZ":i("cs_cz",{moneySymbol:"K\u010D",deleteThisLine:"Smazat tento \u0159\xe1dek",copyThisLine:"Kop\xedrovat tento \u0159\xe1dek",form:{lightFilter:{more:"V\xedc",clear:"Vymazat",confirm:"Potvrdit",itemUnit:"Polo\u017Eky"}},tableForm:{search:"Dotaz",reset:"Resetovat",submit:"Odeslat",collapsed:"Zv\u011Bt\u0161it",expand:"Zmen\u0161it",inputPlaceholder:"Zadejte pros\xedm",selectPlaceholder:"Vyberte pros\xedm"},alert:{clear:"Vymazat",selected:"Vybran\xfd",item:"Polo\u017Eka"},pagination:{total:{range:" ",total:"z",item:"polo\u017Eek"}},tableToolBar:{leftPin:"P\u0159ipnout doleva",rightPin:"P\u0159ipnout doprava",noPin:"Odepnuto",leftFixedTitle:"Fixov\xe1no nalevo",rightFixedTitle:"Fixov\xe1no napravo",noFixedTitle:"Neopraveno",reset:"Resetovat",columnDisplay:"Zobrazen\xed sloupc\u016F",columnSetting:"Nastaven\xed",fullScreen:"Cel\xe1 obrazovka",exitFullScreen:"Ukon\u010Dete celou obrazovku",reload:"Obnovit",density:"Hustota",densityDefault:"V\xfdchoz\xed",densityLarger:"V\u011Bt\u0161\xed",densityMiddle:"St\u0159edn\xed",densitySmall:"Kompaktn\xed"},stepsForm:{next:"Dal\u0161\xed",prev:"P\u0159edchoz\xed",submit:"Dokon\u010Dit"},loginForm:{submitText:"P\u0159ihl\xe1sit se"},editableTable:{onlyOneLineEditor:"Upravit lze pouze jeden \u0159\xe1dek",action:{save:"Ulo\u017Eit",cancel:"Zru\u0161it",delete:"Vymazat",add:"p\u0159idat \u0159\xe1dek dat"}},switch:{open:"otev\u0159\xedt",close:"zav\u0159\xedt"}}),"sk-SK":i("sk_SK",{moneySymbol:"\u20AC",deleteThisLine:"Odstr\xe1ni\u0165 tento riadok",copyThisLine:"Skop\xedrujte tento riadok",form:{lightFilter:{more:"Viac",clear:"Vy\u010Disti\u0165",confirm:"Potvr\u010Fte",itemUnit:"Polo\u017Eky"}},tableForm:{search:"Vyhlada\u0165",reset:"Resetova\u0165",submit:"Odosla\u0165",collapsed:"Rozbali\u0165",expand:"Zbali\u0165",inputPlaceholder:"Pros\xedm, zadajte",selectPlaceholder:"Pros\xedm, vyberte"},alert:{clear:"Vy\u010Disti\u0165",selected:"Vybran\xfd",item:"Polo\u017Eka"},pagination:{total:{range:" ",total:"z",item:"polo\u017Eiek"}},tableToolBar:{leftPin:"Pripn\xfa\u0165 v\u013Eavo",rightPin:"Pripn\xfa\u0165 vpravo",noPin:"Odopnut\xe9",leftFixedTitle:"Fixovan\xe9 na \u013Eavo",rightFixedTitle:"Fixovan\xe9 na pravo",noFixedTitle:"Nefixovan\xe9",reset:"Resetova\u0165",columnDisplay:"Zobrazenie st\u013Apcov",columnSetting:"Nastavenia",fullScreen:"Cel\xe1 obrazovka",exitFullScreen:"Ukon\u010Di\u0165 cel\xfa obrazovku",reload:"Obnovi\u0165",density:"Hustota",densityDefault:"Predvolen\xe9",densityLarger:"V\xe4\u010D\u0161ie",densityMiddle:"Stredn\xe9",densitySmall:"Kompaktn\xe9"},stepsForm:{next:"\u010Eal\u0161ie",prev:"Predch\xe1dzaj\xface",submit:"Potvrdi\u0165"},loginForm:{submitText:"Prihl\xe1si\u0165 sa"},editableTable:{onlyOneLineEditor:"Upravova\u0165 mo\u017Eno iba jeden riadok",action:{save:"Ulo\u017Ei\u0165",cancel:"Zru\u0161i\u0165",delete:"Odstr\xe1ni\u0165",add:"prida\u0165 riadok \xfadajov"}},switch:{open:"otvori\u0165",close:"zavrie\u0165"}}),"he-IL":i("he_IL",{moneySymbol:"\u20AA",deleteThisLine:"\u05DE\u05D7\u05E7 \u05E9\u05D5\u05E8\u05D4 \u05D6\u05D5",copyThisLine:"\u05D4\u05E2\u05EA\u05E7 \u05E9\u05D5\u05E8\u05D4 \u05D6\u05D5",form:{lightFilter:{more:"\u05D9\u05D5\u05EA\u05E8",clear:"\u05E0\u05E7\u05D4",confirm:"\u05D0\u05D9\u05E9\u05D5\u05E8",itemUnit:"\u05E4\u05E8\u05D9\u05D8\u05D9\u05DD"}},tableForm:{search:"\u05D7\u05D9\u05E4\u05D5\u05E9",reset:"\u05D0\u05D9\u05E4\u05D5\u05E1",submit:"\u05E9\u05DC\u05D7",collapsed:"\u05D4\u05E8\u05D7\u05D1",expand:"\u05DB\u05D5\u05D5\u05E5",inputPlaceholder:"\u05D0\u05E0\u05D0 \u05D4\u05DB\u05E0\u05E1",selectPlaceholder:"\u05D0\u05E0\u05D0 \u05D1\u05D7\u05E8"},alert:{clear:"\u05E0\u05E7\u05D4",selected:"\u05E0\u05D1\u05D7\u05E8",item:"\u05E4\u05E8\u05D9\u05D8"},pagination:{total:{range:" ",total:"\u05DE\u05EA\u05D5\u05DA",item:"\u05E4\u05E8\u05D9\u05D8\u05D9\u05DD"}},tableToolBar:{leftPin:"\u05D4\u05E6\u05DE\u05D3 \u05DC\u05E9\u05DE\u05D0\u05DC",rightPin:"\u05D4\u05E6\u05DE\u05D3 \u05DC\u05D9\u05DE\u05D9\u05DF",noPin:"\u05DC\u05D0 \u05DE\u05E6\u05D5\u05E8\u05E3",leftFixedTitle:"\u05DE\u05D5\u05E6\u05DE\u05D3 \u05DC\u05E9\u05DE\u05D0\u05DC",rightFixedTitle:"\u05DE\u05D5\u05E6\u05DE\u05D3 \u05DC\u05D9\u05DE\u05D9\u05DF",noFixedTitle:"\u05DC\u05D0 \u05DE\u05D5\u05E6\u05DE\u05D3",reset:"\u05D0\u05D9\u05E4\u05D5\u05E1",columnDisplay:"\u05EA\u05E6\u05D5\u05D2\u05EA \u05E2\u05DE\u05D5\u05D3\u05D5\u05EA",columnSetting:"\u05D4\u05D2\u05D3\u05E8\u05D5\u05EA",fullScreen:"\u05DE\u05E1\u05DA \u05DE\u05DC\u05D0",exitFullScreen:"\u05E6\u05D0 \u05DE\u05DE\u05E1\u05DA \u05DE\u05DC\u05D0",reload:"\u05E8\u05E2\u05E0\u05DF",density:"\u05E8\u05D6\u05D5\u05DC\u05D5\u05E6\u05D9\u05D4",densityDefault:"\u05D1\u05E8\u05D9\u05E8\u05EA \u05DE\u05D7\u05D3\u05DC",densityLarger:"\u05D2\u05D3\u05D5\u05DC",densityMiddle:"\u05D1\u05D9\u05E0\u05D5\u05E0\u05D9",densitySmall:"\u05E7\u05D8\u05DF"},stepsForm:{next:"\u05D4\u05D1\u05D0",prev:"\u05E7\u05D5\u05D3\u05DD",submit:"\u05E1\u05D9\u05D5\u05DD"},loginForm:{submitText:"\u05DB\u05E0\u05D9\u05E1\u05D4"},editableTable:{onlyOneLineEditor:"\u05E0\u05D9\u05EA\u05DF \u05DC\u05E2\u05E8\u05D5\u05DA \u05E8\u05E7 \u05E9\u05D5\u05E8\u05D4 \u05D0\u05D7\u05EA",action:{save:"\u05E9\u05DE\u05D5\u05E8",cancel:"\u05D1\u05D9\u05D8\u05D5\u05DC",delete:"\u05DE\u05D7\u05D9\u05E7\u05D4",add:"\u05D4\u05D5\u05E1\u05E3 \u05E9\u05D5\u05E8\u05EA \u05E0\u05EA\u05D5\u05E0\u05D9\u05DD"}},switch:{open:"\u05E4\u05EA\u05D7",close:"\u05E1\u05D2\u05D5\u05E8"}}),"uk-UA":i("uk_UA",{moneySymbol:"\u20B4",deleteThisLine:"\u0412\u0438\u0434\u0430\u0442\u0438\u043B\u0438 \u0440\u044F\u0434\u043E\u043A",copyThisLine:"\u0421\u043A\u043E\u043F\u0456\u044E\u0432\u0430\u0442\u0438 \u0440\u044F\u0434\u043E\u043A",form:{lightFilter:{more:"\u0429\u0435",clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438",confirm:"\u041E\u043A",itemUnit:"\u041F\u043E\u0437\u0438\u0446\u0456\u0457"}},tableForm:{search:"\u041F\u043E\u0448\u0443\u043A",reset:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438",submit:"\u0412\u0456\u0434\u043F\u0440\u0430\u0432\u0438\u0442\u0438",collapsed:"\u0420\u043E\u0437\u0433\u043E\u0440\u043D\u0443\u0442\u0438",expand:"\u0417\u0433\u043E\u0440\u043D\u0443\u0442\u0438",inputPlaceholder:"\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u043D\u044F",selectPlaceholder:"\u041E\u0431\u0435\u0440\u0456\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u043D\u044F"},alert:{clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438",selected:"\u041E\u0431\u0440\u0430\u043D\u043E",item:"\u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0456\u0432"},pagination:{total:{range:" ",total:"\u0437",item:"\u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0456\u0432"}},tableToolBar:{leftPin:"\u0417\u0430\u043A\u0440\u0456\u043F\u0438\u0442\u0438 \u0437\u043B\u0456\u0432\u0430",rightPin:"\u0417\u0430\u043A\u0440\u0456\u043F\u0438\u0442\u0438 \u0441\u043F\u0440\u0430\u0432\u0430",noPin:"\u0412\u0456\u0434\u043A\u0440\u0456\u043F\u0438\u0442\u0438",leftFixedTitle:"\u0417\u0430\u043A\u0440\u0456\u043F\u043B\u0435\u043D\u043E \u0437\u043B\u0456\u0432\u0430",rightFixedTitle:"\u0417\u0430\u043A\u0440\u0456\u043F\u043B\u0435\u043D\u043E \u0441\u043F\u0440\u0430\u0432\u0430",noFixedTitle:"\u041D\u0435 \u0437\u0430\u043A\u0440\u0456\u043F\u043B\u0435\u043D\u043E",reset:"\u0421\u043A\u0438\u043D\u0443\u0442\u0438",columnDisplay:"\u0412\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0441\u0442\u043E\u0432\u043F\u0446\u0456\u0432",columnSetting:"\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F",fullScreen:"\u041F\u043E\u0432\u043D\u043E\u0435\u043A\u0440\u0430\u043D\u043D\u0438\u0439 \u0440\u0435\u0436\u0438\u043C",exitFullScreen:"\u0412\u0438\u0439\u0442\u0438 \u0437 \u043F\u043E\u0432\u043D\u043E\u0435\u043A\u0440\u0430\u043D\u043D\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0443",reload:"\u041E\u043D\u043E\u0432\u0438\u0442\u0438",density:"\u0420\u043E\u0437\u043C\u0456\u0440",densityDefault:"\u0417\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C",densityLarger:"\u0412\u0435\u043B\u0438\u043A\u0438\u0439",densityMiddle:"\u0421\u0435\u0440\u0435\u0434\u043D\u0456\u0439",densitySmall:"\u0421\u0442\u0438\u0441\u043B\u0438\u0439"},stepsForm:{next:"\u041D\u0430\u0441\u0442\u0443\u043F\u043D\u0438\u0439",prev:"\u041F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u0456\u0439",submit:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438"},loginForm:{submitText:"\u0412\u0445\u0456\u0445"},editableTable:{onlyOneLineEditor:"\u0422\u0456\u043B\u044C\u043A\u0438 \u043E\u0434\u0438\u043D \u0440\u044F\u0434\u043E\u043A \u043C\u043E\u0436\u0435 \u0431\u0443\u0442\u0438 \u0440\u0435\u0434\u0430\u0433\u043E\u0432\u0430\u043D\u0438\u0439 \u043E\u0434\u043D\u043E\u0447\u0430\u0441\u043D\u043E",action:{save:"\u0417\u0431\u0435\u0440\u0435\u0433\u0442\u0438",cancel:"\u0412\u0456\u0434\u043C\u0456\u043D\u0438\u0442\u0438",delete:"\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438",add:"\u0434\u043E\u0434\u0430\u0442\u0438 \u0440\u044F\u0434\u043E\u043A"}},switch:{open:"\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u043E",close:"\u0417\u0430\u043A\u0440\u0438\u0442\u043E"}}),"uz-UZ":i("uz_UZ",{moneySymbol:"UZS",form:{lightFilter:{more:"Yana",clear:"Tozalash",confirm:"OK",itemUnit:"Pozitsiyalar"}},tableForm:{search:"Qidirish",reset:"Qayta tiklash",submit:"Yuborish",collapsed:"Yig\u2018ish",expand:"Kengaytirish",inputPlaceholder:"Qiymatni kiriting",selectPlaceholder:"Qiymatni tanlang"},alert:{clear:"Tozalash",selected:"Tanlangan",item:"elementlar"},pagination:{total:{range:" ",total:"dan",item:"elementlar"}},tableToolBar:{leftPin:"Chapga mahkamlash",rightPin:"O\u2018ngga mahkamlash",noPin:"Mahkamlashni olib tashlash",leftFixedTitle:"Chapga mahkamlangan",rightFixedTitle:"O\u2018ngga mahkamlangan",noFixedTitle:"Mahkamlashsiz",reset:"Qayta tiklash",columnDisplay:"Ustunni ko\u2018rsatish",columnSetting:"Sozlamalar",fullScreen:"To\u2018liq ekran",exitFullScreen:"To\u2018liq ekrandan chiqish",reload:"Yangilash",density:"O\u2018lcham",densityDefault:"Standart",densityLarger:"Katta",densityMiddle:"O\u2018rtacha",densitySmall:"Kichik"},stepsForm:{next:"Keyingi",prev:"Oldingi",submit:"Tugatish"},loginForm:{submitText:"Kirish"},editableTable:{action:{save:"Saqlash",cancel:"Bekor qilish",delete:"O\u2018chirish",add:"ma\u02BClumotlar qatorini qo\u2018shish"}},switch:{open:"Ochish",close:"Yopish"}}),"nl-NL":i("nl_NL",{moneySymbol:"\u20AC",deleteThisLine:"Verwijder deze regel",copyThisLine:"Kopieer deze regel",form:{lightFilter:{more:"Meer filters",clear:"Wissen",confirm:"Bevestigen",itemUnit:"item"}},tableForm:{search:"Zoeken",reset:"Resetten",submit:"Indienen",collapsed:"Uitvouwen",expand:"Inklappen",inputPlaceholder:"Voer in",selectPlaceholder:"Selecteer"},alert:{clear:"Selectie annuleren",selected:"Geselecteerd",item:"item"},pagination:{total:{range:"Van",total:"items/totaal",item:"items"}},tableToolBar:{leftPin:"Vastzetten aan begin",rightPin:"Vastzetten aan einde",noPin:"Niet vastzetten",leftFixedTitle:"Vastzetten aan de linkerkant",rightFixedTitle:"Vastzetten aan de rechterkant",noFixedTitle:"Niet vastzetten",reset:"Resetten",columnDisplay:"Kolomweergave",columnSetting:"Kolominstellingen",fullScreen:"Volledig scherm",exitFullScreen:"Verlaat volledig scherm",reload:"Vernieuwen",density:"Dichtheid",densityDefault:"Normaal",densityLarger:"Ruim",densityMiddle:"Gemiddeld",densitySmall:"Compact"},stepsForm:{next:"Volgende stap",prev:"Vorige stap",submit:"Indienen"},loginForm:{submitText:"Inloggen"},editableTable:{onlyOneLineEditor:"Slechts \xe9\xe9n regel tegelijk bewerken",action:{save:"Opslaan",cancel:"Annuleren",delete:"Verwijderen",add:"Een regel toevoegen"}},switch:{open:"Openen",close:"Sluiten"}}),"ro-RO":i("ro_RO",{moneySymbol:"RON",deleteThisLine:"\u0218terge acest r\xe2nd",copyThisLine:"Copiaz\u0103 acest r\xe2nd",form:{lightFilter:{more:"Mai multe filtre",clear:"Cur\u0103\u021B\u0103",confirm:"Confirm\u0103",itemUnit:"elemente"}},tableForm:{search:"Caut\u0103",reset:"Reseteaz\u0103",submit:"Trimite",collapsed:"Extinde",expand:"Restr\xe2nge",inputPlaceholder:"Introduce\u021Bi",selectPlaceholder:"Selecta\u021Bi"},alert:{clear:"Anuleaz\u0103 selec\u021Bia",selected:"Selectat",item:"elemente"},pagination:{total:{range:"De la",total:"elemente/total",item:"elemente"}},tableToolBar:{leftPin:"Fixeaz\u0103 la \xeenceput",rightPin:"Fixeaz\u0103 la sf\xe2r\u0219it",noPin:"Nu fixa",leftFixedTitle:"Fixeaz\u0103 \xeen st\xe2nga",rightFixedTitle:"Fixeaz\u0103 \xeen dreapta",noFixedTitle:"Nu fixa",reset:"Reseteaz\u0103",columnDisplay:"Afi\u0219are coloane",columnSetting:"Set\u0103ri coloane",fullScreen:"Ecran complet",exitFullScreen:"Ie\u0219i din ecran complet",reload:"Re\xeencarc\u0103",density:"Densitate",densityDefault:"Normal",densityLarger:"Larg",densityMiddle:"Mediu",densitySmall:"Compact"},stepsForm:{next:"Pasul urm\u0103tor",prev:"Pasul anterior",submit:"Trimite"},loginForm:{submitText:"Autentificare"},editableTable:{onlyOneLineEditor:"Se poate edita doar un r\xe2nd simultan",action:{save:"Salveaz\u0103",cancel:"Anuleaz\u0103",delete:"\u0218terge",add:"Adaug\u0103 un r\xe2nd"}},switch:{open:"Deschide",close:"\xcenchide"}}),"sv-SE":i("sv_SE",{moneySymbol:"SEK",deleteThisLine:"Radera denna rad",copyThisLine:"Kopiera denna rad",form:{lightFilter:{more:"Fler filter",clear:"Rensa",confirm:"Bekr\xe4fta",itemUnit:"objekt"}},tableForm:{search:"S\xf6k",reset:"\xc5terst\xe4ll",submit:"Skicka",collapsed:"Expandera",expand:"F\xe4ll ihop",inputPlaceholder:"V\xe4nligen ange",selectPlaceholder:"V\xe4nligen v\xe4lj"},alert:{clear:"Avbryt val",selected:"Vald",item:"objekt"},pagination:{total:{range:"Fr\xe5n",total:"objekt/totalt",item:"objekt"}},tableToolBar:{leftPin:"F\xe4st till v\xe4nster",rightPin:"F\xe4st till h\xf6ger",noPin:"Inte f\xe4st",leftFixedTitle:"F\xe4st till v\xe4nster",rightFixedTitle:"F\xe4st till h\xf6ger",noFixedTitle:"Inte f\xe4st",reset:"\xc5terst\xe4ll",columnDisplay:"Kolumnvisning",columnSetting:"Kolumninst\xe4llningar",fullScreen:"Fullsk\xe4rm",exitFullScreen:"Avsluta fullsk\xe4rm",reload:"Ladda om",density:"T\xe4thet",densityDefault:"Normal",densityLarger:"L\xf6s",densityMiddle:"Medium",densitySmall:"Kompakt"},stepsForm:{next:"N\xe4sta steg",prev:"F\xf6reg\xe5ende steg",submit:"Skicka"},loginForm:{submitText:"Logga in"},editableTable:{onlyOneLineEditor:"Endast en rad kan redigeras \xe5t g\xe5ngen",action:{save:"Spara",cancel:"Avbryt",delete:"Radera",add:"L\xe4gg till en rad"}},switch:{open:"\xd6ppna",close:"St\xe4ng"}})},c=Object.keys(s),u=function(e){var t=(e||"zh-CN").toLocaleLowerCase();return c.find(function(e){return e.toLocaleLowerCase().includes(t)})}},41628:function(e,t,n){"use strict";n.d(t,{Xj:()=>I,dQ:()=>P,Ow:()=>T,Nd:()=>R,Wf:()=>E,uK:()=>F});var r=n(19741),i=n(88122);function o(e,t){"string"==typeof(n=e)&&-1!==n.indexOf(".")&&1===parseFloat(n)&&(e="100%");var n,r,i="string"==typeof(r=e)&&-1!==r.indexOf("%");return(e=360===t?e:Math.min(t,Math.max(0,parseFloat(e))),i&&(e=parseInt(String(e*t),10)/100),1e-6>Math.abs(e-t))?1:e=360===t?(e<0?e%t+t:e%t)/parseFloat(String(t)):e%t/parseFloat(String(t))}function a(e){return Math.min(1,Math.max(0,e))}function l(e){return(isNaN(e=parseFloat(e))||e<0||e>1)&&(e=1),e}function s(e){return e<=1?"".concat(100*Number(e),"%"):e}function c(e){return 1===e.length?"0"+e:String(e)}function u(e,t,n){e=o(e,255);var r=Math.max(e,t=o(t,255),n=o(n,255)),i=Math.min(e,t,n),a=0,l=0,s=(r+i)/2;if(r===i)l=0,a=0;else{var c=r-i;switch(l=s>.5?c/(2-r-i):c/(r+i),r){case e:a=(t-n)/c+6*(t<n);break;case t:a=(n-e)/c+2;break;case n:a=(e-t)/c+4}a/=6}return{h:a,s:l,l:s}}function d(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+6*n*(t-e):n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function f(e,t,n){e=o(e,255);var r=Math.max(e,t=o(t,255),n=o(n,255)),i=Math.min(e,t,n),a=0,l=r-i;if(r===i)a=0;else{switch(r){case e:a=(t-n)/l+6*(t<n);break;case t:a=(n-e)/l+2;break;case n:a=(e-t)/l+4}a/=6}return{h:a,s:0===r?0:l/r,v:r}}function p(e,t,n,r){var i=[c(Math.round(e).toString(16)),c(Math.round(t).toString(16)),c(Math.round(n).toString(16))];return r&&i[0].startsWith(i[0].charAt(1))&&i[1].startsWith(i[1].charAt(1))&&i[2].startsWith(i[2].charAt(1))?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function h(e){return parseInt(e,16)}var m={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},g="(?:".concat("[-\\+]?\\d*\\.\\d+%?",")|(?:").concat("[-\\+]?\\d+%?",")"),v="[\\s|\\(]+(".concat(g,")[,|\\s]+(").concat(g,")[,|\\s]+(").concat(g,")\\s*\\)?"),y="[\\s|\\(]+(".concat(g,")[,|\\s]+(").concat(g,")[,|\\s]+(").concat(g,")[,|\\s]+(").concat(g,")\\s*\\)?"),b={CSS_UNIT:new RegExp(g),rgb:RegExp("rgb"+v),rgba:RegExp("rgba"+y),hsl:RegExp("hsl"+v),hsla:RegExp("hsla"+y),hsv:RegExp("hsv"+v),hsva:RegExp("hsva"+y),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function x(e){return!!b.CSS_UNIT.exec(String(e))}var Z=function(){function e(t,n){if(void 0===t&&(t=""),void 0===n&&(n={}),t instanceof e)return t;"number"==typeof t&&(t={r:(R=t)>>16,g:(65280&R)>>8,b:255&R}),this.originalInput=t;var r,i,a,c,u,f,p,g,v,y,Z,C,S,w,k,M,F,T,P,E,R,I,j=(w={r:0,g:0,b:0},k=1,M=null,F=null,T=null,P=!1,E=!1,"string"==typeof(r=t)&&(r=function(e){if(0===(e=e.trim().toLowerCase()).length)return!1;var t=!1;if(m[e])e=m[e],t=!0;else if("transparent"===e)return{r:0,g:0,b:0,a:0,format:"name"};var n=b.rgb.exec(e);return n?{r:n[1],g:n[2],b:n[3]}:(n=b.rgba.exec(e))?{r:n[1],g:n[2],b:n[3],a:n[4]}:(n=b.hsl.exec(e))?{h:n[1],s:n[2],l:n[3]}:(n=b.hsla.exec(e))?{h:n[1],s:n[2],l:n[3],a:n[4]}:(n=b.hsv.exec(e))?{h:n[1],s:n[2],v:n[3]}:(n=b.hsva.exec(e))?{h:n[1],s:n[2],v:n[3],a:n[4]}:(n=b.hex8.exec(e))?{r:h(n[1]),g:h(n[2]),b:h(n[3]),a:h(n[4])/255,format:t?"name":"hex8"}:(n=b.hex6.exec(e))?{r:h(n[1]),g:h(n[2]),b:h(n[3]),format:t?"name":"hex"}:(n=b.hex4.exec(e))?{r:h(n[1]+n[1]),g:h(n[2]+n[2]),b:h(n[3]+n[3]),a:h(n[4]+n[4])/255,format:t?"name":"hex8"}:!!(n=b.hex3.exec(e))&&{r:h(n[1]+n[1]),g:h(n[2]+n[2]),b:h(n[3]+n[3]),format:t?"name":"hex"}}(r)),"object"==typeof r&&(x(r.r)&&x(r.g)&&x(r.b)?(i=r.r,a=r.g,c=r.b,w={r:255*o(i,255),g:255*o(a,255),b:255*o(c,255)},P=!0,E="%"===String(r.r).substr(-1)?"prgb":"rgb"):x(r.h)&&x(r.s)&&x(r.v)?(M=s(r.s),F=s(r.v),u=r.h,f=M,p=F,u=6*o(u,360),f=o(f,100),p=o(p,100),g=Math.floor(u),v=u-g,y=p*(1-f),Z=p*(1-v*f),C=p*(1-(1-v)*f),w={r:255*[p,Z,y,y,C,p][S=g%6],g:255*[C,p,p,Z,y,y][S],b:255*[y,y,C,p,p,Z][S]},P=!0,E="hsv"):x(r.h)&&x(r.s)&&x(r.l)&&(M=s(r.s),T=s(r.l),w=function(e,t,n){if(e=o(e,360),t=o(t,100),n=o(n,100),0===t)i=n,a=n,r=n;else{var r,i,a,l=n<.5?n*(1+t):n+t-n*t,s=2*n-l;r=d(s,l,e+1/3),i=d(s,l,e),a=d(s,l,e-1/3)}return{r:255*r,g:255*i,b:255*a}}(r.h,M,T),P=!0,E="hsl"),Object.prototype.hasOwnProperty.call(r,"a")&&(k=r.a)),k=l(k),{ok:P,format:r.format||E,r:Math.min(255,Math.max(w.r,0)),g:Math.min(255,Math.max(w.g,0)),b:Math.min(255,Math.max(w.b,0)),a:k});this.originalInput=t,this.r=j.r,this.g=j.g,this.b=j.b,this.a=j.a,this.roundA=Math.round(100*this.a)/100,this.format=null!=(I=n.format)?I:j.format,this.gradientType=n.gradientType,this.r<1&&(this.r=Math.round(this.r)),this.g<1&&(this.g=Math.round(this.g)),this.b<1&&(this.b=Math.round(this.b)),this.isValid=j.ok}return e.prototype.isDark=function(){return 128>this.getBrightness()},e.prototype.isLight=function(){return!this.isDark()},e.prototype.getBrightness=function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},e.prototype.getLuminance=function(){var e,t,n,r=this.toRgb(),i=r.r/255,o=r.g/255,a=r.b/255;return .2126*(i<=.03928?i/12.92:Math.pow((i+.055)/1.055,2.4))+.7152*(o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4))+.0722*(a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4))},e.prototype.getAlpha=function(){return this.a},e.prototype.setAlpha=function(e){return this.a=l(e),this.roundA=Math.round(100*this.a)/100,this},e.prototype.isMonochrome=function(){return 0===this.toHsl().s},e.prototype.toHsv=function(){var e=f(this.r,this.g,this.b);return{h:360*e.h,s:e.s,v:e.v,a:this.a}},e.prototype.toHsvString=function(){var e=f(this.r,this.g,this.b),t=Math.round(360*e.h),n=Math.round(100*e.s),r=Math.round(100*e.v);return 1===this.a?"hsv(".concat(t,", ").concat(n,"%, ").concat(r,"%)"):"hsva(".concat(t,", ").concat(n,"%, ").concat(r,"%, ").concat(this.roundA,")")},e.prototype.toHsl=function(){var e=u(this.r,this.g,this.b);return{h:360*e.h,s:e.s,l:e.l,a:this.a}},e.prototype.toHslString=function(){var e=u(this.r,this.g,this.b),t=Math.round(360*e.h),n=Math.round(100*e.s),r=Math.round(100*e.l);return 1===this.a?"hsl(".concat(t,", ").concat(n,"%, ").concat(r,"%)"):"hsla(".concat(t,", ").concat(n,"%, ").concat(r,"%, ").concat(this.roundA,")")},e.prototype.toHex=function(e){return void 0===e&&(e=!1),p(this.r,this.g,this.b,e)},e.prototype.toHexString=function(e){return void 0===e&&(e=!1),"#"+this.toHex(e)},e.prototype.toHex8=function(e){var t,n,r,i,o,a;return void 0===e&&(e=!1),t=this.r,n=this.g,r=this.b,i=this.a,o=e,a=[c(Math.round(t).toString(16)),c(Math.round(n).toString(16)),c(Math.round(r).toString(16)),c(Math.round(255*parseFloat(i)).toString(16))],o&&a[0].startsWith(a[0].charAt(1))&&a[1].startsWith(a[1].charAt(1))&&a[2].startsWith(a[2].charAt(1))&&a[3].startsWith(a[3].charAt(1))?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0)+a[3].charAt(0):a.join("")},e.prototype.toHex8String=function(e){return void 0===e&&(e=!1),"#"+this.toHex8(e)},e.prototype.toHexShortString=function(e){return void 0===e&&(e=!1),1===this.a?this.toHexString(e):this.toHex8String(e)},e.prototype.toRgb=function(){return{r:Math.round(this.r),g:Math.round(this.g),b:Math.round(this.b),a:this.a}},e.prototype.toRgbString=function(){var e=Math.round(this.r),t=Math.round(this.g),n=Math.round(this.b);return 1===this.a?"rgb(".concat(e,", ").concat(t,", ").concat(n,")"):"rgba(".concat(e,", ").concat(t,", ").concat(n,", ").concat(this.roundA,")")},e.prototype.toPercentageRgb=function(){var e=function(e){return"".concat(Math.round(100*o(e,255)),"%")};return{r:e(this.r),g:e(this.g),b:e(this.b),a:this.a}},e.prototype.toPercentageRgbString=function(){var e=function(e){return Math.round(100*o(e,255))};return 1===this.a?"rgb(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%)"):"rgba(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%, ").concat(this.roundA,")")},e.prototype.toName=function(){if(0===this.a)return"transparent";if(this.a<1)return!1;for(var e="#"+p(this.r,this.g,this.b,!1),t=0,n=Object.entries(m);t<n.length;t++){var r=n[t],i=r[0];if(e===r[1])return i}return!1},e.prototype.toString=function(e){var t=!!e;e=null!=e?e:this.format;var n=!1,r=this.a<1&&this.a>=0;return!t&&r&&(e.startsWith("hex")||"name"===e)?"name"===e&&0===this.a?this.toName():this.toRgbString():("rgb"===e&&(n=this.toRgbString()),"prgb"===e&&(n=this.toPercentageRgbString()),("hex"===e||"hex6"===e)&&(n=this.toHexString()),"hex3"===e&&(n=this.toHexString(!0)),"hex4"===e&&(n=this.toHex8String(!0)),"hex8"===e&&(n=this.toHex8String()),"name"===e&&(n=this.toName()),"hsl"===e&&(n=this.toHslString()),"hsv"===e&&(n=this.toHsvString()),n||this.toHexString())},e.prototype.toNumber=function(){return(Math.round(this.r)<<16)+(Math.round(this.g)<<8)+Math.round(this.b)},e.prototype.clone=function(){return new e(this.toString())},e.prototype.lighten=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.l+=t/100,n.l=a(n.l),new e(n)},e.prototype.brighten=function(t){void 0===t&&(t=10);var n=this.toRgb();return n.r=Math.max(0,Math.min(255,n.r-Math.round(-(t/100*255)))),n.g=Math.max(0,Math.min(255,n.g-Math.round(-(t/100*255)))),n.b=Math.max(0,Math.min(255,n.b-Math.round(-(t/100*255)))),new e(n)},e.prototype.darken=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.l-=t/100,n.l=a(n.l),new e(n)},e.prototype.tint=function(e){return void 0===e&&(e=10),this.mix("white",e)},e.prototype.shade=function(e){return void 0===e&&(e=10),this.mix("black",e)},e.prototype.desaturate=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.s-=t/100,n.s=a(n.s),new e(n)},e.prototype.saturate=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.s+=t/100,n.s=a(n.s),new e(n)},e.prototype.greyscale=function(){return this.desaturate(100)},e.prototype.spin=function(t){var n=this.toHsl(),r=(n.h+t)%360;return n.h=r<0?360+r:r,new e(n)},e.prototype.mix=function(t,n){void 0===n&&(n=50);var r=this.toRgb(),i=new e(t).toRgb(),o=n/100;return new e({r:(i.r-r.r)*o+r.r,g:(i.g-r.g)*o+r.g,b:(i.b-r.b)*o+r.b,a:(i.a-r.a)*o+r.a})},e.prototype.analogous=function(t,n){void 0===t&&(t=6),void 0===n&&(n=30);var r=this.toHsl(),i=360/n,o=[this];for(r.h=(r.h-(i*t>>1)+720)%360;--t;)r.h=(r.h+i)%360,o.push(new e(r));return o},e.prototype.complement=function(){var t=this.toHsl();return t.h=(t.h+180)%360,new e(t)},e.prototype.monochromatic=function(t){void 0===t&&(t=6);for(var n=this.toHsv(),r=n.h,i=n.s,o=n.v,a=[],l=1/t;t--;)a.push(new e({h:r,s:i,v:o})),o=(o+l)%1;return a},e.prototype.splitcomplement=function(){var t=this.toHsl(),n=t.h;return[this,new e({h:(n+72)%360,s:t.s,l:t.l}),new e({h:(n+216)%360,s:t.s,l:t.l})]},e.prototype.onBackground=function(t){var n=this.toRgb(),r=new e(t).toRgb(),i=n.a+r.a*(1-n.a);return new e({r:(n.r*n.a+r.r*r.a*(1-n.a))/i,g:(n.g*n.a+r.g*r.a*(1-n.a))/i,b:(n.b*n.a+r.b*r.a*(1-n.a))/i,a:i})},e.prototype.triad=function(){return this.polyad(3)},e.prototype.tetrad=function(){return this.polyad(4)},e.prototype.polyad=function(t){for(var n=this.toHsl(),r=n.h,i=[this],o=360/t,a=1;a<t;a++)i.push(new e({h:(r+a*o)%360,s:n.s,l:n.l}));return i},e.prototype.equals=function(t){return this.toRgbString()===new e(t).toRgbString()},e}(),C=n(35236),S=n(35376),w=n(52983),k=n(28800),M=n(35460),F=function(e,t){return new Z(e).setAlpha(t).toRgbString()},T=void 0!==C.Z&&C.Z?C.Z:M,P=T.useToken,E=function(e){return{boxSizing:"border-box",margin:0,padding:0,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,listStyle:"none"}},R=function(e){return{color:e.colorLink,outline:"none",cursor:"pointer",transition:"color ".concat(e.motionDurationSlow),"&:focus, &:hover":{color:e.colorLinkHover},"&:active":{color:e.colorLinkActive}}};function I(e,t){var n,o=(0,w.useContext)(k.L_).token,a=void 0===o?{}:o,l=(0,w.useContext)(k.L_).hashed,s=P(),c=s.token,u=s.hashId,d=(0,w.useContext)(k.L_).theme,f=(0,w.useContext)(S.ZP.ConfigContext),p=f.getPrefixCls,h=f.csp;return a.layout||(a=(0,r.Z)({},c)),a.proComponentsCls=null!=(n=a.proComponentsCls)?n:".".concat(p("pro")),a.antCls=".".concat(p()),{wrapSSR:(0,i.xy)({theme:d,token:a,path:[e],nonce:null==h?void 0:h.nonce,layer:{name:"antd-pro",dependencies:["antd"]}},function(){return t(a)}),hashId:l?u:""}}},35460:function(e,t,n){"use strict";n.r(t),n.d(t,{defaultToken:()=>l,emptyTheme:()=>c,hashCode:()=>s,token:()=>u,useToken:()=>d});var r,i=n(19741),o=n(88122),a=n(35236),l={blue:"#1677ff",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#eb2f96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911",colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff7875",colorInfo:"#1677ff",colorTextBase:"#000",colorBgBase:"#fff",fontFamily:"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInQuint:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:4,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,"blue-1":"#e6f4ff","blue-2":"#bae0ff","blue-3":"#91caff","blue-4":"#69b1ff","blue-5":"#4096ff","blue-6":"#1677ff","blue-7":"#0958d9","blue-8":"#003eb3","blue-9":"#002c8c","blue-10":"#001d66","purple-1":"#f9f0ff","purple-2":"#efdbff","purple-3":"#d3adf7","purple-4":"#b37feb","purple-5":"#9254de","purple-6":"#722ed1","purple-7":"#531dab","purple-8":"#391085","purple-9":"#22075e","purple-10":"#120338","cyan-1":"#e6fffb","cyan-2":"#b5f5ec","cyan-3":"#87e8de","cyan-4":"#5cdbd3","cyan-5":"#36cfc9","cyan-6":"#13c2c2","cyan-7":"#08979c","cyan-8":"#006d75","cyan-9":"#00474f","cyan-10":"#002329","green-1":"#f6ffed","green-2":"#d9f7be","green-3":"#b7eb8f","green-4":"#95de64","green-5":"#73d13d","green-6":"#52c41a","green-7":"#389e0d","green-8":"#237804","green-9":"#135200","green-10":"#092b00","magenta-1":"#fff0f6","magenta-2":"#ffd6e7","magenta-3":"#ffadd2","magenta-4":"#ff85c0","magenta-5":"#f759ab","magenta-6":"#eb2f96","magenta-7":"#c41d7f","magenta-8":"#9e1068","magenta-9":"#780650","magenta-10":"#520339","pink-1":"#fff0f6","pink-2":"#ffd6e7","pink-3":"#ffadd2","pink-4":"#ff85c0","pink-5":"#f759ab","pink-6":"#eb2f96","pink-7":"#c41d7f","pink-8":"#9e1068","pink-9":"#780650","pink-10":"#520339","red-1":"#fff1f0","red-2":"#ffccc7","red-3":"#ffa39e","red-4":"#ff7875","red-5":"#ff4d4f","red-6":"#f5222d","red-7":"#cf1322","red-8":"#a8071a","red-9":"#820014","red-10":"#5c0011","orange-1":"#fff7e6","orange-2":"#ffe7ba","orange-3":"#ffd591","orange-4":"#ffc069","orange-5":"#ffa940","orange-6":"#fa8c16","orange-7":"#d46b08","orange-8":"#ad4e00","orange-9":"#873800","orange-10":"#612500","yellow-1":"#feffe6","yellow-2":"#ffffb8","yellow-3":"#fffb8f","yellow-4":"#fff566","yellow-5":"#ffec3d","yellow-6":"#fadb14","yellow-7":"#d4b106","yellow-8":"#ad8b00","yellow-9":"#876800","yellow-10":"#614700","volcano-1":"#fff2e8","volcano-2":"#ffd8bf","volcano-3":"#ffbb96","volcano-4":"#ff9c6e","volcano-5":"#ff7a45","volcano-6":"#fa541c","volcano-7":"#d4380d","volcano-8":"#ad2102","volcano-9":"#871400","volcano-10":"#610b00","geekblue-1":"#f0f5ff","geekblue-2":"#d6e4ff","geekblue-3":"#adc6ff","geekblue-4":"#85a5ff","geekblue-5":"#597ef7","geekblue-6":"#2f54eb","geekblue-7":"#1d39c4","geekblue-8":"#10239e","geekblue-9":"#061178","geekblue-10":"#030852","gold-1":"#fffbe6","gold-2":"#fff1b8","gold-3":"#ffe58f","gold-4":"#ffd666","gold-5":"#ffc53d","gold-6":"#faad14","gold-7":"#d48806","gold-8":"#ad6800","gold-9":"#874d00","gold-10":"#613400","lime-1":"#fcffe6","lime-2":"#f4ffb8","lime-3":"#eaff8f","lime-4":"#d3f261","lime-5":"#bae637","lime-6":"#a0d911","lime-7":"#7cb305","lime-8":"#5b8c00","lime-9":"#3f6600","lime-10":"#254000",colorText:"rgba(0, 0, 0, 0.88)",colorTextSecondary:"rgba(0, 0, 0, 0.65)",colorTextTertiary:"rgba(0, 0, 0, 0.45)",colorTextQuaternary:"rgba(0, 0, 0, 0.25)",colorFill:"rgba(0, 0, 0, 0.15)",colorFillSecondary:"rgba(0, 0, 0, 0.06)",colorFillTertiary:"rgba(0, 0, 0, 0.04)",colorFillQuaternary:"rgba(0, 0, 0, 0.02)",colorBgLayout:"hsl(220,23%,97%)",colorBgContainer:"#ffffff",colorBgElevated:"#ffffff",colorBgSpotlight:"rgba(0, 0, 0, 0.85)",colorBorder:"#d9d9d9",colorBorderSecondary:"#f0f0f0",colorPrimaryBg:"#e6f4ff",colorPrimaryBgHover:"#bae0ff",colorPrimaryBorder:"#91caff",colorPrimaryBorderHover:"#69b1ff",colorPrimaryHover:"#4096ff",colorPrimaryActive:"#0958d9",colorPrimaryTextHover:"#4096ff",colorPrimaryText:"#1677ff",colorPrimaryTextActive:"#0958d9",colorSuccessBg:"#f6ffed",colorSuccessBgHover:"#d9f7be",colorSuccessBorder:"#b7eb8f",colorSuccessBorderHover:"#95de64",colorSuccessHover:"#95de64",colorSuccessActive:"#389e0d",colorSuccessTextHover:"#73d13d",colorSuccessText:"#52c41a",colorSuccessTextActive:"#389e0d",colorErrorBg:"#fff2f0",colorErrorBgHover:"#fff1f0",colorErrorBorder:"#ffccc7",colorErrorBorderHover:"#ffa39e",colorErrorHover:"#ffa39e",colorErrorActive:"#d9363e",colorErrorTextHover:"#ff7875",colorErrorText:"#ff4d4f",colorErrorTextActive:"#d9363e",colorWarningBg:"#fffbe6",colorWarningBgHover:"#fff1b8",colorWarningBorder:"#ffe58f",colorWarningBorderHover:"#ffd666",colorWarningHover:"#ffd666",colorWarningActive:"#d48806",colorWarningTextHover:"#ffc53d",colorWarningText:"#faad14",colorWarningTextActive:"#d48806",colorInfoBg:"#e6f4ff",colorInfoBgHover:"#bae0ff",colorInfoBorder:"#91caff",colorInfoBorderHover:"#69b1ff",colorInfoHover:"#69b1ff",colorInfoActive:"#0958d9",colorInfoTextHover:"#4096ff",colorInfoText:"#1677ff",colorInfoTextActive:"#0958d9",colorBgMask:"rgba(0, 0, 0, 0.45)",colorWhite:"#fff",sizeXXL:48,sizeXL:32,sizeLG:24,sizeMD:20,sizeMS:16,size:16,sizeSM:12,sizeXS:8,sizeXXS:4,controlHeightSM:24,controlHeightXS:16,controlHeightLG:40,motionDurationFast:"0.1s",motionDurationMid:"0.2s",motionDurationSlow:"0.3s",fontSizes:[12,14,16,20,24,30,38,46,56,68],lineHeights:[1.6666666666666667,1.5714285714285714,1.5,1.4,1.3333333333333333,1.2666666666666666,1.2105263157894737,1.173913043478261,1.1428571428571428,1.1176470588235294],lineWidthBold:2,borderRadiusXS:1,borderRadiusSM:4,borderRadiusLG:8,borderRadiusOuter:4,colorLink:"#1677ff",colorLinkHover:"#69b1ff",colorLinkActive:"#0958d9",colorFillContent:"rgba(0, 0, 0, 0.06)",colorFillContentHover:"rgba(0, 0, 0, 0.15)",colorFillAlter:"rgba(0, 0, 0, 0.02)",colorBgContainerDisabled:"rgba(0, 0, 0, 0.04)",colorBorderBg:"#ffffff",colorSplit:"rgba(5, 5, 5, 0.06)",colorTextPlaceholder:"rgba(0, 0, 0, 0.25)",colorTextDisabled:"rgba(0, 0, 0, 0.25)",colorTextHeading:"rgba(0, 0, 0, 0.88)",colorTextLabel:"rgba(0, 0, 0, 0.65)",colorTextDescription:"rgba(0, 0, 0, 0.45)",colorTextLightSolid:"#fff",colorHighlight:"#ff7875",colorBgTextHover:"rgba(0, 0, 0, 0.06)",colorBgTextActive:"rgba(0, 0, 0, 0.15)",colorIcon:"rgba(0, 0, 0, 0.45)",colorIconHover:"rgba(0, 0, 0, 0.88)",colorErrorOutline:"rgba(255, 38, 5, 0.06)",colorWarningOutline:"rgba(255, 215, 5, 0.1)",fontSizeSM:12,fontSizeLG:16,fontSizeXL:20,fontSizeHeading1:38,fontSizeHeading2:30,fontSizeHeading3:24,fontSizeHeading4:20,fontSizeHeading5:16,fontSizeIcon:12,lineHeight:1.5714285714285714,lineHeightLG:1.5,lineHeightSM:1.6666666666666667,lineHeightHeading1:1.2105263157894737,lineHeightHeading2:1.2666666666666666,lineHeightHeading3:1.3333333333333333,lineHeightHeading4:1.4,lineHeightHeading5:1.5,controlOutlineWidth:2,controlInteractiveSize:16,controlItemBgHover:"rgba(0, 0, 0, 0.04)",controlItemBgActive:"#e6f4ff",controlItemBgActiveHover:"#bae0ff",controlItemBgActiveDisabled:"rgba(0, 0, 0, 0.15)",controlTmpOutline:"rgba(0, 0, 0, 0.02)",controlOutline:"rgba(5, 145, 255, 0.1)",fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:4,paddingXS:8,paddingSM:12,padding:16,paddingMD:20,paddingLG:24,paddingXL:32,paddingContentHorizontalLG:24,paddingContentVerticalLG:16,paddingContentHorizontal:16,paddingContentVertical:12,paddingContentHorizontalSM:16,paddingContentVerticalSM:8,marginXXS:4,marginXS:8,marginSM:12,margin:16,marginMD:20,marginLG:24,marginXL:32,marginXXL:48,boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.03),0 1px 6px -1px rgba(0, 0, 0, 0.02),0 2px 4px 0 rgba(0, 0, 0, 0.02)",boxShadowSecondary:"0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",screenXS:480,screenXSMin:480,screenXSMax:479,screenSM:576,screenSMMin:576,screenSMMax:575,screenMD:768,screenMDMin:768,screenMDMax:767,screenLG:992,screenLGMin:992,screenLGMax:991,screenXL:1200,screenXLMin:1200,screenXLMax:1199,screenXXL:1600,screenXXLMin:1600,screenXXLMax:1599,boxShadowPopoverArrow:"3px 3px 7px rgba(0, 0, 0, 0.1)",boxShadowCard:"0 1px 2px -2px rgba(0, 0, 0, 0.16),0 3px 6px 0 rgba(0, 0, 0, 0.12),0 5px 12px 4px rgba(0, 0, 0, 0.09)",boxShadowDrawerRight:"-6px 0 16px 0 rgba(0, 0, 0, 0.08),-3px 0 6px -4px rgba(0, 0, 0, 0.12),-9px 0 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerLeft:"6px 0 16px 0 rgba(0, 0, 0, 0.08),3px 0 6px -4px rgba(0, 0, 0, 0.12),9px 0 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerUp:"0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerDown:"0 -6px 16px 0 rgba(0, 0, 0, 0.08),0 -3px 6px -4px rgba(0, 0, 0, 0.12),0 -9px 28px 8px rgba(0, 0, 0, 0.05)",boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)",_tokenKey:"19w80ff",_hashId:"css-dev-only-do-not-override-i2zu9q"},s=function(e){for(var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=0xdeadbeef^n,i=0x41c6ce57^n,o=0;o<e.length;o++)r=Math.imul(r^(t=e.charCodeAt(o)),0x9e3779b1),i=Math.imul(i^t,0x5f356495);return r=Math.imul(r^r>>>16,0x85ebca6b)^Math.imul(i^i>>>13,0xc2b2ae35),0x100000000*(2097151&(i=Math.imul(i^i>>>16,0x85ebca6b)^Math.imul(r^r>>>13,0xc2b2ae35)))+(r>>>0)},c=(0,o.jG)(function(e){return e}),u={theme:c,token:(0,i.Z)((0,i.Z)({},l),null===a.Z||void 0===a.Z||null==(r=a.Z.defaultAlgorithm)?void 0:r.call(a.Z,null===a.Z||void 0===a.Z?void 0:a.Z.defaultSeed)),hashId:"pro-".concat(s(JSON.stringify(l)))},d=function(){return u}},24288:function(e,t,n){"use strict";n.d(t,{n:()=>d});var r=n(57987),i=n(52655),o=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,a=function(e){return"*"===e||"x"===e||"X"===e},l=function(e){var t=parseInt(e,10);return isNaN(t)?e:t},s=function(e,t){if(a(e)||a(t))return 0;var n,o,s=(n=l(e),o=l(t),(0,i.Z)(n)!==(0,i.Z)(o)?[String(n),String(o)]:[n,o]),c=(0,r.Z)(s,2),u=c[0],d=c[1];return u>d?1:u<d?-1:0},c=function(e,t){for(var n=0;n<Math.max(e.length,t.length);n++){var r=s(e[n]||"0",t[n]||"0");if(0!==r)return r}return 0},u=function(e){var t,n=e.match(o);return null==n||null==(t=n.shift)||t.call(n),n},d=function(e,t){var n=u(e),r=u(t),i=n.pop(),o=r.pop(),a=c(n,r);return 0!==a?a:i||o?i?-1:1:0}},43902:function(e,t,n){"use strict";n.d(t,{X:()=>l,b:()=>a});var r=n(17697),i=n(30945),o=n(24288),a=function(){var e;return"undefined"==typeof process?r.Z:(null==(e=process)||null==(e=e.env)?void 0:e.ANTD_VERSION)||r.Z},l=function(e,t){var n=(0,o.n)(a(),"4.23.0")>-1?{open:e,onOpenChange:t}:{visible:e,onVisibleChange:t};return(0,i.Y)(n)}},35591:function(e,t,n){"use strict";n.d(t,{S:()=>f});var r=n(71123),i=n(83739),o=n(9078),a=n(14425),l=n(81087),s=n(50793),c=n(62155),u=n(52983),d=n(97458),f=function(e){(0,a.Z)(n,e);var t=(0,l.Z)(n);function n(){var e;(0,r.Z)(this,n);for(var i=arguments.length,a=Array(i),l=0;l<i;l++)a[l]=arguments[l];return e=t.call.apply(t,[this].concat(a)),(0,s.Z)((0,o.Z)(e),"state",{hasError:!1,errorInfo:""}),e}return(0,i.Z)(n,[{key:"componentDidCatch",value:function(e,t){console.log(e,t)}},{key:"render",value:function(){return this.state.hasError?(0,d.jsx)(c.ZP,{status:"error",title:"Something went wrong.",extra:this.state.errorInfo}):this.props.children}}],[{key:"getDerivedStateFromError",value:function(e){return{hasError:!0,errorInfo:e.message}}}]),n}(u.Component)},67144:function(e,t,n){"use strict";n.d(t,{D:()=>l});var r=n(30104),i=n(63587),o=n(52983),a=n(38221);function l(e,t){var n=(0,a.J)(e),l=(0,o.useRef)(),s=(0,o.useCallback)(function(){l.current&&(clearTimeout(l.current),l.current=null)},[]),c=(0,o.useCallback)((0,i.Z)((0,r.Z)().mark(function e(){var o,a,c,u=arguments;return(0,r.Z)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(a=Array(o=u.length),c=0;c<o;c++)a[c]=u[c];if(0!==t&&void 0!==t){e.next=3;break}return e.abrupt("return",n.apply(void 0,a));case 3:return s(),e.abrupt("return",new Promise(function(e){l.current=setTimeout((0,i.Z)((0,r.Z)().mark(function t(){return(0,r.Z)().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.t0=e,t.next=3,n.apply(void 0,a);case 3:return t.t1=t.sent,(0,t.t0)(t.t1),t.abrupt("return");case 6:case"end":return t.stop()}},t)})),t)}));case 5:case"end":return e.stop()}},e)})),[n,s,t]);return(0,o.useEffect)(function(){return s},[s]),{run:c,cancel:s}}},38221:function(e,t,n){"use strict";n.d(t,{J:()=>o});var r=n(81632),i=n(52983),o=function(e){var t=(0,i.useRef)(null);return t.current=e,(0,i.useCallback)(function(){for(var e,n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return null==(e=t.current)?void 0:e.call.apply(e,[t].concat((0,r.Z)(i)))},[])}},49130:function(e,t,n){"use strict";n.d(t,{j:()=>i});var r="undefined"!=typeof process&&null!=process.versions&&null!=process.versions.node,i=function(){return"undefined"!=typeof window&&void 0!==window.document&&void 0!==window.matchMedia&&!r}},30945:function(e,t,n){"use strict";n.d(t,{Y:()=>r});var r=function(e){var t={};if(Object.keys(e||{}).forEach(function(n){void 0!==e[n]&&(t[n]=e[n])}),!(Object.keys(t).length<1))return t}},48047:function(e,t,n){"use strict";n.d(t,{Z:()=>v});var r=n(57987),i=n(52983),o=n(63730),a=n(16447);n(37326);var l=n(22878),s=i.createContext(null),c=n(81632),u=n(57743),d=[],f=n(76650),p=n(99755),h="rc-util-locker-".concat(Date.now()),m=0,g=function(e){return!1!==e&&((0,a.Z)()&&e?"string"==typeof e?document.querySelector(e):"function"==typeof e?e():e:null)},v=i.forwardRef(function(e,t){var n,v,y,b=e.open,x=e.autoLock,Z=e.getContainer,C=(e.debug,e.autoDestroy),S=void 0===C||C,w=e.children,k=i.useState(b),M=(0,r.Z)(k,2),F=M[0],T=M[1],P=F||b;i.useEffect(function(){(S||b)&&T(b)},[b,S]);var E=i.useState(function(){return g(Z)}),R=(0,r.Z)(E,2),I=R[0],j=R[1];i.useEffect(function(){var e=g(Z);j(null!=e?e:null)});var L=function(e,t){var n=i.useState(function(){return(0,a.Z)()?document.createElement("div"):null}),o=(0,r.Z)(n,1)[0],l=i.useRef(!1),f=i.useContext(s),p=i.useState(d),h=(0,r.Z)(p,2),m=h[0],g=h[1],v=f||(l.current?void 0:function(e){g(function(t){return[e].concat((0,c.Z)(t))})});function y(){o.parentElement||document.body.appendChild(o),l.current=!0}function b(){var e;null==(e=o.parentElement)||e.removeChild(o),l.current=!1}return(0,u.Z)(function(){return e?f?f(y):y():b(),b},[e]),(0,u.Z)(function(){m.length&&(m.forEach(function(e){return e()}),g(d))},[m]),[o,v]}(P&&!I,0),A=(0,r.Z)(L,2),O=A[0],N=A[1],D=null!=I?I:O;n=!!(x&&b&&(0,a.Z)()&&(D===O||D===document.body)),v=i.useState(function(){return m+=1,"".concat(h,"_").concat(m)}),y=(0,r.Z)(v,1)[0],(0,u.Z)(function(){if(n){var e=(0,p.o)(document.body).width,t=document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth;(0,f.hq)("\nhtml body {\n  overflow-y: hidden;\n  ".concat(t?"width: calc(100% - ".concat(e,"px);"):"","\n}"),y)}else(0,f.jL)(y);return function(){(0,f.jL)(y)}},[n,y]);var B=null;w&&(0,l.Yr)(w)&&t&&(B=w.ref);var _=(0,l.x1)(B,t);if(!P||!(0,a.Z)()||void 0===I)return null;var H=!1===D,z=w;return t&&(z=i.cloneElement(w,{ref:_})),i.createElement(s.Provider,{value:N},H?z:(0,o.createPortal)(z,D))})},49817:function(e,t,n){"use strict";n.d(t,{Z:()=>H});var r=n(19741),i=n(57987),o=n(95504),a=n(48047),l=n(41229),s=n.n(l),c=n(86708),u=n(81129),d=n(5019),f=n(76990),p=n(3094),h=n(57743),m=n(45015),g=n(52983),v=n(25833),y=n(69211),b=n(22878);function x(e){var t=e.prefixCls,n=e.align,r=e.arrow,i=e.arrowPos,o=r||{},a=o.className,l=o.content,c=i.x,u=i.y,d=g.useRef();if(!n||!n.points)return null;var f={position:"absolute"};if(!1!==n.autoArrow){var p=n.points[0],h=n.points[1],m=p[0],v=p[1],y=h[0],b=h[1];m!==y&&["t","b"].includes(m)?"t"===m?f.top=0:f.bottom=0:f.top=void 0===u?0:u,v!==b&&["l","r"].includes(v)?"l"===v?f.left=0:f.right=0:f.left=void 0===c?0:c}return g.createElement("div",{ref:d,className:s()("".concat(t,"-arrow"),a),style:f},l)}function Z(e){var t=e.prefixCls,n=e.open,r=e.zIndex,i=e.mask,o=e.motion;return i?g.createElement(y.ZP,(0,v.Z)({},o,{motionAppear:!0,visible:n,removeOnLeave:!0}),function(e){var n=e.className;return g.createElement("div",{style:{zIndex:r},className:s()("".concat(t,"-mask"),n)})}):null}var C=g.memo(function(e){return e.children},function(e,t){return t.cache}),S=g.forwardRef(function(e,t){var n=e.popup,o=e.className,a=e.prefixCls,l=e.style,u=e.target,d=e.onVisibleChanged,f=e.open,p=e.keepDom,m=e.fresh,S=e.onClick,w=e.mask,k=e.arrow,M=e.arrowPos,F=e.align,T=e.motion,P=e.maskMotion,E=e.forceRender,R=e.getPopupContainer,I=e.autoDestroy,j=e.portal,L=e.zIndex,A=e.onMouseEnter,O=e.onMouseLeave,N=e.onPointerEnter,D=e.onPointerDownCapture,B=e.ready,_=e.offsetX,H=e.offsetY,z=e.offsetR,V=e.offsetB,W=e.onAlign,$=e.onPrepare,K=e.stretch,U=e.targetWidth,q=e.targetHeight,Y="function"==typeof n?n():n,X=f||p,G=(null==R?void 0:R.length)>0,Q=g.useState(!R||!G),J=(0,i.Z)(Q,2),ee=J[0],et=J[1];if((0,h.Z)(function(){!ee&&G&&u&&et(!0)},[ee,G,u]),!ee)return null;var en="auto",er={left:"-1000vw",top:"-1000vh",right:en,bottom:en};if(B||!f){var ei,eo=F.points,ea=F.dynamicInset||(null==(ei=F._experimental)?void 0:ei.dynamicInset),el=ea&&"r"===eo[0][1],es=ea&&"b"===eo[0][0];el?(er.right=z,er.left=en):(er.left=_,er.right=en),es?(er.bottom=V,er.top=en):(er.top=H,er.bottom=en)}var ec={};return K&&(K.includes("height")&&q?ec.height=q:K.includes("minHeight")&&q&&(ec.minHeight=q),K.includes("width")&&U?ec.width=U:K.includes("minWidth")&&U&&(ec.minWidth=U)),f||(ec.pointerEvents="none"),g.createElement(j,{open:E||X,getContainer:R&&function(){return R(u)},autoDestroy:I},g.createElement(Z,{prefixCls:a,open:f,zIndex:L,mask:w,motion:P}),g.createElement(c.Z,{onResize:W,disabled:!f},function(e){return g.createElement(y.ZP,(0,v.Z)({motionAppear:!0,motionEnter:!0,motionLeave:!0,removeOnLeave:!1,forceRender:E,leavedClassName:"".concat(a,"-hidden")},T,{onAppearPrepare:$,onEnterPrepare:$,visible:f,onVisibleChanged:function(e){var t;null==T||null==(t=T.onVisibleChanged)||t.call(T,e),d(e)}}),function(n,i){var c=n.className,u=n.style,d=s()(a,c,o);return g.createElement("div",{ref:(0,b.sQ)(e,t,i),className:d,style:(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({"--arrow-x":"".concat(M.x||0,"px"),"--arrow-y":"".concat(M.y||0,"px")},er),ec),u),{},{boxSizing:"border-box",zIndex:L},l),onMouseEnter:A,onMouseLeave:O,onPointerEnter:N,onClick:S,onPointerDownCapture:D},k&&g.createElement(x,{prefixCls:a,arrow:k,arrowPos:M,align:F}),g.createElement(C,{cache:!f&&!m},Y))})}))}),w=g.forwardRef(function(e,t){var n=e.children,r=e.getTriggerDOMNode,i=(0,b.Yr)(n),o=g.useCallback(function(e){(0,b.mH)(t,r?r(e):e)},[r]),a=(0,b.x1)(o,(0,b.C4)(n));return i?g.cloneElement(n,{ref:a}):n}),k=g.createContext(null);function M(e){return e?Array.isArray(e)?e:[e]:[]}var F=n(25014);function T(e,t,n,r){return t||(n?{motionName:"".concat(e,"-").concat(n)}:r?{motionName:r}:null)}function P(e){return e.ownerDocument.defaultView}function E(e){for(var t=[],n=null==e?void 0:e.parentElement,r=["hidden","scroll","clip","auto"];n;){var i=P(n).getComputedStyle(n);[i.overflowX,i.overflowY,i.overflow].some(function(e){return r.includes(e)})&&t.push(n),n=n.parentElement}return t}function R(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return Number.isNaN(e)?t:e}function I(e){return R(parseFloat(e),0)}function j(e,t){var n=(0,r.Z)({},e);return(t||[]).forEach(function(e){if(!(e instanceof HTMLBodyElement||e instanceof HTMLHtmlElement)){var t=P(e).getComputedStyle(e),r=t.overflow,i=t.overflowClipMargin,o=t.borderTopWidth,a=t.borderBottomWidth,l=t.borderLeftWidth,s=t.borderRightWidth,c=e.getBoundingClientRect(),u=e.offsetHeight,d=e.clientHeight,f=e.offsetWidth,p=e.clientWidth,h=I(o),m=I(a),g=I(l),v=I(s),y=R(Math.round(c.width/f*1e3)/1e3),b=R(Math.round(c.height/u*1e3)/1e3),x=h*b,Z=g*y,C=0,S=0;if("clip"===r){var w=I(i);C=w*y,S=w*b}var k=c.x+Z-C,M=c.y+x-S,F=k+c.width+2*C-Z-v*y-(f-p-g-v)*y,T=M+c.height+2*S-x-m*b-(u-d-h-m)*b;n.left=Math.max(n.left,k),n.top=Math.max(n.top,M),n.right=Math.min(n.right,F),n.bottom=Math.min(n.bottom,T)}}),n}function L(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n="".concat(t),r=n.match(/^(.*)\%$/);return r?e*(parseFloat(r[1])/100):parseFloat(n)}function A(e,t){var n=(0,i.Z)(t||[],2),r=n[0],o=n[1];return[L(e.width,r),L(e.height,o)]}function O(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return[e[0],e[1]]}function N(e,t){var n,r,i=t[0],o=t[1];return r="t"===i?e.y:"b"===i?e.y+e.height:e.y+e.height/2,{x:"l"===o?e.x:"r"===o?e.x+e.width:e.x+e.width/2,y:r}}function D(e,t){var n={t:"b",b:"t",l:"r",r:"l"};return e.map(function(e,r){return r===t?n[e]||"c":e}).join("")}var B=n(81632);n(37326);var _=["prefixCls","children","action","showAction","hideAction","popupVisible","defaultPopupVisible","onPopupVisibleChange","afterPopupVisibleChange","mouseEnterDelay","mouseLeaveDelay","focusDelay","blurDelay","mask","maskClosable","getPopupContainer","forceRender","autoDestroy","destroyPopupOnHide","popup","popupClassName","popupStyle","popupPlacement","builtinPlacements","popupAlign","zIndex","stretch","getPopupClassNameFromAlign","fresh","alignPoint","onPopupClick","onPopupAlign","arrow","popupMotion","maskMotion","popupTransitionName","popupAnimation","maskTransitionName","maskAnimation","className","getTriggerDOMNode"],H=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.Z;return g.forwardRef(function(t,n){var a,l,v,y,b,x,Z,C,I,L,H,z,V,W,$,K,U,q=t.prefixCls,Y=void 0===q?"rc-trigger-popup":q,X=t.children,G=t.action,Q=t.showAction,J=t.hideAction,ee=t.popupVisible,et=t.defaultPopupVisible,en=t.onPopupVisibleChange,er=t.afterPopupVisibleChange,ei=t.mouseEnterDelay,eo=t.mouseLeaveDelay,ea=void 0===eo?.1:eo,el=t.focusDelay,es=t.blurDelay,ec=t.mask,eu=t.maskClosable,ed=t.getPopupContainer,ef=t.forceRender,ep=t.autoDestroy,eh=t.destroyPopupOnHide,em=t.popup,eg=t.popupClassName,ev=t.popupStyle,ey=t.popupPlacement,eb=t.builtinPlacements,ex=void 0===eb?{}:eb,eZ=t.popupAlign,eC=t.zIndex,eS=t.stretch,ew=t.getPopupClassNameFromAlign,ek=t.fresh,eM=t.alignPoint,eF=t.onPopupClick,eT=t.onPopupAlign,eP=t.arrow,eE=t.popupMotion,eR=t.maskMotion,eI=t.popupTransitionName,ej=t.popupAnimation,eL=t.maskTransitionName,eA=t.maskAnimation,eO=t.className,eN=t.getTriggerDOMNode,eD=(0,o.Z)(t,_),eB=g.useState(!1),e_=(0,i.Z)(eB,2),eH=e_[0],ez=e_[1];(0,h.Z)(function(){ez((0,m.Z)())},[]);var eV=g.useRef({}),eW=g.useContext(k),e$=g.useMemo(function(){return{registerSubPopup:function(e,t){eV.current[e]=t,null==eW||eW.registerSubPopup(e,t)}}},[eW]),eK=(0,p.Z)(),eU=g.useState(null),eq=(0,i.Z)(eU,2),eY=eq[0],eX=eq[1],eG=g.useRef(null),eQ=(0,f.Z)(function(e){eG.current=e,(0,u.Sh)(e)&&eY!==e&&eX(e),null==eW||eW.registerSubPopup(eK,e)}),eJ=g.useState(null),e0=(0,i.Z)(eJ,2),e1=e0[0],e2=e0[1],e5=g.useRef(null),e6=(0,f.Z)(function(e){(0,u.Sh)(e)&&e1!==e&&(e2(e),e5.current=e)}),e8=g.Children.only(X),e3=(null==e8?void 0:e8.props)||{},e4={},e9=(0,f.Z)(function(e){var t,n;return(null==e1?void 0:e1.contains(e))||(null==(t=(0,d.A)(e1))?void 0:t.host)===e||e===e1||(null==eY?void 0:eY.contains(e))||(null==(n=(0,d.A)(eY))?void 0:n.host)===e||e===eY||Object.values(eV.current).some(function(t){return(null==t?void 0:t.contains(e))||e===t})}),e7=T(Y,eE,ej,eI),te=T(Y,eR,eA,eL),tt=g.useState(et||!1),tn=(0,i.Z)(tt,2),tr=tn[0],ti=tn[1],to=null!=ee?ee:tr,ta=(0,f.Z)(function(e){void 0===ee&&ti(e)});(0,h.Z)(function(){ti(ee||!1)},[ee]);var tl=g.useRef(to);tl.current=to;var ts=g.useRef([]);ts.current=[];var tc=(0,f.Z)(function(e){var t;ta(e),(null!=(t=ts.current[ts.current.length-1])?t:to)!==e&&(ts.current.push(e),null==en||en(e))}),tu=g.useRef(),td=function(){clearTimeout(tu.current)},tf=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;td(),0===t?tc(e):tu.current=setTimeout(function(){tc(e)},1e3*t)};g.useEffect(function(){return td},[]);var tp=g.useState(!1),th=(0,i.Z)(tp,2),tm=th[0],tg=th[1];(0,h.Z)(function(e){(!e||to)&&tg(!0)},[to]);var tv=g.useState(null),ty=(0,i.Z)(tv,2),tb=ty[0],tx=ty[1],tZ=g.useState(null),tC=(0,i.Z)(tZ,2),tS=tC[0],tw=tC[1],tk=function(e){tw([e.clientX,e.clientY])},tM=(a=eM&&null!==tS?tS:e1,l=g.useState({ready:!1,offsetX:0,offsetY:0,offsetR:0,offsetB:0,arrowX:0,arrowY:0,scaleX:1,scaleY:1,align:ex[ey]||{}}),y=(v=(0,i.Z)(l,2))[0],b=v[1],x=g.useRef(0),Z=g.useMemo(function(){return eY?E(eY):[]},[eY]),C=g.useRef({}),to||(C.current={}),I=(0,f.Z)(function(){if(eY&&a&&to){var e=eY.ownerDocument,t=P(eY).getComputedStyle(eY),n=t.width,o=t.height,l=t.position,s=eY.style.left,c=eY.style.top,d=eY.style.right,f=eY.style.bottom,p=eY.style.overflow,h=(0,r.Z)((0,r.Z)({},ex[ey]),eZ),m=e.createElement("div");if(null==(S=eY.parentElement)||S.appendChild(m),m.style.left="".concat(eY.offsetLeft,"px"),m.style.top="".concat(eY.offsetTop,"px"),m.style.position=l,m.style.height="".concat(eY.offsetHeight,"px"),m.style.width="".concat(eY.offsetWidth,"px"),eY.style.left="0",eY.style.top="0",eY.style.right="auto",eY.style.bottom="auto",eY.style.overflow="hidden",Array.isArray(a))T={x:a[0],y:a[1],width:0,height:0};else{var g,v,y,x,S,w,k,M,T,E,I,L=a.getBoundingClientRect();L.x=null!=(E=L.x)?E:L.left,L.y=null!=(I=L.y)?I:L.top,T={x:L.x,y:L.y,width:L.width,height:L.height}}var B=eY.getBoundingClientRect();B.x=null!=(w=B.x)?w:B.left,B.y=null!=(k=B.y)?k:B.top;var _=e.documentElement,H=_.clientWidth,z=_.clientHeight,V=_.scrollWidth,W=_.scrollHeight,$=_.scrollTop,K=_.scrollLeft,U=B.height,q=B.width,Y=T.height,X=T.width,G=h.htmlRegion,Q="visible",J="visibleFirst";"scroll"!==G&&G!==J&&(G=Q);var ee=G===J,et=j({left:-K,top:-$,right:V-K,bottom:W-$},Z),en=j({left:0,top:0,right:H,bottom:z},Z),er=G===Q?en:et,ei=ee?en:er;eY.style.left="auto",eY.style.top="auto",eY.style.right="0",eY.style.bottom="0";var eo=eY.getBoundingClientRect();eY.style.left=s,eY.style.top=c,eY.style.right=d,eY.style.bottom=f,eY.style.overflow=p,null==(M=eY.parentElement)||M.removeChild(m);var ea=R(Math.round(q/parseFloat(n)*1e3)/1e3),el=R(Math.round(U/parseFloat(o)*1e3)/1e3);if(!(0===ea||0===el||(0,u.Sh)(a)&&!(0,F.Z)(a))){var es=h.offset,ec=h.targetOffset,eu=A(B,es),ed=(0,i.Z)(eu,2),ef=ed[0],ep=ed[1],eh=A(T,ec),em=(0,i.Z)(eh,2),eg=em[0],ev=em[1];T.x-=eg,T.y-=ev;var eb=h.points||[],eC=(0,i.Z)(eb,2),eS=eC[0],ew=O(eC[1]),ek=O(eS),eM=N(T,ew),eF=N(B,ek),eP=(0,r.Z)({},h),eE=eM.x-eF.x+ef,eR=eM.y-eF.y+ep,eI=tu(eE,eR),ej=tu(eE,eR,en),eL=N(T,["t","l"]),eA=N(B,["t","l"]),eO=N(T,["b","r"]),eN=N(B,["b","r"]),eD=h.overflow||{},eB=eD.adjustX,e_=eD.adjustY,eH=eD.shiftX,ez=eD.shiftY,eV=function(e){return"boolean"==typeof e?e:e>=0};td();var eW=eV(e_),e$=ek[0]===ew[0];if(eW&&"t"===ek[0]&&(v>ei.bottom||C.current.bt)){var eK=eR;e$?eK-=U-Y:eK=eL.y-eN.y-ep;var eU=tu(eE,eK),eq=tu(eE,eK,en);eU>eI||eU===eI&&(!ee||eq>=ej)?(C.current.bt=!0,eR=eK,ep=-ep,eP.points=[D(ek,0),D(ew,0)]):C.current.bt=!1}if(eW&&"b"===ek[0]&&(g<ei.top||C.current.tb)){var eX=eR;e$?eX+=U-Y:eX=eO.y-eA.y-ep;var eG=tu(eE,eX),eQ=tu(eE,eX,en);eG>eI||eG===eI&&(!ee||eQ>=ej)?(C.current.tb=!0,eR=eX,ep=-ep,eP.points=[D(ek,0),D(ew,0)]):C.current.tb=!1}var eJ=eV(eB),e0=ek[1]===ew[1];if(eJ&&"l"===ek[1]&&(x>ei.right||C.current.rl)){var e1=eE;e0?e1-=q-X:e1=eL.x-eN.x-ef;var e2=tu(e1,eR),e5=tu(e1,eR,en);e2>eI||e2===eI&&(!ee||e5>=ej)?(C.current.rl=!0,eE=e1,ef=-ef,eP.points=[D(ek,1),D(ew,1)]):C.current.rl=!1}if(eJ&&"r"===ek[1]&&(y<ei.left||C.current.lr)){var e6=eE;e0?e6+=q-X:e6=eO.x-eA.x-ef;var e8=tu(e6,eR),e3=tu(e6,eR,en);e8>eI||e8===eI&&(!ee||e3>=ej)?(C.current.lr=!0,eE=e6,ef=-ef,eP.points=[D(ek,1),D(ew,1)]):C.current.lr=!1}td();var e4=!0===eH?0:eH;"number"==typeof e4&&(y<en.left&&(eE-=y-en.left-ef,T.x+X<en.left+e4&&(eE+=T.x-en.left+X-e4)),x>en.right&&(eE-=x-en.right-ef,T.x>en.right-e4&&(eE+=T.x-en.right+e4)));var e9=!0===ez?0:ez;"number"==typeof e9&&(g<en.top&&(eR-=g-en.top-ep,T.y+Y<en.top+e9&&(eR+=T.y-en.top+Y-e9)),v>en.bottom&&(eR-=v-en.bottom-ep,T.y>en.bottom-e9&&(eR+=T.y-en.bottom+e9)));var e7=B.x+eE,te=B.y+eR,tt=T.x,tn=T.y,tr=Math.max(e7,tt),ti=Math.min(e7+q,tt+X),ta=Math.max(te,tn),tl=Math.min(te+U,tn+Y);null==eT||eT(eY,eP);var ts=eo.right-B.x-(eE+B.width),tc=eo.bottom-B.y-(eR+B.height);1===ea&&(eE=Math.round(eE),ts=Math.round(ts)),1===el&&(eR=Math.round(eR),tc=Math.round(tc)),b({ready:!0,offsetX:eE/ea,offsetY:eR/el,offsetR:ts/ea,offsetB:tc/el,arrowX:((tr+ti)/2-e7)/ea,arrowY:((ta+tl)/2-te)/el,scaleX:ea,scaleY:el,align:eP})}function tu(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:er,r=B.x+e,i=B.y+t,o=Math.max(r,n.left),a=Math.max(i,n.top);return Math.max(0,(Math.min(r+q,n.right)-o)*(Math.min(i+U,n.bottom)-a))}function td(){v=(g=B.y+eR)+U,x=(y=B.x+eE)+q}}}),L=function(){b(function(e){return(0,r.Z)((0,r.Z)({},e),{},{ready:!1})})},(0,h.Z)(L,[ey]),(0,h.Z)(function(){to||L()},[to]),[y.ready,y.offsetX,y.offsetY,y.offsetR,y.offsetB,y.arrowX,y.arrowY,y.scaleX,y.scaleY,y.align,function(){x.current+=1;var e=x.current;Promise.resolve().then(function(){x.current===e&&I()})}]),tF=(0,i.Z)(tM,11),tT=tF[0],tP=tF[1],tE=tF[2],tR=tF[3],tI=tF[4],tj=tF[5],tL=tF[6],tA=tF[7],tO=tF[8],tN=tF[9],tD=tF[10],tB=(H=void 0===G?"hover":G,g.useMemo(function(){var e=M(null!=Q?Q:H),t=M(null!=J?J:H),n=new Set(e),r=new Set(t);return eH&&(n.has("hover")&&(n.delete("hover"),n.add("click")),r.has("hover")&&(r.delete("hover"),r.add("click"))),[n,r]},[eH,H,Q,J])),t_=(0,i.Z)(tB,2),tH=t_[0],tz=t_[1],tV=tH.has("click"),tW=tz.has("click")||tz.has("contextMenu"),t$=(0,f.Z)(function(){tm||tD()});z=function(){tl.current&&eM&&tW&&tf(!1)},(0,h.Z)(function(){if(to&&e1&&eY){var e=E(e1),t=E(eY),n=P(eY),r=new Set([n].concat((0,B.Z)(e),(0,B.Z)(t)));function i(){t$(),z()}return r.forEach(function(e){e.addEventListener("scroll",i,{passive:!0})}),n.addEventListener("resize",i,{passive:!0}),t$(),function(){r.forEach(function(e){e.removeEventListener("scroll",i),n.removeEventListener("resize",i)})}}},[to,e1,eY]),(0,h.Z)(function(){t$()},[tS,ey]),(0,h.Z)(function(){to&&!(null!=ex&&ex[ey])&&t$()},[JSON.stringify(eZ)]);var tK=g.useMemo(function(){var e=function(e,t,n,r){for(var i=n.points,o=Object.keys(e),a=0;a<o.length;a+=1){var l,s=o[a];if(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0;return n?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}(null==(l=e[s])?void 0:l.points,i,r))return"".concat(t,"-placement-").concat(s)}return""}(ex,Y,tN,eM);return s()(e,null==ew?void 0:ew(tN))},[tN,ew,ex,Y,eM]);g.useImperativeHandle(n,function(){return{nativeElement:e5.current,popupElement:eG.current,forceAlign:t$}});var tU=g.useState(0),tq=(0,i.Z)(tU,2),tY=tq[0],tX=tq[1],tG=g.useState(0),tQ=(0,i.Z)(tG,2),tJ=tQ[0],t0=tQ[1],t1=function(){if(eS&&e1){var e=e1.getBoundingClientRect();tX(e.width),t0(e.height)}};function t2(e,t,n,r){e4[e]=function(i){var o;null==r||r(i),tf(t,n);for(var a=arguments.length,l=Array(a>1?a-1:0),s=1;s<a;s++)l[s-1]=arguments[s];null==(o=e3[e])||o.call.apply(o,[e3,i].concat(l))}}(0,h.Z)(function(){tb&&(tD(),tb(),tx(null))},[tb]),(tV||tW)&&(e4.onClick=function(e){var t;tl.current&&tW?tf(!1):!tl.current&&tV&&(tk(e),tf(!0));for(var n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];null==(t=e3.onClick)||t.call.apply(t,[e3,e].concat(r))});var t5=(V=void 0===eu||eu,(W=g.useRef(to)).current=to,$=g.useRef(!1),g.useEffect(function(){if(tW&&eY&&(!ec||V)){var e=function(){$.current=!1},t=function(e){var t;!W.current||e9((null==(t=e.composedPath)||null==(t=t.call(e))?void 0:t[0])||e.target)||$.current||tf(!1)},n=P(eY);n.addEventListener("pointerdown",e,!0),n.addEventListener("mousedown",t,!0),n.addEventListener("contextmenu",t,!0);var r=(0,d.A)(e1);return r&&(r.addEventListener("mousedown",t,!0),r.addEventListener("contextmenu",t,!0)),function(){n.removeEventListener("pointerdown",e,!0),n.removeEventListener("mousedown",t,!0),n.removeEventListener("contextmenu",t,!0),r&&(r.removeEventListener("mousedown",t,!0),r.removeEventListener("contextmenu",t,!0))}}},[tW,e1,eY,ec,V]),function(){$.current=!0}),t6=tH.has("hover"),t8=tz.has("hover");t6&&(t2("onMouseEnter",!0,ei,function(e){tk(e)}),t2("onPointerEnter",!0,ei,function(e){tk(e)}),K=function(e){(to||tm)&&null!=eY&&eY.contains(e.target)&&tf(!0,ei)},eM&&(e4.onMouseMove=function(e){var t;null==(t=e3.onMouseMove)||t.call(e3,e)})),t8&&(t2("onMouseLeave",!1,ea),t2("onPointerLeave",!1,ea),U=function(){tf(!1,ea)}),tH.has("focus")&&t2("onFocus",!0,el),tz.has("focus")&&t2("onBlur",!1,es),tH.has("contextMenu")&&(e4.onContextMenu=function(e){var t;tl.current&&tz.has("contextMenu")?tf(!1):(tk(e),tf(!0)),e.preventDefault();for(var n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];null==(t=e3.onContextMenu)||t.call.apply(t,[e3,e].concat(r))}),eO&&(e4.className=s()(e3.className,eO));var t3=(0,r.Z)((0,r.Z)({},e3),e4),t4={};["onContextMenu","onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur"].forEach(function(e){eD[e]&&(t4[e]=function(){for(var t,n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];null==(t=t3[e])||t.call.apply(t,[t3].concat(r)),eD[e].apply(eD,r)})});var t9=g.cloneElement(e8,(0,r.Z)((0,r.Z)({},t3),t4)),t7=eP?(0,r.Z)({},!0!==eP?eP:{}):null;return g.createElement(g.Fragment,null,g.createElement(c.Z,{disabled:!to,ref:e6,onResize:function(){t1(),t$()}},g.createElement(w,{getTriggerDOMNode:eN},t9)),g.createElement(k.Provider,{value:e$},g.createElement(S,{portal:e,ref:eQ,prefixCls:Y,popup:em,className:s()(eg,tK),style:ev,target:e1,onMouseEnter:K,onMouseLeave:U,onPointerEnter:K,zIndex:eC,open:to,keepDom:tm,fresh:ek,onClick:eF,onPointerDownCapture:t5,mask:ec,motion:e7,maskMotion:te,onVisibleChanged:function(e){tg(!1),tD(),null==er||er(e)},onPrepare:function(){return new Promise(function(e){t1(),tx(function(){return e})})},forceRender:ef,autoDestroy:ep||eh||!1,getPopupContainer:ed,align:tN,arrow:t7,arrowPos:{x:tj,y:tL},ready:tT,offsetX:tP,offsetY:tE,offsetR:tR,offsetB:tI,onAlign:t$,stretch:eS,targetWidth:tY/tA,targetHeight:tJ/tO})))})}(a.Z)},41029:function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.pathToRegexp=void 0;function r(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function i(e){return e&&e.sensitive?"":"i"}t.pathToRegexp=function e(t,n,o){if(t instanceof RegExp){if(!n)return t;var a=t.source.match(/\((?!\?)/g);if(a)for(var l=0;l<a.length;l++)n.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(t){return e(t,n,o).source}).join("|")+")",i(o)):function(e,t,n){void 0===n&&(n={});for(var o=n.strict,a=void 0!==o&&o,l=n.start,s=n.end,c=n.encode,u=void 0===c?function(e){return e}:c,d="["+r(n.endsWith||"")+"]|$",f="["+r(n.delimiter||"/#?")+"]",p=void 0===l||l?"^":"",h=0;h<e.length;h++){var m=e[h];if("string"==typeof m)p+=r(u(m));else{var g=r(u(m.prefix)),v=r(u(m.suffix));if(m.pattern)if(t&&t.push(m),g||v)if("+"===m.modifier||"*"===m.modifier){var y="*"===m.modifier?"?":"";p+="(?:"+g+"((?:"+m.pattern+")(?:"+v+g+"(?:"+m.pattern+"))*)"+v+")"+y}else p+="(?:"+g+"("+m.pattern+")"+v+")"+m.modifier;else p+="("+m.pattern+")"+m.modifier;else p+="(?:"+g+v+")"+m.modifier}}if(void 0===s||s)a||(p+=f+"?"),p+=n.endsWith?"(?="+d+")":"$";else{var b=e[e.length-1],x="string"==typeof b?f.indexOf(b[b.length-1])>-1:void 0===b;a||(p+="(?:"+f+"(?="+d+"))?"),x||(p+="(?="+f+"|"+d+")")}return new RegExp(p,i(n))}(function(e,t){void 0===t&&(t={});for(var n=function(e){for(var t=[],n=0;n<e.length;){var r=e[n];if("*"===r||"+"===r||"?"===r){t.push({type:"MODIFIER",index:n,value:e[n++]});continue}if("\\"===r){t.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if("{"===r){t.push({type:"OPEN",index:n,value:e[n++]});continue}if("}"===r){t.push({type:"CLOSE",index:n,value:e[n++]});continue}if(":"===r){for(var i="",o=n+1;o<e.length;){var a=e.charCodeAt(o);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[o++];continue}break}if(!i)throw TypeError("Missing parameter name at "+n);t.push({type:"NAME",index:n,value:i}),n=o;continue}if("("===r){var l=1,s="",o=n+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '+o);for(;o<e.length;){if("\\"===e[o]){s+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--l){o++;break}}else if("("===e[o]&&(l++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at "+o);s+=e[o++]}if(l)throw TypeError("Unbalanced pattern at "+n);if(!s)throw TypeError("Missing pattern at "+n);t.push({type:"PATTERN",index:n,value:s}),n=o;continue}t.push({type:"CHAR",index:n,value:e[n++]})}return t.push({type:"END",index:n,value:""}),t}(e),i=t.prefixes,o=void 0===i?"./":i,a="[^"+r(t.delimiter||"/#?")+"]+?",l=[],s=0,c=0,u="",d=function(e){if(c<n.length&&n[c].type===e)return n[c++].value},f=function(e){var t=d(e);if(void 0!==t)return t;var r=n[c];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};c<n.length;){var h=d("CHAR"),m=d("NAME"),g=d("PATTERN");if(m||g){var v=h||"";-1===o.indexOf(v)&&(u+=v,v=""),u&&(l.push(u),u=""),l.push({name:m||s++,prefix:v,suffix:"",pattern:g||a,modifier:d("MODIFIER")||""});continue}var y=h||d("ESCAPED_CHAR");if(y){u+=y;continue}if(u&&(l.push(u),u=""),d("OPEN")){var v=p(),b=d("NAME")||"",x=d("PATTERN")||"",Z=p();f("CLOSE"),l.push({name:b||(x?s++:""),pattern:b&&!x?a:x,prefix:v,suffix:Z,modifier:d("MODIFIER")||""});continue}f("END")}return l}(t,o),n,o)}},28977:function(e){e.exports=function(){"use strict";var e="millisecond",t="second",n="minute",r="hour",i="week",o="month",a="quarter",l="year",s="date",c="Invalid Date",u=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,f=function(e,t,n){var r=String(e);return!r||r.length>=t?e:""+Array(t+1-r.length).join(n)+e},p="en",h={};h[p]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],n=e%100;return"["+e+(t[(n-20)%10]||t[n]||t[0])+"]"}};var m="$isDayjsObject",g=function(e){return e instanceof x||!(!e||!e[m])},v=function e(t,n,r){var i;if(!t)return p;if("string"==typeof t){var o=t.toLowerCase();h[o]&&(i=o),n&&(h[o]=n,i=o);var a=t.split("-");if(!i&&a.length>1)return e(a[0])}else{var l=t.name;h[l]=t,i=l}return!r&&i&&(p=i),i||!r&&p},y=function(e,t){if(g(e))return e.clone();var n="object"==typeof t?t:{};return n.date=e,n.args=arguments,new x(n)},b={s:f,z:function(e){var t=-e.utcOffset(),n=Math.abs(t);return(t<=0?"+":"-")+f(Math.floor(n/60),2,"0")+":"+f(n%60,2,"0")},m:function e(t,n){if(t.date()<n.date())return-e(n,t);var r=12*(n.year()-t.year())+(n.month()-t.month()),i=t.clone().add(r,o),a=n-i<0,l=t.clone().add(r+(a?-1:1),o);return+(-(r+(n-i)/(a?i-l:l-i))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(c){return({M:o,y:l,w:i,d:"day",D:s,h:r,m:n,s:t,ms:e,Q:a})[c]||String(c||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};b.l=v,b.i=g,b.w=function(e,t){return y(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var x=function(){function f(e){this.$L=v(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[m]=!0}var p=f.prototype;return p.parse=function(e){this.$d=function(e){var t=e.date,n=e.utc;if(null===t)return new Date(NaN);if(b.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var r=t.match(u);if(r){var i=r[2]-1||0,o=(r[7]||"0").substring(0,3);return n?new Date(Date.UTC(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,o)):new Date(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,o)}}return new Date(t)}(e),this.init()},p.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},p.$utils=function(){return b},p.isValid=function(){return this.$d.toString()!==c},p.isSame=function(e,t){var n=y(e);return this.startOf(t)<=n&&n<=this.endOf(t)},p.isAfter=function(e,t){return y(e)<this.startOf(t)},p.isBefore=function(e,t){return this.endOf(t)<y(e)},p.$g=function(e,t,n){return b.u(e)?this[t]:this.set(n,e)},p.unix=function(){return Math.floor(this.valueOf()/1e3)},p.valueOf=function(){return this.$d.getTime()},p.startOf=function(e,a){var c=this,u=!!b.u(a)||a,d=b.p(e),f=function(e,t){var n=b.w(c.$u?Date.UTC(c.$y,t,e):new Date(c.$y,t,e),c);return u?n:n.endOf("day")},p=function(e,t){return b.w(c.toDate()[e].apply(c.toDate("s"),(u?[0,0,0,0]:[23,59,59,999]).slice(t)),c)},h=this.$W,m=this.$M,g=this.$D,v="set"+(this.$u?"UTC":"");switch(d){case l:return u?f(1,0):f(31,11);case o:return u?f(1,m):f(0,m+1);case i:var y=this.$locale().weekStart||0,x=(h<y?h+7:h)-y;return f(u?g-x:g+(6-x),m);case"day":case s:return p(v+"Hours",0);case r:return p(v+"Minutes",1);case n:return p(v+"Seconds",2);case t:return p(v+"Milliseconds",3);default:return this.clone()}},p.endOf=function(e){return this.startOf(e,!1)},p.$set=function(i,a){var c,u=b.p(i),d="set"+(this.$u?"UTC":""),f=((c={}).day=d+"Date",c[s]=d+"Date",c[o]=d+"Month",c[l]=d+"FullYear",c[r]=d+"Hours",c[n]=d+"Minutes",c[t]=d+"Seconds",c[e]=d+"Milliseconds",c)[u],p="day"===u?this.$D+(a-this.$W):a;if(u===o||u===l){var h=this.clone().set(s,1);h.$d[f](p),h.init(),this.$d=h.set(s,Math.min(this.$D,h.daysInMonth())).$d}else f&&this.$d[f](p);return this.init(),this},p.set=function(e,t){return this.clone().$set(e,t)},p.get=function(e){return this[b.p(e)]()},p.add=function(e,a){var s,c=this;e=Number(e);var u=b.p(a),d=function(t){var n=y(c);return b.w(n.date(n.date()+Math.round(t*e)),c)};if(u===o)return this.set(o,this.$M+e);if(u===l)return this.set(l,this.$y+e);if("day"===u)return d(1);if(u===i)return d(7);var f=((s={})[n]=6e4,s[r]=36e5,s[t]=1e3,s)[u]||1,p=this.$d.getTime()+e*f;return b.w(p,this)},p.subtract=function(e,t){return this.add(-1*e,t)},p.format=function(e){var t=this,n=this.$locale();if(!this.isValid())return n.invalidDate||c;var r=e||"YYYY-MM-DDTHH:mm:ssZ",i=b.z(this),o=this.$H,a=this.$m,l=this.$M,s=n.weekdays,u=n.months,f=n.meridiem,p=function(e,n,i,o){return e&&(e[n]||e(t,r))||i[n].slice(0,o)},h=function(e){return b.s(o%12||12,e,"0")},m=f||function(e,t,n){var r=e<12?"AM":"PM";return n?r.toLowerCase():r};return r.replace(d,function(e,r){return r||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return b.s(t.$y,4,"0");case"M":return l+1;case"MM":return b.s(l+1,2,"0");case"MMM":return p(n.monthsShort,l,u,3);case"MMMM":return p(u,l);case"D":return t.$D;case"DD":return b.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return p(n.weekdaysMin,t.$W,s,2);case"ddd":return p(n.weekdaysShort,t.$W,s,3);case"dddd":return s[t.$W];case"H":return String(o);case"HH":return b.s(o,2,"0");case"h":return h(1);case"hh":return h(2);case"a":return m(o,a,!0);case"A":return m(o,a,!1);case"m":return String(a);case"mm":return b.s(a,2,"0");case"s":return String(t.$s);case"ss":return b.s(t.$s,2,"0");case"SSS":return b.s(t.$ms,3,"0");case"Z":return i}return null}(e)||i.replace(":","")})},p.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},p.diff=function(e,s,c){var u,d=this,f=b.p(s),p=y(e),h=(p.utcOffset()-this.utcOffset())*6e4,m=this-p,g=function(){return b.m(d,p)};switch(f){case l:u=g()/12;break;case o:u=g();break;case a:u=g()/3;break;case i:u=(m-h)/6048e5;break;case"day":u=(m-h)/864e5;break;case r:u=m/36e5;break;case n:u=m/6e4;break;case t:u=m/1e3;break;default:u=m}return c?u:b.a(u)},p.daysInMonth=function(){return this.endOf(o).$D},p.$locale=function(){return h[this.$L]},p.locale=function(e,t){if(!e)return this.$L;var n=this.clone(),r=v(e,t,!0);return r&&(n.$L=r),n},p.clone=function(){return b.w(this.$d,this)},p.toDate=function(){return new Date(this.valueOf())},p.toJSON=function(){return this.isValid()?this.toISOString():null},p.toISOString=function(){return this.$d.toISOString()},p.toString=function(){return this.$d.toUTCString()},f}(),Z=x.prototype;return y.prototype=Z,[["$ms",e],["$s",t],["$m",n],["$H",r],["$W","day"],["$M",o],["$y",l],["$D",s]].forEach(function(e){Z[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),y.extend=function(e,t){return e.$i||(e(t,x,y),e.$i=!0),y},y.locale=v,y.isDayjs=g,y.unix=function(e){return y(1e3*e)},y.en=h[p],y.Ls=h,y.p={},y}()},58070:function(e,t,n){e.exports=function(e){"use strict";var t={name:"zh-cn",weekdays:"\u661F\u671F\u65E5_\u661F\u671F\u4E00_\u661F\u671F\u4E8C_\u661F\u671F\u4E09_\u661F\u671F\u56DB_\u661F\u671F\u4E94_\u661F\u671F\u516D".split("_"),weekdaysShort:"\u5468\u65E5_\u5468\u4E00_\u5468\u4E8C_\u5468\u4E09_\u5468\u56DB_\u5468\u4E94_\u5468\u516D".split("_"),weekdaysMin:"\u65E5_\u4E00_\u4E8C_\u4E09_\u56DB_\u4E94_\u516D".split("_"),months:"\u4E00\u6708_\u4E8C\u6708_\u4E09\u6708_\u56DB\u6708_\u4E94\u6708_\u516D\u6708_\u4E03\u6708_\u516B\u6708_\u4E5D\u6708_\u5341\u6708_\u5341\u4E00\u6708_\u5341\u4E8C\u6708".split("_"),monthsShort:"1\u6708_2\u6708_3\u6708_4\u6708_5\u6708_6\u6708_7\u6708_8\u6708_9\u6708_10\u6708_11\u6708_12\u6708".split("_"),ordinal:function(e,t){return"W"===t?e+"\u5468":e+"\u65E5"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY\u5E74M\u6708D\u65E5",LLL:"YYYY\u5E74M\u6708D\u65E5Ah\u70B9mm\u5206",LLLL:"YYYY\u5E74M\u6708D\u65E5ddddAh\u70B9mm\u5206",l:"YYYY/M/D",ll:"YYYY\u5E74M\u6708D\u65E5",lll:"YYYY\u5E74M\u6708D\u65E5 HH:mm",llll:"YYYY\u5E74M\u6708D\u65E5dddd HH:mm"},relativeTime:{future:"%s\u5185",past:"%s\u524D",s:"\u51E0\u79D2",m:"1 \u5206\u949F",mm:"%d \u5206\u949F",h:"1 \u5C0F\u65F6",hh:"%d \u5C0F\u65F6",d:"1 \u5929",dd:"%d \u5929",M:"1 \u4E2A\u6708",MM:"%d \u4E2A\u6708",y:"1 \u5E74",yy:"%d \u5E74"},meridiem:function(e,t){var n=100*e+t;return n<600?"\u51CC\u6668":n<900?"\u65E9\u4E0A":n<1100?"\u4E0A\u5348":n<1300?"\u4E2D\u5348":n<1800?"\u4E0B\u5348":"\u665A\u4E0A"}};return(e&&"object"==typeof e&&"default"in e?e:{default:e}).default.locale(t,null,!0),t}(n(28977))},85827:function(e,t){"use strict";t.match=function(e,t={}){let{decode:u=decodeURIComponent,delimiter:d="/"}=t,{regexp:f,keys:p}=function(e,t={}){let{delimiter:u="/",end:d=!0,sensitive:f=!1,trailing:p=!0}=t,h=[],m=[];for(let{tokens:d}of(Array.isArray(e)?e:[e]).map(e=>e instanceof c?e:function(e,t={}){let{encodePath:l=n}=t,u=new s(function*(e){let t=[...e],n=0;function l(){let e="";if(r.test(t[++n]))for(e+=t[n];i.test(t[++n]);)e+=t[n];else if('"'===t[n]){let r=n;for(;n<t.length;){if('"'===t[++n]){n++,r=0;break}"\\"===t[n]?e+=t[++n]:e+=t[n]}if(r)throw TypeError(`Unterminated quote at ${r}: ${o}`)}if(!e)throw TypeError(`Missing parameter name at ${n}: ${o}`);return e}for(;n<t.length;){let e=t[n],r=a[e];if(r)yield{type:r,index:n++,value:e};else if("\\"===e)yield{type:"ESCAPED",index:n++,value:t[n++]};else if(":"===e){let e=l();yield{type:"PARAM",index:n,value:e}}else if("*"===e){let e=l();yield{type:"WILDCARD",index:n,value:e}}else yield{type:"CHAR",index:n,value:t[n++]}}return{type:"END",index:n,value:""}}(e));return new c(function e(t){let n=[];for(;;){let r=u.text();r&&n.push({type:"text",value:l(r)});let i=u.tryConsume("PARAM");if(i){n.push({type:"param",name:i});continue}let o=u.tryConsume("WILDCARD");if(o){n.push({type:"wildcard",name:o});continue}if(u.tryConsume("{")){n.push({type:"group",tokens:e("}")});continue}return u.consume(t),n}}("END"))}(e,t)))for(let e of function* e(t,n,r){if(n===t.length)return yield r;let i=t[n];if("group"===i.type){let o=r.slice();for(let r of e(i.tokens,0,o))yield*e(t,n+1,r)}else r.push(i);yield*e(t,n+1,r)}(d,0,[])){let t=function(e,t,n){let r="",i="",a=!0;for(let u=0;u<e.length;u++){let d=e[u];if("text"===d.type){r+=l(d.value),i+=d.value,a||(a=d.value.includes(t));continue}if("param"===d.type||"wildcard"===d.type){var s,c;if(!a&&!i)throw TypeError(`Missing text after "${d.name}": ${o}`);"param"===d.type?r+=`(${s=t,(c=a?"":i).length<2?s.length<2?`[^${l(s+c)}]`:`(?:(?!${l(s)})[^${l(c)}])`:s.length<2?`(?:(?!${l(c)})[^${l(s)}])`:`(?:(?!${l(c)}|${l(s)})[\\s\\S])`}+)`:r+="([\\s\\S]+)",n.push(d),i="",a=!1;continue}}return r}(e,u,h);m.push(t)}let g=`^(?:${m.join("|")})`;return p&&(g+=`(?:${l(u)}$)?`),{regexp:new RegExp(g+=d?"$":`(?=${l(u)}|$)`,f?"":"i"),keys:h}}(e,t),h=p.map(e=>!1===u?n:"param"===e.type?u:e=>e.split(d).map(u));return function(e){let t=f.exec(e);if(!t)return!1;let n=t[0],r=Object.create(null);for(let e=1;e<t.length;e++){if(void 0===t[e])continue;let n=p[e-1],i=h[e-1];r[n.name]=i(t[e])}return{path:n,params:r}}};let n=e=>e,r=/^[$_\p{ID_Start}]$/u,i=/^[$\u200c\u200d\p{ID_Continue}]$/u,o="https://git.new/pathToRegexpError",a={"{":"{","}":"}","(":"(",")":")","[":"[","]":"]","+":"+","?":"?","!":"!"};function l(e){return e.replace(/[.+*?^${}()[\]|/\\]/g,"\\$&")}class s{constructor(e){this.tokens=e}peek(){if(!this._peek){let e=this.tokens.next();this._peek=e.value}return this._peek}tryConsume(e){let t=this.peek();if(t.type===e)return this._peek=void 0,t.value}consume(e){let t=this.tryConsume(e);if(void 0!==t)return t;let{type:n,index:r}=this.peek();throw TypeError(`Unexpected ${n} at ${r}, expected ${e}: ${o}`)}text(){let e,t="";for(;e=this.tryConsume("CHAR")||this.tryConsume("ESCAPED");)t+=e;return t}}class c{constructor(e){this.tokens=e}}},76072:function(e,t,n){"use strict";n.d(t,{Z:()=>k});var r=n(19741),i=n(57987),o=n(48047),a=n(57743),l=n(52983),s=l.createContext(null),c=l.createContext({}),u=n(50793),d=n(25833),f=n(41229),p=n.n(f),h=n(69211),m=n(46676),g=n(81541),v=n(95504),y=n(22878),b=["prefixCls","className","containerRef"],x=function(e){var t=e.prefixCls,n=e.className,r=e.containerRef,i=(0,v.Z)(e,b),o=l.useContext(c).panel,a=(0,y.x1)(o,r);return l.createElement("div",(0,d.Z)({className:p()("".concat(t,"-content"),n),role:"dialog",ref:a},(0,g.Z)(e,{aria:!0}),{"aria-modal":"true"},i))},Z=n(37326);function C(e){return"string"==typeof e&&String(Number(e))===e?((0,Z.ZP)(!1,"Invalid value type of `width` or `height` which should be number type instead."),Number(e)):e}var S={width:0,height:0,overflow:"hidden",outline:"none",position:"absolute"},w=l.forwardRef(function(e,t){var n,o,a,c=e.prefixCls,f=e.open,v=e.placement,y=e.inline,b=e.push,Z=e.forceRender,w=e.autoFocus,k=e.keyboard,M=e.classNames,F=e.rootClassName,T=e.rootStyle,P=e.zIndex,E=e.className,R=e.id,I=e.style,j=e.motion,L=e.width,A=e.height,O=e.children,N=e.mask,D=e.maskClosable,B=e.maskMotion,_=e.maskClassName,H=e.maskStyle,z=e.afterOpenChange,V=e.onClose,W=e.onMouseEnter,$=e.onMouseOver,K=e.onMouseLeave,U=e.onClick,q=e.onKeyDown,Y=e.onKeyUp,X=e.styles,G=e.drawerRender,Q=l.useRef(),J=l.useRef(),ee=l.useRef();l.useImperativeHandle(t,function(){return Q.current}),l.useEffect(function(){if(f&&w){var e;null==(e=Q.current)||e.focus({preventScroll:!0})}},[f]);var et=l.useState(!1),en=(0,i.Z)(et,2),er=en[0],ei=en[1],eo=l.useContext(s),ea=null!=(n=null!=(o=null==(a="boolean"==typeof b?b?{}:{distance:0}:b||{})?void 0:a.distance)?o:null==eo?void 0:eo.pushDistance)?n:180,el=l.useMemo(function(){return{pushDistance:ea,push:function(){ei(!0)},pull:function(){ei(!1)}}},[ea]);l.useEffect(function(){var e,t;f?null==eo||null==(e=eo.push)||e.call(eo):null==eo||null==(t=eo.pull)||t.call(eo)},[f]),l.useEffect(function(){return function(){var e;null==eo||null==(e=eo.pull)||e.call(eo)}},[]);var es=N&&l.createElement(h.ZP,(0,d.Z)({key:"mask"},B,{visible:f}),function(e,t){var n=e.className,i=e.style;return l.createElement("div",{className:p()("".concat(c,"-mask"),n,null==M?void 0:M.mask,_),style:(0,r.Z)((0,r.Z)((0,r.Z)({},i),H),null==X?void 0:X.mask),onClick:D&&f?V:void 0,ref:t})}),ec="function"==typeof j?j(v):j,eu={};if(er&&ea)switch(v){case"top":eu.transform="translateY(".concat(ea,"px)");break;case"bottom":eu.transform="translateY(".concat(-ea,"px)");break;case"left":eu.transform="translateX(".concat(ea,"px)");break;default:eu.transform="translateX(".concat(-ea,"px)")}"left"===v||"right"===v?eu.width=C(L):eu.height=C(A);var ed={onMouseEnter:W,onMouseOver:$,onMouseLeave:K,onClick:U,onKeyDown:q,onKeyUp:Y},ef=l.createElement(h.ZP,(0,d.Z)({key:"panel"},ec,{visible:f,forceRender:Z,onVisibleChanged:function(e){null==z||z(e)},removeOnLeave:!1,leavedClassName:"".concat(c,"-content-wrapper-hidden")}),function(t,n){var i=t.className,o=t.style,a=l.createElement(x,(0,d.Z)({id:R,containerRef:n,prefixCls:c,className:p()(E,null==M?void 0:M.content),style:(0,r.Z)((0,r.Z)({},I),null==X?void 0:X.content)},(0,g.Z)(e,{aria:!0}),ed),O);return l.createElement("div",(0,d.Z)({className:p()("".concat(c,"-content-wrapper"),null==M?void 0:M.wrapper,i),style:(0,r.Z)((0,r.Z)((0,r.Z)({},eu),o),null==X?void 0:X.wrapper)},(0,g.Z)(e,{data:!0})),G?G(a):a)}),ep=(0,r.Z)({},T);return P&&(ep.zIndex=P),l.createElement(s.Provider,{value:el},l.createElement("div",{className:p()(c,"".concat(c,"-").concat(v),F,(0,u.Z)((0,u.Z)({},"".concat(c,"-open"),f),"".concat(c,"-inline"),y)),style:ep,tabIndex:-1,ref:Q,onKeyDown:function(e){var t,n,r=e.keyCode,i=e.shiftKey;switch(r){case m.Z.TAB:r===m.Z.TAB&&(i||document.activeElement!==ee.current?i&&document.activeElement===J.current&&(null==(n=ee.current)||n.focus({preventScroll:!0})):null==(t=J.current)||t.focus({preventScroll:!0}));break;case m.Z.ESC:V&&k&&(e.stopPropagation(),V(e))}}},es,l.createElement("div",{tabIndex:0,ref:J,style:S,"aria-hidden":"true","data-sentinel":"start"}),ef,l.createElement("div",{tabIndex:0,ref:ee,style:S,"aria-hidden":"true","data-sentinel":"end"})))}),k=function(e){var t=e.open,n=e.prefixCls,s=e.placement,u=e.autoFocus,d=e.keyboard,f=e.width,p=e.mask,h=void 0===p||p,m=e.maskClosable,g=e.getContainer,v=e.forceRender,y=e.afterOpenChange,b=e.destroyOnClose,x=e.onMouseEnter,Z=e.onMouseOver,C=e.onMouseLeave,S=e.onClick,k=e.onKeyDown,M=e.onKeyUp,F=e.panelRef,T=l.useState(!1),P=(0,i.Z)(T,2),E=P[0],R=P[1],I=l.useState(!1),j=(0,i.Z)(I,2),L=j[0],A=j[1];(0,a.Z)(function(){A(!0)},[]);var O=!!L&&void 0!==t&&t,N=l.useRef(),D=l.useRef();(0,a.Z)(function(){O&&(D.current=document.activeElement)},[O]);var B=l.useMemo(function(){return{panel:F}},[F]);if(!v&&!E&&!O&&b)return null;var _=(0,r.Z)((0,r.Z)({},e),{},{open:O,prefixCls:void 0===n?"rc-drawer":n,placement:void 0===s?"right":s,autoFocus:void 0===u||u,keyboard:void 0===d||d,width:void 0===f?378:f,mask:h,maskClosable:void 0===m||m,inline:!1===g,afterOpenChange:function(e){var t,n;R(e),null==y||y(e),e||!D.current||null!=(t=N.current)&&t.contains(D.current)||null==(n=D.current)||n.focus({preventScroll:!0})},ref:N},{onMouseEnter:x,onMouseOver:Z,onMouseLeave:C,onClick:S,onKeyDown:k,onKeyUp:M});return l.createElement(c.Provider,{value:B},l.createElement(o.Z,{open:O||v||E,autoDestroy:!1,getContainer:g,autoLock:h&&(O||E)},l.createElement(w,_)))}},13337:function(e,t,n){"use strict";n.d(t,{aV:()=>em,zb:()=>C,gN:()=>eh,ZM:()=>S,qo:()=>eT,RV:()=>ew,cI:()=>eC,ZP:()=>eE});var r,i=n(52983),o=n(25833),a=n(95504),l=n(30104),s=n(63587),c=n(19741),u=n(81632),d=n(71123),f=n(83739),p=n(9078),h=n(14425),m=n(81087),g=n(50793),v=n(56256),y=n(70198),b=n(37326),x="RC_FORM_INTERNAL_HOOKS",Z=function(){(0,b.ZP)(!1,"Can not find FormContext. Please make sure you wrap Field under Form.")},C=i.createContext({getFieldValue:Z,getFieldsValue:Z,getFieldError:Z,getFieldWarning:Z,getFieldsError:Z,isFieldsTouched:Z,isFieldTouched:Z,isFieldValidating:Z,isFieldsValidating:Z,resetFields:Z,setFields:Z,setFieldValue:Z,setFieldsValue:Z,validateFields:Z,submit:Z,getInternalHooks:function(){return Z(),{dispatch:Z,initEntityValue:Z,registerField:Z,useSubscribe:Z,setInitialValues:Z,destroyForm:Z,setCallbacks:Z,registerWatch:Z,getFields:Z,setValidateMessages:Z,setPreserve:Z,getInitialValue:Z}}}),S=i.createContext(null);function w(e){return null==e?[]:Array.isArray(e)?e:[e]}var k=n(52655);function M(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var F=M(),T=n(77335),P=n(11479),E=n(56397);function R(e){var t="function"==typeof Map?new Map:void 0;return(R=function(e){if(null===e||!function(e){try{return -1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return function(e,t,n){if((0,E.Z)())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,t);var i=new(e.bind.apply(e,r));return n&&(0,P.Z)(i,n.prototype),i}(e,arguments,(0,T.Z)(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),(0,P.Z)(n,e)})(e)}var I=/%[sdj%]/g;function j(e){if(!e||!e.length)return null;var t={};return e.forEach(function(e){var n=e.field;t[n]=t[n]||[],t[n].push(e)}),t}function L(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var i=0,o=n.length;return"function"==typeof e?e.apply(null,n):"string"==typeof e?e.replace(I,function(e){if("%%"===e)return"%";if(i>=o)return e;switch(e){case"%s":return String(n[i++]);case"%d":return Number(n[i++]);case"%j":try{return JSON.stringify(n[i++])}catch(e){return"[Circular]"}default:return e}}):e}function A(e,t){return!!(null==e||"array"===t&&Array.isArray(e)&&!e.length)||("string"===t||"url"===t||"hex"===t||"email"===t||"date"===t||"pattern"===t)&&"string"==typeof e&&!e||!1}function O(e,t,n){var r=0,i=e.length;!function o(a){if(a&&a.length)return void n(a);var l=r;r+=1,l<i?t(e[l],o):n([])}([])}"undefined"!=typeof process&&process.env;var N=function(e){(0,h.Z)(n,e);var t=(0,m.Z)(n);function n(e,r){var i;return(0,d.Z)(this,n),i=t.call(this,"Async Validation Error"),(0,g.Z)((0,p.Z)(i),"errors",void 0),(0,g.Z)((0,p.Z)(i),"fields",void 0),i.errors=e,i.fields=r,i}return(0,f.Z)(n)}(R(Error));function D(e,t){return function(n){var r;return(r=e.fullFields?function(e,t){for(var n=e,r=0;r<t.length&&void 0!=n;r++)n=n[t[r]];return n}(t,e.fullFields):t[n.field||e.fullField],n&&void 0!==n.message)?(n.field=n.field||e.fullField,n.fieldValue=r,n):{message:"function"==typeof n?n():n,fieldValue:r,field:n.field||e.fullField}}}function B(e,t){if(t){for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];"object"===(0,k.Z)(r)&&"object"===(0,k.Z)(e[n])?e[n]=(0,c.Z)((0,c.Z)({},e[n]),r):e[n]=r}}return e}var _="enum",H=function(e,t,n,r,i,o){e.required&&(!n.hasOwnProperty(e.field)||A(t,o||e.type))&&r.push(L(i.messages.required,e.fullField))},z=function(){if(r)return r;var e="[a-fA-F\\d:]",t=function(t){return t&&t.includeBoundaries?"(?:(?<=\\s|^)(?=".concat(e,")|(?<=").concat(e,")(?=\\s|$))"):""},n="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",i="[a-fA-F\\d]{1,4}",o=["(?:".concat(i,":){7}(?:").concat(i,"|:)"),"(?:".concat(i,":){6}(?:").concat(n,"|:").concat(i,"|:)"),"(?:".concat(i,":){5}(?::").concat(n,"|(?::").concat(i,"){1,2}|:)"),"(?:".concat(i,":){4}(?:(?::").concat(i,"){0,1}:").concat(n,"|(?::").concat(i,"){1,3}|:)"),"(?:".concat(i,":){3}(?:(?::").concat(i,"){0,2}:").concat(n,"|(?::").concat(i,"){1,4}|:)"),"(?:".concat(i,":){2}(?:(?::").concat(i,"){0,3}:").concat(n,"|(?::").concat(i,"){1,5}|:)"),"(?:".concat(i,":){1}(?:(?::").concat(i,"){0,4}:").concat(n,"|(?::").concat(i,"){1,6}|:)"),"(?::(?:(?::".concat(i,"){0,5}:").concat(n,"|(?::").concat(i,"){1,7}|:))")],a="(?:".concat(o.join("|"),")").concat("(?:%[0-9a-zA-Z]{1,})?"),l=new RegExp("(?:^".concat(n,"$)|(?:^").concat(a,"$)")),s=new RegExp("^".concat(n,"$")),c=new RegExp("^".concat(a,"$")),u=function(e){return e&&e.exact?l:RegExp("(?:".concat(t(e)).concat(n).concat(t(e),")|(?:").concat(t(e)).concat(a).concat(t(e),")"),"g")};u.v4=function(e){return e&&e.exact?s:RegExp("".concat(t(e)).concat(n).concat(t(e)),"g")},u.v6=function(e){return e&&e.exact?c:RegExp("".concat(t(e)).concat(a).concat(t(e)),"g")};var d=u.v4().source,f=u.v6().source,p="(?:".concat("(?:(?:[a-z]+:)?//)","|www\\.)").concat("(?:\\S+(?::\\S*)?@)?","(?:localhost|").concat(d,"|").concat(f,"|").concat("(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)").concat("(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*").concat("(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",")").concat("(?::\\d{2,5})?").concat('(?:[/?#][^\\s"]*)?');return r=RegExp("(?:^".concat(p,"$)"),"i")},V={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},W={integer:function(e){return W.number(e)&&parseInt(e,10)===e},float:function(e){return W.number(e)&&!W.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return new RegExp(e),!0}catch(e){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear&&!isNaN(e.getTime())},number:function(e){return!isNaN(e)&&"number"==typeof e},object:function(e){return"object"===(0,k.Z)(e)&&!W.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&e.length<=320&&!!e.match(V.email)},url:function(e){return"string"==typeof e&&e.length<=2048&&!!e.match(z())},hex:function(e){return"string"==typeof e&&!!e.match(V.hex)}},$={required:H,whitespace:function(e,t,n,r,i){(/^\s+$/.test(t)||""===t)&&r.push(L(i.messages.whitespace,e.fullField))},type:function(e,t,n,r,i){if(e.required&&void 0===t)return void H(e,t,n,r,i);var o=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(o)>-1?W[o](t)||r.push(L(i.messages.types[o],e.fullField,e.type)):o&&(0,k.Z)(t)!==e.type&&r.push(L(i.messages.types[o],e.fullField,e.type))},range:function(e,t,n,r,i){var o="number"==typeof e.len,a="number"==typeof e.min,l="number"==typeof e.max,s=t,c=null,u="number"==typeof t,d="string"==typeof t,f=Array.isArray(t);if(u?c="number":d?c="string":f&&(c="array"),!c)return!1;f&&(s=t.length),d&&(s=t.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"_").length),o?s!==e.len&&r.push(L(i.messages[c].len,e.fullField,e.len)):a&&!l&&s<e.min?r.push(L(i.messages[c].min,e.fullField,e.min)):l&&!a&&s>e.max?r.push(L(i.messages[c].max,e.fullField,e.max)):a&&l&&(s<e.min||s>e.max)&&r.push(L(i.messages[c].range,e.fullField,e.min,e.max))},enum:function(e,t,n,r,i){e[_]=Array.isArray(e[_])?e[_]:[],-1===e[_].indexOf(t)&&r.push(L(i.messages[_],e.fullField,e[_].join(", ")))},pattern:function(e,t,n,r,i){e.pattern&&(e.pattern instanceof RegExp?(e.pattern.lastIndex=0,e.pattern.test(t)||r.push(L(i.messages.pattern.mismatch,e.fullField,t,e.pattern))):"string"==typeof e.pattern&&(new RegExp(e.pattern).test(t)||r.push(L(i.messages.pattern.mismatch,e.fullField,t,e.pattern))))}},K=function(e,t,n,r,i){var o=e.type,a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t,o)&&!e.required)return n();$.required(e,t,r,a,i,o),A(t,o)||$.type(e,t,r,a,i)}n(a)},U={string:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t,"string")&&!e.required)return n();$.required(e,t,r,o,i,"string"),A(t,"string")||($.type(e,t,r,o,i),$.range(e,t,r,o,i),$.pattern(e,t,r,o,i),!0===e.whitespace&&$.whitespace(e,t,r,o,i))}n(o)},method:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t)&&!e.required)return n();$.required(e,t,r,o,i),void 0!==t&&$.type(e,t,r,o,i)}n(o)},number:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(""===t&&(t=void 0),A(t)&&!e.required)return n();$.required(e,t,r,o,i),void 0!==t&&($.type(e,t,r,o,i),$.range(e,t,r,o,i))}n(o)},boolean:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t)&&!e.required)return n();$.required(e,t,r,o,i),void 0!==t&&$.type(e,t,r,o,i)}n(o)},regexp:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t)&&!e.required)return n();$.required(e,t,r,o,i),A(t)||$.type(e,t,r,o,i)}n(o)},integer:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t)&&!e.required)return n();$.required(e,t,r,o,i),void 0!==t&&($.type(e,t,r,o,i),$.range(e,t,r,o,i))}n(o)},float:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t)&&!e.required)return n();$.required(e,t,r,o,i),void 0!==t&&($.type(e,t,r,o,i),$.range(e,t,r,o,i))}n(o)},array:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(null==t&&!e.required)return n();$.required(e,t,r,o,i,"array"),null!=t&&($.type(e,t,r,o,i),$.range(e,t,r,o,i))}n(o)},object:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t)&&!e.required)return n();$.required(e,t,r,o,i),void 0!==t&&$.type(e,t,r,o,i)}n(o)},enum:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t)&&!e.required)return n();$.required(e,t,r,o,i),void 0!==t&&$.enum(e,t,r,o,i)}n(o)},pattern:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t,"string")&&!e.required)return n();$.required(e,t,r,o,i),A(t,"string")||$.pattern(e,t,r,o,i)}n(o)},date:function(e,t,n,r,i){var o,a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t,"date")&&!e.required)return n();$.required(e,t,r,a,i),!A(t,"date")&&(o=t instanceof Date?t:new Date(t),$.type(e,o,r,a,i),o&&$.range(e,o.getTime(),r,a,i))}n(a)},url:K,hex:K,email:K,required:function(e,t,n,r,i){var o=[],a=Array.isArray(t)?"array":(0,k.Z)(t);$.required(e,t,r,o,i,a),n(o)},any:function(e,t,n,r,i){var o=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(A(t)&&!e.required)return n();$.required(e,t,r,o,i)}n(o)}},q=function(){function e(t){(0,d.Z)(this,e),(0,g.Z)(this,"rules",null),(0,g.Z)(this,"_messages",F),this.define(t)}return(0,f.Z)(e,[{key:"define",value:function(e){var t=this;if(!e)throw Error("Cannot configure a schema with no rules");if("object"!==(0,k.Z)(e)||Array.isArray(e))throw Error("Rules must be an object");this.rules={},Object.keys(e).forEach(function(n){var r=e[n];t.rules[n]=Array.isArray(r)?r:[r]})}},{key:"messages",value:function(e){return e&&(this._messages=B(M(),e)),this._messages}},{key:"validate",value:function(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){},o=t,a=r,l=i;if("function"==typeof a&&(l=a,a={}),!this.rules||0===Object.keys(this.rules).length)return l&&l(null,o),Promise.resolve(o);if(a.messages){var s=this.messages();s===F&&(s=M()),B(s,a.messages),a.messages=s}else a.messages=this.messages();var d={};(a.keys||Object.keys(this.rules)).forEach(function(e){var r=n.rules[e],i=o[e];r.forEach(function(r){var a=r;"function"==typeof a.transform&&(o===t&&(o=(0,c.Z)({},o)),null!=(i=o[e]=a.transform(i))&&(a.type=a.type||(Array.isArray(i)?"array":(0,k.Z)(i)))),(a="function"==typeof a?{validator:a}:(0,c.Z)({},a)).validator=n.getValidationMethod(a),a.validator&&(a.field=e,a.fullField=a.fullField||e,a.type=n.getType(a),d[e]=d[e]||[],d[e].push({rule:a,value:i,source:o,field:e}))})});var f={};return function(e,t,n,r,i){if(t.first){var o=new Promise(function(t,o){var a;O((a=[],Object.keys(e).forEach(function(t){a.push.apply(a,(0,u.Z)(e[t]||[]))}),a),n,function(e){return r(e),e.length?o(new N(e,j(e))):t(i)})});return o.catch(function(e){return e}),o}var a=!0===t.firstFields?Object.keys(e):t.firstFields||[],l=Object.keys(e),s=l.length,c=0,d=[],f=new Promise(function(t,o){var f=function(e){if(d.push.apply(d,e),++c===s)return r(d),d.length?o(new N(d,j(d))):t(i)};l.length||(r(d),t(i)),l.forEach(function(t){var r=e[t];if(-1!==a.indexOf(t))O(r,n,f);else{var i=[],o=0,l=r.length;function s(e){i.push.apply(i,(0,u.Z)(e||[])),++o===l&&f(i)}r.forEach(function(e){n(e,s)})}})});return f.catch(function(e){return e}),f}(d,a,function(t,n){var r,i,l,s=t.rule,d=("object"===s.type||"array"===s.type)&&("object"===(0,k.Z)(s.fields)||"object"===(0,k.Z)(s.defaultField));function p(e,t){return(0,c.Z)((0,c.Z)({},t),{},{fullField:"".concat(s.fullField,".").concat(e),fullFields:s.fullFields?[].concat((0,u.Z)(s.fullFields),[e]):[e]})}function h(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=Array.isArray(r)?r:[r];!a.suppressWarning&&i.length&&e.warning("async-validator:",i),i.length&&void 0!==s.message&&(i=[].concat(s.message));var l=i.map(D(s,o));if(a.first&&l.length)return f[s.field]=1,n(l);if(d){if(s.required&&!t.value)return void 0!==s.message?l=[].concat(s.message).map(D(s,o)):a.error&&(l=[a.error(s,L(a.messages.required,s.field))]),n(l);var h={};s.defaultField&&Object.keys(t.value).map(function(e){h[e]=s.defaultField});var m={};Object.keys(h=(0,c.Z)((0,c.Z)({},h),t.rule.fields)).forEach(function(e){var t=h[e],n=Array.isArray(t)?t:[t];m[e]=n.map(p.bind(null,e))});var g=new e(m);g.messages(a.messages),t.rule.options&&(t.rule.options.messages=a.messages,t.rule.options.error=a.error),g.validate(t.value,t.rule.options||a,function(e){var t=[];l&&l.length&&t.push.apply(t,(0,u.Z)(l)),e&&e.length&&t.push.apply(t,(0,u.Z)(e)),n(t.length?t:null)})}else n(l)}if(d=d&&(s.required||!s.required&&t.value),s.field=t.field,s.asyncValidator)r=s.asyncValidator(s,t.value,h,t.source,a);else if(s.validator){try{r=s.validator(s,t.value,h,t.source,a)}catch(e){null==(i=(l=console).error)||i.call(l,e),a.suppressValidatorError||setTimeout(function(){throw e},0),h(e.message)}!0===r?h():!1===r?h("function"==typeof s.message?s.message(s.fullField||s.field):s.message||"".concat(s.fullField||s.field," fails")):r instanceof Array?h(r):r instanceof Error&&h(r.message)}r&&r.then&&r.then(function(){return h()},function(e){return h(e)})},function(e){!function(e){for(var t=[],n={},r=0;r<e.length;r++){var i,a=e[r];Array.isArray(a)?t=(i=t).concat.apply(i,(0,u.Z)(a)):t.push(a)}t.length?(n=j(t),l(t,n)):l(null,o)}(e)},o)}},{key:"getType",value:function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!U.hasOwnProperty(e.type))throw Error(L("Unknown rule type %s",e.type));return e.type||"string"}},{key:"getValidationMethod",value:function(e){if("function"==typeof e.validator)return e.validator;var t=Object.keys(e),n=t.indexOf("message");return(-1!==n&&t.splice(n,1),1===t.length&&"required"===t[0])?U.required:U[this.getType(e)]||void 0}}]),e}();(0,g.Z)(q,"register",function(e,t){if("function"!=typeof t)throw Error("Cannot register a validator by type, validator is not a function");U[e]=t}),(0,g.Z)(q,"warning",function(){}),(0,g.Z)(q,"messages",F),(0,g.Z)(q,"validators",U);var Y="'${name}' is not a valid ${type}",X={default:"Validation error on field '${name}'",required:"'${name}' is required",enum:"'${name}' must be one of [${enum}]",whitespace:"'${name}' cannot be empty",date:{format:"'${name}' is invalid for format date",parse:"'${name}' could not be parsed as date",invalid:"'${name}' is invalid date"},types:{string:Y,method:Y,array:Y,object:Y,number:Y,date:Y,boolean:Y,integer:Y,float:Y,regexp:Y,email:Y,url:Y,hex:Y},string:{len:"'${name}' must be exactly ${len} characters",min:"'${name}' must be at least ${min} characters",max:"'${name}' cannot be longer than ${max} characters",range:"'${name}' must be between ${min} and ${max} characters"},number:{len:"'${name}' must equal ${len}",min:"'${name}' cannot be less than ${min}",max:"'${name}' cannot be greater than ${max}",range:"'${name}' must be between ${min} and ${max}"},array:{len:"'${name}' must be exactly ${len} in length",min:"'${name}' cannot be less than ${min} in length",max:"'${name}' cannot be greater than ${max} in length",range:"'${name}' must be between ${min} and ${max} in length"},pattern:{mismatch:"'${name}' does not match pattern ${pattern}"}},G=n(22477),Q="CODE_LOGIC_ERROR";function J(e,t,n,r,i){return ee.apply(this,arguments)}function ee(){return(ee=(0,s.Z)((0,l.Z)().mark(function e(t,n,r,o,a){var s,d,f,p,h,m,v,y,b;return(0,l.Z)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=(0,c.Z)({},r),delete s.ruleIndex,q.warning=function(){},s.validator&&(d=s.validator,s.validator=function(){try{return d.apply(void 0,arguments)}catch(e){return console.error(e),Promise.reject(Q)}}),f=null,s&&"array"===s.type&&s.defaultField&&(f=s.defaultField,delete s.defaultField),p=new q((0,g.Z)({},t,[s])),h=(0,G.T)(X,o.validateMessages),p.messages(h),m=[],e.prev=10,e.next=13,Promise.resolve(p.validate((0,g.Z)({},t,n),(0,c.Z)({},o)));case 13:e.next=18;break;case 15:e.prev=15,e.t0=e.catch(10),e.t0.errors&&(m=e.t0.errors.map(function(e,t){var n=e.message,r=n===Q?h.default:n;return i.isValidElement(r)?i.cloneElement(r,{key:"error_".concat(t)}):r}));case 18:if(!(!m.length&&f)){e.next=23;break}return e.next=21,Promise.all(n.map(function(e,n){return J("".concat(t,".").concat(n),e,f,o,a)}));case 21:return v=e.sent,e.abrupt("return",v.reduce(function(e,t){return[].concat((0,u.Z)(e),(0,u.Z)(t))},[]));case 23:return y=(0,c.Z)((0,c.Z)({},r),{},{name:t,enum:(r.enum||[]).join(", ")},a),b=m.map(function(e){return"string"==typeof e?function(e,t){return e.replace(/\\?\$\{\w+\}/g,function(e){return e.startsWith("\\")?e.slice(1):t[e.slice(2,-1)]})}(e,y):e}),e.abrupt("return",b);case 26:case"end":return e.stop()}},e,null,[[10,15]])}))).apply(this,arguments)}function et(){return(et=(0,s.Z)((0,l.Z)().mark(function e(t){return(0,l.Z)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",Promise.all(t).then(function(e){var t;return(t=[]).concat.apply(t,(0,u.Z)(e))}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function en(){return(en=(0,s.Z)((0,l.Z)().mark(function e(t){var n;return(0,l.Z)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=0,e.abrupt("return",new Promise(function(e){t.forEach(function(r){r.then(function(r){r.errors.length&&e([r]),(n+=1)===t.length&&e([])})})}));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}var er=n(17829);function ei(e){return w(e)}function eo(e,t){var n={};return t.forEach(function(t){var r=(0,er.Z)(e,t);n=(0,G.Z)(n,t,r)}),n}function ea(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return e&&e.some(function(e){return el(t,e,n)})}function el(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return!!e&&!!t&&(!!n||e.length===t.length)&&t.every(function(t,n){return e[n]===t})}function es(e){var t=arguments.length<=1?void 0:arguments[1];return t&&t.target&&"object"===(0,k.Z)(t.target)&&e in t.target?t.target[e]:t}function ec(e,t,n){var r=e.length;if(t<0||t>=r||n<0||n>=r)return e;var i=e[t],o=t-n;return o>0?[].concat((0,u.Z)(e.slice(0,n)),[i],(0,u.Z)(e.slice(n,t)),(0,u.Z)(e.slice(t+1,r))):o<0?[].concat((0,u.Z)(e.slice(0,t)),(0,u.Z)(e.slice(t+1,n+1)),[i],(0,u.Z)(e.slice(n+1,r))):e}var eu=["name"],ed=[];function ef(e,t,n,r,i,o){return"function"==typeof e?e(t,n,"source"in o?{source:o.source}:{}):r!==i}var ep=function(e){(0,h.Z)(n,e);var t=(0,m.Z)(n);function n(e){var r;return(0,d.Z)(this,n),r=t.call(this,e),(0,g.Z)((0,p.Z)(r),"state",{resetCount:0}),(0,g.Z)((0,p.Z)(r),"cancelRegisterFunc",null),(0,g.Z)((0,p.Z)(r),"mounted",!1),(0,g.Z)((0,p.Z)(r),"touched",!1),(0,g.Z)((0,p.Z)(r),"dirty",!1),(0,g.Z)((0,p.Z)(r),"validatePromise",void 0),(0,g.Z)((0,p.Z)(r),"prevValidating",void 0),(0,g.Z)((0,p.Z)(r),"errors",ed),(0,g.Z)((0,p.Z)(r),"warnings",ed),(0,g.Z)((0,p.Z)(r),"cancelRegister",function(){var e=r.props,t=e.preserve,n=e.isListField,i=e.name;r.cancelRegisterFunc&&r.cancelRegisterFunc(n,t,ei(i)),r.cancelRegisterFunc=null}),(0,g.Z)((0,p.Z)(r),"getNamePath",function(){var e=r.props,t=e.name,n=e.fieldContext.prefixName;return void 0!==t?[].concat((0,u.Z)(void 0===n?[]:n),(0,u.Z)(t)):[]}),(0,g.Z)((0,p.Z)(r),"getRules",function(){var e=r.props,t=e.rules,n=e.fieldContext;return(void 0===t?[]:t).map(function(e){return"function"==typeof e?e(n):e})}),(0,g.Z)((0,p.Z)(r),"refresh",function(){r.mounted&&r.setState(function(e){return{resetCount:e.resetCount+1}})}),(0,g.Z)((0,p.Z)(r),"metaCache",null),(0,g.Z)((0,p.Z)(r),"triggerMetaEvent",function(e){var t=r.props.onMetaChange;if(t){var n=(0,c.Z)((0,c.Z)({},r.getMeta()),{},{destroy:e});(0,y.Z)(r.metaCache,n)||t(n),r.metaCache=n}else r.metaCache=null}),(0,g.Z)((0,p.Z)(r),"onStoreChange",function(e,t,n){var i=r.props,o=i.shouldUpdate,a=i.dependencies,l=void 0===a?[]:a,s=i.onReset,c=n.store,u=r.getNamePath(),d=r.getValue(e),f=r.getValue(c),p=t&&ea(t,u);switch("valueUpdate"===n.type&&"external"===n.source&&!(0,y.Z)(d,f)&&(r.touched=!0,r.dirty=!0,r.validatePromise=null,r.errors=ed,r.warnings=ed,r.triggerMetaEvent()),n.type){case"reset":if(!t||p){r.touched=!1,r.dirty=!1,r.validatePromise=void 0,r.errors=ed,r.warnings=ed,r.triggerMetaEvent(),null==s||s(),r.refresh();return}break;case"remove":if(o&&ef(o,e,c,d,f,n))return void r.reRender();break;case"setField":var h=n.data;if(p){"touched"in h&&(r.touched=h.touched),"validating"in h&&!("originRCField"in h)&&(r.validatePromise=h.validating?Promise.resolve([]):null),"errors"in h&&(r.errors=h.errors||ed),"warnings"in h&&(r.warnings=h.warnings||ed),r.dirty=!0,r.triggerMetaEvent(),r.reRender();return}if("value"in h&&ea(t,u,!0)||o&&!u.length&&ef(o,e,c,d,f,n))return void r.reRender();break;case"dependenciesUpdate":if(l.map(ei).some(function(e){return ea(n.relatedFields,e)}))return void r.reRender();break;default:if(p||(!l.length||u.length||o)&&ef(o,e,c,d,f,n))return void r.reRender()}!0===o&&r.reRender()}),(0,g.Z)((0,p.Z)(r),"validateRules",function(e){var t=r.getNamePath(),n=r.getValue(),i=e||{},o=i.triggerName,a=i.validateOnly,d=Promise.resolve().then((0,s.Z)((0,l.Z)().mark(function i(){var a,f,p,h,m,g,v;return(0,l.Z)().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:if(r.mounted){i.next=2;break}return i.abrupt("return",[]);case 2:if(p=void 0!==(f=(a=r.props).validateFirst)&&f,h=a.messageVariables,m=a.validateDebounce,g=r.getRules(),o&&(g=g.filter(function(e){return e}).filter(function(e){var t=e.validateTrigger;return!t||w(t).includes(o)})),!(m&&o)){i.next=10;break}return i.next=8,new Promise(function(e){setTimeout(e,m)});case 8:if(r.validatePromise===d){i.next=10;break}return i.abrupt("return",[]);case 10:return(v=function(e,t,n,r,i,o){var a,u,d=e.join("."),f=n.map(function(e,t){var n=e.validator,r=(0,c.Z)((0,c.Z)({},e),{},{ruleIndex:t});return n&&(r.validator=function(e,t,r){var i=!1,o=n(e,t,function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];Promise.resolve().then(function(){(0,b.ZP)(!i,"Your validator function has already return a promise. `callback` will be ignored."),i||r.apply(void 0,t)})});i=o&&"function"==typeof o.then&&"function"==typeof o.catch,(0,b.ZP)(i,"`callback` is deprecated. Please return a promise instead."),i&&o.then(function(){r()}).catch(function(e){r(e||" ")})}),r}).sort(function(e,t){var n=e.warningOnly,r=e.ruleIndex,i=t.warningOnly,o=t.ruleIndex;return!!n==!!i?r-o:n?1:-1});if(!0===i)u=new Promise((a=(0,s.Z)((0,l.Z)().mark(function e(n,i){var a,s,c;return(0,l.Z)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:a=0;case 1:if(!(a<f.length)){e.next=12;break}return s=f[a],e.next=5,J(d,t,s,r,o);case 5:if(!(c=e.sent).length){e.next=9;break}return i([{errors:c,rule:s}]),e.abrupt("return");case 9:a+=1,e.next=1;break;case 12:n([]);case 13:case"end":return e.stop()}},e)})),function(e,t){return a.apply(this,arguments)}));else{var p=f.map(function(e){return J(d,t,e,r,o).then(function(t){return{errors:t,rule:e}})});u=(i?function(e){return en.apply(this,arguments)}(p):function(e){return et.apply(this,arguments)}(p)).then(function(e){return Promise.reject(e)})}return u.catch(function(e){return e}),u}(t,n,g,e,p,h)).catch(function(e){return e}).then(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ed;if(r.validatePromise===d){r.validatePromise=null;var t,n=[],i=[];null==(t=e.forEach)||t.call(e,function(e){var t=e.rule.warningOnly,r=e.errors,o=void 0===r?ed:r;t?i.push.apply(i,(0,u.Z)(o)):n.push.apply(n,(0,u.Z)(o))}),r.errors=n,r.warnings=i,r.triggerMetaEvent(),r.reRender()}}),i.abrupt("return",v);case 13:case"end":return i.stop()}},i)})));return void 0!==a&&a||(r.validatePromise=d,r.dirty=!0,r.errors=ed,r.warnings=ed,r.triggerMetaEvent(),r.reRender()),d}),(0,g.Z)((0,p.Z)(r),"isFieldValidating",function(){return!!r.validatePromise}),(0,g.Z)((0,p.Z)(r),"isFieldTouched",function(){return r.touched}),(0,g.Z)((0,p.Z)(r),"isFieldDirty",function(){return!!r.dirty||void 0!==r.props.initialValue||void 0!==(0,r.props.fieldContext.getInternalHooks(x).getInitialValue)(r.getNamePath())}),(0,g.Z)((0,p.Z)(r),"getErrors",function(){return r.errors}),(0,g.Z)((0,p.Z)(r),"getWarnings",function(){return r.warnings}),(0,g.Z)((0,p.Z)(r),"isListField",function(){return r.props.isListField}),(0,g.Z)((0,p.Z)(r),"isList",function(){return r.props.isList}),(0,g.Z)((0,p.Z)(r),"isPreserve",function(){return r.props.preserve}),(0,g.Z)((0,p.Z)(r),"getMeta",function(){return r.prevValidating=r.isFieldValidating(),{touched:r.isFieldTouched(),validating:r.prevValidating,errors:r.errors,warnings:r.warnings,name:r.getNamePath(),validated:null===r.validatePromise}}),(0,g.Z)((0,p.Z)(r),"getOnlyChild",function(e){if("function"==typeof e){var t=r.getMeta();return(0,c.Z)((0,c.Z)({},r.getOnlyChild(e(r.getControlled(),t,r.props.fieldContext))),{},{isFunction:!0})}var n=(0,v.Z)(e);return 1===n.length&&i.isValidElement(n[0])?{child:n[0],isFunction:!1}:{child:n,isFunction:!1}}),(0,g.Z)((0,p.Z)(r),"getValue",function(e){var t=r.props.fieldContext.getFieldsValue,n=r.getNamePath();return(0,er.Z)(e||t(!0),n)}),(0,g.Z)((0,p.Z)(r),"getControlled",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=r.props,n=t.name,i=t.trigger,o=t.validateTrigger,a=t.getValueFromEvent,l=t.normalize,s=t.valuePropName,u=t.getValueProps,d=t.fieldContext,f=void 0!==o?o:d.validateTrigger,p=r.getNamePath(),h=d.getInternalHooks,m=d.getFieldsValue,v=h(x).dispatch,y=r.getValue(),b=u||function(e){return(0,g.Z)({},s,e)},Z=e[i],C=void 0!==n?b(y):{},S=(0,c.Z)((0,c.Z)({},e),C);return S[i]=function(){r.touched=!0,r.dirty=!0,r.triggerMetaEvent();for(var e,t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];e=a?a.apply(void 0,n):es.apply(void 0,[s].concat(n)),l&&(e=l(e,y,m(!0))),e!==y&&v({type:"updateValue",namePath:p,value:e}),Z&&Z.apply(void 0,n)},w(f||[]).forEach(function(e){var t=S[e];S[e]=function(){t&&t.apply(void 0,arguments);var n=r.props.rules;n&&n.length&&v({type:"validateField",namePath:p,triggerName:e})}}),S}),e.fieldContext&&(0,(0,e.fieldContext.getInternalHooks)(x).initEntityValue)((0,p.Z)(r)),r}return(0,f.Z)(n,[{key:"componentDidMount",value:function(){var e=this.props,t=e.shouldUpdate,n=e.fieldContext;if(this.mounted=!0,n){var r=(0,n.getInternalHooks)(x).registerField;this.cancelRegisterFunc=r(this)}!0===t&&this.reRender()}},{key:"componentWillUnmount",value:function(){this.cancelRegister(),this.triggerMetaEvent(!0),this.mounted=!1}},{key:"reRender",value:function(){this.mounted&&this.forceUpdate()}},{key:"render",value:function(){var e,t=this.state.resetCount,n=this.props.children,r=this.getOnlyChild(n),o=r.child;return r.isFunction?e=o:i.isValidElement(o)?e=i.cloneElement(o,this.getControlled(o.props)):((0,b.ZP)(!o,"`children` of Field is not validate ReactElement."),e=o),i.createElement(i.Fragment,{key:t},e)}}]),n}(i.Component);(0,g.Z)(ep,"contextType",C),(0,g.Z)(ep,"defaultProps",{trigger:"onChange",valuePropName:"value"});var eh=function(e){var t,n=e.name,r=(0,a.Z)(e,eu),l=i.useContext(C),s=i.useContext(S),c=void 0!==n?ei(n):void 0,u=null!=(t=r.isListField)?t:!!s,d="keep";return u||(d="_".concat((c||[]).join("_"))),i.createElement(ep,(0,o.Z)({key:d,name:c,isListField:u},r,{fieldContext:l}))},em=function(e){var t=e.name,n=e.initialValue,r=e.children,o=e.rules,a=e.validateTrigger,l=e.isListField,s=i.useContext(C),d=i.useContext(S),f=i.useRef({keys:[],id:0}).current,p=i.useMemo(function(){var e=ei(s.prefixName)||[];return[].concat((0,u.Z)(e),(0,u.Z)(ei(t)))},[s.prefixName,t]),h=i.useMemo(function(){return(0,c.Z)((0,c.Z)({},s),{},{prefixName:p})},[s,p]),m=i.useMemo(function(){return{getKey:function(e){var t=p.length,n=e[t];return[f.keys[n],e.slice(t+1)]}}},[p]);return"function"!=typeof r?((0,b.ZP)(!1,"Form.List only accepts function as children."),null):i.createElement(S.Provider,{value:m},i.createElement(C.Provider,{value:h},i.createElement(eh,{name:[],shouldUpdate:function(e,t,n){return"internal"!==n.source&&e!==t},rules:o,validateTrigger:a,initialValue:n,isList:!0,isListField:null!=l?l:!!d},function(e,t){var n=e.value,i=e.onChange,o=s.getFieldValue,a=function(){return o(p||[])||[]},l=(void 0===n?[]:n)||[];return Array.isArray(l)||(l=[]),r(l.map(function(e,t){var n=f.keys[t];return void 0===n&&(f.keys[t]=f.id,n=f.keys[t],f.id+=1),{name:t,key:n,isListField:!0}}),{add:function(e,t){var n=a();t>=0&&t<=n.length?(f.keys=[].concat((0,u.Z)(f.keys.slice(0,t)),[f.id],(0,u.Z)(f.keys.slice(t))),i([].concat((0,u.Z)(n.slice(0,t)),[e],(0,u.Z)(n.slice(t))))):(f.keys=[].concat((0,u.Z)(f.keys),[f.id]),i([].concat((0,u.Z)(n),[e]))),f.id+=1},remove:function(e){var t=a(),n=new Set(Array.isArray(e)?e:[e]);n.size<=0||(f.keys=f.keys.filter(function(e,t){return!n.has(t)}),i(t.filter(function(e,t){return!n.has(t)})))},move:function(e,t){if(e!==t){var n=a();e<0||e>=n.length||t<0||t>=n.length||(f.keys=ec(f.keys,e,t),i(ec(n,e,t)))}}},t)})))},eg=n(57987),ev="__@field_split__";function ey(e){return e.map(function(e){return"".concat((0,k.Z)(e),":").concat(e)}).join(ev)}var eb=function(){function e(){(0,d.Z)(this,e),(0,g.Z)(this,"kvs",new Map)}return(0,f.Z)(e,[{key:"set",value:function(e,t){this.kvs.set(ey(e),t)}},{key:"get",value:function(e){return this.kvs.get(ey(e))}},{key:"update",value:function(e,t){var n=t(this.get(e));n?this.set(e,n):this.delete(e)}},{key:"delete",value:function(e){this.kvs.delete(ey(e))}},{key:"map",value:function(e){return(0,u.Z)(this.kvs.entries()).map(function(t){var n=(0,eg.Z)(t,2),r=n[0],i=n[1];return e({key:r.split(ev).map(function(e){var t=e.match(/^([^:]*):(.*)$/),n=(0,eg.Z)(t,3),r=n[1],i=n[2];return"number"===r?Number(i):i}),value:i})})}},{key:"toJSON",value:function(){var e={};return this.map(function(t){var n=t.key,r=t.value;return e[n.join(".")]=r,null}),e}}]),e}(),ex=["name"],eZ=(0,f.Z)(function e(t){var n=this;(0,d.Z)(this,e),(0,g.Z)(this,"formHooked",!1),(0,g.Z)(this,"forceRootUpdate",void 0),(0,g.Z)(this,"subscribable",!0),(0,g.Z)(this,"store",{}),(0,g.Z)(this,"fieldEntities",[]),(0,g.Z)(this,"initialValues",{}),(0,g.Z)(this,"callbacks",{}),(0,g.Z)(this,"validateMessages",null),(0,g.Z)(this,"preserve",null),(0,g.Z)(this,"lastValidatePromise",null),(0,g.Z)(this,"getForm",function(){return{getFieldValue:n.getFieldValue,getFieldsValue:n.getFieldsValue,getFieldError:n.getFieldError,getFieldWarning:n.getFieldWarning,getFieldsError:n.getFieldsError,isFieldsTouched:n.isFieldsTouched,isFieldTouched:n.isFieldTouched,isFieldValidating:n.isFieldValidating,isFieldsValidating:n.isFieldsValidating,resetFields:n.resetFields,setFields:n.setFields,setFieldValue:n.setFieldValue,setFieldsValue:n.setFieldsValue,validateFields:n.validateFields,submit:n.submit,_init:!0,getInternalHooks:n.getInternalHooks}}),(0,g.Z)(this,"getInternalHooks",function(e){return e===x?(n.formHooked=!0,{dispatch:n.dispatch,initEntityValue:n.initEntityValue,registerField:n.registerField,useSubscribe:n.useSubscribe,setInitialValues:n.setInitialValues,destroyForm:n.destroyForm,setCallbacks:n.setCallbacks,setValidateMessages:n.setValidateMessages,getFields:n.getFields,setPreserve:n.setPreserve,getInitialValue:n.getInitialValue,registerWatch:n.registerWatch}):((0,b.ZP)(!1,"`getInternalHooks` is internal usage. Should not call directly."),null)}),(0,g.Z)(this,"useSubscribe",function(e){n.subscribable=e}),(0,g.Z)(this,"prevWithoutPreserves",null),(0,g.Z)(this,"setInitialValues",function(e,t){if(n.initialValues=e||{},t){var r,i=(0,G.T)(e,n.store);null==(r=n.prevWithoutPreserves)||r.map(function(t){var n=t.key;i=(0,G.Z)(i,n,(0,er.Z)(e,n))}),n.prevWithoutPreserves=null,n.updateStore(i)}}),(0,g.Z)(this,"destroyForm",function(e){if(e)n.updateStore({});else{var t=new eb;n.getFieldEntities(!0).forEach(function(e){n.isMergedPreserve(e.isPreserve())||t.set(e.getNamePath(),!0)}),n.prevWithoutPreserves=t}}),(0,g.Z)(this,"getInitialValue",function(e){var t=(0,er.Z)(n.initialValues,e);return e.length?(0,G.T)(t):t}),(0,g.Z)(this,"setCallbacks",function(e){n.callbacks=e}),(0,g.Z)(this,"setValidateMessages",function(e){n.validateMessages=e}),(0,g.Z)(this,"setPreserve",function(e){n.preserve=e}),(0,g.Z)(this,"watchList",[]),(0,g.Z)(this,"registerWatch",function(e){return n.watchList.push(e),function(){n.watchList=n.watchList.filter(function(t){return t!==e})}}),(0,g.Z)(this,"notifyWatch",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(n.watchList.length){var t=n.getFieldsValue(),r=n.getFieldsValue(!0);n.watchList.forEach(function(n){n(t,r,e)})}}),(0,g.Z)(this,"timeoutId",null),(0,g.Z)(this,"warningUnhooked",function(){}),(0,g.Z)(this,"updateStore",function(e){n.store=e}),(0,g.Z)(this,"getFieldEntities",function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return e?n.fieldEntities.filter(function(e){return e.getNamePath().length}):n.fieldEntities}),(0,g.Z)(this,"getFieldsMap",function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=new eb;return n.getFieldEntities(e).forEach(function(e){var n=e.getNamePath();t.set(n,e)}),t}),(0,g.Z)(this,"getFieldEntitiesForNamePathList",function(e){if(!e)return n.getFieldEntities(!0);var t=n.getFieldsMap(!0);return e.map(function(e){var n=ei(e);return t.get(n)||{INVALIDATE_NAME_PATH:ei(e)}})}),(0,g.Z)(this,"getFieldsValue",function(e,t){if(n.warningUnhooked(),!0===e||Array.isArray(e)?(r=e,i=t):e&&"object"===(0,k.Z)(e)&&(o=e.strict,i=e.filter),!0===r&&!i)return n.store;var r,i,o,a=n.getFieldEntitiesForNamePathList(Array.isArray(r)?r:null),l=[];return a.forEach(function(e){var t,n,a,s="INVALIDATE_NAME_PATH"in e?e.INVALIDATE_NAME_PATH:e.getNamePath();if(o){if(null!=(a=e.isList)&&a.call(e))return}else if(!r&&null!=(t=(n=e).isListField)&&t.call(n))return;if(i){var c="getMeta"in e?e.getMeta():null;i(c)&&l.push(s)}else l.push(s)}),eo(n.store,l.map(ei))}),(0,g.Z)(this,"getFieldValue",function(e){n.warningUnhooked();var t=ei(e);return(0,er.Z)(n.store,t)}),(0,g.Z)(this,"getFieldsError",function(e){return n.warningUnhooked(),n.getFieldEntitiesForNamePathList(e).map(function(t,n){return!t||"INVALIDATE_NAME_PATH"in t?{name:ei(e[n]),errors:[],warnings:[]}:{name:t.getNamePath(),errors:t.getErrors(),warnings:t.getWarnings()}})}),(0,g.Z)(this,"getFieldError",function(e){n.warningUnhooked();var t=ei(e);return n.getFieldsError([t])[0].errors}),(0,g.Z)(this,"getFieldWarning",function(e){n.warningUnhooked();var t=ei(e);return n.getFieldsError([t])[0].warnings}),(0,g.Z)(this,"isFieldsTouched",function(){n.warningUnhooked();for(var e,t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];var o=r[0],a=r[1],l=!1;0===r.length?e=null:1===r.length?Array.isArray(o)?(e=o.map(ei),l=!1):(e=null,l=o):(e=o.map(ei),l=a);var s=n.getFieldEntities(!0),c=function(e){return e.isFieldTouched()};if(!e)return l?s.every(function(e){return c(e)||e.isList()}):s.some(c);var d=new eb;e.forEach(function(e){d.set(e,[])}),s.forEach(function(t){var n=t.getNamePath();e.forEach(function(e){e.every(function(e,t){return n[t]===e})&&d.update(e,function(e){return[].concat((0,u.Z)(e),[t])})})});var f=function(e){return e.some(c)},p=d.map(function(e){return e.value});return l?p.every(f):p.some(f)}),(0,g.Z)(this,"isFieldTouched",function(e){return n.warningUnhooked(),n.isFieldsTouched([e])}),(0,g.Z)(this,"isFieldsValidating",function(e){n.warningUnhooked();var t=n.getFieldEntities();if(!e)return t.some(function(e){return e.isFieldValidating()});var r=e.map(ei);return t.some(function(e){return ea(r,e.getNamePath())&&e.isFieldValidating()})}),(0,g.Z)(this,"isFieldValidating",function(e){return n.warningUnhooked(),n.isFieldsValidating([e])}),(0,g.Z)(this,"resetWithFieldInitialValue",function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=new eb,i=n.getFieldEntities(!0);i.forEach(function(e){var t=e.props.initialValue,n=e.getNamePath();if(void 0!==t){var i=r.get(n)||new Set;i.add({entity:e,value:t}),r.set(n,i)}}),t.entities?e=t.entities:t.namePathList?(e=[],t.namePathList.forEach(function(t){var n,i=r.get(t);i&&(n=e).push.apply(n,(0,u.Z)((0,u.Z)(i).map(function(e){return e.entity})))})):e=i,e.forEach(function(e){if(void 0!==e.props.initialValue){var i=e.getNamePath();if(void 0!==n.getInitialValue(i))(0,b.ZP)(!1,"Form already set 'initialValues' with path '".concat(i.join("."),"'. Field can not overwrite it."));else{var o=r.get(i);if(o&&o.size>1)(0,b.ZP)(!1,"Multiple Field with path '".concat(i.join("."),"' set 'initialValue'. Can not decide which one to pick."));else if(o){var a=n.getFieldValue(i);e.isListField()||t.skipExist&&void 0!==a||n.updateStore((0,G.Z)(n.store,i,(0,u.Z)(o)[0].value))}}}})}),(0,g.Z)(this,"resetFields",function(e){n.warningUnhooked();var t=n.store;if(!e){n.updateStore((0,G.T)(n.initialValues)),n.resetWithFieldInitialValue(),n.notifyObservers(t,null,{type:"reset"}),n.notifyWatch();return}var r=e.map(ei);r.forEach(function(e){var t=n.getInitialValue(e);n.updateStore((0,G.Z)(n.store,e,t))}),n.resetWithFieldInitialValue({namePathList:r}),n.notifyObservers(t,r,{type:"reset"}),n.notifyWatch(r)}),(0,g.Z)(this,"setFields",function(e){n.warningUnhooked();var t=n.store,r=[];e.forEach(function(e){var i=e.name,o=(0,a.Z)(e,ex),l=ei(i);r.push(l),"value"in o&&n.updateStore((0,G.Z)(n.store,l,o.value)),n.notifyObservers(t,[l],{type:"setField",data:e})}),n.notifyWatch(r)}),(0,g.Z)(this,"getFields",function(){return n.getFieldEntities(!0).map(function(e){var t=e.getNamePath(),r=e.getMeta(),i=(0,c.Z)((0,c.Z)({},r),{},{name:t,value:n.getFieldValue(t)});return Object.defineProperty(i,"originRCField",{value:!0}),i})}),(0,g.Z)(this,"initEntityValue",function(e){var t=e.props.initialValue;if(void 0!==t){var r=e.getNamePath();void 0===(0,er.Z)(n.store,r)&&n.updateStore((0,G.Z)(n.store,r,t))}}),(0,g.Z)(this,"isMergedPreserve",function(e){var t=void 0!==e?e:n.preserve;return null==t||t}),(0,g.Z)(this,"registerField",function(e){n.fieldEntities.push(e);var t=e.getNamePath();if(n.notifyWatch([t]),void 0!==e.props.initialValue){var r=n.store;n.resetWithFieldInitialValue({entities:[e],skipExist:!0}),n.notifyObservers(r,[e.getNamePath()],{type:"valueUpdate",source:"internal"})}return function(r,i){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];if(n.fieldEntities=n.fieldEntities.filter(function(t){return t!==e}),!n.isMergedPreserve(i)&&(!r||o.length>1)){var a=r?void 0:n.getInitialValue(t);if(t.length&&n.getFieldValue(t)!==a&&n.fieldEntities.every(function(e){return!el(e.getNamePath(),t)})){var l=n.store;n.updateStore((0,G.Z)(l,t,a,!0)),n.notifyObservers(l,[t],{type:"remove"}),n.triggerDependenciesUpdate(l,t)}}n.notifyWatch([t])}}),(0,g.Z)(this,"dispatch",function(e){switch(e.type){case"updateValue":var t=e.namePath,r=e.value;n.updateValue(t,r);break;case"validateField":var i=e.namePath,o=e.triggerName;n.validateFields([i],{triggerName:o})}}),(0,g.Z)(this,"notifyObservers",function(e,t,r){if(n.subscribable){var i=(0,c.Z)((0,c.Z)({},r),{},{store:n.getFieldsValue(!0)});n.getFieldEntities().forEach(function(n){(0,n.onStoreChange)(e,t,i)})}else n.forceRootUpdate()}),(0,g.Z)(this,"triggerDependenciesUpdate",function(e,t){var r=n.getDependencyChildrenFields(t);return r.length&&n.validateFields(r),n.notifyObservers(e,r,{type:"dependenciesUpdate",relatedFields:[t].concat((0,u.Z)(r))}),r}),(0,g.Z)(this,"updateValue",function(e,t){var r=ei(e),i=n.store;n.updateStore((0,G.Z)(n.store,r,t)),n.notifyObservers(i,[r],{type:"valueUpdate",source:"internal"}),n.notifyWatch([r]);var o=n.triggerDependenciesUpdate(i,r),a=n.callbacks.onValuesChange;a&&a(eo(n.store,[r]),n.getFieldsValue()),n.triggerOnFieldsChange([r].concat((0,u.Z)(o)))}),(0,g.Z)(this,"setFieldsValue",function(e){n.warningUnhooked();var t=n.store;if(e){var r=(0,G.T)(n.store,e);n.updateStore(r)}n.notifyObservers(t,null,{type:"valueUpdate",source:"external"}),n.notifyWatch()}),(0,g.Z)(this,"setFieldValue",function(e,t){n.setFields([{name:e,value:t,errors:[],warnings:[]}])}),(0,g.Z)(this,"getDependencyChildrenFields",function(e){var t=new Set,r=[],i=new eb;return n.getFieldEntities().forEach(function(e){(e.props.dependencies||[]).forEach(function(t){var n=ei(t);i.update(n,function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Set;return t.add(e),t})})}),!function e(n){(i.get(n)||new Set).forEach(function(n){if(!t.has(n)){t.add(n);var i=n.getNamePath();n.isFieldDirty()&&i.length&&(r.push(i),e(i))}})}(e),r}),(0,g.Z)(this,"triggerOnFieldsChange",function(e,t){var r=n.callbacks.onFieldsChange;if(r){var i=n.getFields();if(t){var o=new eb;t.forEach(function(e){var t=e.name,n=e.errors;o.set(t,n)}),i.forEach(function(e){e.errors=o.get(e.name)||e.errors})}var a=i.filter(function(t){return ea(e,t.name)});a.length&&r(a,i)}}),(0,g.Z)(this,"validateFields",function(e,t){n.warningUnhooked(),Array.isArray(e)||"string"==typeof e||"string"==typeof t?(a=e,l=t):l=e;var r,i,o,a,l,s=!!a,d=s?a.map(ei):[],f=[],p=String(Date.now()),h=new Set,m=l||{},g=m.recursive,v=m.dirty;n.getFieldEntities(!0).forEach(function(e){if((s||d.push(e.getNamePath()),e.props.rules&&e.props.rules.length)&&(!v||e.isFieldDirty())){var t=e.getNamePath();if(h.add(t.join(p)),!s||ea(d,t,g)){var r=e.validateRules((0,c.Z)({validateMessages:(0,c.Z)((0,c.Z)({},X),n.validateMessages)},l));f.push(r.then(function(){return{name:t,errors:[],warnings:[]}}).catch(function(e){var n,r=[],i=[];return(null==(n=e.forEach)||n.call(e,function(e){var t=e.rule.warningOnly,n=e.errors;t?i.push.apply(i,(0,u.Z)(n)):r.push.apply(r,(0,u.Z)(n))}),r.length)?Promise.reject({name:t,errors:r,warnings:i}):{name:t,errors:r,warnings:i}}))}}});var y=(r=!1,i=f.length,o=[],f.length?new Promise(function(e,t){f.forEach(function(n,a){n.catch(function(e){return r=!0,e}).then(function(n){i-=1,o[a]=n,i>0||(r&&t(o),e(o))})})}):Promise.resolve([]));n.lastValidatePromise=y,y.catch(function(e){return e}).then(function(e){var t=e.map(function(e){return e.name});n.notifyObservers(n.store,t,{type:"validateFinish"}),n.triggerOnFieldsChange(t,e)});var b=y.then(function(){return n.lastValidatePromise===y?Promise.resolve(n.getFieldsValue(d)):Promise.reject([])}).catch(function(e){var t=e.filter(function(e){return e&&e.errors.length});return Promise.reject({values:n.getFieldsValue(d),errorFields:t,outOfDate:n.lastValidatePromise!==y})});b.catch(function(e){return e});var x=d.filter(function(e){return h.has(e.join(p))});return n.triggerOnFieldsChange(x),b}),(0,g.Z)(this,"submit",function(){n.warningUnhooked(),n.validateFields().then(function(e){var t=n.callbacks.onFinish;if(t)try{t(e)}catch(e){console.error(e)}}).catch(function(e){var t=n.callbacks.onFinishFailed;t&&t(e)})}),this.forceRootUpdate=t}),eC=function(e){var t=i.useRef(),n=i.useState({}),r=(0,eg.Z)(n,2)[1];return t.current||(e?t.current=e:t.current=new eZ(function(){r({})}).getForm()),[t.current]},eS=i.createContext({triggerFormChange:function(){},triggerFormFinish:function(){},registerForm:function(){},unregisterForm:function(){}}),ew=function(e){var t=e.validateMessages,n=e.onFormChange,r=e.onFormFinish,o=e.children,a=i.useContext(eS),l=i.useRef({});return i.createElement(eS.Provider,{value:(0,c.Z)((0,c.Z)({},a),{},{validateMessages:(0,c.Z)((0,c.Z)({},a.validateMessages),t),triggerFormChange:function(e,t){n&&n(e,{changedFields:t,forms:l.current}),a.triggerFormChange(e,t)},triggerFormFinish:function(e,t){r&&r(e,{values:t,forms:l.current}),a.triggerFormFinish(e,t)},registerForm:function(e,t){e&&(l.current=(0,c.Z)((0,c.Z)({},l.current),{},(0,g.Z)({},e,t))),a.registerForm(e,t)},unregisterForm:function(e){var t=(0,c.Z)({},l.current);delete t[e],l.current=t,a.unregisterForm(e)}})},o)},ek=["name","initialValues","fields","form","preserve","children","component","validateMessages","validateTrigger","onValuesChange","onFieldsChange","onFinish","onFinishFailed","clearOnDestroy"];function eM(e){try{return JSON.stringify(e)}catch(e){return Math.random()}}var eF=function(){},eT=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t[0],o=t[1],a=void 0===o?{}:o,l=a&&a._init?{form:a}:a,s=l.form,c=(0,i.useState)(),u=(0,eg.Z)(c,2),d=u[0],f=u[1],p=(0,i.useMemo)(function(){return eM(d)},[d]),h=(0,i.useRef)(p);h.current=p;var m=(0,i.useContext)(C),g=s||m,v=g&&g._init,y=ei(r),b=(0,i.useRef)(y);return b.current=y,eF(y),(0,i.useEffect)(function(){if(v){var e=g.getFieldsValue,t=(0,g.getInternalHooks)(x).registerWatch,n=function(e,t){var n=l.preserve?t:e;return"function"==typeof r?r(n):(0,er.Z)(n,b.current)},i=t(function(e,t){var r=n(e,t),i=eM(r);h.current!==i&&(h.current=i,f(r))}),o=n(e(),e(!0));return d!==o&&f(o),i}},[v]),d},eP=i.forwardRef(function(e,t){var n,r=e.name,l=e.initialValues,s=e.fields,d=e.form,f=e.preserve,p=e.children,h=e.component,m=void 0===h?"form":h,g=e.validateMessages,v=e.validateTrigger,y=void 0===v?"onChange":v,b=e.onValuesChange,Z=e.onFieldsChange,w=e.onFinish,M=e.onFinishFailed,F=e.clearOnDestroy,T=(0,a.Z)(e,ek),P=i.useRef(null),E=i.useContext(eS),R=eC(d),I=(0,eg.Z)(R,1)[0],j=I.getInternalHooks(x),L=j.useSubscribe,A=j.setInitialValues,O=j.setCallbacks,N=j.setValidateMessages,D=j.setPreserve,B=j.destroyForm;i.useImperativeHandle(t,function(){return(0,c.Z)((0,c.Z)({},I),{},{nativeElement:P.current})}),i.useEffect(function(){return E.registerForm(r,I),function(){E.unregisterForm(r)}},[E,I,r]),N((0,c.Z)((0,c.Z)({},E.validateMessages),g)),O({onValuesChange:b,onFieldsChange:function(e){if(E.triggerFormChange(r,e),Z){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];Z.apply(void 0,[e].concat(n))}},onFinish:function(e){E.triggerFormFinish(r,e),w&&w(e)},onFinishFailed:M}),D(f);var _=i.useRef(null);A(l,!_.current),_.current||(_.current=!0),i.useEffect(function(){return function(){return B(F)}},[]);var H="function"==typeof p;n=H?p(I.getFieldsValue(!0),I):p,L(!H);var z=i.useRef();i.useEffect(function(){!function(e,t){if(e===t)return!0;if(!e&&t||e&&!t||!e||!t||"object"!==(0,k.Z)(e)||"object"!==(0,k.Z)(t))return!1;var n=new Set([].concat(Object.keys(e),Object.keys(t)));return(0,u.Z)(n).every(function(n){var r=e[n],i=t[n];return"function"==typeof r&&"function"==typeof i||r===i})}(z.current||[],s||[])&&I.setFields(s||[]),z.current=s},[s,I]);var V=i.useMemo(function(){return(0,c.Z)((0,c.Z)({},I),{},{validateTrigger:y})},[I,y]),W=i.createElement(S.Provider,{value:null},i.createElement(C.Provider,{value:V},n));return!1===m?W:i.createElement(m,(0,o.Z)({},T,{ref:P,onSubmit:function(e){e.preventDefault(),e.stopPropagation(),I.submit()},onReset:function(e){var t;e.preventDefault(),I.resetFields(),null==(t=T.onReset)||t.call(T,e)}}),W)});eP.FormProvider=ew,eP.Field=eh,eP.List=em,eP.useForm=eC,eP.useWatch=eT;var eE=eP},70799:function(e,t,n){"use strict";n.d(t,{iz:()=>ej,Wd:()=>eR,sN:()=>eh,ZP:()=>ez,ck:()=>eh,Xl:()=>F,BW:()=>eO});var r=n(25833),i=n(50793),o=n(19741),a=n(81632),l=n(57987),s=n(95504),c=n(41229),u=n.n(c),d=n(80781),f=n(29202),p=n(70198),h=n(37326),m=n(52983),g=n(63730),v=m.createContext(null);function y(e,t){return void 0===e?null:"".concat(e,"-").concat(t)}function b(e){return y(m.useContext(v),e)}var x=n(19377),Z=["children","locked"],C=m.createContext(null);function S(e){var t=e.children,n=e.locked,r=(0,s.Z)(e,Z),i=m.useContext(C),a=(0,x.Z)(function(){var e;return e=(0,o.Z)({},i),Object.keys(r).forEach(function(t){var n=r[t];void 0!==n&&(e[t]=n)}),e},[i,r],function(e,t){return!n&&(e[0]!==t[0]||!(0,p.Z)(e[1],t[1],!0))});return m.createElement(C.Provider,{value:a},t)}var w=m.createContext(null);function k(){return m.useContext(w)}var M=m.createContext([]);function F(e){var t=m.useContext(M);return m.useMemo(function(){return void 0!==e?[].concat((0,a.Z)(t),[e]):t},[t,e])}var T=m.createContext(null),P=m.createContext({}),E=n(25014);function R(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if((0,E.Z)(e)){var n=e.nodeName.toLowerCase(),r=["input","select","textarea","button"].includes(n)||e.isContentEditable||"a"===n&&!!e.getAttribute("href"),i=e.getAttribute("tabindex"),o=Number(i),a=null;return i&&!Number.isNaN(o)?a=o:r&&null===a&&(a=0),r&&e.disabled&&(a=null),null!==a&&(a>=0||t&&a<0)}return!1}var I=n(46676),j=n(21213),L=I.Z.LEFT,A=I.Z.RIGHT,O=I.Z.UP,N=I.Z.DOWN,D=I.Z.ENTER,B=I.Z.ESC,_=I.Z.HOME,H=I.Z.END,z=[O,N,L,A];function V(e,t){return(function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=(0,a.Z)(e.querySelectorAll("*")).filter(function(e){return R(e,t)});return R(e,t)&&n.unshift(e),n})(e,!0).filter(function(e){return t.has(e)})}function W(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(!e)return null;var i=V(e,t),o=i.length,a=i.findIndex(function(e){return n===e});return r<0?-1===a?a=o-1:a-=1:r>0&&(a+=1),i[a=(a+o)%o]}var $=function(e,t){var n=new Set,r=new Map,i=new Map;return e.forEach(function(e){var o=document.querySelector("[data-menu-id='".concat(y(t,e),"']"));o&&(n.add(o),i.set(o,e),r.set(e,o))}),{elements:n,key2element:r,element2key:i}},K="__RC_UTIL_PATH_SPLIT__",U=function(e){return e.join(K)},q="rc-menu-more";function Y(e){var t=m.useRef(e);t.current=e;var n=m.useCallback(function(){for(var e,n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];return null==(e=t.current)?void 0:e.call.apply(e,[t].concat(r))},[]);return e?n:void 0}var X=Math.random().toFixed(5).toString().slice(2),G=0,Q=n(71123),J=n(83739),ee=n(14425),et=n(81087),en=n(63851),er=n(22878);function ei(e,t,n,r){var i=m.useContext(C),o=i.activeKey,a=i.onActive,l=i.onInactive,s={active:o===e};return t||(s.onMouseEnter=function(t){null==n||n({key:e,domEvent:t}),a(e)},s.onMouseLeave=function(t){null==r||r({key:e,domEvent:t}),l(e)}),s}function eo(e){var t=m.useContext(C),n=t.mode,r=t.rtl,i=t.inlineIndent;return"inline"!==n?null:r?{paddingRight:e*i}:{paddingLeft:e*i}}function ea(e){var t,n=e.icon,r=e.props,i=e.children;return null===n||!1===n?null:("function"==typeof n?t=m.createElement(n,(0,o.Z)({},r)):"boolean"!=typeof n&&(t=n),t||i||null)}var el=["item"];function es(e){var t=e.item,n=(0,s.Z)(e,el);return Object.defineProperty(n,"item",{get:function(){return(0,h.ZP)(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),n}var ec=["title","attribute","elementRef"],eu=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],ed=["active"],ef=function(e){(0,ee.Z)(n,e);var t=(0,et.Z)(n);function n(){return(0,Q.Z)(this,n),t.apply(this,arguments)}return(0,J.Z)(n,[{key:"render",value:function(){var e=this.props,t=e.title,n=e.attribute,i=e.elementRef,o=(0,s.Z)(e,ec),a=(0,en.Z)(o,["eventKey","popupClassName","popupOffset","onTitleClick"]);return(0,h.ZP)(!n,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),m.createElement(d.Z.Item,(0,r.Z)({},n,{title:"string"==typeof t?t:void 0},a,{ref:i}))}}]),n}(m.Component),ep=m.forwardRef(function(e,t){var n=e.style,l=e.className,c=e.eventKey,d=(e.warnKey,e.disabled),f=e.itemIcon,p=e.children,h=e.role,g=e.onMouseEnter,v=e.onMouseLeave,y=e.onClick,x=e.onKeyDown,Z=e.onFocus,S=(0,s.Z)(e,eu),w=b(c),k=m.useContext(C),M=k.prefixCls,T=k.onItemClick,E=k.disabled,R=k.overflowDisabled,j=k.itemIcon,L=k.selectedKeys,A=k.onActive,O=m.useContext(P)._internalRenderMenuItem,N="".concat(M,"-item"),D=m.useRef(),B=m.useRef(),_=E||d,H=(0,er.x1)(t,B),z=F(c),V=function(e){return{key:c,keyPath:(0,a.Z)(z).reverse(),item:D.current,domEvent:e}},W=ei(c,_,g,v),$=W.active,K=(0,s.Z)(W,ed),U=L.includes(c),q=eo(z.length),Y={};"option"===e.role&&(Y["aria-selected"]=U);var X=m.createElement(ef,(0,r.Z)({ref:D,elementRef:H,role:null===h?"none":h||"menuitem",tabIndex:d?null:-1,"data-menu-id":R&&w?null:w},(0,en.Z)(S,["extra"]),K,Y,{component:"li","aria-disabled":d,style:(0,o.Z)((0,o.Z)({},q),n),className:u()(N,(0,i.Z)((0,i.Z)((0,i.Z)({},"".concat(N,"-active"),$),"".concat(N,"-selected"),U),"".concat(N,"-disabled"),_),l),onClick:function(e){if(!_){var t=V(e);null==y||y(es(t)),T(t)}},onKeyDown:function(e){if(null==x||x(e),e.which===I.Z.ENTER){var t=V(e);null==y||y(es(t)),T(t)}},onFocus:function(e){A(c),null==Z||Z(e)}}),p,m.createElement(ea,{props:(0,o.Z)((0,o.Z)({},e),{},{isSelected:U}),icon:f||j}));return O&&(X=O(X,e,{selected:U})),X}),eh=m.forwardRef(function(e,t){var n=e.eventKey,i=k(),o=F(n);return(m.useEffect(function(){if(i)return i.registerPath(n,o),function(){i.unregisterPath(n,o)}},[o]),i)?null:m.createElement(ep,(0,r.Z)({},e,{ref:t}))}),em=["className","children"],eg=m.forwardRef(function(e,t){var n=e.className,i=e.children,o=(0,s.Z)(e,em),a=m.useContext(C),l=a.prefixCls,c=a.mode,d=a.rtl;return m.createElement("ul",(0,r.Z)({className:u()(l,d&&"".concat(l,"-rtl"),"".concat(l,"-sub"),"".concat(l,"-").concat("inline"===c?"inline":"vertical"),n),role:"menu"},o,{"data-menu-list":!0,ref:t}),i)});eg.displayName="SubMenuList";var ev=n(56256);function ey(e,t){return(0,ev.Z)(e).map(function(e,n){if(m.isValidElement(e)){var r,i,o=e.key,l=null!=(r=null==(i=e.props)?void 0:i.eventKey)?r:o;null==l&&(l="tmp_key-".concat([].concat((0,a.Z)(t),[n]).join("-")));var s={key:l,eventKey:l};return m.cloneElement(e,s)}return e})}var eb=n(49817),ex={adjustX:1,adjustY:1},eZ={topLeft:{points:["bl","tl"],overflow:ex},topRight:{points:["br","tr"],overflow:ex},bottomLeft:{points:["tl","bl"],overflow:ex},bottomRight:{points:["tr","br"],overflow:ex},leftTop:{points:["tr","tl"],overflow:ex},leftBottom:{points:["br","bl"],overflow:ex},rightTop:{points:["tl","tr"],overflow:ex},rightBottom:{points:["bl","br"],overflow:ex}},eC={topLeft:{points:["bl","tl"],overflow:ex},topRight:{points:["br","tr"],overflow:ex},bottomLeft:{points:["tl","bl"],overflow:ex},bottomRight:{points:["tr","br"],overflow:ex},rightTop:{points:["tr","tl"],overflow:ex},rightBottom:{points:["br","bl"],overflow:ex},leftTop:{points:["tl","tr"],overflow:ex},leftBottom:{points:["bl","br"],overflow:ex}};function eS(e,t,n){return t||(n?n[e]||n.other:void 0)}var ew={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function ek(e){var t=e.prefixCls,n=e.visible,r=e.children,a=e.popup,s=e.popupStyle,c=e.popupClassName,d=e.popupOffset,f=e.disabled,p=e.mode,h=e.onVisibleChange,g=m.useContext(C),v=g.getPopupContainer,y=g.rtl,b=g.subMenuOpenDelay,x=g.subMenuCloseDelay,Z=g.builtinPlacements,S=g.triggerSubMenuAction,w=g.forceSubMenuRender,k=g.rootClassName,M=g.motion,F=g.defaultMotions,T=m.useState(!1),P=(0,l.Z)(T,2),E=P[0],R=P[1],I=y?(0,o.Z)((0,o.Z)({},eC),Z):(0,o.Z)((0,o.Z)({},eZ),Z),L=ew[p],A=eS(p,M,F),O=m.useRef(A);"inline"!==p&&(O.current=A);var N=(0,o.Z)((0,o.Z)({},O.current),{},{leavedClassName:"".concat(t,"-hidden"),removeOnLeave:!1,motionAppear:!0}),D=m.useRef();return m.useEffect(function(){return D.current=(0,j.Z)(function(){R(n)}),function(){j.Z.cancel(D.current)}},[n]),m.createElement(eb.Z,{prefixCls:t,popupClassName:u()("".concat(t,"-popup"),(0,i.Z)({},"".concat(t,"-rtl"),y),c,k),stretch:"horizontal"===p?"minWidth":null,getPopupContainer:v,builtinPlacements:I,popupPlacement:L,popupVisible:E,popup:a,popupStyle:s,popupAlign:d&&{offset:d},action:f?[]:[S],mouseEnterDelay:b,mouseLeaveDelay:x,onPopupVisibleChange:h,forceRender:w,popupMotion:N,fresh:!0},r)}var eM=n(69211);function eF(e){var t=e.id,n=e.open,i=e.keyPath,a=e.children,s="inline",c=m.useContext(C),u=c.prefixCls,d=c.forceSubMenuRender,f=c.motion,p=c.defaultMotions,h=c.mode,g=m.useRef(!1);g.current=h===s;var v=m.useState(!g.current),y=(0,l.Z)(v,2),b=y[0],x=y[1],Z=!!g.current&&n;m.useEffect(function(){g.current&&x(!1)},[h]);var w=(0,o.Z)({},eS(s,f,p));i.length>1&&(w.motionAppear=!1);var k=w.onVisibleChanged;return(w.onVisibleChanged=function(e){return g.current||e||x(!0),null==k?void 0:k(e)},b)?null:m.createElement(S,{mode:s,locked:!g.current},m.createElement(eM.ZP,(0,r.Z)({visible:Z},w,{forceRender:d,removeOnLeave:!1,leavedClassName:"".concat(u,"-hidden")}),function(e){var n=e.className,r=e.style;return m.createElement(eg,{id:t,className:n,style:r},a)}))}var eT=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],eP=["active"],eE=m.forwardRef(function(e,t){var n=e.style,a=e.className,c=e.title,f=e.eventKey,p=(e.warnKey,e.disabled),h=e.internalPopupClose,g=e.children,v=e.itemIcon,y=e.expandIcon,x=e.popupClassName,Z=e.popupOffset,w=e.popupStyle,k=e.onClick,M=e.onMouseEnter,E=e.onMouseLeave,R=e.onTitleClick,I=e.onTitleMouseEnter,j=e.onTitleMouseLeave,L=(0,s.Z)(e,eT),A=b(f),O=m.useContext(C),N=O.prefixCls,D=O.mode,B=O.openKeys,_=O.disabled,H=O.overflowDisabled,z=O.activeKey,V=O.selectedKeys,W=O.itemIcon,$=O.expandIcon,K=O.onItemClick,U=O.onOpenChange,q=O.onActive,X=m.useContext(P)._internalRenderSubMenuItem,G=m.useContext(T).isSubPathKey,Q=F(),J="".concat(N,"-submenu"),ee=_||p,et=m.useRef(),en=m.useRef(),er=null!=y?y:$,el=B.includes(f),ec=!H&&el,eu=G(V,f),ed=ei(f,ee,I,j),ef=ed.active,ep=(0,s.Z)(ed,eP),eh=m.useState(!1),em=(0,l.Z)(eh,2),ev=em[0],ey=em[1],eb=function(e){ee||ey(e)},ex=m.useMemo(function(){return ef||"inline"!==D&&(ev||G([z],f))},[D,ef,z,ev,f,G]),eZ=eo(Q.length),eC=Y(function(e){null==k||k(es(e)),K(e)}),eS=A&&"".concat(A,"-popup"),ew=m.useMemo(function(){return m.createElement(ea,{icon:"horizontal"!==D?er:void 0,props:(0,o.Z)((0,o.Z)({},e),{},{isOpen:ec,isSubMenu:!0})},m.createElement("i",{className:"".concat(J,"-arrow")}))},[D,er,e,ec,J]),eM=m.createElement("div",(0,r.Z)({role:"menuitem",style:eZ,className:"".concat(J,"-title"),tabIndex:ee?null:-1,ref:et,title:"string"==typeof c?c:null,"data-menu-id":H&&A?null:A,"aria-expanded":ec,"aria-haspopup":!0,"aria-controls":eS,"aria-disabled":ee,onClick:function(e){ee||(null==R||R({key:f,domEvent:e}),"inline"===D&&U(f,!el))},onFocus:function(){q(f)}},ep),c,ew),eE=m.useRef(D);if("inline"!==D&&Q.length>1?eE.current="vertical":eE.current=D,!H){var eR=eE.current;eM=m.createElement(ek,{mode:eR,prefixCls:J,visible:!h&&ec&&"inline"!==D,popupClassName:x,popupOffset:Z,popupStyle:w,popup:m.createElement(S,{mode:"horizontal"===eR?"vertical":eR},m.createElement(eg,{id:eS,ref:en},g)),disabled:ee,onVisibleChange:function(e){"inline"!==D&&U(f,e)}},eM)}var eI=m.createElement(d.Z.Item,(0,r.Z)({ref:t,role:"none"},L,{component:"li",style:n,className:u()(J,"".concat(J,"-").concat(D),a,(0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)({},"".concat(J,"-open"),ec),"".concat(J,"-active"),ex),"".concat(J,"-selected"),eu),"".concat(J,"-disabled"),ee)),onMouseEnter:function(e){eb(!0),null==M||M({key:f,domEvent:e})},onMouseLeave:function(e){eb(!1),null==E||E({key:f,domEvent:e})}}),eM,!H&&m.createElement(eF,{id:eS,open:ec,keyPath:Q},g));return X&&(eI=X(eI,e,{selected:eu,active:ex,open:ec,disabled:ee})),m.createElement(S,{onItemClick:eC,mode:"horizontal"===D?"vertical":D,itemIcon:null!=v?v:W,expandIcon:er},eI)}),eR=m.forwardRef(function(e,t){var n,i=e.eventKey,o=e.children,a=F(i),l=ey(o,a),s=k();return m.useEffect(function(){if(s)return s.registerPath(i,a),function(){s.unregisterPath(i,a)}},[a]),n=s?l:m.createElement(eE,(0,r.Z)({ref:t},e),l),m.createElement(M.Provider,{value:a},n)}),eI=n(52655);function ej(e){var t=e.className,n=e.style,r=m.useContext(C).prefixCls;return k()?null:m.createElement("li",{role:"separator",className:u()("".concat(r,"-item-divider"),t),style:n})}var eL=["className","title","eventKey","children"],eA=m.forwardRef(function(e,t){var n=e.className,i=e.title,o=(e.eventKey,e.children),a=(0,s.Z)(e,eL),l=m.useContext(C).prefixCls,c="".concat(l,"-item-group");return m.createElement("li",(0,r.Z)({ref:t,role:"presentation"},a,{onClick:function(e){return e.stopPropagation()},className:u()(c,n)}),m.createElement("div",{role:"presentation",className:"".concat(c,"-title"),title:"string"==typeof i?i:void 0},i),m.createElement("ul",{role:"group",className:"".concat(c,"-list")},o))}),eO=m.forwardRef(function(e,t){var n=e.eventKey,i=ey(e.children,F(n));return k()?i:m.createElement(eA,(0,r.Z)({ref:t},(0,en.Z)(e,["warnKey"])),i)}),eN=["label","children","key","type","extra"];function eD(e,t,n,i,a){var l=e,c=(0,o.Z)({divider:ej,item:eh,group:eO,submenu:eR},i);return t&&(l=function e(t,n,i){var o=n.item,a=n.group,l=n.submenu,c=n.divider;return(t||[]).map(function(t,u){if(t&&"object"===(0,eI.Z)(t)){var d=t.label,f=t.children,p=t.key,h=t.type,g=t.extra,v=(0,s.Z)(t,eN),y=null!=p?p:"tmp-".concat(u);return f||"group"===h?"group"===h?m.createElement(a,(0,r.Z)({key:y},v,{title:d}),e(f,n,i)):m.createElement(l,(0,r.Z)({key:y},v,{title:d}),e(f,n,i)):"divider"===h?m.createElement(c,(0,r.Z)({key:y},v)):m.createElement(o,(0,r.Z)({key:y},v,{extra:g}),d,(!!g||0===g)&&m.createElement("span",{className:"".concat(i,"-item-extra")},g))}return null}).filter(function(e){return e})}(t,c,a)),ey(l,n)}var eB=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],e_=[],eH=m.forwardRef(function(e,t){var n,c,h,y,b,x,Z,C,k,M,F,E,R,I,Q,J,ee,et,en,er,ei,eo,ea,el,ec,eu,ed=e.prefixCls,ef=void 0===ed?"rc-menu":ed,ep=e.rootClassName,em=e.style,eg=e.className,ev=e.tabIndex,ey=e.items,eb=e.children,ex=e.direction,eZ=e.id,eC=e.mode,eS=void 0===eC?"vertical":eC,ew=e.inlineCollapsed,ek=e.disabled,eM=e.disabledOverflow,eF=e.subMenuOpenDelay,eT=e.subMenuCloseDelay,eP=e.forceSubMenuRender,eE=e.defaultOpenKeys,eI=e.openKeys,ej=e.activeKey,eL=e.defaultActiveFirst,eA=e.selectable,eO=void 0===eA||eA,eN=e.multiple,eH=void 0!==eN&&eN,ez=e.defaultSelectedKeys,eV=e.selectedKeys,eW=e.onSelect,e$=e.onDeselect,eK=e.inlineIndent,eU=e.motion,eq=e.defaultMotions,eY=e.triggerSubMenuAction,eX=e.builtinPlacements,eG=e.itemIcon,eQ=e.expandIcon,eJ=e.overflowedIndicator,e0=void 0===eJ?"...":eJ,e1=e.overflowedIndicatorPopupClassName,e2=e.getPopupContainer,e5=e.onClick,e6=e.onOpenChange,e8=e.onKeyDown,e3=(e.openAnimation,e.openTransitionName,e._internalRenderMenuItem),e4=e._internalRenderSubMenuItem,e9=e._internalComponents,e7=(0,s.Z)(e,eB),te=m.useMemo(function(){return[eD(eb,ey,e_,e9,ef),eD(eb,ey,e_,{},ef)]},[eb,ey,e9]),tt=(0,l.Z)(te,2),tn=tt[0],tr=tt[1],ti=m.useState(!1),to=(0,l.Z)(ti,2),ta=to[0],tl=to[1],ts=m.useRef(),tc=(n=(0,f.Z)(eZ,{value:eZ}),h=(c=(0,l.Z)(n,2))[0],y=c[1],m.useEffect(function(){G+=1;var e="".concat(X,"-").concat(G);y("rc-menu-uuid-".concat(e))},[]),h),tu="rtl"===ex,td=(0,f.Z)(eE,{value:eI,postState:function(e){return e||e_}}),tf=(0,l.Z)(td,2),tp=tf[0],th=tf[1],tm=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];function n(){th(e),null==e6||e6(e)}t?(0,g.flushSync)(n):n()},tg=m.useState(tp),tv=(0,l.Z)(tg,2),ty=tv[0],tb=tv[1],tx=m.useRef(!1),tZ=m.useMemo(function(){return("inline"===eS||"vertical"===eS)&&ew?["vertical",ew]:[eS,!1]},[eS,ew]),tC=(0,l.Z)(tZ,2),tS=tC[0],tw=tC[1],tk="inline"===tS,tM=m.useState(tS),tF=(0,l.Z)(tM,2),tT=tF[0],tP=tF[1],tE=m.useState(tw),tR=(0,l.Z)(tE,2),tI=tR[0],tj=tR[1];m.useEffect(function(){tP(tS),tj(tw),tx.current&&(tk?th(ty):tm(e_))},[tS,tw]);var tL=m.useState(0),tA=(0,l.Z)(tL,2),tO=tA[0],tN=tA[1],tD=tO>=tn.length-1||"horizontal"!==tT||eM;m.useEffect(function(){tk&&tb(tp)},[tp]),m.useEffect(function(){return tx.current=!0,function(){tx.current=!1}},[]);var tB=(b=m.useState({}),x=(0,l.Z)(b,2)[1],Z=(0,m.useRef)(new Map),C=(0,m.useRef)(new Map),k=m.useState([]),F=(M=(0,l.Z)(k,2))[0],E=M[1],R=(0,m.useRef)(0),I=(0,m.useRef)(!1),Q=function(){I.current||x({})},J=(0,m.useCallback)(function(e,t){var n=U(t);C.current.set(n,e),Z.current.set(e,n),R.current+=1;var r=R.current;Promise.resolve().then(function(){r===R.current&&Q()})},[]),ee=(0,m.useCallback)(function(e,t){var n=U(t);C.current.delete(n),Z.current.delete(e)},[]),et=(0,m.useCallback)(function(e){E(e)},[]),en=(0,m.useCallback)(function(e,t){var n=(Z.current.get(e)||"").split(K);return t&&F.includes(n[0])&&n.unshift(q),n},[F]),er=(0,m.useCallback)(function(e,t){return e.filter(function(e){return void 0!==e}).some(function(e){return en(e,!0).includes(t)})},[en]),ei=(0,m.useCallback)(function(e){var t="".concat(Z.current.get(e)).concat(K),n=new Set;return(0,a.Z)(C.current.keys()).forEach(function(e){e.startsWith(t)&&n.add(C.current.get(e))}),n},[]),m.useEffect(function(){return function(){I.current=!0}},[]),{registerPath:J,unregisterPath:ee,refreshOverflowKeys:et,isSubPathKey:er,getKeyPath:en,getKeys:function(){var e=(0,a.Z)(Z.current.keys());return F.length&&e.push(q),e},getSubPathKeys:ei}),t_=tB.registerPath,tH=tB.unregisterPath,tz=tB.refreshOverflowKeys,tV=tB.isSubPathKey,tW=tB.getKeyPath,t$=tB.getKeys,tK=tB.getSubPathKeys,tU=m.useMemo(function(){return{registerPath:t_,unregisterPath:tH}},[t_,tH]),tq=m.useMemo(function(){return{isSubPathKey:tV}},[tV]);m.useEffect(function(){tz(tD?e_:tn.slice(tO+1).map(function(e){return e.key}))},[tO,tD]);var tY=(0,f.Z)(ej||eL&&(null==(eu=tn[0])?void 0:eu.key),{value:ej}),tX=(0,l.Z)(tY,2),tG=tX[0],tQ=tX[1],tJ=Y(function(e){tQ(e)}),t0=Y(function(){tQ(void 0)});(0,m.useImperativeHandle)(t,function(){return{list:ts.current,focus:function(e){var t,n,r=$(t$(),tc),i=r.elements,o=r.key2element,a=r.element2key,l=V(ts.current,i),s=null!=tG?tG:l[0]?a.get(l[0]):null==(t=tn.find(function(e){return!e.props.disabled}))?void 0:t.key,c=o.get(s);s&&c&&(null==c||null==(n=c.focus)||n.call(c,e))}}});var t1=(0,f.Z)(ez||[],{value:eV,postState:function(e){return Array.isArray(e)?e:null==e?e_:[e]}}),t2=(0,l.Z)(t1,2),t5=t2[0],t6=t2[1],t8=function(e){if(eO){var t,n=e.key,r=t5.includes(n);t6(t=eH?r?t5.filter(function(e){return e!==n}):[].concat((0,a.Z)(t5),[n]):[n]);var i=(0,o.Z)((0,o.Z)({},e),{},{selectedKeys:t});r?null==e$||e$(i):null==eW||eW(i)}!eH&&tp.length&&"inline"!==tT&&tm(e_)},t3=Y(function(e){null==e5||e5(es(e)),t8(e)}),t4=Y(function(e,t){var n=tp.filter(function(t){return t!==e});if(t)n.push(e);else if("inline"!==tT){var r=tK(e);n=n.filter(function(e){return!r.has(e)})}(0,p.Z)(tp,n,!0)||tm(n,!0)}),t9=(eo=function(e,t){var n=null!=t?t:!tp.includes(e);t4(e,n)},ea=m.useRef(),(el=m.useRef()).current=tG,ec=function(){j.Z.cancel(ea.current)},m.useEffect(function(){return function(){ec()}},[]),function(e){var t=e.which;if([].concat(z,[D,B,_,H]).includes(t)){var n=t$(),r=$(n,tc),o=r,a=o.elements,l=o.key2element,s=o.element2key,c=function(e,t){for(var n=e||document.activeElement;n;){if(t.has(n))return n;n=n.parentElement}return null}(l.get(tG),a),u=s.get(c),d=function(e,t,n,r){var o,a="prev",l="next",s="children",c="parent";if("inline"===e&&r===D)return{inlineTrigger:!0};var u=(0,i.Z)((0,i.Z)({},O,a),N,l),d=(0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)({},L,n?l:a),A,n?a:l),N,s),D,s),f=(0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)({},O,a),N,l),D,s),B,c),L,n?s:c),A,n?c:s);switch(null==(o=({inline:u,horizontal:d,vertical:f,inlineSub:u,horizontalSub:f,verticalSub:f})["".concat(e).concat(t?"":"Sub")])?void 0:o[r]){case a:return{offset:-1,sibling:!0};case l:return{offset:1,sibling:!0};case c:return{offset:-1,sibling:!1};case s:return{offset:1,sibling:!1};default:return null}}(tT,1===tW(u,!0).length,tu,t);if(!d&&t!==_&&t!==H)return;(z.includes(t)||[_,H].includes(t))&&e.preventDefault();var f=function(e){if(e){var t=e,n=e.querySelector("a");null!=n&&n.getAttribute("href")&&(t=n);var r=s.get(e);tQ(r),ec(),ea.current=(0,j.Z)(function(){el.current===r&&t.focus()})}};if([_,H].includes(t)||d.sibling||!c){var p,h=c&&"inline"!==tT?function(e){for(var t=e;t;){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}(c):ts.current,m=V(h,a);f(t===_?m[0]:t===H?m[m.length-1]:W(h,a,c,d.offset))}else if(d.inlineTrigger)eo(u);else if(d.offset>0)eo(u,!0),ec(),ea.current=(0,j.Z)(function(){r=$(n,tc);var e=c.getAttribute("aria-controls");f(W(document.getElementById(e),r.elements))},5);else if(d.offset<0){var g=tW(u,!0),v=g[g.length-2],y=l.get(v);eo(v,!1),f(y)}}null==e8||e8(e)});m.useEffect(function(){tl(!0)},[]);var t7=m.useMemo(function(){return{_internalRenderMenuItem:e3,_internalRenderSubMenuItem:e4}},[e3,e4]),ne="horizontal"!==tT||eM?tn:tn.map(function(e,t){return m.createElement(S,{key:e.key,overflowDisabled:t>tO},e)}),nt=m.createElement(d.Z,(0,r.Z)({id:eZ,ref:ts,prefixCls:"".concat(ef,"-overflow"),component:"ul",itemComponent:eh,className:u()(ef,"".concat(ef,"-root"),"".concat(ef,"-").concat(tT),eg,(0,i.Z)((0,i.Z)({},"".concat(ef,"-inline-collapsed"),tI),"".concat(ef,"-rtl"),tu),ep),dir:ex,style:em,role:"menu",tabIndex:void 0===ev?0:ev,data:ne,renderRawItem:function(e){return e},renderRawRest:function(e){var t=e.length,n=t?tn.slice(-t):null;return m.createElement(eR,{eventKey:q,title:e0,disabled:tD,internalPopupClose:0===t,popupClassName:e1},n)},maxCount:"horizontal"!==tT||eM?d.Z.INVALIDATE:d.Z.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(e){tN(e)},onKeyDown:t9},e7));return m.createElement(P.Provider,{value:t7},m.createElement(v.Provider,{value:tc},m.createElement(S,{prefixCls:ef,rootClassName:ep,mode:tT,openKeys:tp,rtl:tu,disabled:ek,motion:ta?eU:null,defaultMotions:ta?eq:null,activeKey:tG,onActive:tJ,onInactive:t0,selectedKeys:t5,inlineIndent:void 0===eK?24:eK,subMenuOpenDelay:void 0===eF?.1:eF,subMenuCloseDelay:void 0===eT?.1:eT,forceSubMenuRender:eP,builtinPlacements:eX,triggerSubMenuAction:void 0===eY?"hover":eY,getPopupContainer:e2,itemIcon:eG,expandIcon:eQ,onItemClick:t3,onOpenChange:t4},m.createElement(T.Provider,{value:tq},nt),m.createElement("div",{style:{display:"none"},"aria-hidden":!0},m.createElement(w.Provider,{value:tU},tr)))))});eH.Item=eh,eH.SubMenu=eR,eH.ItemGroup=eO,eH.Divider=ej;var ez=eH},80781:function(e,t,n){"use strict";n.d(t,{Z:()=>P});var r=n(25833),i=n(19741),o=n(57987),a=n(95504),l=n(52983),s=n(41229),c=n.n(s),u=n(86708),d=n(57743),f=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],p=void 0,h=l.forwardRef(function(e,t){var n,o=e.prefixCls,s=e.invalidate,d=e.item,h=e.renderItem,m=e.responsive,g=e.responsiveDisabled,v=e.registerSize,y=e.itemKey,b=e.className,x=e.style,Z=e.children,C=e.display,S=e.order,w=e.component,k=(0,a.Z)(e,f),M=m&&!C;l.useEffect(function(){return function(){v(y,null)}},[]);var F=h&&d!==p?h(d,{index:S}):Z;s||(n={opacity:+!M,height:M?0:p,overflowY:M?"hidden":p,order:m?S:p,pointerEvents:M?"none":p,position:M?"absolute":p});var T={};M&&(T["aria-hidden"]=!0);var P=l.createElement(void 0===w?"div":w,(0,r.Z)({className:c()(!s&&o,b),style:(0,i.Z)((0,i.Z)({},n),x)},T,k,{ref:t}),F);return m&&(P=l.createElement(u.Z,{onResize:function(e){v(y,e.offsetWidth)},disabled:g},P)),P});h.displayName="Item";var m=n(76990),g=n(63730),v=n(21213);function y(e,t){var n=l.useState(t),r=(0,o.Z)(n,2),i=r[0],a=r[1];return[i,(0,m.Z)(function(t){e(function(){a(t)})})]}var b=l.createContext(null),x=["component"],Z=["className"],C=["className"],S=l.forwardRef(function(e,t){var n=l.useContext(b);if(!n){var i=e.component,o=(0,a.Z)(e,x);return l.createElement(void 0===i?"div":i,(0,r.Z)({},o,{ref:t}))}var s=n.className,u=(0,a.Z)(n,Z),d=e.className,f=(0,a.Z)(e,C);return l.createElement(b.Provider,{value:null},l.createElement(h,(0,r.Z)({ref:t,className:c()(s,d)},u,f)))});S.displayName="RawItem";var w=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],k="responsive",M="invalidate";function F(e){return"+ ".concat(e.length," ...")}var T=l.forwardRef(function(e,t){var n,s=e.prefixCls,f=void 0===s?"rc-overflow":s,p=e.data,m=void 0===p?[]:p,x=e.renderItem,Z=e.renderRawItem,C=e.itemKey,S=e.itemWidth,T=void 0===S?10:S,P=e.ssr,E=e.style,R=e.className,I=e.maxCount,j=e.renderRest,L=e.renderRawRest,A=e.suffix,O=e.component,N=e.itemComponent,D=e.onVisibleChange,B=(0,a.Z)(e,w),_="full"===P,H=(n=l.useRef(null),function(e){if(!n.current){n.current=[];var t=function(){(0,g.unstable_batchedUpdates)(function(){n.current.forEach(function(e){e()}),n.current=null})};if("undefined"==typeof MessageChannel)(0,v.Z)(t);else{var r=new MessageChannel;r.port1.onmessage=function(){return t()},r.port2.postMessage(void 0)}}n.current.push(e)}),z=y(H,null),V=(0,o.Z)(z,2),W=V[0],$=V[1],K=W||0,U=y(H,new Map),q=(0,o.Z)(U,2),Y=q[0],X=q[1],G=y(H,0),Q=(0,o.Z)(G,2),J=Q[0],ee=Q[1],et=y(H,0),en=(0,o.Z)(et,2),er=en[0],ei=en[1],eo=y(H,0),ea=(0,o.Z)(eo,2),el=ea[0],es=ea[1],ec=(0,l.useState)(null),eu=(0,o.Z)(ec,2),ed=eu[0],ef=eu[1],ep=(0,l.useState)(null),eh=(0,o.Z)(ep,2),em=eh[0],eg=eh[1],ev=l.useMemo(function(){return null===em&&_?Number.MAX_SAFE_INTEGER:em||0},[em,W]),ey=(0,l.useState)(!1),eb=(0,o.Z)(ey,2),ex=eb[0],eZ=eb[1],eC="".concat(f,"-item"),eS=Math.max(J,er),ew=I===k,ek=m.length&&ew,eM=I===M,eF=ek||"number"==typeof I&&m.length>I,eT=(0,l.useMemo)(function(){var e=m;return ek?e=null===W&&_?m:m.slice(0,Math.min(m.length,K/T)):"number"==typeof I&&(e=m.slice(0,I)),e},[m,T,W,I,ek]),eP=(0,l.useMemo)(function(){return ek?m.slice(ev+1):m.slice(eT.length)},[m,eT,ek,ev]),eE=(0,l.useCallback)(function(e,t){var n;return"function"==typeof C?C(e):null!=(n=C&&(null==e?void 0:e[C]))?n:t},[C]),eR=(0,l.useCallback)(x||function(e){return e},[x]);function eI(e,t,n){(em!==e||void 0!==t&&t!==ed)&&(eg(e),n||(eZ(e<m.length-1),null==D||D(e)),void 0!==t&&ef(t))}function ej(e,t){X(function(n){var r=new Map(n);return null===t?r.delete(e):r.set(e,t),r})}function eL(e){return Y.get(eE(eT[e],e))}(0,d.Z)(function(){if(K&&"number"==typeof eS&&eT){var e=el,t=eT.length,n=t-1;if(!t)return void eI(0,null);for(var r=0;r<t;r+=1){var i=eL(r);if(_&&(i=i||0),void 0===i){eI(r-1,void 0,!0);break}if(e+=i,0===n&&e<=K||r===n-1&&e+eL(n)<=K){eI(n,null);break}if(e+eS>K){eI(r-1,e-i-el+er);break}}A&&eL(0)+el>K&&ef(null)}},[K,Y,er,el,eE,eT]);var eA=ex&&!!eP.length,eO={};null!==ed&&ek&&(eO={position:"absolute",left:ed,top:0});var eN={prefixCls:eC,responsive:ek,component:N,invalidate:eM},eD=Z?function(e,t){var n=eE(e,t);return l.createElement(b.Provider,{key:n,value:(0,i.Z)((0,i.Z)({},eN),{},{order:t,item:e,itemKey:n,registerSize:ej,display:t<=ev})},Z(e,t))}:function(e,t){var n=eE(e,t);return l.createElement(h,(0,r.Z)({},eN,{order:t,key:n,item:e,renderItem:eR,itemKey:n,registerSize:ej,display:t<=ev}))},eB={order:eA?ev:Number.MAX_SAFE_INTEGER,className:"".concat(eC,"-rest"),registerSize:function(e,t){ei(t),ee(er)},display:eA},e_=j||F,eH=L?l.createElement(b.Provider,{value:(0,i.Z)((0,i.Z)({},eN),eB)},L(eP)):l.createElement(h,(0,r.Z)({},eN,eB),"function"==typeof e_?e_(eP):e_),ez=l.createElement(void 0===O?"div":O,(0,r.Z)({className:c()(!eM&&f,R),style:E,ref:t},B),eT.map(eD),eF?eH:null,A&&l.createElement(h,(0,r.Z)({},eN,{responsive:ew,responsiveDisabled:!ek,order:ev,className:"".concat(eC,"-suffix"),registerSize:function(e,t){es(t)},display:!0,style:eO}),A));return ew?l.createElement(u.Z,{onResize:function(e,t){$(t.clientWidth)},disabled:!ek},ez):ez});T.displayName="Overflow",T.Item=S,T.RESPONSIVE=k,T.INVALIDATE=M;var P=T},4068:function(e,t){"use strict";t.Z={items_per_page:"\u6761/\u9875",jump_to:"\u8DF3\u81F3",jump_to_confirm:"\u786E\u5B9A",page:"\u9875",prev_page:"\u4E0A\u4E00\u9875",next_page:"\u4E0B\u4E00\u9875",prev_5:"\u5411\u524D 5 \u9875",next_5:"\u5411\u540E 5 \u9875",prev_3:"\u5411\u524D 3 \u9875",next_3:"\u5411\u540E 3 \u9875",page_size:"\u9875\u7801"}},43980:function(e,t,n){"use strict";var r=n(19741),i=n(89746);t.Z=(0,r.Z)((0,r.Z)({},i.z),{},{locale:"zh_CN",today:"\u4ECA\u5929",now:"\u6B64\u523B",backToToday:"\u8FD4\u56DE\u4ECA\u5929",ok:"\u786E\u5B9A",timeSelect:"\u9009\u62E9\u65F6\u95F4",dateSelect:"\u9009\u62E9\u65E5\u671F",weekSelect:"\u9009\u62E9\u5468",clear:"\u6E05\u9664",week:"\u5468",month:"\u6708",year:"\u5E74",previousMonth:"\u4E0A\u4E2A\u6708 (\u7FFB\u9875\u4E0A\u952E)",nextMonth:"\u4E0B\u4E2A\u6708 (\u7FFB\u9875\u4E0B\u952E)",monthSelect:"\u9009\u62E9\u6708\u4EFD",yearSelect:"\u9009\u62E9\u5E74\u4EFD",decadeSelect:"\u9009\u62E9\u5E74\u4EE3",previousYear:"\u4E0A\u4E00\u5E74 (Control\u952E\u52A0\u5DE6\u65B9\u5411\u952E)",nextYear:"\u4E0B\u4E00\u5E74 (Control\u952E\u52A0\u53F3\u65B9\u5411\u952E)",previousDecade:"\u4E0A\u4E00\u5E74\u4EE3",nextDecade:"\u4E0B\u4E00\u5E74\u4EE3",previousCentury:"\u4E0A\u4E00\u4E16\u7EAA",nextCentury:"\u4E0B\u4E00\u4E16\u7EAA",yearFormat:"YYYY\u5E74",cellDateFormat:"D",monthBeforeYear:!1})},86708:function(e,t,n){"use strict";n.d(t,{Z:()=>_});var r=n(25833),i=n(52983),o=n(56256);n(37326);var a=n(19741),l=n(52655),s=n(81129),c=n(22878),u=i.createContext(null),d=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some(function(e,r){return e[0]===t&&(n=r,!0)}),n}function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),r=this.__entries__[n];return r&&r[1]},t.prototype.set=function(t,n){var r=e(this.__entries__,t);~r?this.__entries__[r][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,r=e(n,t);~r&&n.splice(r,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var i=r[n];e.call(t,i[1],i[0])}},t}(),f="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,p=void 0!==n.g&&n.g.Math===Math?n.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),h="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(p):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)},m=["top","right","bottom","left","width","height","size","weight"],g="undefined"!=typeof MutationObserver,v=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var n=!1,r=!1,i=0;function o(){n&&(n=!1,e()),r&&l()}function a(){h(o)}function l(){var e=Date.now();if(n){if(e-i<2)return;r=!0}else n=!0,r=!1,setTimeout(a,20);i=e}return l}(this.refresh.bind(this),0)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter(function(e){return e.gatherActive(),e.hasActive()});return e.forEach(function(e){return e.broadcastActive()}),e.length>0},e.prototype.connect_=function(){f&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),g?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){f&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;m.some(function(e){return!!~n.indexOf(e)})&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),y=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var i=r[n];Object.defineProperty(e,i,{value:t[i],enumerable:!1,writable:!1,configurable:!0})}return e},b=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||p},x=w(0,0,0,0);function Z(e){return parseFloat(e)||0}function C(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce(function(t,n){return t+Z(e["border-"+n+"-width"])},0)}var S="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof b(e).SVGGraphicsElement}:function(e){return e instanceof b(e).SVGElement&&"function"==typeof e.getBBox};function w(e,t,n,r){return{x:e,y:t,width:n,height:r}}var k=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=w(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=function(e){if(!f)return x;if(S(e)){var t;return w(0,0,(t=e.getBBox()).width,t.height)}return function(e){var t,n=e.clientWidth,r=e.clientHeight;if(!n&&!r)return x;var i=b(e).getComputedStyle(e),o=function(e){for(var t={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var i=r[n],o=e["padding-"+i];t[i]=Z(o)}return t}(i),a=o.left+o.right,l=o.top+o.bottom,s=Z(i.width),c=Z(i.height);if("border-box"===i.boxSizing&&(Math.round(s+a)!==n&&(s-=C(i,"left","right")+a),Math.round(c+l)!==r&&(c-=C(i,"top","bottom")+l)),(t=e)!==b(t).document.documentElement){var u=Math.round(s+a)-n,d=Math.round(c+l)-r;1!==Math.abs(u)&&(s-=u),1!==Math.abs(d)&&(c-=d)}return w(o.left,o.top,s,c)}(e)}(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),M=function(e,t){var n,r,i,o,a,l=(n=t.x,r=t.y,i=t.width,o=t.height,y(a=Object.create(("undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object).prototype),{x:n,y:r,width:i,height:o,top:r,right:n+i,bottom:o+r,left:n}),a);y(this,{target:e,contentRect:l})},F=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new d,"function"!=typeof e)throw TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof b(e).Element))throw TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new k(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof b(e).Element))throw TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(t){t.isActive()&&e.activeObservations_.push(t)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map(function(e){return new M(e.target,e.broadcastRect())});this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),T="undefined"!=typeof WeakMap?new WeakMap:new d,P=function e(t){if(!(this instanceof e))throw TypeError("Cannot call a class as a function.");if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");var n=new F(t,v.getInstance(),this);T.set(this,n)};["observe","unobserve","disconnect"].forEach(function(e){P.prototype[e]=function(){var t;return(t=T.get(this))[e].apply(t,arguments)}});var E=void 0!==p.ResizeObserver?p.ResizeObserver:P,R=new Map,I=new E(function(e){e.forEach(function(e){var t,n=e.target;null==(t=R.get(n))||t.forEach(function(e){return e(n)})})}),j=n(71123),L=n(83739),A=n(14425),O=n(81087),N=function(e){(0,A.Z)(n,e);var t=(0,O.Z)(n);function n(){return(0,j.Z)(this,n),t.apply(this,arguments)}return(0,L.Z)(n,[{key:"render",value:function(){return this.props.children}}]),n}(i.Component),D=i.forwardRef(function(e,t){var n=e.children,r=e.disabled,o=i.useRef(null),d=i.useRef(null),f=i.useContext(u),p="function"==typeof n,h=p?n(o):n,m=i.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),g=!p&&i.isValidElement(h)&&(0,c.Yr)(h),v=g?(0,c.C4)(h):null,y=(0,c.x1)(v,o),b=function(){var e;return(0,s.ZP)(o.current)||(o.current&&"object"===(0,l.Z)(o.current)?(0,s.ZP)(null==(e=o.current)?void 0:e.nativeElement):null)||(0,s.ZP)(d.current)};i.useImperativeHandle(t,function(){return b()});var x=i.useRef(e);x.current=e;var Z=i.useCallback(function(e){var t=x.current,n=t.onResize,r=t.data,i=e.getBoundingClientRect(),o=i.width,l=i.height,s=e.offsetWidth,c=e.offsetHeight,u=Math.floor(o),d=Math.floor(l);if(m.current.width!==u||m.current.height!==d||m.current.offsetWidth!==s||m.current.offsetHeight!==c){var p={width:u,height:d,offsetWidth:s,offsetHeight:c};m.current=p;var h=s===Math.round(o)?o:s,g=c===Math.round(l)?l:c,v=(0,a.Z)((0,a.Z)({},p),{},{offsetWidth:h,offsetHeight:g});null==f||f(v,e,r),n&&Promise.resolve().then(function(){n(v,e)})}},[]);return i.useEffect(function(){var e=b();return e&&!r&&(R.has(e)||(R.set(e,new Set),I.observe(e)),R.get(e).add(Z)),function(){R.has(e)&&(R.get(e).delete(Z),!R.get(e).size&&(I.unobserve(e),R.delete(e)))}},[o.current,r]),i.createElement(N,{ref:d},g?i.cloneElement(h,{ref:y}):h)}),B=i.forwardRef(function(e,t){var n=e.children;return("function"==typeof n?[n]:(0,o.Z)(n)).map(function(n,o){var a=(null==n?void 0:n.key)||"".concat("rc-observer-key","-").concat(o);return i.createElement(D,(0,r.Z)({},e,{key:a,ref:0===o?t:void 0}),n)})});B.Collection=function(e){var t=e.children,n=e.onBatchResize,r=i.useRef(0),o=i.useRef([]),a=i.useContext(u),l=i.useCallback(function(e,t,i){r.current+=1;var l=r.current;o.current.push({size:e,element:t,data:i}),Promise.resolve().then(function(){l===r.current&&(null==n||n(o.current),o.current=[])}),null==a||a(e,t,i)},[n,a]);return i.createElement(u.Provider,{value:l},t)};var _=B},44393:function(e,t,n){"use strict";n.d(t,{G:()=>a,Z:()=>v});var r=n(41229),i=n.n(r),o=n(52983);function a(e){var t=e.children,n=e.prefixCls,r=e.id,a=e.overlayInnerStyle,l=e.bodyClassName,s=e.className,c=e.style;return o.createElement("div",{className:i()("".concat(n,"-content"),s),style:c},o.createElement("div",{className:i()("".concat(n,"-inner"),l),id:r,role:"tooltip",style:a},"function"==typeof t?t():t))}var l=n(25833),s=n(19741),c=n(95504),u=n(49817),d={shiftX:64,adjustY:1},f={adjustX:1,shiftY:!0},p=[0,0],h={left:{points:["cr","cl"],overflow:f,offset:[-4,0],targetOffset:p},right:{points:["cl","cr"],overflow:f,offset:[4,0],targetOffset:p},top:{points:["bc","tc"],overflow:d,offset:[0,-4],targetOffset:p},bottom:{points:["tc","bc"],overflow:d,offset:[0,4],targetOffset:p},topLeft:{points:["bl","tl"],overflow:d,offset:[0,-4],targetOffset:p},leftTop:{points:["tr","tl"],overflow:f,offset:[-4,0],targetOffset:p},topRight:{points:["br","tr"],overflow:d,offset:[0,-4],targetOffset:p},rightTop:{points:["tl","tr"],overflow:f,offset:[4,0],targetOffset:p},bottomRight:{points:["tr","br"],overflow:d,offset:[0,4],targetOffset:p},rightBottom:{points:["bl","br"],overflow:f,offset:[4,0],targetOffset:p},bottomLeft:{points:["tl","bl"],overflow:d,offset:[0,4],targetOffset:p},leftBottom:{points:["br","bl"],overflow:f,offset:[-4,0],targetOffset:p}},m=n(3094),g=["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle","arrowContent","overlay","id","showArrow","classNames","styles"],v=(0,o.forwardRef)(function(e,t){var n,r,d,f=e.overlayClassName,p=e.trigger,v=e.mouseEnterDelay,y=e.mouseLeaveDelay,b=e.overlayStyle,x=e.prefixCls,Z=void 0===x?"rc-tooltip":x,C=e.children,S=e.onVisibleChange,w=e.afterVisibleChange,k=e.transitionName,M=e.animation,F=e.motion,T=e.placement,P=e.align,E=e.destroyTooltipOnHide,R=e.defaultVisible,I=e.getTooltipContainer,j=e.overlayInnerStyle,L=(e.arrowContent,e.overlay),A=e.id,O=e.showArrow,N=e.classNames,D=e.styles,B=(0,c.Z)(e,g),_=(0,m.Z)(A),H=(0,o.useRef)(null);(0,o.useImperativeHandle)(t,function(){return H.current});var z=(0,s.Z)({},B);return"visible"in e&&(z.popupVisible=e.visible),o.createElement(u.Z,(0,l.Z)({popupClassName:i()(f,null==N?void 0:N.root),prefixCls:Z,popup:function(){return o.createElement(a,{key:"content",prefixCls:Z,id:_,bodyClassName:null==N?void 0:N.body,overlayInnerStyle:(0,s.Z)((0,s.Z)({},j),null==D?void 0:D.body)},L)},action:void 0===p?["hover"]:p,builtinPlacements:h,popupPlacement:void 0===T?"right":T,ref:H,popupAlign:void 0===P?{}:P,getPopupContainer:I,onPopupVisibleChange:S,afterPopupVisibleChange:w,popupTransitionName:k,popupAnimation:M,popupMotion:F,defaultPopupVisible:R,autoDestroy:void 0!==E&&E,mouseLeaveDelay:void 0===y?.1:y,popupStyle:(0,s.Z)((0,s.Z)({},b),null==D?void 0:D.root),mouseEnterDelay:void 0===v?0:v,arrow:void 0===O||O},z),(r=(null==(n=o.Children.only(C))?void 0:n.props)||{},d=(0,s.Z)((0,s.Z)({},r),{},{"aria-describedby":L?_:null}),o.cloneElement(C,d)))})},99755:function(e,t,n){"use strict";n.d(t,{Z:()=>a,o:()=>l});var r,i=n(76650);function o(e){var t,n,r="rc-scrollbar-measure-".concat(Math.random().toString(36).substring(7)),o=document.createElement("div");o.id=r;var a=o.style;if(a.position="absolute",a.left="0",a.top="0",a.width="100px",a.height="100px",a.overflow="scroll",e){var l=getComputedStyle(e);a.scrollbarColor=l.scrollbarColor,a.scrollbarWidth=l.scrollbarWidth;var s=getComputedStyle(e,"::-webkit-scrollbar"),c=parseInt(s.width,10),u=parseInt(s.height,10);try{var d=c?"width: ".concat(s.width,";"):"",f=u?"height: ".concat(s.height,";"):"";(0,i.hq)("\n#".concat(r,"::-webkit-scrollbar {\n").concat(d,"\n").concat(f,"\n}"),r)}catch(e){console.error(e),t=c,n=u}}document.body.appendChild(o);var p=e&&t&&!isNaN(t)?t:o.offsetWidth-o.clientWidth,h=e&&n&&!isNaN(n)?n:o.offsetHeight-o.clientHeight;return document.body.removeChild(o),(0,i.jL)(r),{width:p,height:h}}function a(e){return"undefined"==typeof document?0:((e||void 0===r)&&(r=o()),r.width)}function l(e){return"undefined"!=typeof document&&e&&e instanceof Element?o(e):{width:0,height:0}}},3094:function(e,t,n){"use strict";var r,i=n(57987),o=n(19741),a=n(52983),l=0,s=(0,o.Z)({},r||(r=n.t(a,2))).useId;t.Z=s?function(e){var t=s();return e||t}:function(e){var t=a.useState("ssr-id"),n=(0,i.Z)(t,2),r=n[0],o=n[1];return(a.useEffect(function(){var e=l;l+=1,o("rc_unique_".concat(e))},[]),e)?e:r}},45015:function(e,t){"use strict";t.Z=function(){if("undefined"==typeof navigator||"undefined"==typeof window)return!1;var e=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(null==e?void 0:e.substr(0,4))}},66555:function(e,t,n){"use strict";var r=n(52983),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useState,a=r.useEffect,l=r.useLayoutEffect,s=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!i(e,n)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),i=r[0].inst,u=r[1];return l(function(){i.value=n,i.getSnapshot=t,c(i)&&u({inst:i})},[e,n,t]),a(function(){return c(i)&&u({inst:i}),e(function(){c(i)&&u({inst:i})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},66990:function(e,t,n){"use strict";e.exports=n(66555)},9722:function(e,t,n){"use strict";var r,i,o,a=n(97458),l=n(9465),s=n(52983),c=n(30154),u=n(82174);"undefined"!=typeof document&&(window._routeModules={});var d=[{path:"/",children:[{_component:"@_modern_js_src/reply-admin/routes/account-admin/page",id:"reply-admin_account-admin/page",type:"nested",path:"account-admin",lazyImport:()=>Promise.all([n.e("563"),n.e("204"),n.e("393"),n.e("424"),n.e("598"),n.e("62")]).then(n.bind(n,41218)).then(e=>(0,c.u3)(e,"reply-admin_account-admin/page")).catch(c.UC),component:(0,s.lazy)(()=>Promise.all([n.e("563"),n.e("204"),n.e("393"),n.e("424"),n.e("598"),n.e("62")]).then(n.bind(n,41218)).then(e=>(0,c.u3)(e,"reply-admin_account-admin/page")).catch(c.UC))},{_component:"@_modern_js_src/reply-admin/routes/reply-params-admin/page",id:"reply-admin_reply-params-admin/page",type:"nested",path:"reply-params-admin",lazyImport:()=>Promise.all([n.e("563"),n.e("204"),n.e("393"),n.e("424"),n.e("598"),n.e("151")]).then(n.bind(n,14932)).then(e=>(0,c.u3)(e,"reply-admin_reply-params-admin/page")).catch(c.UC),component:(0,s.lazy)(()=>Promise.all([n.e("563"),n.e("204"),n.e("393"),n.e("424"),n.e("598"),n.e("151")]).then(n.bind(n,14932)).then(e=>(0,c.u3)(e,"reply-admin_reply-params-admin/page")).catch(c.UC))},{_component:"@_modern_js_src/reply-admin/routes/reply-record-admin/page",id:"reply-admin_reply-record-admin/page",type:"nested",path:"reply-record-admin",lazyImport:()=>Promise.all([n.e("563"),n.e("204"),n.e("393"),n.e("424"),n.e("260")]).then(n.bind(n,83369)).then(e=>(0,c.u3)(e,"reply-admin_reply-record-admin/page")).catch(c.UC),component:(0,s.lazy)(()=>Promise.all([n.e("563"),n.e("204"),n.e("393"),n.e("424"),n.e("260")]).then(n.bind(n,83369)).then(e=>(0,c.u3)(e,"reply-admin_reply-record-admin/page")).catch(c.UC))}],isRoot:!0,_component:"@_modern_js_src/reply-admin/routes/layout",id:"reply-admin_layout",type:"nested",lazyImport:()=>Promise.resolve().then(n.bind(n,82174)).then(e=>(0,c.u3)(e,"reply-admin_layout")).catch(c.UC),component:u.default}];(0,l.cE)({entryName:"reply-admin",layoutApp:o,routes:d,appInit:i,appConfig:r});var f=n(96712),p=n(57597),h=n(51568),m="function"==typeof p.Z?(0,p.Z)((0,l.te)()):p.Z,g=[];g.push((0,h.NA)((0,f.f)({serverBase:["/frontend/reply-admin"]},(m||{}).router,((m||{}).routerByEntries||{})["reply-admin"],((0,l.nB)()||{}).router))),(0,f.v)(g,m);var v=n(33229),y=n(55241),b=(0,v.s)();(0,y.sY)((0,a.jsx)(b,{}),"root")},84966:function(e,t,n){"use strict";n.d(t,{u:()=>P,s:()=>O,f:()=>$,d:()=>W,o:()=>D,b:()=>a,m:()=>f,U:()=>s,z:()=>Z,S:()=>K,i:()=>v,t:()=>T,O:()=>c,g:()=>U,B:()=>p,e:()=>u,r:()=>F,c:()=>z,n:()=>B,a:()=>d,I:()=>M});var r=n(52983),i=n(2832),o=Object.prototype.hasOwnProperty;let a=new WeakMap,l=()=>{},s=l(),c=Object,u=e=>e===s,d=e=>"function"==typeof e,f=(e,t)=>({...e,...t}),p=e=>d(e.then),h={},m={},g="undefined",v=typeof window!=g,y=typeof document!=g,b=v&&"Deno"in window,x=()=>v&&typeof window.requestAnimationFrame!=g,Z=(e,t)=>{let n=a.get(e);return[()=>!u(t)&&e.get(t)||h,r=>{if(!u(t)){let i=e.get(t);t in m||(m[t]=i),n[5](t,f(i,r),i||h)}},n[6],()=>!u(t)&&t in m?m[t]:!u(t)&&e.get(t)||h]},C=!0,[S,w]=v&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[l,l],k={initFocus:e=>(y&&document.addEventListener("visibilitychange",e),S("focus",e),()=>{y&&document.removeEventListener("visibilitychange",e),w("focus",e)}),initReconnect:e=>{let t=()=>{C=!0,e()},n=()=>{C=!1};return S("online",t),S("offline",n),()=>{w("online",t),w("offline",n)}}},M=!r.useId,F=!v||b,T=e=>x()?window.requestAnimationFrame(e):setTimeout(e,1),P=F?r.useEffect:r.useLayoutEffect,E="undefined"!=typeof navigator&&navigator.connection,R=!F&&E&&(["slow-2g","2g"].includes(E.effectiveType)||E.saveData),I=new WeakMap,j=(e,t)=>c.prototype.toString.call(e)===`[object ${t}]`,L=0,A=e=>{let t,n,r=typeof e,i=j(e,"Date"),o=j(e,"RegExp"),a=j(e,"Object");if(c(e)!==e||i||o)t=i?e.toJSON():"symbol"==r?e.toString():"string"==r?JSON.stringify(e):""+e;else{if(t=I.get(e))return t;if(t=++L+"~",I.set(e,t),Array.isArray(e)){for(n=0,t="@";n<e.length;n++)t+=A(e[n])+",";I.set(e,t)}if(a){t="#";let r=c.keys(e).sort();for(;!u(n=r.pop());)u(e[n])||(t+=n+":"+A(e[n])+",");I.set(e,t)}}return t},O=e=>{if(d(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?A(e):"",t]},N=0,D=()=>++N;async function B(...e){let[t,n,r,o]=e,l=f({populateCache:!0,throwOnError:!0},"boolean"==typeof o?{revalidate:o}:o||{}),c=l.populateCache,h=l.rollbackOnError,m=l.optimisticData,g=e=>"function"==typeof h?h(e):!1!==h,v=l.throwOnError;if(d(n)){let e=[];for(let r of t.keys())!/^\$(inf|sub)\$/.test(r)&&n(t.get(r)._k)&&e.push(r);return Promise.all(e.map(y))}return y(n);async function y(n){let o,[f]=O(n);if(!f)return;let[h,y]=Z(t,f),[b,x,C,S]=a.get(t),w=()=>{let e=b[f];return(d(l.revalidate)?l.revalidate(h().data,n):!1!==l.revalidate)&&(delete C[f],delete S[f],e&&e[0])?e[0](i.QQ).then(()=>h().data):h().data};if(e.length<3)return w();let k=r,M=D();x[f]=[M,0];let F=!u(m),T=h(),P=T.data,E=T._c,R=u(E)?P:E;if(F&&y({data:m=d(m)?m(R,P):m,_c:R}),d(k))try{k=k(R)}catch(e){o=e}if(k&&p(k)){if(k=await k.catch(e=>{o=e}),M!==x[f][0]){if(o)throw o;return k}o&&F&&g(o)&&(c=!0,y({data:R,_c:s}))}if(c&&!o&&(d(c)?y({data:c(k,R),error:s,_c:s}):y({data:k,error:s,_c:s})),x[f][1]=D(),Promise.resolve(w()).then(()=>{y({_c:s})}),o){if(v)throw o;return}return k}}let _=(e,t)=>{for(let n in e)e[n][0]&&e[n][0](t)},H=(e,t)=>{if(!a.has(e)){let n=f(k,t),r=Object.create(null),o=B.bind(s,e),c=l,u=Object.create(null),d=(e,t)=>{let n=u[e]||[];return u[e]=n,n.push(t),()=>n.splice(n.indexOf(t),1)},p=(t,n,r)=>{e.set(t,n);let i=u[t];if(i)for(let e of i)e(n,r)},h=()=>{if(!a.has(e)&&(a.set(e,[r,Object.create(null),Object.create(null),Object.create(null),o,p,d]),!F)){let t=n.initFocus(setTimeout.bind(s,_.bind(s,r,i.N4))),o=n.initReconnect(setTimeout.bind(s,_.bind(s,r,i.l2)));c=()=>{t&&t(),o&&o(),a.delete(e)}}};return h(),[e,o,h,c]}return[e,a.get(e)[4]]},[z,V]=H(new Map),W=f({onLoadingSlow:l,onSuccess:l,onError:l,onErrorRetry:(e,t,n,r,i)=>{let o=n.errorRetryCount,a=i.retryCount,l=~~((Math.random()+.5)*(1<<(a<8?a:8)))*n.errorRetryInterval;(u(o)||!(a>o))&&setTimeout(r,l,i)},onDiscarded:l,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:R?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:R?5e3:3e3,compare:function e(t,n){var r,i;if(t===n)return!0;if(t&&n&&(r=t.constructor)===n.constructor){if(r===Date)return t.getTime()===n.getTime();if(r===RegExp)return t.toString()===n.toString();if(r===Array){if((i=t.length)===n.length)for(;i--&&e(t[i],n[i]););return -1===i}if(!r||"object"==typeof t){for(r in i=0,t)if(o.call(t,r)&&++i&&!o.call(n,r)||!(r in n)||!e(t[r],n[r]))return!1;return Object.keys(n).length===i}}return t!=t&&n!=n},isPaused:()=>!1,cache:z,mutate:V,fallback:{}},{isOnline:()=>C,isVisible:()=>{let e=y&&document.visibilityState;return u(e)||"hidden"!==e}}),$=(e,t)=>{let n=f(e,t);if(t){let{use:r,fallback:i}=e,{use:o,fallback:a}=t;r&&o&&(n.use=r.concat(o)),i&&a&&(n.fallback=f(i,a))}return n},K=(0,r.createContext)({}),U=e=>{let{value:t}=e,n=(0,r.useContext)(K),i=d(t),o=(0,r.useMemo)(()=>i?t(n):t,[i,n,t]),a=(0,r.useMemo)(()=>i?o:$(n,o),[i,n,o]),l=o&&o.provider,c=(0,r.useRef)(s);l&&!c.current&&(c.current=H(l(a.cache||z),o));let u=c.current;return u&&(a.cache=u[0],a.mutate=u[1]),P(()=>{if(u)return u[2]&&u[2](),u[3]},[]),(0,r.createElement)(K.Provider,f(e,{value:a}))}},2832:function(e,t,n){"use strict";n.d(t,{N4:()=>r,QQ:()=>o,aU:()=>a,l2:()=>i});let r=0,i=1,o=2,a=3},52008:function(e,t,n){"use strict";n.d(t,{ko:()=>d,kY:()=>s,s6:()=>u});var r=n(84966),i=n(52983);let o=r.i&&window.__SWR_DEVTOOLS_USE__,a=o?window.__SWR_DEVTOOLS_USE__:[],l=e=>(0,r.a)(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],s=()=>(0,r.m)(r.d,(0,i.useContext)(r.S)),c=a.concat(e=>(t,n,i)=>{let o=n&&((...e)=>{let[i]=(0,r.s)(t),[,,,o]=r.b.get(r.c);if(i.startsWith("$inf$"))return n(...e);let a=o[i];return(0,r.e)(a)?n(...e):(delete o[i],a)});return e(t,o,i)}),u=e=>function(...t){let n=s(),[i,o,a]=l(t),u=(0,r.f)(n,a),d=e,{use:f}=u,p=(f||[]).concat(c);for(let e=p.length;e--;)d=p[e](d);return d(i,o||u.fetcher||null,u)},d=(e,t,n)=>{let r=t[e]||(t[e]=[]);return r.push(n),()=>{let e=r.indexOf(n);e>=0&&(r[e]=r[r.length-1],r.pop())}};o&&(window.__SWR_DEVTOOLS_REACT__=i)},65327:function(e,t,n){"use strict";n.d(t,{J$:()=>f,ZP:()=>p});var r=n(52983),i=n(66990),o=n(84966),a=n(2832),l=n(52008);let s=()=>{},c=s();new WeakMap;let u=r.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),d={dedupe:!0},f=o.O.defineProperty(o.g,"defaultValue",{value:o.d}),p=(0,l.s6)((e,t,n)=>{let{cache:s,compare:c,suspense:f,fallbackData:p,revalidateOnMount:h,revalidateIfStale:m,refreshInterval:g,refreshWhenHidden:v,refreshWhenOffline:y,keepPreviousData:b}=n,[x,Z,C,S]=o.b.get(s),[w,k]=(0,o.s)(e),M=(0,r.useRef)(!1),F=(0,r.useRef)(!1),T=(0,r.useRef)(w),P=(0,r.useRef)(t),E=(0,r.useRef)(n),R=()=>E.current,I=()=>R().isVisible()&&R().isOnline(),[j,L,A,O]=(0,o.z)(s,w),N=(0,r.useRef)({}).current,D=(0,o.e)(p)?(0,o.e)(n.fallback)?o.U:n.fallback[w]:p,B=(e,t)=>{for(let n in N)if("data"===n){if(!c(e[n],t[n])&&(!(0,o.e)(e[n])||!c(q,t[n])))return!1}else if(t[n]!==e[n])return!1;return!0},_=(0,r.useMemo)(()=>{let e=!!w&&!!t&&((0,o.e)(h)?!R().isPaused()&&!f&&!1!==m:h),n=t=>{let n=(0,o.m)(t);return(delete n._k,e)?{isValidating:!0,isLoading:!0,...n}:n},r=j(),i=O(),a=n(r),l=r===i?a:n(i),s=a;return[()=>{let e=n(j());return B(e,s)?(s.data=e.data,s.isLoading=e.isLoading,s.isValidating=e.isValidating,s.error=e.error,s):(s=e,e)},()=>l]},[s,w]),H=(0,i.useSyncExternalStore)((0,r.useCallback)(e=>A(w,(t,n)=>{B(n,t)||e()}),[s,w]),_[0],_[1]),z=!M.current,V=x[w]&&x[w].length>0,W=H.data,$=(0,o.e)(W)?D&&(0,o.B)(D)?u(D):D:W,K=H.error,U=(0,r.useRef)($),q=b?(0,o.e)(W)?(0,o.e)(U.current)?$:U.current:W:$,Y=(!V||!!(0,o.e)(K))&&(z&&!(0,o.e)(h)?h:!R().isPaused()&&(f?!(0,o.e)($)&&m:(0,o.e)($)||m)),X=!!(w&&t&&z&&Y),G=(0,o.e)(H.isValidating)?X:H.isValidating,Q=(0,o.e)(H.isLoading)?X:H.isLoading,J=(0,r.useCallback)(async e=>{let t,r,i=P.current;if(!w||!i||F.current||R().isPaused())return!1;let l=!0,s=e||{},u=!C[w]||!s.dedupe,d=()=>o.I?!F.current&&w===T.current&&M.current:w===T.current,f={isValidating:!1,isLoading:!1},p=()=>{L(f)},h=()=>{let e=C[w];e&&e[1]===r&&delete C[w]},m={isValidating:!0};(0,o.e)(j().data)&&(m.isLoading=!0);try{if(u&&(L(m),n.loadingTimeout&&(0,o.e)(j().data)&&setTimeout(()=>{l&&d()&&R().onLoadingSlow(w,n)},n.loadingTimeout),C[w]=[i(k),(0,o.o)()]),[t,r]=C[w],t=await t,u&&setTimeout(h,n.dedupingInterval),!C[w]||C[w][1]!==r)return u&&d()&&R().onDiscarded(w),!1;f.error=o.U;let e=Z[w];if(!(0,o.e)(e)&&(r<=e[0]||r<=e[1]||0===e[1]))return p(),u&&d()&&R().onDiscarded(w),!1;let a=j().data;f.data=c(a,t)?a:t,u&&d()&&R().onSuccess(t,w,n)}catch(n){h();let e=R(),{shouldRetryOnError:t}=e;!e.isPaused()&&(f.error=n,u&&d()&&(e.onError(n,w,e),(!0===t||(0,o.a)(t)&&t(n))&&(!R().revalidateOnFocus||!R().revalidateOnReconnect||I())&&e.onErrorRetry(n,w,e,e=>{let t=x[w];t&&t[0]&&t[0](a.aU,e)},{retryCount:(s.retryCount||0)+1,dedupe:!0})))}return l=!1,p(),!0},[w,s]),ee=(0,r.useCallback)((...e)=>(0,o.n)(s,T.current,...e),[]);if((0,o.u)(()=>{P.current=t,E.current=n,(0,o.e)(W)||(U.current=W)}),(0,o.u)(()=>{if(!w)return;let e=J.bind(o.U,d),t=0;R().revalidateOnFocus&&(t=Date.now()+R().focusThrottleInterval);let n=(0,l.ko)(w,x,(n,r={})=>{if(n==a.N4){let n=Date.now();R().revalidateOnFocus&&n>t&&I()&&(t=n+R().focusThrottleInterval,e())}else if(n==a.l2)R().revalidateOnReconnect&&I()&&e();else if(n==a.QQ)return J();else if(n==a.aU)return J(r)});return F.current=!1,T.current=w,M.current=!0,L({_k:k}),Y&&((0,o.e)($)||o.r?e():(0,o.t)(e)),()=>{F.current=!0,n()}},[w]),(0,o.u)(()=>{let e;function t(){let t=(0,o.a)(g)?g(j().data):g;t&&-1!==e&&(e=setTimeout(n,t))}function n(){!j().error&&(v||R().isVisible())&&(y||R().isOnline())?J(d).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[g,v,y,w]),(0,r.useDebugValue)(q),f&&(0,o.e)($)&&w){if(!o.I&&o.r)throw Error("Fallback data is required when using Suspense in SSR.");P.current=t,E.current=n,F.current=!1;let e=S[w];if((0,o.e)(e)||u(ee(e)),(0,o.e)(K)){let e=J(d);(0,o.e)(q)||(e.status="fulfilled",e.value=!0),u(e)}else throw K}return{mutate:ee,get data(){return N.data=!0,q},get error(){return N.error=!0,K},get isValidating(){return N.isValidating=!0,G},get isLoading(){return N.isLoading=!0,Q}}})}}]);