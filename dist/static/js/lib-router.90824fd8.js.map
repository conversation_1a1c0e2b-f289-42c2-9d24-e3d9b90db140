{"version": 3, "file": "static/js/lib-router.90824fd8.js", "sources": ["webpack://web-dashboard/../../node_modules/.pnpm/@remix-run+router@1.20.0/node_modules/@remix-run/router/dist/router.js", "webpack://web-dashboard/../../node_modules/.pnpm/react-router-dom@6.27.0_rea_16ea9ffbee2b729323e435fcee79418b/node_modules/react-router-dom/dist/index.js", "webpack://web-dashboard/../../node_modules/.pnpm/react-router@6.27.0_react@18.2.0/node_modules/react-router/dist/index.js"], "sourcesContent": ["/**\n * @remix-run/router v1.20.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Actions represent the type of change to a location value.\n */\nvar Action;\n(function (Action) {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Action[\"Pop\"] = \"POP\";\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Action[\"Push\"] = \"PUSH\";\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Action[\"Replace\"] = \"REPLACE\";\n})(Action || (Action = {}));\nconst PopStateEventType = \"popstate\";\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nfunction createMemoryHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    initialEntries = [\"/\"],\n    initialIndex,\n    v5Compat = false\n  } = options;\n  let entries; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) => createMemoryLocation(entry, typeof entry === \"string\" ? null : entry.state, index === 0 ? \"default\" : undefined));\n  let index = clampIndex(initialIndex == null ? entries.length - 1 : initialIndex);\n  let action = Action.Pop;\n  let listener = null;\n  function clampIndex(n) {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation() {\n    return entries[index];\n  }\n  function createMemoryLocation(to, state, key) {\n    if (state === void 0) {\n      state = null;\n    }\n    let location = createLocation(entries ? getCurrentLocation().pathname : \"/\", to, state, key);\n    warning(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in memory history: \" + JSON.stringify(to));\n    return location;\n  }\n  function createHref(to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  let history = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\"\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta: 1\n        });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta: 0\n        });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta\n        });\n      }\n    },\n    listen(fn) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    }\n  };\n  return history;\n}\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nfunction createBrowserHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createBrowserLocation(window, globalHistory) {\n    let {\n      pathname,\n      search,\n      hash\n    } = window.location;\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createBrowserHref(window, to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  return getUrlBasedHistory(createBrowserLocation, createBrowserHref, null, options);\n}\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nfunction createHashHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createHashLocation(window, globalHistory) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\"\n    } = parsePath(window.location.hash.substr(1));\n    // Hash URL should always have a leading / just like window.location.pathname\n    // does, so if an app ends up at a route like /#something then we add a\n    // leading slash so all of our path-matching behaves the same as if it would\n    // in a browser router.  This is particularly important when there exists a\n    // root splat route (<Route path=\"*\">) since that matches internally against\n    // \"/*\" and we'd expect /#something to 404 in a hash router app.\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createHashHref(window, to) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n  function validateHashLocation(location, to) {\n    warning(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in hash history.push(\" + JSON.stringify(to) + \")\");\n  }\n  return getUrlBasedHistory(createHashLocation, createHashHref, validateHashLocation, options);\n}\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience, so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location, index) {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index\n  };\n}\n/**\n * Creates a Location object with a unique key from the given Path\n */\nfunction createLocation(current, to, state, key) {\n  if (state === void 0) {\n    state = null;\n  }\n  let location = _extends({\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\"\n  }, typeof to === \"string\" ? parsePath(to) : to, {\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: to && to.key || key || createKey()\n  });\n  return location;\n}\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nfunction createPath(_ref) {\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\"\n  } = _ref;\n  if (search && search !== \"?\") pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\") pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nfunction parsePath(path) {\n  let parsedPath = {};\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n  return parsedPath;\n}\nfunction getUrlBasedHistory(getLocation, createHref, validateLocation, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    window = document.defaultView,\n    v5Compat = false\n  } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener = null;\n  let index = getIndex();\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), \"\");\n  }\n  function getIndex() {\n    let state = globalHistory.state || {\n      idx: null\n    };\n    return state.idx;\n  }\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({\n        action,\n        location: history.location,\n        delta\n      });\n    }\n  }\n  function push(to, state) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location,\n        delta: 1\n      });\n    }\n  }\n  function replace(to, state) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location,\n        delta: 0\n      });\n    }\n  }\n  function createURL(to) {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base = window.location.origin !== \"null\" ? window.location.origin : window.location.href;\n    let href = typeof to === \"string\" ? to : createPath(to);\n    // Treating this as a full URL will strip any trailing spaces so we need to\n    // pre-encode them since they might be part of a matching splat param from\n    // an ancestor route\n    href = href.replace(/ $/, \"%20\");\n    invariant(base, \"No window.location.(origin|href) available to create URL for href: \" + href);\n    return new URL(href, base);\n  }\n  let history = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    }\n  };\n  return history;\n}\n//#endregion\n\nvar ResultType;\n(function (ResultType) {\n  ResultType[\"data\"] = \"data\";\n  ResultType[\"deferred\"] = \"deferred\";\n  ResultType[\"redirect\"] = \"redirect\";\n  ResultType[\"error\"] = \"error\";\n})(ResultType || (ResultType = {}));\nconst immutableRouteKeys = new Set([\"lazy\", \"caseSensitive\", \"path\", \"id\", \"index\", \"children\"]);\nfunction isIndexRoute(route) {\n  return route.index === true;\n}\n// Walk the route tree generating unique IDs where necessary, so we are working\n// solely with AgnosticDataRouteObject's within the Router\nfunction convertRoutesToDataRoutes(routes, mapRouteProperties, parentPath, manifest) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  if (manifest === void 0) {\n    manifest = {};\n  }\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, String(index)];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(route.index !== true || !route.children, \"Cannot specify children on an index route\");\n    invariant(!manifest[id], \"Found a route id collision on id \\\"\" + id + \"\\\".  Route \" + \"id's must be globally unique within Data Router usages\");\n    if (isIndexRoute(route)) {\n      let indexRoute = _extends({}, route, mapRouteProperties(route), {\n        id\n      });\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute = _extends({}, route, mapRouteProperties(route), {\n        id,\n        children: undefined\n      });\n      manifest[id] = pathOrLayoutRoute;\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(route.children, mapRouteProperties, treePath, manifest);\n      }\n      return pathOrLayoutRoute;\n    }\n  });\n}\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\nfunction matchRoutes(routes, locationArg, basename) {\n  if (basename === void 0) {\n    basename = \"/\";\n  }\n  return matchRoutesImpl(routes, locationArg, basename, false);\n}\nfunction matchRoutesImpl(routes, locationArg, basename, allowPartial) {\n  let location = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n  if (pathname == null) {\n    return null;\n  }\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch(branches[i], decoded, allowPartial);\n  }\n  return matches;\n}\nfunction convertRouteMatchToUiMatch(match, loaderData) {\n  let {\n    route,\n    pathname,\n    params\n  } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle\n  };\n}\nfunction flattenRoutes(routes, branches, parentsMeta, parentPath) {\n  if (branches === void 0) {\n    branches = [];\n  }\n  if (parentsMeta === void 0) {\n    parentsMeta = [];\n  }\n  if (parentPath === void 0) {\n    parentPath = \"\";\n  }\n  let flattenRoute = (route, index, relativePath) => {\n    let meta = {\n      relativePath: relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route\n    };\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(meta.relativePath.startsWith(parentPath), \"Absolute route path \\\"\" + meta.relativePath + \"\\\" nested under path \" + (\"\\\"\" + parentPath + \"\\\" is not valid. An absolute child route path \") + \"must start with the combined path of all its parent routes.\");\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n    // Add the children before adding this route to the array, so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n      // Our types know better, but runtime JS may not!\n      // @ts-expect-error\n      route.index !== true, \"Index routes must not have child routes. Please remove \" + (\"all child routes from route path \\\"\" + path + \"\\\".\"));\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta\n    });\n  };\n  routes.forEach((route, index) => {\n    var _route$path;\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !((_route$path = route.path) != null && _route$path.includes(\"?\"))) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n  return branches;\n}\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path) {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n  let [first, ...rest] = segments;\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n  let result = [];\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children, so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explode _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(...restExploded.map(subpath => subpath === \"\" ? required : [required, subpath].join(\"/\")));\n  // Then, if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map(exploded => path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded);\n}\nfunction rankRouteBranches(branches) {\n  branches.sort((a, b) => a.score !== b.score ? b.score - a.score // Higher score first\n  : compareIndexes(a.routesMeta.map(meta => meta.childrenIndex), b.routesMeta.map(meta => meta.childrenIndex)));\n}\nconst paramRe = /^:[\\w-]+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = s => s === \"*\";\nfunction computeScore(path, index) {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n  return segments.filter(s => !isSplat(s)).reduce((score, segment) => score + (paramRe.test(segment) ? dynamicSegmentValue : segment === \"\" ? emptySegmentValue : staticSegmentValue), initialScore);\n}\nfunction compareIndexes(a, b) {\n  let siblings = a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n  return siblings ?\n  // If two routes are siblings, we should try to match the earlier sibling\n  // first. This allows people to have fine-grained control over the matching\n  // behavior by simply putting routes with identical paths in the order they\n  // want them tried.\n  a[a.length - 1] - b[b.length - 1] :\n  // Otherwise, it doesn't really make sense to rank non-siblings by index,\n  // so they sort equally.\n  0;\n}\nfunction matchRouteBranch(branch, pathname, allowPartial) {\n  if (allowPartial === void 0) {\n    allowPartial = false;\n  }\n  let {\n    routesMeta\n  } = branch;\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname = matchedPathname === \"/\" ? pathname : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath({\n      path: meta.relativePath,\n      caseSensitive: meta.caseSensitive,\n      end\n    }, remainingPathname);\n    let route = meta.route;\n    if (!match && end && allowPartial && !routesMeta[routesMeta.length - 1].route.index) {\n      match = matchPath({\n        path: meta.relativePath,\n        caseSensitive: meta.caseSensitive,\n        end: false\n      }, remainingPathname);\n    }\n    if (!match) {\n      return null;\n    }\n    Object.assign(matchedParams, match.params);\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(joinPaths([matchedPathname, match.pathnameBase])),\n      route\n    });\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n  return matches;\n}\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\nfunction generatePath(originalPath, params) {\n  if (params === void 0) {\n    params = {};\n  }\n  let path = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(false, \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n    path = path.replace(/\\*$/, \"/*\");\n  }\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n  const stringify = p => p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n  const segments = path.split(/\\/+/).map((segment, index, array) => {\n    const isLastSegment = index === array.length - 1;\n    // only apply the splat if it's the last segment\n    if (isLastSegment && segment === \"*\") {\n      const star = \"*\";\n      // Apply the splat\n      return stringify(params[star]);\n    }\n    const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n    if (keyMatch) {\n      const [, key, optional] = keyMatch;\n      let param = params[key];\n      invariant(optional === \"?\" || param != null, \"Missing \\\":\" + key + \"\\\" param\");\n      return stringify(param);\n    }\n    // Remove any optional markers from optional static segments\n    return segment.replace(/\\?$/g, \"\");\n  })\n  // Remove empty segments\n  .filter(segment => !!segment);\n  return prefix + segments.join(\"/\");\n}\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\nfunction matchPath(pattern, pathname) {\n  if (typeof pattern === \"string\") {\n    pattern = {\n      path: pattern,\n      caseSensitive: false,\n      end: true\n    };\n  }\n  let [matcher, compiledParams] = compilePath(pattern.path, pattern.caseSensitive, pattern.end);\n  let match = pathname.match(matcher);\n  if (!match) return null;\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params = compiledParams.reduce((memo, _ref, index) => {\n    let {\n      paramName,\n      isOptional\n    } = _ref;\n    // We need to compute the pathnameBase here using the raw splat value\n    // instead of using params[\"*\"] later because it will be decoded then\n    if (paramName === \"*\") {\n      let splatValue = captureGroups[index] || \"\";\n      pathnameBase = matchedPathname.slice(0, matchedPathname.length - splatValue.length).replace(/(.)\\/+$/, \"$1\");\n    }\n    const value = captureGroups[index];\n    if (isOptional && !value) {\n      memo[paramName] = undefined;\n    } else {\n      memo[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n    }\n    return memo;\n  }, {});\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern\n  };\n}\nfunction compilePath(path, caseSensitive, end) {\n  if (caseSensitive === void 0) {\n    caseSensitive = false;\n  }\n  if (end === void 0) {\n    end = true;\n  }\n  warning(path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"), \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n  let params = [];\n  let regexpSource = \"^\" + path.replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n  .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n  .replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n  .replace(/\\/:([\\w-]+)(\\?)?/g, (_, paramName, isOptional) => {\n    params.push({\n      paramName,\n      isOptional: isOptional != null\n    });\n    return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n  });\n  if (path.endsWith(\"*\")) {\n    params.push({\n      paramName: \"*\"\n    });\n    regexpSource += path === \"*\" || path === \"/*\" ? \"(.*)$\" // Already matched the initial /, just match the rest\n    : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex, so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else ;\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n  return [matcher, params];\n}\nfunction decodePath(value) {\n  try {\n    return value.split(\"/\").map(v => decodeURIComponent(v).replace(/\\//g, \"%2F\")).join(\"/\");\n  } catch (error) {\n    warning(false, \"The URL path \\\"\" + value + \"\\\" could not be decoded because it is is a \" + \"malformed URL segment. This is probably due to a bad percent \" + (\"encoding (\" + error + \").\"));\n    return value;\n  }\n}\n/**\n * @private\n */\nfunction stripBasename(pathname, basename) {\n  if (basename === \"/\") return pathname;\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\") ? basename.length - 1 : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n  return pathname.slice(startIndex) || \"/\";\n}\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\nfunction resolvePath(to, fromPathname) {\n  if (fromPathname === void 0) {\n    fromPathname = \"/\";\n  }\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\"\n  } = typeof to === \"string\" ? parsePath(to) : to;\n  let pathname = toPathname ? toPathname.startsWith(\"/\") ? toPathname : resolvePathname(toPathname, fromPathname) : fromPathname;\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash)\n  };\n}\nfunction resolvePathname(relativePath, fromPathname) {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n  relativeSegments.forEach(segment => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\nfunction getInvalidPathError(char, field, dest, path) {\n  return \"Cannot include a '\" + char + \"' character in a manually specified \" + (\"`to.\" + field + \"` field [\" + JSON.stringify(path) + \"].  Please separate it out to the \") + (\"`to.\" + dest + \"` field. Alternatively you may provide the full path as \") + \"a string in <Link to=\\\"...\\\"> and the router will parse it for you.\";\n}\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nfunction getPathContributingMatches(matches) {\n  return matches.filter((match, index) => index === 0 || match.route.path && match.route.path.length > 0);\n}\n// Return the array of pathnames for the current route matches - used to\n// generate the routePathnames input for resolveTo()\nfunction getResolveToMatches(matches, v7_relativeSplatPath) {\n  let pathMatches = getPathContributingMatches(matches);\n  // When v7_relativeSplatPath is enabled, use the full pathname for the leaf\n  // match so we include splat values for \".\" links.  See:\n  // https://github.com/remix-run/react-router/issues/11052#issuecomment-**********\n  if (v7_relativeSplatPath) {\n    return pathMatches.map((match, idx) => idx === pathMatches.length - 1 ? match.pathname : match.pathnameBase);\n  }\n  return pathMatches.map(match => match.pathnameBase);\n}\n/**\n * @private\n */\nfunction resolveTo(toArg, routePathnames, locationPathname, isPathRelative) {\n  if (isPathRelative === void 0) {\n    isPathRelative = false;\n  }\n  let to;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = _extends({}, toArg);\n    invariant(!to.pathname || !to.pathname.includes(\"?\"), getInvalidPathError(\"?\", \"pathname\", \"search\", to));\n    invariant(!to.pathname || !to.pathname.includes(\"#\"), getInvalidPathError(\"#\", \"pathname\", \"hash\", to));\n    invariant(!to.search || !to.search.includes(\"#\"), getInvalidPathError(\"#\", \"search\", \"hash\", to));\n  }\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n  let from;\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n    // With relative=\"route\" (the default), each leading .. segment means\n    // \"go up one route\" instead of \"go up one URL segment\".  This is a key\n    // difference from how <a href> works and a major reason we call this a\n    // \"to\" value instead of a \"href\".\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n      to.pathname = toSegments.join(\"/\");\n    }\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n  let path = resolvePath(to, from);\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash = toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash = (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (!path.pathname.endsWith(\"/\") && (hasExplicitTrailingSlash || hasCurrentTrailingSlash)) {\n    path.pathname += \"/\";\n  }\n  return path;\n}\n/**\n * @private\n */\nfunction getToPathname(to) {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || to.pathname === \"\" ? \"/\" : typeof to === \"string\" ? parsePath(to).pathname : to.pathname;\n}\n/**\n * @private\n */\nconst joinPaths = paths => paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n/**\n * @private\n */\nconst normalizePathname = pathname => pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n/**\n * @private\n */\nconst normalizeSearch = search => !search || search === \"?\" ? \"\" : search.startsWith(\"?\") ? search : \"?\" + search;\n/**\n * @private\n */\nconst normalizeHash = hash => !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\nconst json = function json(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n  return new Response(JSON.stringify(data), _extends({}, responseInit, {\n    headers\n  }));\n};\nclass DataWithResponseInit {\n  constructor(data, init) {\n    this.type = \"DataWithResponseInit\";\n    this.data = data;\n    this.init = init || null;\n  }\n}\n/**\n * Create \"responses\" that contain `status`/`headers` without forcing\n * serialization into an actual `Response` - used by Remix single fetch\n */\nfunction data(data, init) {\n  return new DataWithResponseInit(data, typeof init === \"number\" ? {\n    status: init\n  } : init);\n}\nclass AbortedDeferredError extends Error {}\nclass DeferredData {\n  constructor(data, responseInit) {\n    this.pendingKeysSet = new Set();\n    this.subscribers = new Set();\n    this.deferredKeys = [];\n    invariant(data && typeof data === \"object\" && !Array.isArray(data), \"defer() only accepts plain objects\");\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject;\n    this.abortPromise = new Promise((_, r) => reject = r);\n    this.controller = new AbortController();\n    let onAbort = () => reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () => this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n    this.data = Object.entries(data).reduce((acc, _ref2) => {\n      let [key, value] = _ref2;\n      return Object.assign(acc, {\n        [key]: this.trackPromise(key, value)\n      });\n    }, {});\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n    this.init = responseInit;\n  }\n  trackPromise(key, value) {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise = Promise.race([value, this.abortPromise]).then(data => this.onSettle(promise, key, undefined, data), error => this.onSettle(promise, key, error));\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n    Object.defineProperty(promise, \"_tracked\", {\n      get: () => true\n    });\n    return promise;\n  }\n  onSettle(promise, key, error, data) {\n    if (this.controller.signal.aborted && error instanceof AbortedDeferredError) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      return Promise.reject(error);\n    }\n    this.pendingKeysSet.delete(key);\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\"Deferred data for key \\\"\" + key + \"\\\" resolved/rejected with `undefined`, \" + \"you must resolve/reject with a value or `null`.\");\n      Object.defineProperty(promise, \"_error\", {\n        get: () => undefinedError\n      });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n    Object.defineProperty(promise, \"_data\", {\n      get: () => data\n    });\n    this.emit(false, key);\n    return data;\n  }\n  emit(aborted, settledKey) {\n    this.subscribers.forEach(subscriber => subscriber(aborted, settledKey));\n  }\n  subscribe(fn) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n  async resolveData(signal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise(resolve => {\n        this.subscribe(aborted => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n  get unwrappedData() {\n    invariant(this.data !== null && this.done, \"Can only unwrap data on initialized and settled deferreds\");\n    return Object.entries(this.data).reduce((acc, _ref3) => {\n      let [key, value] = _ref3;\n      return Object.assign(acc, {\n        [key]: unwrapTrackedPromise(value)\n      });\n    }, {});\n  }\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\nfunction isTrackedPromise(value) {\n  return value instanceof Promise && value._tracked === true;\n}\nfunction unwrapTrackedPromise(value) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\nconst defer = function defer(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  return new DeferredData(data, responseInit);\n};\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nconst redirect = function redirect(url, init) {\n  if (init === void 0) {\n    init = 302;\n  }\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = {\n      status: responseInit\n    };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n  return new Response(null, _extends({}, responseInit, {\n    headers\n  }));\n};\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nconst redirectDocument = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\n/**\n * A redirect response that will perform a `history.replaceState` instead of a\n * `history.pushState` for client-side navigation redirects.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nconst replace = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Replace\", \"true\");\n  return response;\n};\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n *\n * We don't export the class for public use since it's an implementation\n * detail, but we export the interface above so folks can build their own\n * abstractions around instances via isRouteErrorResponse()\n */\nclass ErrorResponseImpl {\n  constructor(status, statusText, data, internal) {\n    if (internal === void 0) {\n      internal = false;\n    }\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nfunction isRouteErrorResponse(error) {\n  return error != null && typeof error.status === \"number\" && typeof error.statusText === \"string\" && typeof error.internal === \"boolean\" && \"data\" in error;\n}\n\nconst validMutationMethodsArr = [\"post\", \"put\", \"patch\", \"delete\"];\nconst validMutationMethods = new Set(validMutationMethodsArr);\nconst validRequestMethodsArr = [\"get\", ...validMutationMethodsArr];\nconst validRequestMethods = new Set(validRequestMethodsArr);\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\nconst IDLE_NAVIGATION = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined\n};\nconst IDLE_FETCHER = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined\n};\nconst IDLE_BLOCKER = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined\n};\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\nconst defaultMapRouteProperties = route => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary)\n});\nconst TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Create a router and listen to history POP navigations\n */\nfunction createRouter(init) {\n  const routerWindow = init.window ? init.window : typeof window !== \"undefined\" ? window : undefined;\n  const isBrowser = typeof routerWindow !== \"undefined\" && typeof routerWindow.document !== \"undefined\" && typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n  invariant(init.routes.length > 0, \"You must provide a non-empty routes array to createRouter\");\n  let mapRouteProperties;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = route => ({\n      hasErrorBoundary: detectErrorBoundary(route)\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Routes keyed by ID\n  let manifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(init.routes, mapRouteProperties, undefined, manifest);\n  let inFlightDataRoutes;\n  let basename = init.basename || \"/\";\n  let dataStrategyImpl = init.dataStrategy || defaultDataStrategy;\n  let patchRoutesOnNavigationImpl = init.patchRoutesOnNavigation;\n  // Config driven behavior flags\n  let future = _extends({\n    v7_fetcherPersist: false,\n    v7_normalizeFormMethod: false,\n    v7_partialHydration: false,\n    v7_prependBasename: false,\n    v7_relativeSplatPath: false,\n    v7_skipActionErrorRevalidation: false\n  }, init.future);\n  // Cleanup function for history\n  let unlistenHistory = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialErrors = null;\n  if (initialMatches == null && !patchRoutesOnNavigationImpl) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname\n    });\n    let {\n      matches,\n      route\n    } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = {\n      [route.id]: error\n    };\n  }\n  // In SPA apps, if the user provided a patchRoutesOnNavigation implementation and\n  // our initial match is a splat route, clear them out so we run through lazy\n  // discovery on hydration in case there's a more accurate lazy route match.\n  // In SSR apps (with `hydrationData`), we expect that the server will send\n  // up the proper matched routes so we don't want to run lazy discovery on\n  // initial hydration and want to hydrate into the splat route.\n  if (initialMatches && !init.hydrationData) {\n    let fogOfWar = checkFogOfWar(initialMatches, dataRoutes, init.history.location.pathname);\n    if (fogOfWar.active) {\n      initialMatches = null;\n    }\n  }\n  let initialized;\n  if (!initialMatches) {\n    initialized = false;\n    initialMatches = [];\n    // If partial hydration and fog of war is enabled, we will be running\n    // `patchRoutesOnNavigation` during hydration so include any partial matches as\n    // the initial matches so we can properly render `HydrateFallback`'s\n    if (future.v7_partialHydration) {\n      let fogOfWar = checkFogOfWar(null, dataRoutes, init.history.location.pathname);\n      if (fogOfWar.active && fogOfWar.matches) {\n        initialMatches = fogOfWar.matches;\n      }\n    }\n  } else if (initialMatches.some(m => m.route.lazy)) {\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    initialized = false;\n  } else if (!initialMatches.some(m => m.route.loader)) {\n    // If we've got no loaders to run, then we're good to go\n    initialized = true;\n  } else if (future.v7_partialHydration) {\n    // If partial hydration is enabled, we're initialized so long as we were\n    // provided with hydrationData for every route with a loader, and no loaders\n    // were marked for explicit hydration\n    let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n    let errors = init.hydrationData ? init.hydrationData.errors : null;\n    // If errors exist, don't consider routes below the boundary\n    if (errors) {\n      let idx = initialMatches.findIndex(m => errors[m.route.id] !== undefined);\n      initialized = initialMatches.slice(0, idx + 1).every(m => !shouldLoadRouteOnHydration(m.route, loaderData, errors));\n    } else {\n      initialized = initialMatches.every(m => !shouldLoadRouteOnHydration(m.route, loaderData, errors));\n    }\n  } else {\n    // Without partial hydration - we're initialized if we were provided any\n    // hydrationData - which is expected to be complete\n    initialized = init.hydrationData != null;\n  }\n  let router;\n  let state = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: init.hydrationData && init.hydrationData.loaderData || {},\n    actionData: init.hydrationData && init.hydrationData.actionData || null,\n    errors: init.hydrationData && init.hydrationData.errors || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map()\n  };\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction = Action.Pop;\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n  // AbortController for the active navigation\n  let pendingNavigationController;\n  // Should the current navigation enable document.startViewTransition?\n  let pendingViewTransitionEnabled = false;\n  // Store applied view transitions so we can apply them on POP\n  let appliedViewTransitions = new Map();\n  // Cleanup function for persisting applied transitions to sessionStorage\n  let removePageHideEventListener = null;\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes = [];\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads = new Set();\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map();\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map();\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set();\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map();\n  // Ref-count mounted fetchers so we know when it's ok to clean them up\n  let activeFetchers = new Map();\n  // Fetchers that have requested a delete when using v7_fetcherPersist,\n  // they'll be officially removed after they return to idle\n  let deletedFetchers = new Set();\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map();\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map();\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let unblockBlockerHistoryUpdate = undefined;\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(_ref => {\n      let {\n        action: historyAction,\n        location,\n        delta\n      } = _ref;\n      // Ignore this event if it was just us resetting the URL from a\n      // blocked POP navigation\n      if (unblockBlockerHistoryUpdate) {\n        unblockBlockerHistoryUpdate();\n        unblockBlockerHistoryUpdate = undefined;\n        return;\n      }\n      warning(blockerFunctions.size === 0 || delta != null, \"You are trying to use a blocker on a POP navigation to a location \" + \"that was not created by @remix-run/router. This will fail silently in \" + \"production. This can happen if you are navigating outside the router \" + \"via `window.history.pushState`/`window.location.hash` instead of using \" + \"router navigation APIs.  This can also happen if you are using \" + \"createHashRouter and the user manually changes the URL.\");\n      let blockerKey = shouldBlockNavigation({\n        currentLocation: state.location,\n        nextLocation: location,\n        historyAction\n      });\n      if (blockerKey && delta != null) {\n        // Restore the URL to match the current UI, but don't update router state\n        let nextHistoryUpdatePromise = new Promise(resolve => {\n          unblockBlockerHistoryUpdate = resolve;\n        });\n        init.history.go(delta * -1);\n        // Put the blocker into a blocked state\n        updateBlocker(blockerKey, {\n          state: \"blocked\",\n          location,\n          proceed() {\n            updateBlocker(blockerKey, {\n              state: \"proceeding\",\n              proceed: undefined,\n              reset: undefined,\n              location\n            });\n            // Re-do the same POP navigation we just blocked, after the url\n            // restoration is also complete.  See:\n            // https://github.com/remix-run/react-router/issues/11613\n            nextHistoryUpdatePromise.then(() => init.history.go(delta));\n          },\n          reset() {\n            let blockers = new Map(state.blockers);\n            blockers.set(blockerKey, IDLE_BLOCKER);\n            updateState({\n              blockers\n            });\n          }\n        });\n        return;\n      }\n      return startNavigation(historyAction, location);\n    });\n    if (isBrowser) {\n      // FIXME: This feels gross.  How can we cleanup the lines between\n      // scrollRestoration/appliedTransitions persistance?\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () => persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () => routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(Action.Pop, state.location, {\n        initialHydration: true\n      });\n    }\n    return router;\n  }\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n  // Subscribe to state updates for the router\n  function subscribe(fn) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n  // Update our state and notify the calling context of the change\n  function updateState(newState, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    state = _extends({}, state, newState);\n    // Prep fetcher cleanup so we can tell the UI which fetcher data entries\n    // can be removed\n    let completedFetchers = [];\n    let deletedFetchersKeys = [];\n    if (future.v7_fetcherPersist) {\n      state.fetchers.forEach((fetcher, key) => {\n        if (fetcher.state === \"idle\") {\n          if (deletedFetchers.has(key)) {\n            // Unmounted from the UI and can be totally removed\n            deletedFetchersKeys.push(key);\n          } else {\n            // Returned to idle but still mounted in the UI, so semi-remains for\n            // revalidations and such\n            completedFetchers.push(key);\n          }\n        }\n      });\n    }\n    // Iterate over a local copy so that if flushSync is used and we end up\n    // removing and adding a new subscriber due to the useCallback dependencies,\n    // we don't get ourselves into a loop calling the new subscriber immediately\n    [...subscribers].forEach(subscriber => subscriber(state, {\n      deletedFetchers: deletedFetchersKeys,\n      viewTransitionOpts: opts.viewTransitionOpts,\n      flushSync: opts.flushSync === true\n    }));\n    // Remove idle fetchers from state since we only care about in-flight fetchers.\n    if (future.v7_fetcherPersist) {\n      completedFetchers.forEach(key => state.fetchers.delete(key));\n      deletedFetchersKeys.forEach(key => deleteFetcher(key));\n    }\n  }\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(location, newState, _temp) {\n    var _location$state, _location$state2;\n    let {\n      flushSync\n    } = _temp === void 0 ? {} : _temp;\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload = state.actionData != null && state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && state.navigation.state === \"loading\" && ((_location$state = location.state) == null ? void 0 : _location$state._isRedirect) !== true;\n    let actionData;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData ? mergeLoaderData(state.loaderData, newState.loaderData, newState.matches || [], newState.errors) : state.loaderData;\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset = pendingPreventScrollReset === true || state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && ((_location$state2 = location.state) == null ? void 0 : _location$state2._isRedirect) !== true;\n    // Commit any in-flight routes at the end of the HMR revalidation \"navigation\"\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n    if (isUninterruptedRevalidation) ; else if (pendingAction === Action.Pop) ; else if (pendingAction === Action.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === Action.Replace) {\n      init.history.replace(location, location.state);\n    }\n    let viewTransitionOpts;\n    // On POP, enable transitions if they were enabled on the original navigation\n    if (pendingAction === Action.Pop) {\n      // Forward takes precedence so they behave like the original navigation\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        // If we don't have a previous forward nav, assume we're popping back to\n        // the new location and enable if that location previously enabled\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      // Store the applied transition on PUSH/REPLACE\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = new Set([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location\n      };\n    }\n    updateState(_extends({}, newState, {\n      actionData,\n      loaderData,\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      restoreScrollPosition: getSavedScrollPosition(location, newState.matches || state.matches),\n      preventScrollReset,\n      blockers\n    }), {\n      viewTransitionOpts,\n      flushSync: flushSync === true\n    });\n    // Reset stateful navigation vars\n    pendingAction = Action.Pop;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n  }\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(to, opts) {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n    let normalizedPath = normalizeTo(state.location, state.matches, basename, future.v7_prependBasename, to, future.v7_relativeSplatPath, opts == null ? void 0 : opts.fromRouteId, opts == null ? void 0 : opts.relative);\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(future.v7_normalizeFormMethod, false, normalizedPath, opts);\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = _extends({}, nextLocation, init.history.encodeLocation(nextLocation));\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n    let historyAction = Action.Push;\n    if (userReplace === true) {\n      historyAction = Action.Replace;\n    } else if (userReplace === false) ; else if (submission != null && isMutationMethod(submission.formMethod) && submission.formAction === state.location.pathname + state.location.search) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = Action.Replace;\n    }\n    let preventScrollReset = opts && \"preventScrollReset\" in opts ? opts.preventScrollReset === true : undefined;\n    let flushSync = (opts && opts.flushSync) === true;\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction\n    });\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey, IDLE_BLOCKER);\n          updateState({\n            blockers\n          });\n        }\n      });\n      return;\n    }\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.viewTransition,\n      flushSync\n    });\n  }\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({\n      revalidation: \"loading\"\n    });\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true\n      });\n      return;\n    }\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(pendingAction || state.historyAction, state.navigation.location, {\n      overrideNavigation: state.navigation,\n      // Proxy through any rending view transition\n      enableViewTransition: pendingViewTransitionEnabled === true\n    });\n  }\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(historyAction, location, opts) {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation = (opts && opts.startUninterruptedRevalidation) === true;\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n    let fogOfWar = checkFogOfWar(matches, routesToUse, location.pathname);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let {\n        error,\n        notFoundMatches,\n        route\n      } = handleNavigational404(location.pathname);\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error\n        }\n      }, {\n        flushSync\n      });\n      return;\n    }\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial hydration will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (state.initialized && !isRevalidationRequired && isHashChangeOnly(state.location, location) && !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))) {\n      completeNavigation(location, {\n        matches\n      }, {\n        flushSync\n      });\n      return;\n    }\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(init.history, location, pendingNavigationController.signal, opts && opts.submission);\n    let pendingActionResult;\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingActionResult = [findNearestBoundary(matches).route.id, {\n        type: ResultType.error,\n        error: opts.pendingError\n      }];\n    } else if (opts && opts.submission && isMutationMethod(opts.submission.formMethod)) {\n      // Call action if we received an action submission\n      let actionResult = await handleAction(request, location, opts.submission, matches, fogOfWar.active, {\n        replace: opts.replace,\n        flushSync\n      });\n      if (actionResult.shortCircuited) {\n        return;\n      }\n      // If we received a 404 from handleAction, it's because we couldn't lazily\n      // discover the destination route so we don't want to call loaders\n      if (actionResult.pendingActionResult) {\n        let [routeId, result] = actionResult.pendingActionResult;\n        if (isErrorResult(result) && isRouteErrorResponse(result.error) && result.error.status === 404) {\n          pendingNavigationController = null;\n          completeNavigation(location, {\n            matches: actionResult.matches,\n            loaderData: {},\n            errors: {\n              [routeId]: result.error\n            }\n          });\n          return;\n        }\n      }\n      matches = actionResult.matches || matches;\n      pendingActionResult = actionResult.pendingActionResult;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n      // No need to do fog of war matching again on loader execution\n      fogOfWar.active = false;\n      // Create a GET request for the loaders\n      request = createClientSideRequest(init.history, request.url, request.signal);\n    }\n    // Call loaders\n    let {\n      shortCircuited,\n      matches: updatedMatches,\n      loaderData,\n      errors\n    } = await handleLoaders(request, location, matches, fogOfWar.active, loadingNavigation, opts && opts.submission, opts && opts.fetcherSubmission, opts && opts.replace, opts && opts.initialHydration === true, flushSync, pendingActionResult);\n    if (shortCircuited) {\n      return;\n    }\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n    completeNavigation(location, _extends({\n      matches: updatedMatches || matches\n    }, getActionDataForCommit(pendingActionResult), {\n      loaderData,\n      errors\n    }));\n  }\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(request, location, submission, matches, isFogOfWar, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    interruptActiveLoads();\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({\n      navigation\n    }, {\n      flushSync: opts.flushSync === true\n    });\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(matches, location.pathname, request.signal);\n      if (discoverResult.type === \"aborted\") {\n        return {\n          shortCircuited: true\n        };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches).route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          pendingActionResult: [boundaryId, {\n            type: ResultType.error,\n            error: discoverResult.error\n          }]\n        };\n      } else if (!discoverResult.matches) {\n        let {\n          notFoundMatches,\n          error,\n          route\n        } = handleNavigational404(location.pathname);\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [route.id, {\n            type: ResultType.error,\n            error\n          }]\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n    // Call our action and get the result\n    let result;\n    let actionMatch = getTargetMatch(matches, location);\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id\n        })\n      };\n    } else {\n      let results = await callDataStrategy(\"action\", state, request, [actionMatch], matches, null);\n      result = results[actionMatch.route.id];\n      if (request.signal.aborted) {\n        return {\n          shortCircuited: true\n        };\n      }\n    }\n    if (isRedirectResult(result)) {\n      let replace;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        let location = normalizeRedirectLocation(result.response.headers.get(\"Location\"), new URL(request.url), basename);\n        replace = location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(request, result, true, {\n        submission,\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n    }\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      // By default, all submissions to the current location are REPLACE\n      // navigations, but if the action threw an error that'll be rendered in\n      // an errorElement, we fall back to PUSH so that the user can use the\n      // back button to get back to the pre-submission form location to try\n      // again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = Action.Push;\n      }\n      return {\n        matches,\n        pendingActionResult: [boundaryMatch.route.id, result]\n      };\n    }\n    return {\n      matches,\n      pendingActionResult: [actionMatch.route.id, result]\n    };\n  }\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(request, location, matches, isFogOfWar, overrideNavigation, submission, fetcherSubmission, replace, initialHydration, flushSync, pendingActionResult) {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation = overrideNavigation || getLoadingNavigation(location, submission);\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission = submission || fetcherSubmission || getSubmissionFromNavigation(loadingNavigation);\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    // If we have partialHydration enabled, then don't update the state for the\n    // initial data load since it's not a \"navigation\"\n    let shouldUpdateNavigationState = !isUninterruptedRevalidation && (!future.v7_partialHydration || !initialHydration);\n    // When fog of war is enabled, we enter our `loading` state earlier so we\n    // can discover new routes during the `loading` state.  We skip this if\n    // we've already run actions since we would have done our matching already.\n    // If the children() function threw then, we want to proceed with the\n    // partial matches it discovered.\n    if (isFogOfWar) {\n      if (shouldUpdateNavigationState) {\n        let actionData = getUpdatedActionData(pendingActionResult);\n        updateState(_extends({\n          navigation: loadingNavigation\n        }, actionData !== undefined ? {\n          actionData\n        } : {}), {\n          flushSync\n        });\n      }\n      let discoverResult = await discoverRoutes(matches, location.pathname, request.signal);\n      if (discoverResult.type === \"aborted\") {\n        return {\n          shortCircuited: true\n        };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches).route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          loaderData: {},\n          errors: {\n            [boundaryId]: discoverResult.error\n          }\n        };\n      } else if (!discoverResult.matches) {\n        let {\n          error,\n          notFoundMatches,\n          route\n        } = handleNavigational404(location.pathname);\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error\n          }\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(init.history, state, matches, activeSubmission, location, future.v7_partialHydration && initialHydration === true, future.v7_skipActionErrorRevalidation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, deletedFetchers, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, pendingActionResult);\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(routeId => !(matches && matches.some(m => m.route.id === routeId)) || matchesToLoad && matchesToLoad.some(m => m.route.id === routeId));\n    pendingNavigationLoadId = ++incrementingLoadId;\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(location, _extends({\n        matches,\n        loaderData: {},\n        // Commit pending error if we're short circuiting\n        errors: pendingActionResult && isErrorResult(pendingActionResult[1]) ? {\n          [pendingActionResult[0]]: pendingActionResult[1].error\n        } : null\n      }, getActionDataForCommit(pendingActionResult), updatedFetchers ? {\n        fetchers: new Map(state.fetchers)\n      } : {}), {\n        flushSync\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    if (shouldUpdateNavigationState) {\n      let updates = {};\n      if (!isFogOfWar) {\n        // Only update navigation/actionNData if we didn't already do it above\n        updates.navigation = loadingNavigation;\n        let actionData = getUpdatedActionData(pendingActionResult);\n        if (actionData !== undefined) {\n          updates.actionData = actionData;\n        }\n      }\n      if (revalidatingFetchers.length > 0) {\n        updates.fetchers = getUpdatedRevalidatingFetchers(revalidatingFetchers);\n      }\n      updateState(updates, {\n        flushSync\n      });\n    }\n    revalidatingFetchers.forEach(rf => {\n      abortFetcher(rf.key);\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () => revalidatingFetchers.forEach(f => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\"abort\", abortPendingFetchRevalidations);\n    }\n    let {\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state, matches, matchesToLoad, revalidatingFetchers, request);\n    if (request.signal.aborted) {\n      return {\n        shortCircuited: true\n      };\n    }\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\"abort\", abortPendingFetchRevalidations);\n    }\n    revalidatingFetchers.forEach(rf => fetchControllers.delete(rf.key));\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    // Process and commit output from loaders\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, matches, loaderResults, pendingActionResult, revalidatingFetchers, fetcherResults, activeDeferreds);\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe(aborted => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n    // Preserve SSR errors during partial hydration\n    if (future.v7_partialHydration && initialHydration && state.errors) {\n      errors = _extends({}, state.errors, errors);\n    }\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers = updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n    return _extends({\n      matches,\n      loaderData,\n      errors\n    }, shouldUpdateFetchers ? {\n      fetchers: new Map(state.fetchers)\n    } : {});\n  }\n  function getUpdatedActionData(pendingActionResult) {\n    if (pendingActionResult && !isErrorResult(pendingActionResult[1])) {\n      // This is cast to `any` currently because `RouteData`uses any and it\n      // would be a breaking change to use any.\n      // TODO: v7 - change `RouteData` to use `unknown` instead of `any`\n      return {\n        [pendingActionResult[0]]: pendingActionResult[1].data\n      };\n    } else if (state.actionData) {\n      if (Object.keys(state.actionData).length === 0) {\n        return null;\n      } else {\n        return state.actionData;\n      }\n    }\n  }\n  function getUpdatedRevalidatingFetchers(revalidatingFetchers) {\n    revalidatingFetchers.forEach(rf => {\n      let fetcher = state.fetchers.get(rf.key);\n      let revalidatingFetcher = getLoadingFetcher(undefined, fetcher ? fetcher.data : undefined);\n      state.fetchers.set(rf.key, revalidatingFetcher);\n    });\n    return new Map(state.fetchers);\n  }\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(key, routeId, href, opts) {\n    if (isServer) {\n      throw new Error(\"router.fetch() was called during the server render, but it shouldn't be. \" + \"You are likely calling a useFetcher() method in the body of your component. \" + \"Try moving it to a useEffect or a callback.\");\n    }\n    abortFetcher(key);\n    let flushSync = (opts && opts.flushSync) === true;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(state.location, state.matches, basename, future.v7_prependBasename, href, future.v7_relativeSplatPath, routeId, opts == null ? void 0 : opts.relative);\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n    let fogOfWar = checkFogOfWar(matches, routesToUse, normalizedPath);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n    if (!matches) {\n      setFetcherError(key, routeId, getInternalRouterError(404, {\n        pathname: normalizedPath\n      }), {\n        flushSync\n      });\n      return;\n    }\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(future.v7_normalizeFormMethod, true, normalizedPath, opts);\n    if (error) {\n      setFetcherError(key, routeId, error, {\n        flushSync\n      });\n      return;\n    }\n    let match = getTargetMatch(matches, path);\n    let preventScrollReset = (opts && opts.preventScrollReset) === true;\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(key, routeId, path, match, matches, fogOfWar.active, flushSync, preventScrollReset, submission);\n      return;\n    }\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, {\n      routeId,\n      path\n    });\n    handleFetcherLoader(key, routeId, path, match, matches, fogOfWar.active, flushSync, preventScrollReset, submission);\n  }\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(key, routeId, path, match, requestMatches, isFogOfWar, flushSync, preventScrollReset, submission) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n    function detectAndHandle405Error(m) {\n      if (!m.route.action && !m.route.lazy) {\n        let error = getInternalRouterError(405, {\n          method: submission.formMethod,\n          pathname: path,\n          routeId: routeId\n        });\n        setFetcherError(key, routeId, error, {\n          flushSync\n        });\n        return true;\n      }\n      return false;\n    }\n    if (!isFogOfWar && detectAndHandle405Error(match)) {\n      return;\n    }\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync\n    });\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(init.history, path, abortController.signal, submission);\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(requestMatches, path, fetchRequest.signal);\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, {\n          flushSync\n        });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(key, routeId, getInternalRouterError(404, {\n          pathname: path\n        }), {\n          flushSync\n        });\n        return;\n      } else {\n        requestMatches = discoverResult.matches;\n        match = getTargetMatch(requestMatches, path);\n        if (detectAndHandle405Error(match)) {\n          return;\n        }\n      }\n    }\n    // Call the action for the fetcher\n    fetchControllers.set(key, abortController);\n    let originatingLoadId = incrementingLoadId;\n    let actionResults = await callDataStrategy(\"action\", state, fetchRequest, [match], requestMatches, key);\n    let actionResult = actionResults[match.route.id];\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n    // When using v7_fetcherPersist, we don't want errors bubbling up to the UI\n    // or redirects processed for unmounted fetchers so we just revert them to\n    // idle\n    if (future.v7_fetcherPersist && deletedFetchers.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      }\n      // Let SuccessResult's fall through for revalidation\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          // A new navigation was kicked off after our action started, so that\n          // should take precedence over this redirect navigation.  We already\n          // set isRevalidationRequired so all loaders for the new route should\n          // fire unless opted out via shouldRevalidate\n          updateFetcherState(key, getDoneFetcher(undefined));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(fetchRequest, actionResult, false, {\n            fetcherSubmission: submission,\n            preventScrollReset\n          });\n        }\n      }\n      // Process any non-redirect errors thrown\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n    }\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(init.history, nextLocation, abortController.signal);\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches = state.navigation.state !== \"idle\" ? matchRoutes(routesToUse, state.navigation.location, basename) : state.matches;\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(init.history, state, matches, submission, nextLocation, false, future.v7_skipActionErrorRevalidation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, deletedFetchers, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, [match.route.id, actionResult]);\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers.filter(rf => rf.key !== key).forEach(rf => {\n      let staleKey = rf.key;\n      let existingFetcher = state.fetchers.get(staleKey);\n      let revalidatingFetcher = getLoadingFetcher(undefined, existingFetcher ? existingFetcher.data : undefined);\n      state.fetchers.set(staleKey, revalidatingFetcher);\n      abortFetcher(staleKey);\n      if (rf.controller) {\n        fetchControllers.set(staleKey, rf.controller);\n      }\n    });\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n    let abortPendingFetchRevalidations = () => revalidatingFetchers.forEach(rf => abortFetcher(rf.key));\n    abortController.signal.addEventListener(\"abort\", abortPendingFetchRevalidations);\n    let {\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state, matches, matchesToLoad, revalidatingFetchers, revalidationRequest);\n    if (abortController.signal.aborted) {\n      return;\n    }\n    abortController.signal.removeEventListener(\"abort\", abortPendingFetchRevalidations);\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach(r => fetchControllers.delete(r.key));\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      return startRedirectNavigation(revalidationRequest, redirect.result, false, {\n        preventScrollReset\n      });\n    }\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      return startRedirectNavigation(revalidationRequest, redirect.result, false, {\n        preventScrollReset\n      });\n    }\n    // Process and commit output from loaders\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, matches, loaderResults, undefined, revalidatingFetchers, fetcherResults, activeDeferreds);\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n    abortStaleFetchLoads(loadId);\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (state.navigation.state === \"loading\" && loadId > pendingNavigationLoadId) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers)\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(state.loaderData, loaderData, matches, errors),\n        fetchers: new Map(state.fetchers)\n      });\n      isRevalidationRequired = false;\n    }\n  }\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(key, routeId, path, match, matches, isFogOfWar, flushSync, preventScrollReset, submission) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getLoadingFetcher(submission, existingFetcher ? existingFetcher.data : undefined), {\n      flushSync\n    });\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(init.history, path, abortController.signal);\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(matches, path, fetchRequest.signal);\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, {\n          flushSync\n        });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(key, routeId, getInternalRouterError(404, {\n          pathname: path\n        }), {\n          flushSync\n        });\n        return;\n      } else {\n        matches = discoverResult.matches;\n        match = getTargetMatch(matches, path);\n      }\n    }\n    // Call the loader for this fetcher route match\n    fetchControllers.set(key, abortController);\n    let originatingLoadId = incrementingLoadId;\n    let results = await callDataStrategy(\"loader\", state, fetchRequest, [match], matches, key);\n    let result = results[match.route.id];\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result = (await resolveDeferredData(result, fetchRequest.signal, true)) || result;\n    }\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n    // We don't want errors bubbling up or redirects followed for unmounted\n    // fetchers, so short circuit here if it was removed from the UI\n    if (deletedFetchers.has(key)) {\n      updateFetcherState(key, getDoneFetcher(undefined));\n      return;\n    }\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(fetchRequest, result, false, {\n          preventScrollReset\n        });\n        return;\n      }\n    }\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n    // Put the fetcher back into an idle state\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(request, redirect, isNavigation, _temp2) {\n    let {\n      submission,\n      fetcherSubmission,\n      preventScrollReset,\n      replace\n    } = _temp2 === void 0 ? {} : _temp2;\n    if (redirect.response.headers.has(\"X-Remix-Revalidate\")) {\n      isRevalidationRequired = true;\n    }\n    let location = redirect.response.headers.get(\"Location\");\n    invariant(location, \"Expected a Location header on the redirect Response\");\n    location = normalizeRedirectLocation(location, new URL(request.url), basename);\n    let redirectLocation = createLocation(state.location, location, {\n      _isRedirect: true\n    });\n    if (isBrowser) {\n      let isDocumentReload = false;\n      if (redirect.response.headers.has(\"X-Remix-Reload-Document\")) {\n        // Hard reload if the response contained X-Remix-Reload-Document\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(location)) {\n        const url = init.history.createURL(location);\n        isDocumentReload =\n        // Hard reload if it's an absolute URL to a new origin\n        url.origin !== routerWindow.location.origin ||\n        // Hard reload if it's an absolute URL that does not match our basename\n        stripBasename(url.pathname, basename) == null;\n      }\n      if (isDocumentReload) {\n        if (replace) {\n          routerWindow.location.replace(location);\n        } else {\n          routerWindow.location.assign(location);\n        }\n        return;\n      }\n    }\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n    let redirectHistoryAction = replace === true || redirect.response.headers.has(\"X-Remix-Replace\") ? Action.Replace : Action.Push;\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let {\n      formMethod,\n      formAction,\n      formEncType\n    } = state.navigation;\n    if (!submission && !fetcherSubmission && formMethod && formAction && formEncType) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    let activeSubmission = submission || fetcherSubmission;\n    if (redirectPreserveMethodStatusCodes.has(redirect.response.status) && activeSubmission && isMutationMethod(activeSubmission.formMethod)) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: _extends({}, activeSubmission, {\n          formAction: location\n        }),\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation ? pendingViewTransitionEnabled : undefined\n      });\n    } else {\n      // If we have a navigation submission, we will preserve it through the\n      // redirect navigation\n      let overrideNavigation = getLoadingNavigation(redirectLocation, submission);\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation ? pendingViewTransitionEnabled : undefined\n      });\n    }\n  }\n  // Utility wrapper for calling dataStrategy client-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(type, state, request, matchesToLoad, matches, fetcherKey) {\n    let results;\n    let dataResults = {};\n    try {\n      results = await callDataStrategyImpl(dataStrategyImpl, type, state, request, matchesToLoad, matches, fetcherKey, manifest, mapRouteProperties);\n    } catch (e) {\n      // If the outer dataStrategy method throws, just return the error for all\n      // matches - and it'll naturally bubble to the root\n      matchesToLoad.forEach(m => {\n        dataResults[m.route.id] = {\n          type: ResultType.error,\n          error: e\n        };\n      });\n      return dataResults;\n    }\n    for (let [routeId, result] of Object.entries(results)) {\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result;\n        dataResults[routeId] = {\n          type: ResultType.redirect,\n          response: normalizeRelativeRoutingRedirectResponse(response, request, routeId, matches, basename, future.v7_relativeSplatPath)\n        };\n      } else {\n        dataResults[routeId] = await convertDataStrategyResultToDataResult(result);\n      }\n    }\n    return dataResults;\n  }\n  async function callLoadersAndMaybeResolveData(state, matches, matchesToLoad, fetchersToLoad, request) {\n    let currentMatches = state.matches;\n    // Kick off loaders and fetchers in parallel\n    let loaderResultsPromise = callDataStrategy(\"loader\", state, request, matchesToLoad, matches, null);\n    let fetcherResultsPromise = Promise.all(fetchersToLoad.map(async f => {\n      if (f.matches && f.match && f.controller) {\n        let results = await callDataStrategy(\"loader\", state, createClientSideRequest(init.history, f.path, f.controller.signal), [f.match], f.matches, f.key);\n        let result = results[f.match.route.id];\n        // Fetcher results are keyed by fetcher key from here on out, not routeId\n        return {\n          [f.key]: result\n        };\n      } else {\n        return Promise.resolve({\n          [f.key]: {\n            type: ResultType.error,\n            error: getInternalRouterError(404, {\n              pathname: f.path\n            })\n          }\n        });\n      }\n    }));\n    let loaderResults = await loaderResultsPromise;\n    let fetcherResults = (await fetcherResultsPromise).reduce((acc, r) => Object.assign(acc, r), {});\n    await Promise.all([resolveNavigationDeferredResults(matches, loaderResults, request.signal, currentMatches, state.loaderData), resolveFetcherDeferredResults(matches, fetcherResults, fetchersToLoad)]);\n    return {\n      loaderResults,\n      fetcherResults\n    };\n  }\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.add(key);\n      }\n      abortFetcher(key);\n    });\n  }\n  function updateFetcherState(key, fetcher, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    state.fetchers.set(key, fetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    }, {\n      flushSync: (opts && opts.flushSync) === true\n    });\n  }\n  function setFetcherError(key, routeId, error, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error\n      },\n      fetchers: new Map(state.fetchers)\n    }, {\n      flushSync: (opts && opts.flushSync) === true\n    });\n  }\n  function getFetcher(key) {\n    if (future.v7_fetcherPersist) {\n      activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n      // If this fetcher was previously marked for deletion, unmark it since we\n      // have a new instance\n      if (deletedFetchers.has(key)) {\n        deletedFetchers.delete(key);\n      }\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n  function deleteFetcher(key) {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (fetchControllers.has(key) && !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    deletedFetchers.delete(key);\n    cancelledFetcherLoads.delete(key);\n    state.fetchers.delete(key);\n  }\n  function deleteFetcherAndUpdateState(key) {\n    if (future.v7_fetcherPersist) {\n      let count = (activeFetchers.get(key) || 0) - 1;\n      if (count <= 0) {\n        activeFetchers.delete(key);\n        deletedFetchers.add(key);\n      } else {\n        activeFetchers.set(key, count);\n      }\n    } else {\n      deleteFetcher(key);\n    }\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n  }\n  function abortFetcher(key) {\n    let controller = fetchControllers.get(key);\n    if (controller) {\n      controller.abort();\n      fetchControllers.delete(key);\n    }\n  }\n  function markFetchersDone(keys) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n  function markFetchRedirectsDone() {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, \"Expected fetcher: \" + key);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n  function abortStaleFetchLoads(landedId) {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, \"Expected fetcher: \" + key);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n  function getBlocker(key, fn) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n    return blocker;\n  }\n  function deleteBlocker(key) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key, newBlocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(blocker.state === \"unblocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"proceeding\" || blocker.state === \"blocked\" && newBlocker.state === \"unblocked\" || blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\", \"Invalid blocker state transition: \" + blocker.state + \" -> \" + newBlocker.state);\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({\n      blockers\n    });\n  }\n  function shouldBlockNavigation(_ref2) {\n    let {\n      currentLocation,\n      nextLocation,\n      historyAction\n    } = _ref2;\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({\n      currentLocation,\n      nextLocation,\n      historyAction\n    })) {\n      return blockerKey;\n    }\n  }\n  function handleNavigational404(pathname) {\n    let error = getInternalRouterError(404, {\n      pathname\n    });\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let {\n      matches,\n      route\n    } = getShortCircuitMatches(routesToUse);\n    // Cancel all pending deferred on 404s since we don't keep any routes\n    cancelActiveDeferreds();\n    return {\n      notFoundMatches: matches,\n      route,\n      error\n    };\n  }\n  function cancelActiveDeferreds(predicate) {\n    let cancelledRouteIds = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(positions, getPosition, getKey) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({\n          restoreScrollPosition: y\n        });\n      }\n    }\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n  function getScrollKey(location, matches) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(location, matches.map(m => convertRouteMatchToUiMatch(m, state.loaderData)));\n      return key || location.key;\n    }\n    return location.key;\n  }\n  function saveScrollPosition(location, matches) {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n  function getSavedScrollPosition(location, matches) {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n  function checkFogOfWar(matches, routesToUse, pathname) {\n    if (patchRoutesOnNavigationImpl) {\n      if (!matches) {\n        let fogMatches = matchRoutesImpl(routesToUse, pathname, basename, true);\n        return {\n          active: true,\n          matches: fogMatches || []\n        };\n      } else {\n        if (Object.keys(matches[0].params).length > 0) {\n          // If we matched a dynamic param or a splat, it might only be because\n          // we haven't yet discovered other routes that would match with a\n          // higher score.  Call patchRoutesOnNavigation just to be sure\n          let partialMatches = matchRoutesImpl(routesToUse, pathname, basename, true);\n          return {\n            active: true,\n            matches: partialMatches\n          };\n        }\n      }\n    }\n    return {\n      active: false,\n      matches: null\n    };\n  }\n  async function discoverRoutes(matches, pathname, signal) {\n    if (!patchRoutesOnNavigationImpl) {\n      return {\n        type: \"success\",\n        matches\n      };\n    }\n    let partialMatches = matches;\n    while (true) {\n      let isNonHMR = inFlightDataRoutes == null;\n      let routesToUse = inFlightDataRoutes || dataRoutes;\n      let localManifest = manifest;\n      try {\n        await patchRoutesOnNavigationImpl({\n          path: pathname,\n          matches: partialMatches,\n          patch: (routeId, children) => {\n            if (signal.aborted) return;\n            patchRoutesImpl(routeId, children, routesToUse, localManifest, mapRouteProperties);\n          }\n        });\n      } catch (e) {\n        return {\n          type: \"error\",\n          error: e,\n          partialMatches\n        };\n      } finally {\n        // If we are not in the middle of an HMR revalidation and we changed the\n        // routes, provide a new identity so when we `updateState` at the end of\n        // this navigation/fetch `router.routes` will be a new identity and\n        // trigger a re-run of memoized `router.routes` dependencies.\n        // HMR will already update the identity and reflow when it lands\n        // `inFlightDataRoutes` in `completeNavigation`\n        if (isNonHMR && !signal.aborted) {\n          dataRoutes = [...dataRoutes];\n        }\n      }\n      if (signal.aborted) {\n        return {\n          type: \"aborted\"\n        };\n      }\n      let newMatches = matchRoutes(routesToUse, pathname, basename);\n      if (newMatches) {\n        return {\n          type: \"success\",\n          matches: newMatches\n        };\n      }\n      let newPartialMatches = matchRoutesImpl(routesToUse, pathname, basename, true);\n      // Avoid loops if the second pass results in the same partial matches\n      if (!newPartialMatches || partialMatches.length === newPartialMatches.length && partialMatches.every((m, i) => m.route.id === newPartialMatches[i].route.id)) {\n        return {\n          type: \"success\",\n          matches: null\n        };\n      }\n      partialMatches = newPartialMatches;\n    }\n  }\n  function _internalSetRoutes(newRoutes) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(newRoutes, mapRouteProperties, undefined, manifest);\n  }\n  function patchRoutes(routeId, children) {\n    let isNonHMR = inFlightDataRoutes == null;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    patchRoutesImpl(routeId, children, routesToUse, manifest, mapRouteProperties);\n    // If we are not in the middle of an HMR revalidation and we changed the\n    // routes, provide a new identity and trigger a reflow via `updateState`\n    // to re-run memoized `router.routes` dependencies.\n    // HMR will already update the identity and reflow when it lands\n    // `inFlightDataRoutes` in `completeNavigation`\n    if (isNonHMR) {\n      dataRoutes = [...dataRoutes];\n      updateState({});\n    }\n  }\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: to => init.history.createHref(to),\n    encodeLocation: to => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: deleteFetcherAndUpdateState,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    patchRoutes,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes\n  };\n  return router;\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\nconst UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\nfunction createStaticHandler(routes, opts) {\n  invariant(routes.length > 0, \"You must provide a non-empty routes array to createStaticHandler\");\n  let manifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties;\n  if (opts != null && opts.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts != null && opts.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = route => ({\n      hasErrorBoundary: detectErrorBoundary(route)\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Config driven behavior flags\n  let future = _extends({\n    v7_relativeSplatPath: false,\n    v7_throwAbortReason: false\n  }, opts ? opts.future : null);\n  let dataRoutes = convertRoutesToDataRoutes(routes, mapRouteProperties, undefined, manifest);\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   *\n   * - `opts.requestContext` is an optional server context that will be passed\n   *   to actions/loaders in the `context` parameter\n   * - `opts.skipLoaderErrorBubbling` is an optional parameter that will prevent\n   *   the bubbling of errors which allows single-fetch-type implementations\n   *   where the client will handle the bubbling and we may need to return data\n   *   for the handling route\n   */\n  async function query(request, _temp3) {\n    let {\n      requestContext,\n      skipLoaderErrorBubbling,\n      dataStrategy\n    } = _temp3 === void 0 ? {} : _temp3;\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, {\n        method\n      });\n      let {\n        matches: methodNotAllowedMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n      let {\n        matches: notFoundMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    let result = await queryImpl(request, location, matches, requestContext, dataStrategy || null, skipLoaderErrorBubbling === true, null);\n    if (isResponse(result)) {\n      return result;\n    }\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return _extends({\n      location,\n      basename\n    }, result);\n  }\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   *\n   * - `opts.routeId` allows you to specify the specific route handler to call.\n   *   If not provided the handler will determine the proper route by matching\n   *   against `request.url`\n   * - `opts.requestContext` is an optional server context that will be passed\n   *    to actions/loaders in the `context` parameter\n   */\n  async function queryRoute(request, _temp4) {\n    let {\n      routeId,\n      requestContext,\n      dataStrategy\n    } = _temp4 === void 0 ? {} : _temp4;\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, {\n        method\n      });\n    } else if (!matches) {\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n    let match = routeId ? matches.find(m => m.route.id === routeId) : getTargetMatch(matches, location);\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n    let result = await queryImpl(request, location, matches, requestContext, dataStrategy || null, false, match);\n    if (isResponse(result)) {\n      return result;\n    }\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n    if (result.loaderData) {\n      var _result$activeDeferre;\n      let data = Object.values(result.loaderData)[0];\n      if ((_result$activeDeferre = result.activeDeferreds) != null && _result$activeDeferre[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n    return undefined;\n  }\n  async function queryImpl(request, location, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch) {\n    invariant(request.signal, \"query()/queryRoute() requests must contain an AbortController signal\");\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(request, matches, routeMatch || getTargetMatch(matches, location), requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch != null);\n        return result;\n      }\n      let result = await loadRouteData(request, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch);\n      return isResponse(result) ? result : _extends({}, result, {\n        actionData: null,\n        actionHeaders: {}\n      });\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction for a\n      // `queryRoute` call, we throw the `DataStrategyResult` to bail out early\n      // and then return or throw the raw Response here accordingly\n      if (isDataStrategyResult(e) && isResponse(e.result)) {\n        if (e.type === ResultType.error) {\n          throw e.result;\n        }\n        return e.result;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n  async function submit(request, matches, actionMatch, requestContext, dataStrategy, skipLoaderErrorBubbling, isRouteRequest) {\n    let result;\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error\n      };\n    } else {\n      let results = await callDataStrategy(\"action\", request, [actionMatch], matches, isRouteRequest, requestContext, dataStrategy);\n      result = results[actionMatch.route.id];\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest, future);\n      }\n    }\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.response.status,\n        headers: {\n          Location: result.response.headers.get(\"Location\")\n        }\n      });\n    }\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error\n      };\n    }\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: {\n          [actionMatch.route.id]: result.data\n        },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal\n    });\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = skipLoaderErrorBubbling ? actionMatch : findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(loaderRequest, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, null, [boundaryMatch.route.id, result]);\n      // action status codes take precedence over loader status codes\n      return _extends({}, context, {\n        statusCode: isRouteErrorResponse(result.error) ? result.error.status : result.statusCode != null ? result.statusCode : 500,\n        actionData: null,\n        actionHeaders: _extends({}, result.headers ? {\n          [actionMatch.route.id]: result.headers\n        } : {})\n      });\n    }\n    let context = await loadRouteData(loaderRequest, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, null);\n    return _extends({}, context, {\n      actionData: {\n        [actionMatch.route.id]: result.data\n      }\n    }, result.statusCode ? {\n      statusCode: result.statusCode\n    } : {}, {\n      actionHeaders: result.headers ? {\n        [actionMatch.route.id]: result.headers\n      } : {}\n    });\n  }\n  async function loadRouteData(request, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch, pendingActionResult) {\n    let isRouteRequest = routeMatch != null;\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (isRouteRequest && !(routeMatch != null && routeMatch.route.loader) && !(routeMatch != null && routeMatch.route.lazy)) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch == null ? void 0 : routeMatch.route.id\n      });\n    }\n    let requestMatches = routeMatch ? [routeMatch] : pendingActionResult && isErrorResult(pendingActionResult[1]) ? getLoaderMatchesUntilBoundary(matches, pendingActionResult[0]) : matches;\n    let matchesToLoad = requestMatches.filter(m => m.route.loader || m.route.lazy);\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce((acc, m) => Object.assign(acc, {\n          [m.route.id]: null\n        }), {}),\n        errors: pendingActionResult && isErrorResult(pendingActionResult[1]) ? {\n          [pendingActionResult[0]]: pendingActionResult[1].error\n        } : null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    let results = await callDataStrategy(\"loader\", request, matchesToLoad, matches, isRouteRequest, requestContext, dataStrategy);\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest, future);\n    }\n    // Process and commit output from loaders\n    let activeDeferreds = new Map();\n    let context = processRouteLoaderData(matches, results, pendingActionResult, activeDeferreds, skipLoaderErrorBubbling);\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set(matchesToLoad.map(match => match.route.id));\n    matches.forEach(match => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n    return _extends({}, context, {\n      matches,\n      activeDeferreds: activeDeferreds.size > 0 ? Object.fromEntries(activeDeferreds.entries()) : null\n    });\n  }\n  // Utility wrapper for calling dataStrategy server-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(type, request, matchesToLoad, matches, isRouteRequest, requestContext, dataStrategy) {\n    let results = await callDataStrategyImpl(dataStrategy || defaultDataStrategy, type, null, request, matchesToLoad, matches, null, manifest, mapRouteProperties, requestContext);\n    let dataResults = {};\n    await Promise.all(matches.map(async match => {\n      if (!(match.route.id in results)) {\n        return;\n      }\n      let result = results[match.route.id];\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result;\n        // Throw redirects and let the server handle them with an HTTP redirect\n        throw normalizeRelativeRoutingRedirectResponse(response, request, match.route.id, matches, basename, future.v7_relativeSplatPath);\n      }\n      if (isResponse(result.result) && isRouteRequest) {\n        // For SSR single-route requests, we want to hand Responses back\n        // directly without unwrapping\n        throw result;\n      }\n      dataResults[match.route.id] = await convertDataStrategyResultToDataResult(result);\n    }));\n    return dataResults;\n  }\n  return {\n    dataRoutes,\n    query,\n    queryRoute\n  };\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nfunction getStaticContextFromError(routes, context, error) {\n  let newContext = _extends({}, context, {\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error\n    }\n  });\n  return newContext;\n}\nfunction throwStaticHandlerAbortedError(request, isRouteRequest, future) {\n  if (future.v7_throwAbortReason && request.signal.reason !== undefined) {\n    throw request.signal.reason;\n  }\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(method + \"() call aborted: \" + request.method + \" \" + request.url);\n}\nfunction isSubmissionNavigation(opts) {\n  return opts != null && (\"formData\" in opts && opts.formData != null || \"body\" in opts && opts.body !== undefined);\n}\nfunction normalizeTo(location, matches, basename, prependBasename, to, v7_relativeSplatPath, fromRouteId, relative) {\n  let contextualMatches;\n  let activeRouteMatch;\n  if (fromRouteId) {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n  // Resolve the relative path\n  let path = resolveTo(to ? to : \".\", getResolveToMatches(contextualMatches, v7_relativeSplatPath), stripBasename(location.pathname, basename) || location.pathname, relative === \"path\");\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n  // Account for `?index` params when routing to the current location\n  if ((to == null || to === \"\" || to === \".\") && activeRouteMatch) {\n    let nakedIndex = hasNakedIndexQuery(path.search);\n    if (activeRouteMatch.route.index && !nakedIndex) {\n      // Add one when we're targeting an index route\n      path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n    } else if (!activeRouteMatch.route.index && nakedIndex) {\n      // Remove existing ones when we're not\n      let params = new URLSearchParams(path.search);\n      let indexValues = params.getAll(\"index\");\n      params.delete(\"index\");\n      indexValues.filter(v => v).forEach(v => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? \"?\" + qs : \"\";\n    }\n  }\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(normalizeFormMethod, isFetcher, path, opts) {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return {\n      path\n    };\n  }\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, {\n        method: opts.formMethod\n      })\n    };\n  }\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, {\n      type: \"invalid-body\"\n    })\n  });\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod ? rawFormMethod.toUpperCase() : rawFormMethod.toLowerCase();\n  let formAction = stripHashFromPath(path);\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n      let text = typeof opts.body === \"string\" ? opts.body : opts.body instanceof FormData || opts.body instanceof URLSearchParams ?\n      // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n      Array.from(opts.body.entries()).reduce((acc, _ref3) => {\n        let [name, value] = _ref3;\n        return \"\" + acc + name + \"=\" + value + \"\\n\";\n      }, \"\") : String(opts.body);\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text\n        }\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n      try {\n        let json = typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined\n          }\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n  invariant(typeof FormData === \"function\", \"FormData is not available in this environment\");\n  let searchParams;\n  let formData;\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n  let submission = {\n    formMethod,\n    formAction,\n    formEncType: opts && opts.formEncType || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined\n  };\n  if (isMutationMethod(submission.formMethod)) {\n    return {\n      path,\n      submission\n    };\n  }\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = \"?\" + searchParams;\n  return {\n    path: createPath(parsedPath),\n    submission\n  };\n}\n// Filter out all routes at/below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(matches, boundaryId, includeBoundary) {\n  if (includeBoundary === void 0) {\n    includeBoundary = false;\n  }\n  let index = matches.findIndex(m => m.route.id === boundaryId);\n  if (index >= 0) {\n    return matches.slice(0, includeBoundary ? index + 1 : index);\n  }\n  return matches;\n}\nfunction getMatchesToLoad(history, state, matches, submission, location, initialHydration, skipActionErrorRevalidation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, deletedFetchers, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, pendingActionResult) {\n  let actionResult = pendingActionResult ? isErrorResult(pendingActionResult[1]) ? pendingActionResult[1].error : pendingActionResult[1].data : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryMatches = matches;\n  if (initialHydration && state.errors) {\n    // On initial hydration, only consider matches up to _and including_ the boundary.\n    // This is inclusive to handle cases where a server loader ran successfully,\n    // a child server loader bubbled up to this route, but this route has\n    // `clientLoader.hydrate` so we want to still run the `clientLoader` so that\n    // we have a complete version of `loaderData`\n    boundaryMatches = getLoaderMatchesUntilBoundary(matches, Object.keys(state.errors)[0], true);\n  } else if (pendingActionResult && isErrorResult(pendingActionResult[1])) {\n    // If an action threw an error, we call loaders up to, but not including the\n    // boundary\n    boundaryMatches = getLoaderMatchesUntilBoundary(matches, pendingActionResult[0]);\n  }\n  // Don't revalidate loaders by default after action 4xx/5xx responses\n  // when the flag is enabled.  They can still opt-into revalidation via\n  // `shouldRevalidate` via `actionResult`\n  let actionStatus = pendingActionResult ? pendingActionResult[1].statusCode : undefined;\n  let shouldSkipRevalidation = skipActionErrorRevalidation && actionStatus && actionStatus >= 400;\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    let {\n      route\n    } = match;\n    if (route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n    if (route.loader == null) {\n      return false;\n    }\n    if (initialHydration) {\n      return shouldLoadRouteOnHydration(route, state.loaderData, state.errors);\n    }\n    // Always call the loader on new route instances and pending defer cancellations\n    if (isNewLoader(state.loaderData, state.matches[index], match) || cancelledDeferredRoutes.some(id => id === match.route.id)) {\n      return true;\n    }\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n    return shouldRevalidateLoader(match, _extends({\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params\n    }, submission, {\n      actionResult,\n      actionStatus,\n      defaultShouldRevalidate: shouldSkipRevalidation ? false :\n      // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n      isRevalidationRequired || currentUrl.pathname + currentUrl.search === nextUrl.pathname + nextUrl.search ||\n      // Search params affect all loaders\n      currentUrl.search !== nextUrl.search || isNewRouteInstance(currentRouteMatch, nextRouteMatch)\n    }));\n  });\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate:\n    //  - on initial hydration (shouldn't be any fetchers then anyway)\n    //  - if fetcher won't be present in the subsequent render\n    //    - no longer matches the URL (v7_fetcherPersist=false)\n    //    - was unmounted but persisted due to v7_fetcherPersist=true\n    if (initialHydration || !matches.some(m => m.route.id === f.routeId) || deletedFetchers.has(key)) {\n      return;\n    }\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null\n      });\n      return;\n    }\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.has(key)) {\n      // Always mark for revalidation if the fetcher was cancelled\n      cancelledFetcherLoads.delete(key);\n      shouldRevalidate = true;\n    } else if (fetcher && fetcher.state !== \"idle\" && fetcher.data === undefined) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, _extends({\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params\n      }, submission, {\n        actionResult,\n        actionStatus,\n        defaultShouldRevalidate: shouldSkipRevalidation ? false : isRevalidationRequired\n      }));\n    }\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController()\n      });\n    }\n  });\n  return [navigationMatches, revalidatingFetchers];\n}\nfunction shouldLoadRouteOnHydration(route, loaderData, errors) {\n  // We dunno if we have a loader - gotta find out!\n  if (route.lazy) {\n    return true;\n  }\n  // No loader, nothing to initialize\n  if (!route.loader) {\n    return false;\n  }\n  let hasData = loaderData != null && loaderData[route.id] !== undefined;\n  let hasError = errors != null && errors[route.id] !== undefined;\n  // Don't run if we error'd during SSR\n  if (!hasData && hasError) {\n    return false;\n  }\n  // Explicitly opting-in to running on hydration\n  if (typeof route.loader === \"function\" && route.loader.hydrate === true) {\n    return true;\n  }\n  // Otherwise, run if we're not yet initialized with anything\n  return !hasData && !hasError;\n}\nfunction isNewLoader(currentLoaderData, currentMatch, match) {\n  let isNew =\n  // [a] -> [a, b]\n  !currentMatch ||\n  // [a, b] -> [a, c]\n  match.route.id !== currentMatch.route.id;\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\nfunction isNewRouteInstance(currentMatch, match) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    currentPath != null && currentPath.endsWith(\"*\") && currentMatch.params[\"*\"] !== match.params[\"*\"]\n  );\n}\nfunction shouldRevalidateLoader(loaderMatch, arg) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n  return arg.defaultShouldRevalidate;\n}\nfunction patchRoutesImpl(routeId, children, routesToUse, manifest, mapRouteProperties) {\n  var _childrenToPatch;\n  let childrenToPatch;\n  if (routeId) {\n    let route = manifest[routeId];\n    invariant(route, \"No route found to patch children into: routeId = \" + routeId);\n    if (!route.children) {\n      route.children = [];\n    }\n    childrenToPatch = route.children;\n  } else {\n    childrenToPatch = routesToUse;\n  }\n  // Don't patch in routes we already know about so that `patch` is idempotent\n  // to simplify user-land code. This is useful because we re-call the\n  // `patchRoutesOnNavigation` function for matched routes with params.\n  let uniqueChildren = children.filter(newRoute => !childrenToPatch.some(existingRoute => isSameRoute(newRoute, existingRoute)));\n  let newRoutes = convertRoutesToDataRoutes(uniqueChildren, mapRouteProperties, [routeId || \"_\", \"patch\", String(((_childrenToPatch = childrenToPatch) == null ? void 0 : _childrenToPatch.length) || \"0\")], manifest);\n  childrenToPatch.push(...newRoutes);\n}\nfunction isSameRoute(newRoute, existingRoute) {\n  // Most optimal check is by id\n  if (\"id\" in newRoute && \"id\" in existingRoute && newRoute.id === existingRoute.id) {\n    return true;\n  }\n  // Second is by pathing differences\n  if (!(newRoute.index === existingRoute.index && newRoute.path === existingRoute.path && newRoute.caseSensitive === existingRoute.caseSensitive)) {\n    return false;\n  }\n  // Pathless layout routes are trickier since we need to check children.\n  // If they have no children then they're the same as far as we can tell\n  if ((!newRoute.children || newRoute.children.length === 0) && (!existingRoute.children || existingRoute.children.length === 0)) {\n    return true;\n  }\n  // Otherwise, we look to see if every child in the new route is already\n  // represented in the existing route's children\n  return newRoute.children.every((aChild, i) => {\n    var _existingRoute$childr;\n    return (_existingRoute$childr = existingRoute.children) == null ? void 0 : _existingRoute$childr.some(bChild => isSameRoute(aChild, bChild));\n  });\n}\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(route, mapRouteProperties, manifest) {\n  if (!route.lazy) {\n    return;\n  }\n  let lazyRoute = await route.lazy();\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue = routeToUpdate[lazyRouteProperty];\n    let isPropertyStaticallyDefined = staticRouteValue !== undefined &&\n    // This property isn't static since it should always be updated based\n    // on the route updates\n    lazyRouteProperty !== \"hasErrorBoundary\";\n    warning(!isPropertyStaticallyDefined, \"Route \\\"\" + routeToUpdate.id + \"\\\" has a static property \\\"\" + lazyRouteProperty + \"\\\" \" + \"defined but its lazy function is also returning a value for this property. \" + (\"The lazy route property \\\"\" + lazyRouteProperty + \"\\\" will be ignored.\"));\n    if (!isPropertyStaticallyDefined && !immutableRouteKeys.has(lazyRouteProperty)) {\n      routeUpdates[lazyRouteProperty] = lazyRoute[lazyRouteProperty];\n    }\n  }\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, _extends({}, mapRouteProperties(routeToUpdate), {\n    lazy: undefined\n  }));\n}\n// Default implementation of `dataStrategy` which fetches all loaders in parallel\nasync function defaultDataStrategy(_ref4) {\n  let {\n    matches\n  } = _ref4;\n  let matchesToLoad = matches.filter(m => m.shouldLoad);\n  let results = await Promise.all(matchesToLoad.map(m => m.resolve()));\n  return results.reduce((acc, result, i) => Object.assign(acc, {\n    [matchesToLoad[i].route.id]: result\n  }), {});\n}\nasync function callDataStrategyImpl(dataStrategyImpl, type, state, request, matchesToLoad, matches, fetcherKey, manifest, mapRouteProperties, requestContext) {\n  let loadRouteDefinitionsPromises = matches.map(m => m.route.lazy ? loadLazyRouteModule(m.route, mapRouteProperties, manifest) : undefined);\n  let dsMatches = matches.map((match, i) => {\n    let loadRoutePromise = loadRouteDefinitionsPromises[i];\n    let shouldLoad = matchesToLoad.some(m => m.route.id === match.route.id);\n    // `resolve` encapsulates route.lazy(), executing the loader/action,\n    // and mapping return values/thrown errors to a `DataStrategyResult`.  Users\n    // can pass a callback to take fine-grained control over the execution\n    // of the loader/action\n    let resolve = async handlerOverride => {\n      if (handlerOverride && request.method === \"GET\" && (match.route.lazy || match.route.loader)) {\n        shouldLoad = true;\n      }\n      return shouldLoad ? callLoaderOrAction(type, request, match, loadRoutePromise, handlerOverride, requestContext) : Promise.resolve({\n        type: ResultType.data,\n        result: undefined\n      });\n    };\n    return _extends({}, match, {\n      shouldLoad,\n      resolve\n    });\n  });\n  // Send all matches here to allow for a middleware-type implementation.\n  // handler will be a no-op for unneeded routes and we filter those results\n  // back out below.\n  let results = await dataStrategyImpl({\n    matches: dsMatches,\n    request,\n    params: matches[0].params,\n    fetcherKey,\n    context: requestContext\n  });\n  // Wait for all routes to load here but 'swallow the error since we want\n  // it to bubble up from the `await loadRoutePromise` in `callLoaderOrAction` -\n  // called from `match.resolve()`\n  try {\n    await Promise.all(loadRouteDefinitionsPromises);\n  } catch (e) {\n    // No-op\n  }\n  return results;\n}\n// Default logic for calling a loader/action is the user has no specified a dataStrategy\nasync function callLoaderOrAction(type, request, match, loadRoutePromise, handlerOverride, staticContext) {\n  let result;\n  let onReject;\n  let runHandler = handler => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject;\n    // This will never resolve so safe to type it as Promise<DataStrategyResult> to\n    // satisfy the function return value\n    let abortPromise = new Promise((_, r) => reject = r);\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n    let actualHandler = ctx => {\n      if (typeof handler !== \"function\") {\n        return Promise.reject(new Error(\"You cannot call the handler for a route which defines a boolean \" + (\"\\\"\" + type + \"\\\" [routeId: \" + match.route.id + \"]\")));\n      }\n      return handler({\n        request,\n        params: match.params,\n        context: staticContext\n      }, ...(ctx !== undefined ? [ctx] : []));\n    };\n    let handlerPromise = (async () => {\n      try {\n        let val = await (handlerOverride ? handlerOverride(ctx => actualHandler(ctx)) : actualHandler());\n        return {\n          type: \"data\",\n          result: val\n        };\n      } catch (e) {\n        return {\n          type: \"error\",\n          result: e\n        };\n      }\n    })();\n    return Promise.race([handlerPromise, abortPromise]);\n  };\n  try {\n    let handler = match.route[type];\n    // If we have a route.lazy promise, await that first\n    if (loadRoutePromise) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let handlerError;\n        let [value] = await Promise.all([\n        // If the handler throws, don't let it immediately bubble out,\n        // since we need to let the lazy() execution finish so we know if this\n        // route has a boundary that can handle the error\n        runHandler(handler).catch(e => {\n          handlerError = e;\n        }), loadRoutePromise]);\n        if (handlerError !== undefined) {\n          throw handlerError;\n        }\n        result = value;\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadRoutePromise;\n        handler = match.route[type];\n        if (handler) {\n          // Handler still runs even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return {\n            type: ResultType.data,\n            result: undefined\n          };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n    invariant(result.result !== undefined, \"You defined \" + (type === \"action\" ? \"an action\" : \"a loader\") + \" for route \" + (\"\\\"\" + match.route.id + \"\\\" but didn't return anything from your `\" + type + \"` \") + \"function. Please return a value or `null`.\");\n  } catch (e) {\n    // We should already be catching and converting normal handler executions to\n    // DataStrategyResults and returning them, so anything that throws here is an\n    // unexpected error we still need to wrap\n    return {\n      type: ResultType.error,\n      result: e\n    };\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n  return result;\n}\nasync function convertDataStrategyResultToDataResult(dataStrategyResult) {\n  let {\n    result,\n    type\n  } = dataStrategyResult;\n  if (isResponse(result)) {\n    let data;\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      // Check between word boundaries instead of startsWith() due to the last\n      // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data = null;\n        } else {\n          data = await result.json();\n        }\n      } else {\n        data = await result.text();\n      }\n    } catch (e) {\n      return {\n        type: ResultType.error,\n        error: e\n      };\n    }\n    if (type === ResultType.error) {\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(result.status, result.statusText, data),\n        statusCode: result.status,\n        headers: result.headers\n      };\n    }\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers\n    };\n  }\n  if (type === ResultType.error) {\n    if (isDataWithResponseInit(result)) {\n      var _result$init2;\n      if (result.data instanceof Error) {\n        var _result$init;\n        return {\n          type: ResultType.error,\n          error: result.data,\n          statusCode: (_result$init = result.init) == null ? void 0 : _result$init.status\n        };\n      }\n      // Convert thrown data() to ErrorResponse instances\n      result = new ErrorResponseImpl(((_result$init2 = result.init) == null ? void 0 : _result$init2.status) || 500, undefined, result.data);\n    }\n    return {\n      type: ResultType.error,\n      error: result,\n      statusCode: isRouteErrorResponse(result) ? result.status : undefined\n    };\n  }\n  if (isDeferredData(result)) {\n    var _result$init3, _result$init4;\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: (_result$init3 = result.init) == null ? void 0 : _result$init3.status,\n      headers: ((_result$init4 = result.init) == null ? void 0 : _result$init4.headers) && new Headers(result.init.headers)\n    };\n  }\n  if (isDataWithResponseInit(result)) {\n    var _result$init5, _result$init6;\n    return {\n      type: ResultType.data,\n      data: result.data,\n      statusCode: (_result$init5 = result.init) == null ? void 0 : _result$init5.status,\n      headers: (_result$init6 = result.init) != null && _result$init6.headers ? new Headers(result.init.headers) : undefined\n    };\n  }\n  return {\n    type: ResultType.data,\n    data: result\n  };\n}\n// Support relative routing in internal redirects\nfunction normalizeRelativeRoutingRedirectResponse(response, request, routeId, matches, basename, v7_relativeSplatPath) {\n  let location = response.headers.get(\"Location\");\n  invariant(location, \"Redirects returned/thrown from loaders/actions must have a Location header\");\n  if (!ABSOLUTE_URL_REGEX.test(location)) {\n    let trimmedMatches = matches.slice(0, matches.findIndex(m => m.route.id === routeId) + 1);\n    location = normalizeTo(new URL(request.url), trimmedMatches, basename, true, location, v7_relativeSplatPath);\n    response.headers.set(\"Location\", location);\n  }\n  return response;\n}\nfunction normalizeRedirectLocation(location, currentUrl, basename) {\n  if (ABSOLUTE_URL_REGEX.test(location)) {\n    // Strip off the protocol+origin for same-origin + same-basename absolute redirects\n    let normalizedLocation = location;\n    let url = normalizedLocation.startsWith(\"//\") ? new URL(currentUrl.protocol + normalizedLocation) : new URL(normalizedLocation);\n    let isSameBasename = stripBasename(url.pathname, basename) != null;\n    if (url.origin === currentUrl.origin && isSameBasename) {\n      return url.pathname + url.search + url.hash;\n    }\n  }\n  return location;\n}\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(history, location, signal, submission) {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init = {\n    signal\n  };\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let {\n      formMethod,\n      formEncType\n    } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({\n        \"Content-Type\": formEncType\n      });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (formEncType === \"application/x-www-form-urlencoded\" && submission.formData) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n  return new Request(url, init);\n}\nfunction convertFormDataToSearchParams(formData) {\n  let searchParams = new URLSearchParams();\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n  return searchParams;\n}\nfunction convertSearchParamsToFormData(searchParams) {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\nfunction processRouteLoaderData(matches, results, pendingActionResult, activeDeferreds, skipLoaderErrorBubbling) {\n  // Fill in loaderData/errors from our loaders\n  let loaderData = {};\n  let errors = null;\n  let statusCode;\n  let foundError = false;\n  let loaderHeaders = {};\n  let pendingError = pendingActionResult && isErrorResult(pendingActionResult[1]) ? pendingActionResult[1].error : undefined;\n  // Process loader results into state.loaderData/state.errors\n  matches.forEach(match => {\n    if (!(match.route.id in results)) {\n      return;\n    }\n    let id = match.route.id;\n    let result = results[id];\n    invariant(!isRedirectResult(result), \"Cannot handle redirect results in processLoaderData\");\n    if (isErrorResult(result)) {\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError !== undefined) {\n        error = pendingError;\n        pendingError = undefined;\n      }\n      errors = errors || {};\n      if (skipLoaderErrorBubbling) {\n        errors[id] = error;\n      } else {\n        // Look upwards from the matched route for the closest ancestor error\n        // boundary, defaulting to the root match.  Prefer higher error values\n        // if lower errors bubble to the same boundary\n        let boundaryMatch = findNearestBoundary(matches, id);\n        if (errors[boundaryMatch.route.id] == null) {\n          errors[boundaryMatch.route.id] = error;\n        }\n      }\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error) ? result.error.status : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode != null && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      } else {\n        loaderData[id] = result.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      }\n    }\n  });\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError !== undefined && pendingActionResult) {\n    errors = {\n      [pendingActionResult[0]]: pendingError\n    };\n    loaderData[pendingActionResult[0]] = undefined;\n  }\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders\n  };\n}\nfunction processLoaderData(state, matches, results, pendingActionResult, revalidatingFetchers, fetcherResults, activeDeferreds) {\n  let {\n    loaderData,\n    errors\n  } = processRouteLoaderData(matches, results, pendingActionResult, activeDeferreds, false // This method is only called client side so we always want to bubble\n  );\n  // Process results from our revalidating fetchers\n  revalidatingFetchers.forEach(rf => {\n    let {\n      key,\n      match,\n      controller\n    } = rf;\n    let result = fetcherResults[key];\n    invariant(result, \"Did not find corresponding fetcher result\");\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      return;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match == null ? void 0 : match.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = _extends({}, errors, {\n          [boundaryMatch.route.id]: result.error\n        });\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  });\n  return {\n    loaderData,\n    errors\n  };\n}\nfunction mergeLoaderData(loaderData, newLoaderData, matches, errors) {\n  let mergedLoaderData = _extends({}, newLoaderData);\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\nfunction getActionDataForCommit(pendingActionResult) {\n  if (!pendingActionResult) {\n    return {};\n  }\n  return isErrorResult(pendingActionResult[1]) ? {\n    // Clear out prior actionData on errors\n    actionData: {}\n  } : {\n    actionData: {\n      [pendingActionResult[0]]: pendingActionResult[1].data\n    }\n  };\n}\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(matches, routeId) {\n  let eligibleMatches = routeId ? matches.slice(0, matches.findIndex(m => m.route.id === routeId) + 1) : [...matches];\n  return eligibleMatches.reverse().find(m => m.route.hasErrorBoundary === true) || matches[0];\n}\nfunction getShortCircuitMatches(routes) {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.length === 1 ? routes[0] : routes.find(r => r.index || !r.path || r.path === \"/\") || {\n    id: \"__shim-error-route__\"\n  };\n  return {\n    matches: [{\n      params: {},\n      pathname: \"\",\n      pathnameBase: \"\",\n      route\n    }],\n    route\n  };\n}\nfunction getInternalRouterError(status, _temp5) {\n  let {\n    pathname,\n    routeId,\n    method,\n    type,\n    message\n  } = _temp5 === void 0 ? {} : _temp5;\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide a `loader` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = \"Route \\\"\" + routeId + \"\\\" does not match URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = \"No route matches URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method.toUpperCase() + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide an `action` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (method) {\n      errorMessage = \"Invalid request method \\\"\" + method.toUpperCase() + \"\\\"\";\n    }\n  }\n  return new ErrorResponseImpl(status || 500, statusText, new Error(errorMessage), true);\n}\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(results) {\n  let entries = Object.entries(results);\n  for (let i = entries.length - 1; i >= 0; i--) {\n    let [key, result] = entries[i];\n    if (isRedirectResult(result)) {\n      return {\n        key,\n        result\n      };\n    }\n  }\n}\nfunction stripHashFromPath(path) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath(_extends({}, parsedPath, {\n    hash: \"\"\n  }));\n}\nfunction isHashChangeOnly(a, b) {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\nfunction isDataStrategyResult(result) {\n  return result != null && typeof result === \"object\" && \"type\" in result && \"result\" in result && (result.type === ResultType.data || result.type === ResultType.error);\n}\nfunction isRedirectDataStrategyResultResult(result) {\n  return isResponse(result.result) && redirectStatusCodes.has(result.result.status);\n}\nfunction isDeferredResult(result) {\n  return result.type === ResultType.deferred;\n}\nfunction isErrorResult(result) {\n  return result.type === ResultType.error;\n}\nfunction isRedirectResult(result) {\n  return (result && result.type) === ResultType.redirect;\n}\nfunction isDataWithResponseInit(value) {\n  return typeof value === \"object\" && value != null && \"type\" in value && \"data\" in value && \"init\" in value && value.type === \"DataWithResponseInit\";\n}\nfunction isDeferredData(value) {\n  let deferred = value;\n  return deferred && typeof deferred === \"object\" && typeof deferred.data === \"object\" && typeof deferred.subscribe === \"function\" && typeof deferred.cancel === \"function\" && typeof deferred.resolveData === \"function\";\n}\nfunction isResponse(value) {\n  return value != null && typeof value.status === \"number\" && typeof value.statusText === \"string\" && typeof value.headers === \"object\" && typeof value.body !== \"undefined\";\n}\nfunction isRedirectResponse(result) {\n  if (!isResponse(result)) {\n    return false;\n  }\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\nfunction isValidMethod(method) {\n  return validRequestMethods.has(method.toLowerCase());\n}\nfunction isMutationMethod(method) {\n  return validMutationMethods.has(method.toLowerCase());\n}\nasync function resolveNavigationDeferredResults(matches, results, signal, currentMatches, currentLoaderData) {\n  let entries = Object.entries(results);\n  for (let index = 0; index < entries.length; index++) {\n    let [routeId, result] = entries[index];\n    let match = matches.find(m => (m == null ? void 0 : m.route.id) === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n    let currentMatch = currentMatches.find(m => m.route.id === match.route.id);\n    let isRevalidatingLoader = currentMatch != null && !isNewRouteInstance(currentMatch, match) && (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n    if (isDeferredResult(result) && isRevalidatingLoader) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, false).then(result => {\n        if (result) {\n          results[routeId] = result;\n        }\n      });\n    }\n  }\n}\nasync function resolveFetcherDeferredResults(matches, results, revalidatingFetchers) {\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let {\n      key,\n      routeId,\n      controller\n    } = revalidatingFetchers[index];\n    let result = results[key];\n    let match = matches.find(m => (m == null ? void 0 : m.route.id) === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n    if (isDeferredResult(result)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      invariant(controller, \"Expected an AbortController for revalidating fetcher deferred result\");\n      await resolveDeferredData(result, controller.signal, true).then(result => {\n        if (result) {\n          results[key] = result;\n        }\n      });\n    }\n  }\n}\nasync function resolveDeferredData(result, signal, unwrap) {\n  if (unwrap === void 0) {\n    unwrap = false;\n  }\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e\n      };\n    }\n  }\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data\n  };\n}\nfunction hasNakedIndexQuery(search) {\n  return new URLSearchParams(search).getAll(\"index\").some(v => v === \"\");\n}\nfunction getTargetMatch(matches, location) {\n  let search = typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (matches[matches.length - 1].route.index && hasNakedIndexQuery(search || \"\")) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\nfunction getSubmissionFromNavigation(navigation) {\n  let {\n    formMethod,\n    formAction,\n    formEncType,\n    text,\n    formData,\n    json\n  } = navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined\n    };\n  }\n}\nfunction getLoadingNavigation(location, submission) {\n  if (submission) {\n    let navigation = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text\n    };\n    return navigation;\n  } else {\n    let navigation = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined\n    };\n    return navigation;\n  }\n}\nfunction getSubmittingNavigation(location, submission) {\n  let navigation = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text\n  };\n  return navigation;\n}\nfunction getLoadingFetcher(submission, data) {\n  if (submission) {\n    let fetcher = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data\n    };\n    return fetcher;\n  } else {\n    let fetcher = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data\n    };\n    return fetcher;\n  }\n}\nfunction getSubmittingFetcher(submission, existingFetcher) {\n  let fetcher = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined\n  };\n  return fetcher;\n}\nfunction getDoneFetcher(data) {\n  let fetcher = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data\n  };\n  return fetcher;\n}\nfunction restoreAppliedTransitions(_window, transitions) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(TRANSITIONS_STORAGE_KEY);\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n    // no-op, use default empty object\n  }\n}\nfunction persistAppliedTransitions(_window, transitions) {\n  if (transitions.size > 0) {\n    let json = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(TRANSITIONS_STORAGE_KEY, JSON.stringify(json));\n    } catch (error) {\n      warning(false, \"Failed to save applied view transitions in sessionStorage (\" + error + \").\");\n    }\n  }\n}\n//#endregion\n\nexport { AbortedDeferredError, Action, IDLE_BLOCKER, IDLE_FETCHER, IDLE_NAVIGATION, UNSAFE_DEFERRED_SYMBOL, DeferredData as UNSAFE_DeferredData, ErrorResponseImpl as UNSAFE_ErrorResponseImpl, convertRouteMatchToUiMatch as UNSAFE_convertRouteMatchToUiMatch, convertRoutesToDataRoutes as UNSAFE_convertRoutesToDataRoutes, decodePath as UNSAFE_decodePath, getResolveToMatches as UNSAFE_getResolveToMatches, invariant as UNSAFE_invariant, warning as UNSAFE_warning, createBrowserHistory, createHashHistory, createMemoryHistory, createPath, createRouter, createStaticHandler, data, defer, generatePath, getStaticContextFromError, getToPathname, isDataWithResponseInit, isDeferredData, isRouteErrorResponse, joinPaths, json, matchPath, matchRoutes, normalizePathname, parsePath, redirect, redirectDocument, replace, resolvePath, resolveTo, stripBasename };\n//# sourceMappingURL=router.js.map\n", "/**\n * React Router DOM v6.27.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { UNSAFE_mapRouteProperties, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, Router, UNSAFE_useRoutesImpl, UNSAFE_NavigationContext, useHref, useResolvedPath, useLocation, useNavigate, createPath, UNSAFE_useRouteId, UNSAFE_RouteContext, useMatches, useNavigation, useBlocker } from 'react-router';\nexport { AbortedDeferredError, Await, MemoryRouter, Navigate, NavigationType, Outlet, Route, Router, Routes, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, UNSAFE_LocationContext, UNSAFE_NavigationContext, UNSAFE_RouteContext, UNSAFE_useRouteId, createMemoryRouter, createPath, createRoutesFromChildren, createRoutesFromElements, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, redirectDocument, renderMatches, replace, resolvePath, useActionData, useAsyncError, useAsyncValue, useBlocker, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes } from 'react-router';\nimport { stripBasename, UNSAFE_warning, createRouter, createBrowserHistory, createHashHistory, UNSAFE_ErrorResponseImpl, UNSAFE_invariant, joinPaths, IDLE_FETCHER, matchPath } from '@remix-run/router';\nexport { UNSAFE_ErrorResponseImpl } from '@remix-run/router';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n\nconst defaultMethod = \"get\";\nconst defaultEncType = \"application/x-www-form-urlencoded\";\nfunction isHtmlElement(object) {\n  return object != null && typeof object.tagName === \"string\";\n}\nfunction isButtonElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\nfunction isFormElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\nfunction isInputElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\nfunction shouldProcessLinkClick(event, target) {\n  return event.button === 0 && (\n  // Ignore everything but left clicks\n  !target || target === \"_self\") &&\n  // Let browser handle \"target=_blank\" etc.\n  !isModifiedEvent(event) // Ignore clicks with modifier keys\n  ;\n}\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nfunction createSearchParams(init) {\n  if (init === void 0) {\n    init = \"\";\n  }\n  return new URLSearchParams(typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce((memo, key) => {\n    let value = init[key];\n    return memo.concat(Array.isArray(value) ? value.map(v => [key, v]) : [[key, value]]);\n  }, []));\n}\nfunction getSearchParamsForLocation(locationSearch, defaultSearchParams) {\n  let searchParams = createSearchParams(locationSearch);\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach(value => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n  return searchParams;\n}\n// One-time check for submitter support\nlet _formDataSupportsSubmitter = null;\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(document.createElement(\"form\"),\n      // @ts-expect-error if FormData supports the submitter parameter, this will throw\n      0);\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\nconst supportedFormEncTypes = new Set([\"application/x-www-form-urlencoded\", \"multipart/form-data\", \"text/plain\"]);\nfunction getFormEncType(encType) {\n  if (encType != null && !supportedFormEncTypes.has(encType)) {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"\\\"\" + encType + \"\\\" is not a valid `encType` for `<Form>`/`<fetcher.Form>` \" + (\"and will default to \\\"\" + defaultEncType + \"\\\"\")) : void 0;\n    return null;\n  }\n  return encType;\n}\nfunction getFormSubmissionInfo(target, basename) {\n  let method;\n  let action;\n  let encType;\n  let formData;\n  let body;\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n    formData = new FormData(target);\n  } else if (isButtonElement(target) || isInputElement(target) && (target.type === \"submit\" || target.type === \"image\")) {\n    let form = target.form;\n    if (form == null) {\n      throw new Error(\"Cannot submit a <button> or <input type=\\\"submit\\\"> without a <form>\");\n    }\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"formmethod\") || form.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"formenctype\")) || getFormEncType(form.getAttribute(\"enctype\")) || defaultEncType;\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let {\n        name,\n        type,\n        value\n      } = target;\n      if (type === \"image\") {\n        let prefix = name ? name + \".\" : \"\";\n        formData.append(prefix + \"x\", \"0\");\n        formData.append(prefix + \"y\", \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\"Cannot submit element that is not <form>, <button>, or \" + \"<input type=\\\"submit|image\\\">\");\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n  return {\n    action,\n    method: method.toLowerCase(),\n    encType,\n    formData,\n    body\n  };\n}\n\nconst _excluded = [\"onClick\", \"relative\", \"reloadDocument\", \"replace\", \"state\", \"target\", \"to\", \"preventScrollReset\", \"viewTransition\"],\n  _excluded2 = [\"aria-current\", \"caseSensitive\", \"className\", \"end\", \"style\", \"to\", \"viewTransition\", \"children\"],\n  _excluded3 = [\"fetcherKey\", \"navigate\", \"reloadDocument\", \"replace\", \"state\", \"method\", \"action\", \"onSubmit\", \"relative\", \"preventScrollReset\", \"viewTransition\"];\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"6\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\nfunction createBrowserRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createBrowserHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes,\n    mapRouteProperties: UNSAFE_mapRouteProperties,\n    dataStrategy: opts == null ? void 0 : opts.dataStrategy,\n    patchRoutesOnNavigation: opts == null ? void 0 : opts.patchRoutesOnNavigation,\n    window: opts == null ? void 0 : opts.window\n  }).initialize();\n}\nfunction createHashRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createHashHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes,\n    mapRouteProperties: UNSAFE_mapRouteProperties,\n    dataStrategy: opts == null ? void 0 : opts.dataStrategy,\n    patchRoutesOnNavigation: opts == null ? void 0 : opts.patchRoutesOnNavigation,\n    window: opts == null ? void 0 : opts.window\n  }).initialize();\n}\nfunction parseHydrationData() {\n  var _window;\n  let state = (_window = window) == null ? void 0 : _window.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = _extends({}, state, {\n      errors: deserializeErrors(state.errors)\n    });\n  }\n  return state;\n}\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new UNSAFE_ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nconst ViewTransitionContext = /*#__PURE__*/React.createContext({\n  isTransitioning: false\n});\nif (process.env.NODE_ENV !== \"production\") {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\nconst FetchersContext = /*#__PURE__*/React.createContext(new Map());\nif (process.env.NODE_ENV !== \"production\") {\n  FetchersContext.displayName = \"Fetchers\";\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\nfunction startTransitionSafe(cb) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\nfunction flushSyncSafe(cb) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\nclass Deferred {\n  constructor() {\n    this.status = \"pending\";\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = value => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = reason => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nfunction RouterProvider(_ref) {\n  let {\n    fallbackElement,\n    router,\n    future\n  } = _ref;\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState();\n  let [vtContext, setVtContext] = React.useState({\n    isTransitioning: false\n  });\n  let [renderDfd, setRenderDfd] = React.useState();\n  let [transition, setTransition] = React.useState();\n  let [interruption, setInterruption] = React.useState();\n  let fetcherData = React.useRef(new Map());\n  let {\n    v7_startTransition\n  } = future || {};\n  let optInStartTransition = React.useCallback(cb => {\n    if (v7_startTransition) {\n      startTransitionSafe(cb);\n    } else {\n      cb();\n    }\n  }, [v7_startTransition]);\n  let setState = React.useCallback((newState, _ref2) => {\n    let {\n      deletedFetchers,\n      flushSync: flushSync,\n      viewTransitionOpts: viewTransitionOpts\n    } = _ref2;\n    deletedFetchers.forEach(key => fetcherData.current.delete(key));\n    newState.fetchers.forEach((fetcher, key) => {\n      if (fetcher.data !== undefined) {\n        fetcherData.current.set(key, fetcher.data);\n      }\n    });\n    let isViewTransitionUnavailable = router.window == null || router.window.document == null || typeof router.window.document.startViewTransition !== \"function\";\n    // If this isn't a view transition or it's not available in this browser,\n    // just update and be done with it\n    if (!viewTransitionOpts || isViewTransitionUnavailable) {\n      if (flushSync) {\n        flushSyncSafe(() => setStateImpl(newState));\n      } else {\n        optInStartTransition(() => setStateImpl(newState));\n      }\n      return;\n    }\n    // flushSync + startViewTransition\n    if (flushSync) {\n      // Flush through the context to mark DOM elements as transition=ing\n      flushSyncSafe(() => {\n        // Cancel any pending transitions\n        if (transition) {\n          renderDfd && renderDfd.resolve();\n          transition.skipTransition();\n        }\n        setVtContext({\n          isTransitioning: true,\n          flushSync: true,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation\n        });\n      });\n      // Update the DOM\n      let t = router.window.document.startViewTransition(() => {\n        flushSyncSafe(() => setStateImpl(newState));\n      });\n      // Clean up after the animation completes\n      t.finished.finally(() => {\n        flushSyncSafe(() => {\n          setRenderDfd(undefined);\n          setTransition(undefined);\n          setPendingState(undefined);\n          setVtContext({\n            isTransitioning: false\n          });\n        });\n      });\n      flushSyncSafe(() => setTransition(t));\n      return;\n    }\n    // startTransition + startViewTransition\n    if (transition) {\n      // Interrupting an in-progress transition, cancel and let everything flush\n      // out, and then kick off a new transition from the interruption state\n      renderDfd && renderDfd.resolve();\n      transition.skipTransition();\n      setInterruption({\n        state: newState,\n        currentLocation: viewTransitionOpts.currentLocation,\n        nextLocation: viewTransitionOpts.nextLocation\n      });\n    } else {\n      // Completed navigation update with opted-in view transitions, let 'er rip\n      setPendingState(newState);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: viewTransitionOpts.currentLocation,\n        nextLocation: viewTransitionOpts.nextLocation\n      });\n    }\n  }, [router.window, transition, renderDfd, fetcherData, optInStartTransition]);\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred());\n    }\n  }, [vtContext]);\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({\n          isTransitioning: false\n        });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (renderDfd && pendingState && state.location.key === pendingState.location.key) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n  React.useEffect(() => {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(fallbackElement == null || !router.future.v7_partialHydration, \"`<RouterProvider fallbackElement>` is deprecated when using \" + \"`v7_partialHydration`, use a `HydrateFallback` component instead\") : void 0;\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  let navigator = React.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: n => router.navigate(n),\n      push: (to, state, opts) => router.navigate(to, {\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      }),\n      replace: (to, state, opts) => router.navigate(to, {\n        replace: true,\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\";\n  let dataRouterContext = React.useMemo(() => ({\n    router,\n    navigator,\n    static: false,\n    basename\n  }), [router, navigator, basename]);\n  let routerFuture = React.useMemo(() => ({\n    v7_relativeSplatPath: router.future.v7_relativeSplatPath\n  }), [router.future.v7_relativeSplatPath]);\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(UNSAFE_DataRouterContext.Provider, {\n    value: dataRouterContext\n  }, /*#__PURE__*/React.createElement(UNSAFE_DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(FetchersContext.Provider, {\n    value: fetcherData.current\n  }, /*#__PURE__*/React.createElement(ViewTransitionContext.Provider, {\n    value: vtContext\n  }, /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    location: state.location,\n    navigationType: state.historyAction,\n    navigator: navigator,\n    future: routerFuture\n  }, state.initialized || router.future.v7_partialHydration ? /*#__PURE__*/React.createElement(MemoizedDataRoutes, {\n    routes: router.routes,\n    future: router.future,\n    state: state\n  }) : fallbackElement))))), null);\n}\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = /*#__PURE__*/React.memo(DataRoutes);\nfunction DataRoutes(_ref3) {\n  let {\n    routes,\n    future,\n    state\n  } = _ref3;\n  return UNSAFE_useRoutesImpl(routes, undefined, state, future);\n}\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nfunction BrowserRouter(_ref4) {\n  let {\n    basename,\n    children,\n    future,\n    window\n  } = _ref4;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nfunction HashRouter(_ref5) {\n  let {\n    basename,\n    children,\n    future,\n    window\n  } = _ref5;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter(_ref6) {\n  let {\n    basename,\n    children,\n    future,\n    history\n  } = _ref6;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\nconst isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nconst Link = /*#__PURE__*/React.forwardRef(function LinkWithRef(_ref7, ref) {\n  let {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition\n    } = _ref7,\n    rest = _objectWithoutPropertiesLoose(_ref7, _excluded);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  // Rendered into <a href> for absolute URLs\n  let absoluteHref;\n  let isExternal = false;\n  if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n    // Render the absolute href server- and client-side\n    absoluteHref = to;\n    // Only check for external origins client-side\n    if (isBrowser) {\n      try {\n        let currentUrl = new URL(window.location.href);\n        let targetUrl = to.startsWith(\"//\") ? new URL(currentUrl.protocol + to) : new URL(to);\n        let path = stripBasename(targetUrl.pathname, basename);\n        if (targetUrl.origin === currentUrl.origin && path != null) {\n          // Strip the protocol/origin/basename for same-origin absolute URLs\n          to = path + targetUrl.search + targetUrl.hash;\n        } else {\n          isExternal = true;\n        }\n      } catch (e) {\n        // We can't do external URL detection without a valid URL\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"<Link to=\\\"\" + to + \"\\\"> contains an invalid URL which will probably break \" + \"when clicked - please update to a valid URL path.\") : void 0;\n      }\n    }\n  }\n  // Rendered into <a href> for relative URLs\n  let href = useHref(to, {\n    relative\n  });\n  let internalOnClick = useLinkClickHandler(to, {\n    replace,\n    state,\n    target,\n    preventScrollReset,\n    relative,\n    viewTransition\n  });\n  function handleClick(event) {\n    if (onClick) onClick(event);\n    if (!event.defaultPrevented) {\n      internalOnClick(event);\n    }\n  }\n  return (\n    /*#__PURE__*/\n    // eslint-disable-next-line jsx-a11y/anchor-has-content\n    React.createElement(\"a\", _extends({}, rest, {\n      href: absoluteHref || href,\n      onClick: isExternal || reloadDocument ? onClick : handleClick,\n      ref: ref,\n      target: target\n    }))\n  );\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Link.displayName = \"Link\";\n}\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nconst NavLink = /*#__PURE__*/React.forwardRef(function NavLinkWithRef(_ref8, ref) {\n  let {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children\n    } = _ref8,\n    rest = _objectWithoutPropertiesLoose(_ref8, _excluded2);\n  let path = useResolvedPath(to, {\n    relative: rest.relative\n  });\n  let location = useLocation();\n  let routerState = React.useContext(UNSAFE_DataRouterStateContext);\n  let {\n    navigator,\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let isTransitioning = routerState != null &&\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  useViewTransitionState(path) && viewTransition === true;\n  let toPathname = navigator.encodeLocation ? navigator.encodeLocation(path).pathname : path.pathname;\n  let locationPathname = location.pathname;\n  let nextLocationPathname = routerState && routerState.navigation && routerState.navigation.location ? routerState.navigation.location.pathname : null;\n  if (!caseSensitive) {\n    locationPathname = locationPathname.toLowerCase();\n    nextLocationPathname = nextLocationPathname ? nextLocationPathname.toLowerCase() : null;\n    toPathname = toPathname.toLowerCase();\n  }\n  if (nextLocationPathname && basename) {\n    nextLocationPathname = stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n  }\n  // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n  // we're looking for a slash _after_ what's in `to`.  For example:\n  //\n  // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n  // both want to look for a / at index 6 to match URL `/users/matt`\n  const endSlashPosition = toPathname !== \"/\" && toPathname.endsWith(\"/\") ? toPathname.length - 1 : toPathname.length;\n  let isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(endSlashPosition) === \"/\";\n  let isPending = nextLocationPathname != null && (nextLocationPathname === toPathname || !end && nextLocationPathname.startsWith(toPathname) && nextLocationPathname.charAt(toPathname.length) === \"/\");\n  let renderProps = {\n    isActive,\n    isPending,\n    isTransitioning\n  };\n  let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n  let className;\n  if (typeof classNameProp === \"function\") {\n    className = classNameProp(renderProps);\n  } else {\n    // If the className prop is not a function, we use a default `active`\n    // class for <NavLink />s that are active. In v5 `active` was the default\n    // value for `activeClassName`, but we are removing that API and can still\n    // use the old default behavior for a cleaner upgrade path and keep the\n    // simple styling rules working as they currently do.\n    className = [classNameProp, isActive ? \"active\" : null, isPending ? \"pending\" : null, isTransitioning ? \"transitioning\" : null].filter(Boolean).join(\" \");\n  }\n  let style = typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n  return /*#__PURE__*/React.createElement(Link, _extends({}, rest, {\n    \"aria-current\": ariaCurrent,\n    className: className,\n    ref: ref,\n    style: style,\n    to: to,\n    viewTransition: viewTransition\n  }), typeof children === \"function\" ? children(renderProps) : children);\n});\nif (process.env.NODE_ENV !== \"production\") {\n  NavLink.displayName = \"NavLink\";\n}\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nconst Form = /*#__PURE__*/React.forwardRef((_ref9, forwardedRef) => {\n  let {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition\n    } = _ref9,\n    props = _objectWithoutPropertiesLoose(_ref9, _excluded3);\n  let submit = useSubmit();\n  let formAction = useFormAction(action, {\n    relative\n  });\n  let formMethod = method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n  let submitHandler = event => {\n    onSubmit && onSubmit(event);\n    if (event.defaultPrevented) return;\n    event.preventDefault();\n    let submitter = event.nativeEvent.submitter;\n    let submitMethod = (submitter == null ? void 0 : submitter.getAttribute(\"formmethod\")) || method;\n    submit(submitter || event.currentTarget, {\n      fetcherKey,\n      method: submitMethod,\n      navigate,\n      replace,\n      state,\n      relative,\n      preventScrollReset,\n      viewTransition\n    });\n  };\n  return /*#__PURE__*/React.createElement(\"form\", _extends({\n    ref: forwardedRef,\n    method: formMethod,\n    action: formAction,\n    onSubmit: reloadDocument ? onSubmit : submitHandler\n  }, props));\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Form.displayName = \"Form\";\n}\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nfunction ScrollRestoration(_ref10) {\n  let {\n    getKey,\n    storageKey\n  } = _ref10;\n  useScrollRestoration({\n    getKey,\n    storageKey\n  });\n  return null;\n}\nif (process.env.NODE_ENV !== \"production\") {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\nvar DataRouterHook;\n(function (DataRouterHook) {\n  DataRouterHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n  DataRouterHook[\"UseSubmit\"] = \"useSubmit\";\n  DataRouterHook[\"UseSubmitFetcher\"] = \"useSubmitFetcher\";\n  DataRouterHook[\"UseFetcher\"] = \"useFetcher\";\n  DataRouterHook[\"useViewTransitionState\"] = \"useViewTransitionState\";\n})(DataRouterHook || (DataRouterHook = {}));\nvar DataRouterStateHook;\n(function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseFetcher\"] = \"useFetcher\";\n  DataRouterStateHook[\"UseFetchers\"] = \"useFetchers\";\n  DataRouterStateHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n})(DataRouterStateHook || (DataRouterStateHook = {}));\n// Internal hooks\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(UNSAFE_DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(UNSAFE_DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return state;\n}\n// External hooks\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nfunction useLinkClickHandler(to, _temp) {\n  let {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition\n  } = _temp === void 0 ? {} : _temp;\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, {\n    relative\n  });\n  return React.useCallback(event => {\n    if (shouldProcessLinkClick(event, target)) {\n      event.preventDefault();\n      // If the URL hasn't changed, a regular <a> will do a replace instead of\n      // a push, so do the same here unless the replace prop is explicitly set\n      let replace = replaceProp !== undefined ? replaceProp : createPath(location) === createPath(path);\n      navigate(to, {\n        replace,\n        state,\n        preventScrollReset,\n        relative,\n        viewTransition\n      });\n    }\n  }, [location, navigate, path, replaceProp, state, target, to, preventScrollReset, relative, viewTransition]);\n}\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nfunction useSearchParams(defaultInit) {\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(typeof URLSearchParams !== \"undefined\", \"You cannot use the `useSearchParams` hook in a browser that does not \" + \"support the URLSearchParams API. If you need to support Internet \" + \"Explorer 11, we recommend you load a polyfill such as \" + \"https://github.com/ungap/url-search-params.\") : void 0;\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n  let location = useLocation();\n  let searchParams = React.useMemo(() =>\n  // Only merge in the defaults if we haven't yet called setSearchParams.\n  // Once we call that we want those to take precedence, otherwise you can't\n  // remove a param with setSearchParams({}) if it has an initial value\n  getSearchParamsForLocation(location.search, hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current), [location.search]);\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback((nextInit, navigateOptions) => {\n    const newSearchParams = createSearchParams(typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit);\n    hasSetSearchParamsRef.current = true;\n    navigate(\"?\" + newSearchParams, navigateOptions);\n  }, [navigate, searchParams]);\n  return [searchParams, setSearchParams];\n}\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\"You are calling submit during the server render. \" + \"Try calling submit within a `useEffect` or callback instead.\");\n  }\n}\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => \"__\" + String(++fetcherId) + \"__\";\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nfunction useSubmit() {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let currentRouteId = UNSAFE_useRouteId();\n  return React.useCallback(function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    validateClientSideSubmission();\n    let {\n      action,\n      method,\n      encType,\n      formData,\n      body\n    } = getFormSubmissionInfo(target, basename);\n    if (options.navigate === false) {\n      let key = options.fetcherKey || getUniqueFetcherId();\n      router.fetch(key, currentRouteId, options.action || action, {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        body,\n        formMethod: options.method || method,\n        formEncType: options.encType || encType,\n        flushSync: options.flushSync\n      });\n    } else {\n      router.navigate(options.action || action, {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        body,\n        formMethod: options.method || method,\n        formEncType: options.encType || encType,\n        replace: options.replace,\n        state: options.state,\n        fromRouteId: currentRouteId,\n        flushSync: options.flushSync,\n        viewTransition: options.viewTransition\n      });\n    }\n  }, [router, basename, currentRouteId]);\n}\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nfunction useFormAction(action, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let routeContext = React.useContext(UNSAFE_RouteContext);\n  !routeContext ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFormAction must be used inside a RouteContext\") : UNSAFE_invariant(false) : void 0;\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = _extends({}, useResolvedPath(action ? action : \".\", {\n    relative\n  }));\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some(v => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter(v => v).forEach(v => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? \"?\" + qs : \"\";\n    }\n  }\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n  }\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nfunction useFetcher(_temp3) {\n  var _route$matches;\n  let {\n    key\n  } = _temp3 === void 0 ? {} : _temp3;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(UNSAFE_RouteContext);\n  let routeId = (_route$matches = route.matches[route.matches.length - 1]) == null ? void 0 : _route$matches.route.id;\n  !fetcherData ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher must be used inside a FetchersContext\") : UNSAFE_invariant(false) : void 0;\n  !route ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher must be used inside a RouteContext\") : UNSAFE_invariant(false) : void 0;\n  !(routeId != null) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher can only be used on routes that contain a unique \\\"id\\\"\") : UNSAFE_invariant(false) : void 0;\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n  // Fetcher additions\n  let load = React.useCallback((href, opts) => {\n    !routeId ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"No routeId available for fetcher.load()\") : UNSAFE_invariant(false) : void 0;\n    router.fetch(fetcherKey, routeId, href, opts);\n  }, [fetcherKey, routeId, router]);\n  let submitImpl = useSubmit();\n  let submit = React.useCallback((target, opts) => {\n    submitImpl(target, _extends({}, opts, {\n      navigate: false,\n      fetcherKey\n    }));\n  }, [fetcherKey, submitImpl]);\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = /*#__PURE__*/React.forwardRef((props, ref) => {\n      return /*#__PURE__*/React.createElement(Form, _extends({}, props, {\n        navigate: false,\n        fetcherKey: fetcherKey,\n        ref: ref\n      }));\n    });\n    if (process.env.NODE_ENV !== \"production\") {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(() => _extends({\n    Form: FetcherForm,\n    submit,\n    load\n  }, fetcher, {\n    data\n  }), [FetcherForm, submit, load, fetcher, data]);\n  return fetcherWithComponents;\n}\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nfunction useFetchers() {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(_ref11 => {\n    let [key, fetcher] = _ref11;\n    return _extends({}, fetcher, {\n      key\n    });\n  });\n}\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions = {};\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration(_temp4) {\n  let {\n    getKey,\n    storageKey\n  } = _temp4 === void 0 ? {} : _temp4;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let {\n    restoreScrollPosition,\n    preventScrollReset\n  } = useDataRouterState(DataRouterStateHook.UseScrollRestoration);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n  // Save positions on pagehide\n  usePageHide(React.useCallback(() => {\n    if (navigation.state === \"idle\") {\n      let key = (getKey ? getKey(location, matches) : null) || location.key;\n      savedScrollPositions[key] = window.scrollY;\n    }\n    try {\n      sessionStorage.setItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY, JSON.stringify(savedScrollPositions));\n    } catch (error) {\n      process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (\" + error + \").\") : void 0;\n    }\n    window.history.scrollRestoration = \"auto\";\n  }, [storageKey, getKey, navigation.state, location, matches]));\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY);\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename = getKey && basename !== \"/\" ? (location, matches) => getKey( // Strip the basename to match useLocation()\n      _extends({}, location, {\n        pathname: stripBasename(location.pathname, basename) || location.pathname\n      }), matches) : getKey;\n      let disableScrollRestoration = router == null ? void 0 : router.enableScrollRestoration(savedScrollPositions, () => window.scrollY, getKeyWithoutBasename);\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(decodeURIComponent(location.hash.slice(1)));\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction useBeforeUnload(callback, options) {\n  let {\n    capture\n  } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? {\n      capture\n    } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(callback, options) {\n  let {\n    capture\n  } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? {\n      capture\n    } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt(_ref12) {\n  let {\n    when,\n    message\n  } = _ref12;\n  let blocker = useBlocker(when);\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(to, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  let vtContext = React.useContext(ViewTransitionContext);\n  !(vtContext != null) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" + \"Did you accidentally import `RouterProvider` from `react-router`?\") : UNSAFE_invariant(false) : void 0;\n  let {\n    basename\n  } = useDataRouterContext(DataRouterHook.useViewTransitionState);\n  let path = useResolvedPath(to, {\n    relative: opts.relative\n  });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n  let currentPath = stripBasename(vtContext.currentLocation.pathname, basename) || vtContext.currentLocation.pathname;\n  let nextPath = stripBasename(vtContext.nextLocation.pathname, basename) || vtContext.nextLocation.pathname;\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return matchPath(path.pathname, nextPath) != null || matchPath(path.pathname, currentPath) != null;\n}\n//#endregion\n\nexport { BrowserRouter, Form, HashRouter, Link, NavLink, RouterProvider, ScrollRestoration, FetchersContext as UNSAFE_FetchersContext, ViewTransitionContext as UNSAFE_ViewTransitionContext, useScrollRestoration as UNSAFE_useScrollRestoration, createBrowserRouter, createHashRouter, createSearchParams, HistoryRouter as unstable_HistoryRouter, usePrompt as unstable_usePrompt, useBeforeUnload, useFetcher, useFetchers, useFormAction, useLinkClickHandler, useSearchParams, useSubmit, useViewTransitionState };\n//# sourceMappingURL=index.js.map\n", "/**\n * React Router v6.27.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { UNSAFE_invariant, joinPaths, matchPath, UNSAFE_decodePath, UNSAFE_getResolveToMatches, UNSAFE_warning, resolveTo, parsePath, matchRoutes, Action, UNSAFE_convertRouteMatchToUiMatch, stripBasename, IDLE_BLOCKER, isRouteErrorResponse, createMemoryHistory, AbortedDeferredError, createRouter } from '@remix-run/router';\nexport { AbortedDeferredError, Action as NavigationType, createPath, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, redirectDocument, replace, resolvePath } from '@remix-run/router';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nconst DataRouterContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterContext.displayName = \"DataRouter\";\n}\nconst DataRouterStateContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\nconst AwaitContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  AwaitContext.displayName = \"Await\";\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\n\nconst NavigationContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  NavigationContext.displayName = \"Navigation\";\n}\nconst LocationContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  LocationContext.displayName = \"Location\";\n}\nconst RouteContext = /*#__PURE__*/React.createContext({\n  outlet: null,\n  matches: [],\n  isDataRoute: false\n});\nif (process.env.NODE_ENV !== \"production\") {\n  RouteContext.displayName = \"Route\";\n}\nconst RouteErrorContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\nfunction useHref(to, _temp) {\n  let {\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useHref() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    basename,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    hash,\n    pathname,\n    search\n  } = useResolvedPath(to, {\n    relative\n  });\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname = pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n  return navigator.createHref({\n    pathname: joinedPathname,\n    search,\n    hash\n  });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\nfunction useInRouterContext() {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\nfunction useLocation() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useLocation() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\nfunction useNavigationType() {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\nfunction useMatch(pattern) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useMatch() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    pathname\n  } = useLocation();\n  return React.useMemo(() => matchPath(pattern, UNSAFE_decodePath(pathname)), [pathname, pattern]);\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\n\nconst navigateEffectWarning = \"You should call navigate() in a React.useEffect(), not when \" + \"your component is first rendered.\";\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(cb) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nfunction useNavigate() {\n  let {\n    isDataRoute\n  } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\nfunction useNavigateUnstable() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useNavigate() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let {\n    basename,\n    future,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath));\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(activeRef.current, navigateEffectWarning) : void 0;\n\n    // Short circuit here since if this happens on first render the navigate\n    // is useless because we haven't wired up our history listener yet\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      navigator.go(to);\n      return;\n    }\n    let path = resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, options.relative === \"path\");\n\n    // If we're operating within a basename, prepend it to the pathname prior\n    // to handing off to history (but only if we're not in a data router,\n    // otherwise it'll prepend the basename inside of the router).\n    // If this is a root navigation, then we navigate to the raw basename\n    // which allows the basename to have full control over the presence of a\n    // trailing slash on root links\n    if (dataRouterContext == null && basename !== \"/\") {\n      path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n    }\n    (!!options.replace ? navigator.replace : navigator.push)(path, options.state, options);\n  }, [basename, navigator, routePathnamesJson, locationPathname, dataRouterContext]);\n  return navigate;\n}\nconst OutletContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\nfunction useOutletContext() {\n  return React.useContext(OutletContext);\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\nfunction useOutlet(context) {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return /*#__PURE__*/React.createElement(OutletContext.Provider, {\n      value: context\n    }, outlet);\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\nfunction useParams() {\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? routeMatch.params : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\nfunction useResolvedPath(to, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    future\n  } = React.useContext(NavigationContext);\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath));\n  return React.useMemo(() => resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, relative === \"path\"), [to, routePathnamesJson, locationPathname, relative]);\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\nfunction useRoutes(routes, locationArg) {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nfunction useRoutesImpl(routes, locationArg, dataRouterState, future) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useRoutes() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    matches: parentMatches\n  } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n  if (process.env.NODE_ENV !== \"production\") {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = parentRoute && parentRoute.path || \"\";\n    warningOnce(parentPathname, !parentRoute || parentPath.endsWith(\"*\"), \"You rendered descendant <Routes> (or called `useRoutes()`) at \" + (\"\\\"\" + parentPathname + \"\\\" (under <Route path=\\\"\" + parentPath + \"\\\">) but the \") + \"parent route path has no trailing \\\"*\\\". This means if you navigate \" + \"deeper, the parent won't match anymore and therefore the child \" + \"routes will never render.\\n\\n\" + (\"Please change the parent <Route path=\\\"\" + parentPath + \"\\\"> to <Route \") + (\"path=\\\"\" + (parentPath === \"/\" ? \"*\" : parentPath + \"/*\") + \"\\\">.\"));\n  }\n  let locationFromContext = useLocation();\n  let location;\n  if (locationArg) {\n    var _parsedLocationArg$pa;\n    let parsedLocationArg = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n    !(parentPathnameBase === \"/\" || ((_parsedLocationArg$pa = parsedLocationArg.pathname) == null ? void 0 : _parsedLocationArg$pa.startsWith(parentPathnameBase))) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, \" + \"the location pathname must begin with the portion of the URL pathname that was \" + (\"matched by all parent routes. The current pathname base is \\\"\" + parentPathnameBase + \"\\\" \") + (\"but pathname \\\"\" + parsedLocationArg.pathname + \"\\\" was given in the `location` prop.\")) : UNSAFE_invariant(false) : void 0;\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n  let pathname = location.pathname || \"/\";\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n  let matches = matchRoutes(routes, {\n    pathname: remainingPathname\n  });\n  if (process.env.NODE_ENV !== \"production\") {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(parentRoute || matches != null, \"No routes matched location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \") : void 0;\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(matches == null || matches[matches.length - 1].route.element !== undefined || matches[matches.length - 1].route.Component !== undefined || matches[matches.length - 1].route.lazy !== undefined, \"Matched leaf route at location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \" + \"does not have an element or Component. This means it will render an <Outlet /> with a \" + \"null value by default resulting in an \\\"empty\\\" page.\") : void 0;\n  }\n  let renderedMatches = _renderMatches(matches && matches.map(match => Object.assign({}, match, {\n    params: Object.assign({}, parentParams, match.params),\n    pathname: joinPaths([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathname).pathname : match.pathname]),\n    pathnameBase: match.pathnameBase === \"/\" ? parentPathnameBase : joinPaths([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathnameBase).pathname : match.pathnameBase])\n  })), parentMatches, dataRouterState, future);\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return /*#__PURE__*/React.createElement(LocationContext.Provider, {\n      value: {\n        location: _extends({\n          pathname: \"/\",\n          search: \"\",\n          hash: \"\",\n          state: null,\n          key: \"default\"\n        }, location),\n        navigationType: Action.Pop\n      }\n    }, renderedMatches);\n  }\n  return renderedMatches;\n}\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error) ? error.status + \" \" + error.statusText : error instanceof Error ? error.message : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = {\n    padding: \"0.5rem\",\n    backgroundColor: lightgrey\n  };\n  let codeStyles = {\n    padding: \"2px 4px\",\n    backgroundColor: lightgrey\n  };\n  let devInfo = null;\n  if (process.env.NODE_ENV !== \"production\") {\n    console.error(\"Error handled by React Router default ErrorBoundary:\", error);\n    devInfo = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"p\", null, \"\\uD83D\\uDCBF Hey developer \\uD83D\\uDC4B\"), /*#__PURE__*/React.createElement(\"p\", null, \"You can provide a way better UX than this when your app throws errors by providing your own \", /*#__PURE__*/React.createElement(\"code\", {\n      style: codeStyles\n    }, \"ErrorBoundary\"), \" or\", \" \", /*#__PURE__*/React.createElement(\"code\", {\n      style: codeStyles\n    }, \"errorElement\"), \" prop on your route.\"));\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"h2\", null, \"Unexpected Application Error!\"), /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      fontStyle: \"italic\"\n    }\n  }, message), stack ? /*#__PURE__*/React.createElement(\"pre\", {\n    style: preStyles\n  }, stack) : null, devInfo);\n}\nconst defaultErrorElement = /*#__PURE__*/React.createElement(DefaultErrorComponent, null);\nclass RenderErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error: error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location || state.revalidation !== \"idle\" && props.revalidation === \"idle\") {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"React Router caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    return this.state.error !== undefined ? /*#__PURE__*/React.createElement(RouteContext.Provider, {\n      value: this.props.routeContext\n    }, /*#__PURE__*/React.createElement(RouteErrorContext.Provider, {\n      value: this.state.error,\n      children: this.props.component\n    })) : this.props.children;\n  }\n}\nfunction RenderedRoute(_ref) {\n  let {\n    routeContext,\n    match,\n    children\n  } = _ref;\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (dataRouterContext && dataRouterContext.static && dataRouterContext.staticContext && (match.route.errorElement || match.route.ErrorBoundary)) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n  return /*#__PURE__*/React.createElement(RouteContext.Provider, {\n    value: routeContext\n  }, children);\n}\nfunction _renderMatches(matches, parentMatches, dataRouterState, future) {\n  var _dataRouterState;\n  if (parentMatches === void 0) {\n    parentMatches = [];\n  }\n  if (dataRouterState === void 0) {\n    dataRouterState = null;\n  }\n  if (future === void 0) {\n    future = null;\n  }\n  if (matches == null) {\n    var _future;\n    if (!dataRouterState) {\n      return null;\n    }\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches;\n    } else if ((_future = future) != null && _future.v7_partialHydration && parentMatches.length === 0 && !dataRouterState.initialized && dataRouterState.matches.length > 0) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches;\n    } else {\n      return null;\n    }\n  }\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = (_dataRouterState = dataRouterState) == null ? void 0 : _dataRouterState.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(m => m.route.id && (errors == null ? void 0 : errors[m.route.id]) !== undefined);\n    !(errorIndex >= 0) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"Could not find a matching route for errors on route IDs: \" + Object.keys(errors).join(\",\")) : UNSAFE_invariant(false) : void 0;\n    renderedMatches = renderedMatches.slice(0, Math.min(renderedMatches.length, errorIndex + 1));\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n      if (match.route.id) {\n        let {\n          loaderData,\n          errors\n        } = dataRouterState;\n        let needsToRunLoader = match.route.loader && loaderData[match.route.id] === undefined && (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error;\n    let shouldRenderHydrateFallback = false;\n    let errorElement = null;\n    let hydrateFallbackElement = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\"route-fallback\", false, \"No `HydrateFallback` element provided to render during initial hydration\");\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = /*#__PURE__*/React.createElement(match.route.Component, null);\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return /*#__PURE__*/React.createElement(RenderedRoute, {\n        match: match,\n        routeContext: {\n          outlet,\n          matches,\n          isDataRoute: dataRouterState != null\n        },\n        children: children\n      });\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState && (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? /*#__PURE__*/React.createElement(RenderErrorBoundary, {\n      location: dataRouterState.location,\n      revalidation: dataRouterState.revalidation,\n      component: errorElement,\n      error: error,\n      children: getChildren(),\n      routeContext: {\n        outlet: null,\n        matches,\n        isDataRoute: true\n      }\n    }) : getChildren();\n  }, null);\n}\nvar DataRouterHook = /*#__PURE__*/function (DataRouterHook) {\n  DataRouterHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterHook[\"UseRevalidator\"] = \"useRevalidator\";\n  DataRouterHook[\"UseNavigateStable\"] = \"useNavigate\";\n  return DataRouterHook;\n}(DataRouterHook || {});\nvar DataRouterStateHook = /*#__PURE__*/function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterStateHook[\"UseLoaderData\"] = \"useLoaderData\";\n  DataRouterStateHook[\"UseActionData\"] = \"useActionData\";\n  DataRouterStateHook[\"UseRouteError\"] = \"useRouteError\";\n  DataRouterStateHook[\"UseNavigation\"] = \"useNavigation\";\n  DataRouterStateHook[\"UseRouteLoaderData\"] = \"useRouteLoaderData\";\n  DataRouterStateHook[\"UseMatches\"] = \"useMatches\";\n  DataRouterStateHook[\"UseRevalidator\"] = \"useRevalidator\";\n  DataRouterStateHook[\"UseNavigateStable\"] = \"useNavigate\";\n  DataRouterStateHook[\"UseRouteId\"] = \"useRouteId\";\n  return DataRouterStateHook;\n}(DataRouterStateHook || {});\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return state;\n}\nfunction useRouteContext(hookName) {\n  let route = React.useContext(RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  !thisRoute.route.id ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, hookName + \" can only be used on routes that contain a unique \\\"id\\\"\") : UNSAFE_invariant(false) : void 0;\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nfunction useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nfunction useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nfunction useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(() => ({\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation\n  }), [dataRouterContext.router.revalidate, state.revalidation]);\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nfunction useMatches() {\n  let {\n    matches,\n    loaderData\n  } = useDataRouterState(DataRouterStateHook.UseMatches);\n  return React.useMemo(() => matches.map(m => UNSAFE_convertRouteMatchToUiMatch(m, loaderData)), [matches, loaderData]);\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nfunction useLoaderData() {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\"You cannot `useLoaderData` in an errorElement (routeId: \" + routeId + \")\");\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nfunction useRouteLoaderData(routeId) {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nfunction useActionData() {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nfunction useRouteError() {\n  var _state$errors;\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return (_state$errors = state.errors) == null ? void 0 : _state$errors[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nfunction useAsyncValue() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nfunction useAsyncError() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._error;\n}\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nfunction useBlocker(shouldBlock) {\n  let {\n    router,\n    basename\n  } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback(arg => {\n    if (typeof shouldBlock !== \"function\") {\n      return !!shouldBlock;\n    }\n    if (basename === \"/\") {\n      return shouldBlock(arg);\n    }\n\n    // If they provided us a function and we've got an active basename, strip\n    // it from the locations we expose to the user to match the behavior of\n    // useLocation\n    let {\n      currentLocation,\n      nextLocation,\n      historyAction\n    } = arg;\n    return shouldBlock({\n      currentLocation: _extends({}, currentLocation, {\n        pathname: stripBasename(currentLocation.pathname, basename) || currentLocation.pathname\n      }),\n      nextLocation: _extends({}, nextLocation, {\n        pathname: stripBasename(nextLocation.pathname, basename) || nextLocation.pathname\n      }),\n      historyAction\n    });\n  }, [basename, shouldBlock]);\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey) ? state.blockers.get(blockerKey) : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable() {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(activeRef.current, navigateEffectWarning) : void 0;\n\n    // Short circuit here since if this happens on first render the navigate\n    // is useless because we haven't wired up our router subscriber yet\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      router.navigate(to);\n    } else {\n      router.navigate(to, _extends({\n        fromRouteId: id\n      }, options));\n    }\n  }, [router, id]);\n  return navigate;\n}\nconst alreadyWarned = {};\nfunction warningOnce(key, cond, message) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, message) : void 0;\n  }\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nfunction RouterProvider(_ref) {\n  let {\n    fallbackElement,\n    router,\n    future\n  } = _ref;\n  let [state, setStateImpl] = React.useState(router.state);\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    if (v7_startTransition && startTransitionImpl) {\n      startTransitionImpl(() => setStateImpl(newState));\n    } else {\n      setStateImpl(newState);\n    }\n  }, [setStateImpl, v7_startTransition]);\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n  React.useEffect(() => {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(fallbackElement == null || !router.future.v7_partialHydration, \"`<RouterProvider fallbackElement>` is deprecated when using \" + \"`v7_partialHydration`, use a `HydrateFallback` component instead\") : void 0;\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  let navigator = React.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: n => router.navigate(n),\n      push: (to, state, opts) => router.navigate(to, {\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      }),\n      replace: (to, state, opts) => router.navigate(to, {\n        replace: true,\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\";\n  let dataRouterContext = React.useMemo(() => ({\n    router,\n    navigator,\n    static: false,\n    basename\n  }), [router, navigator, basename]);\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(DataRouterContext.Provider, {\n    value: dataRouterContext\n  }, /*#__PURE__*/React.createElement(DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    location: state.location,\n    navigationType: state.historyAction,\n    navigator: navigator,\n    future: {\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath\n    }\n  }, state.initialized || router.future.v7_partialHydration ? /*#__PURE__*/React.createElement(DataRoutes, {\n    routes: router.routes,\n    future: router.future,\n    state: state\n  }) : fallbackElement))), null);\n}\nfunction DataRoutes(_ref2) {\n  let {\n    routes,\n    future,\n    state\n  } = _ref2;\n  return useRoutesImpl(routes, undefined, state, future);\n}\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nfunction MemoryRouter(_ref3) {\n  let {\n    basename,\n    children,\n    initialEntries,\n    initialIndex,\n    future\n  } = _ref3;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nfunction Navigate(_ref4) {\n  let {\n    to,\n    replace,\n    state,\n    relative\n  } = _ref4;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of\n  // the router loaded. We can help them understand how to avoid that.\n  \"<Navigate> may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    future,\n    static: isStatic\n  } = React.useContext(NavigationContext);\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(!isStatic, \"<Navigate> must not be used on the initial render in a <StaticRouter>. \" + \"This is a no-op, but you should modify your code so the <Navigate> is \" + \"only ever rendered in response to some user interaction or state change.\") : void 0;\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(to, UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath), locationPathname, relative === \"path\");\n  let jsonPath = JSON.stringify(path);\n  React.useEffect(() => navigate(JSON.parse(jsonPath), {\n    replace,\n    state,\n    relative\n  }), [navigate, jsonPath, relative, replace, state]);\n  return null;\n}\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nfunction Outlet(props) {\n  return useOutlet(props.context);\n}\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nfunction Route(_props) {\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"A <Route> is only ever to be used as the child of <Routes> element, \" + \"never rendered directly. Please wrap your <Route> in a <Routes>.\") : UNSAFE_invariant(false) ;\n}\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nfunction Router(_ref5) {\n  let {\n    basename: basenameProp = \"/\",\n    children = null,\n    location: locationProp,\n    navigationType = Action.Pop,\n    navigator,\n    static: staticProp = false,\n    future\n  } = _ref5;\n  !!useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"You cannot render a <Router> inside another <Router>.\" + \" You should never have more than one in your app.\") : UNSAFE_invariant(false) : void 0;\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(() => ({\n    basename,\n    navigator,\n    static: staticProp,\n    future: _extends({\n      v7_relativeSplatPath: false\n    }, future)\n  }), [basename, future, navigator, staticProp]);\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\"\n  } = locationProp;\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n    if (trailingPathname == null) {\n      return null;\n    }\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key\n      },\n      navigationType\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(locationContext != null, \"<Router basename=\\\"\" + basename + \"\\\"> is not able to match the URL \" + (\"\\\"\" + pathname + search + hash + \"\\\" because it does not start with the \") + \"basename, so the <Router> won't render anything.\") : void 0;\n  if (locationContext == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(NavigationContext.Provider, {\n    value: navigationContext\n  }, /*#__PURE__*/React.createElement(LocationContext.Provider, {\n    children: children,\n    value: locationContext\n  }));\n}\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nfunction Routes(_ref6) {\n  let {\n    children,\n    location\n  } = _ref6;\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nfunction Await(_ref7) {\n  let {\n    children,\n    errorElement,\n    resolve\n  } = _ref7;\n  return /*#__PURE__*/React.createElement(AwaitErrorBoundary, {\n    resolve: resolve,\n    errorElement: errorElement\n  }, /*#__PURE__*/React.createElement(ResolveAwait, null, children));\n}\nvar AwaitRenderStatus = /*#__PURE__*/function (AwaitRenderStatus) {\n  AwaitRenderStatus[AwaitRenderStatus[\"pending\"] = 0] = \"pending\";\n  AwaitRenderStatus[AwaitRenderStatus[\"success\"] = 1] = \"success\";\n  AwaitRenderStatus[AwaitRenderStatus[\"error\"] = 2] = \"error\";\n  return AwaitRenderStatus;\n}(AwaitRenderStatus || {});\nconst neverSettledPromise = new Promise(() => {});\nclass AwaitErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"<Await> caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    let {\n      children,\n      errorElement,\n      resolve\n    } = this.props;\n    let promise = null;\n    let status = AwaitRenderStatus.pending;\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_data\", {\n        get: () => resolve\n      });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_error\", {\n        get: () => renderError\n      });\n    } else if (resolve._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status = \"_error\" in promise ? AwaitRenderStatus.error : \"_data\" in promise ? AwaitRenderStatus.success : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", {\n        get: () => true\n      });\n      promise = resolve.then(data => Object.defineProperty(resolve, \"_data\", {\n        get: () => data\n      }), error => Object.defineProperty(resolve, \"_error\", {\n        get: () => error\n      }));\n    }\n    if (status === AwaitRenderStatus.error && promise._error instanceof AbortedDeferredError) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: errorElement\n      });\n    }\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: children\n      });\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait(_ref8) {\n  let {\n    children\n  } = _ref8;\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, toRender);\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\nfunction createRoutesFromChildren(children, parentPath) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  let routes = [];\n  React.Children.forEach(children, (element, index) => {\n    if (! /*#__PURE__*/React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n    let treePath = [...parentPath, index];\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(routes, createRoutesFromChildren(element.props.children, treePath));\n      return;\n    }\n    !(element.type === Route) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"[\" + (typeof element.type === \"string\" ? element.type : element.type.name) + \"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>\") : UNSAFE_invariant(false) : void 0;\n    !(!element.props.index || !element.props.children) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"An index route cannot have child routes.\") : UNSAFE_invariant(false) : void 0;\n    let route = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary: element.props.ErrorBoundary != null || element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy\n    };\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(element.props.children, treePath);\n    }\n    routes.push(route);\n  });\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nfunction renderMatches(matches) {\n  return _renderMatches(matches);\n}\n\nfunction mapRouteProperties(route) {\n  let updates = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null\n  };\n  if (route.Component) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.element) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `Component` and `element` on your route - \" + \"`Component` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      element: /*#__PURE__*/React.createElement(route.Component),\n      Component: undefined\n    });\n  }\n  if (route.HydrateFallback) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.hydrateFallbackElement) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" + \"`HydrateFallback` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: /*#__PURE__*/React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined\n    });\n  }\n  if (route.ErrorBoundary) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.errorElement) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" + \"`ErrorBoundary` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      errorElement: /*#__PURE__*/React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined\n    });\n  }\n  return updates;\n}\nfunction createMemoryRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createMemoryHistory({\n      initialEntries: opts == null ? void 0 : opts.initialEntries,\n      initialIndex: opts == null ? void 0 : opts.initialIndex\n    }),\n    hydrationData: opts == null ? void 0 : opts.hydrationData,\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts == null ? void 0 : opts.dataStrategy,\n    patchRoutesOnNavigation: opts == null ? void 0 : opts.patchRoutesOnNavigation\n  }).initialize();\n}\n\nexport { Await, MemoryRouter, Navigate, Outlet, Route, Router, RouterProvider, Routes, DataRouterContext as UNSAFE_DataRouterContext, DataRouterStateContext as UNSAFE_DataRouterStateContext, LocationContext as UNSAFE_LocationContext, NavigationContext as UNSAFE_NavigationContext, RouteContext as UNSAFE_RouteContext, mapRouteProperties as UNSAFE_mapRouteProperties, useRouteId as UNSAFE_useRouteId, useRoutesImpl as UNSAFE_useRoutesImpl, createMemoryRouter, createRoutesFromChildren, createRoutesFromChildren as createRoutesFromElements, renderMatches, useActionData, useAsyncError, useAsyncValue, useBlocker, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes };\n//# sourceMappingURL=index.js.map\n"], "names": ["Action", "ResultType", "_extends", "Object", "target", "i", "arguments", "source", "key", "PopStateEventType", "createBrowserHistory", "options", "getUrlBasedHistory", "window", "globalHistory", "pathname", "search", "hash", "createLocation", "to", "createPath", "createHashHistory", "parsePath", "base", "href", "url", "hashIndex", "location", "warning", "JSON", "invariant", "value", "message", "Error", "cond", "console", "e", "getHistoryState", "index", "current", "state", "Math", "_ref", "path", "parsed<PERSON><PERSON>", "searchIndex", "getLocation", "createHref", "validateLocation", "document", "v5Compat", "action", "listener", "getIndex", "handlePop", "nextIndex", "delta", "history", "createURL", "URL", "fn", "push", "historyState", "error", "DOMException", "replace", "n", "immutableRouteKeys", "Set", "convertRoutesToDataRoutes", "routes", "mapRouteProperties", "parentPath", "manifest", "route", "treePath", "String", "id", "indexRoute", "pathOrLayoutRoute", "undefined", "matchRoutes", "locationArg", "basename", "matchRoutesImpl", "allowPartial", "stripBasename", "branches", "flattenRoutes", "parents<PERSON>eta", "flattenRoute", "relativePath", "segments", "initialScore", "meta", "joinPaths", "routesMeta", "isSplat", "s", "score", "segment", "paramRe", "_route$path", "exploded", "explodeOptionalSegments", "first", "rest", "isOptional", "required", "restExploded", "result", "subpath", "a", "b", "siblings", "matches", "decoded", "decodePath", "v", "decodeURIComponent", "matchRouteBranch", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "match", "matchPath", "normalizePathname", "convertRouteMatchToUiMatch", "loaderData", "params", "pattern", "caseSensitive", "regexpSource", "matcher", "compiledParams", "_", "paramName", "RegExp", "pathnameBase", "captureGroups", "memo", "splatValue", "startIndex", "nextChar", "getInvalidPathError", "char", "field", "dest", "getPathContributingMatches", "getResolveToMatches", "v7_relativeSplatPath", "pathMatches", "idx", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "from", "isEmptyPath", "toPathname", "routePathnameIndex", "toSegments", "<PERSON><PERSON><PERSON>", "fromPathname", "relativeSegments", "normalizeSearch", "normalizeHash", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "paths", "Aborted<PERSON>eferredError", "ErrorResponseImpl", "status", "statusText", "data", "internal", "isRouteErrorResponse", "validMutationMethodsArr", "validMutationMethods", "validRequestMethods", "redirectStatusCodes", "redirectPreserveMethodStatusCodes", "IDLE_NAVIGATION", "IDLE_FETCHER", "IDLE_BLOCKER", "ABSOLUTE_URL_REGEX", "defaultMapRouteProperties", "Boolean", "TRANSITIONS_STORAGE_KEY", "createRouter", "init", "inFlightDataRoutes", "initialized", "router", "pendingNavigationController", "unblockBlockerHistoryUpdate", "routerWindow", "<PERSON><PERSON><PERSON><PERSON>", "isServer", "detectErrorBoundary", "dataRoutes", "dataStrategyImpl", "defaultDataStrategy", "patchRoutesOnNavigationImpl", "future", "unlistenHistory", "subscribers", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "initialMatches", "initialErrors", "getInternalRouterError", "getShortCircuitMatches", "fogOfWar", "checkFogOfWar", "m", "errors", "shouldLoadRouteOnHydration", "Map", "pendingAction", "pendingPreventScrollReset", "pendingViewTransitionEnabled", "appliedViewTransitions", "removePageHideEventListener", "isUninterruptedRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "fetchRedirectIds", "fetchLoadMatches", "activeFetchers", "deletedFetchers", "activeDeferreds", "blockerFunctions", "updateState", "newState", "opts", "completedFetchers", "deletedFetchersKeys", "fetcher", "subscriber", "deleteFetcher", "completeNavigation", "_temp", "_location$state", "_location$state2", "actionData", "viewTransitionOpts", "flushSync", "isActionReload", "isMutationMethod", "mergeLoaderData", "blockers", "k", "preventScrollReset", "priorPaths", "toPaths", "getSavedScrollPosition", "navigate", "normalizedPath", "normalizeTo", "submission", "normalizeNavigateOptions", "currentLocation", "nextLocation", "userReplace", "historyAction", "blockerKey", "shouldBlockNavigation", "updateBlocker", "startNavigation", "pendingActionResult", "getScrollKey", "routesToUse", "loadingNavigation", "notFoundMatches", "handleNavigational404", "AbortController", "request", "createClientSideRequest", "findNearestBoundary", "actionResult", "handleAction", "routeId", "isErrorResult", "getLoadingNavigation", "shortCircuited", "updatedMatches", "handleLoaders", "getActionDataForCommit", "isFogOfWar", "interruptActiveLoads", "navigation", "discoverResult", "discoverRoutes", "boundaryId", "actionMatch", "getTargetMatch", "results", "callDataStrategy", "isRedirectResult", "normalizeRedirectLocation", "startRedirectNavigation", "isDeferredResult", "boundaryMatch", "overrideNavigation", "fetcherSubmission", "initialHydration", "activeSubmission", "getSubmissionFromNavigation", "shouldUpdateNavigationState", "getUpdatedActionData", "matchesToLoad", "revalidatingFetchers", "getMatchesToLoad", "cancelActiveDeferreds", "updatedFetchers", "markFetchRedirectsDone", "updates", "rf", "revalidatingFetcher", "getLoadingFetcher", "abort<PERSON><PERSON><PERSON>", "abortPendingFetchRevalidations", "f", "loaderResults", "fetcherResults", "callLoadersAndMaybeResolveData", "redirect", "findRedirect", "processLoaderData", "deferredData", "aborted", "didAbortFetchLoads", "abortStaleFetchLoads", "shouldUpdateFetchers", "handleFetcherAction", "requestMatches", "existingFetcher", "detectAndHandle405Error", "setFetcherError", "updateFetcherState", "abortController", "fetchRequest", "originatingLoadId", "actionResults", "getDoneFetcher", "revalidationRequest", "loadId", "loadFetcher", "staleKey", "r", "done<PERSON>etcher", "handleFetcherLoader", "resolveDeferredData", "isNavigation", "_temp2", "redirectLocation", "isDocumentReload", "redirectHistoryAction", "formMethod", "formAction", "formEncType", "type", "fetcher<PERSON>ey", "dataResults", "callDataStrategyImpl", "isResponse", "response", "normalizeRelativeRoutingRedirectResponse", "trimmedMatches", "convertDataStrategyResultToDataResult", "fetchersToLoad", "currentMatches", "loaderResultsPromise", "fetcherResultsPromise", "Promise", "acc", "resolveNavigationDeferredResults", "resolveFetcherDeferredResults", "getFetcher", "controller", "markFetchersDone", "keys", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "deleteBlocker", "newBlocker", "blocker", "_ref2", "entries", "Array", "blockerFunction", "predicate", "cancelledRouteIds", "dfd", "y", "fogMatches", "signal", "partialMatch<PERSON>", "isNonHMR", "localManifest", "children", "patchRoutesImpl", "newMatches", "newPartialMatches", "initialize", "nextHistoryUpdatePromise", "resolve", "_window", "transitions", "sessionPositions", "json", "_saveAppliedTransitions", "persistAppliedTransitions", "subscribe", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "fetch", "revalidate", "count", "dispose", "get<PERSON><PERSON>er", "patchRoutes", "_internalSetRoutes", "newRoutes", "prependBasename", "fromRouteId", "relative", "contextualMatches", "activeRouteMatch", "nakedIndex", "hasNakedIndexQuery", "URLSearchParams", "indexValues", "qs", "normalizeFormMethod", "isFetcher", "method", "searchParams", "formData", "getInvalidBodyError", "rawFormMethod", "stripHashFromPath", "text", "FormData", "_ref3", "name", "convertFormDataToSearchParams", "convertSearchParamsToFormData", "getLoaderMatchesUntilBoundary", "includeBoundary", "skipActionErrorRevalidation", "currentUrl", "nextUrl", "boundaryMatches", "actionStatus", "shouldSkipRevalidation", "navigationMatches", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "is<PERSON>ew<PERSON><PERSON>der", "currentRouteMatch", "shouldRevalidateLoader", "nextRouteMatch", "isNewRouteInstance", "fetcherMatches", "fetcherMatch", "shouldRevalidate", "hasData", "<PERSON><PERSON><PERSON><PERSON>", "currentPath", "loaderMatch", "arg", "routeChoice", "_childrenToPatch", "childrenToPatch", "newRoute", "existingRoute", "isSameRoute", "<PERSON><PERSON><PERSON><PERSON>", "_existingRoute$childr", "b<PERSON><PERSON><PERSON>", "loadLazyRouteModule", "lazyRoute", "routeToUpdate", "routeUpdates", "lazyRouteProperty", "isPropertyStaticallyDefined", "staticRouteValue", "_ref4", "requestContext", "loadRouteDefinitionsPromises", "dsMatches", "loadRoutePromise", "shouldLoad", "handlerOverride", "callLoaderOrAction", "staticContext", "onReject", "<PERSON><PERSON><PERSON><PERSON>", "handler", "reject", "abortPromise", "<PERSON><PERSON><PERSON><PERSON>", "ctx", "val", "handlerError", "dataStrategyResult", "_result$init2", "_result$init", "_result$init3", "_result$init4", "_result$init5", "_result$init6", "contentType", "isDataWithResponseInit", "deferred", "Headers", "normalizedLocation", "isSameBasename", "Request", "statusCode", "found<PERSON><PERSON>r", "loaderHeaders", "pendingError", "newLoaderData", "mergedLoaderData", "eligibleMatches", "_temp5", "errorMessage", "isRevalidatingLoader", "unwrap", "Symbol", "DataRouterHook", "DataRouterStateHook", "createSearchParams", "createBrowserRouter", "parseHydrationData", "createHashRouter", "deserializeErrors", "serialized", "ErrorConstructor", "ViewTransitionContext", "FetchersContext", "startTransitionImpl", "flushSyncImpl", "flushSyncSafe", "cb", "Deferred", "reason", "RouterProvider", "fallbackElement", "setStateImpl", "pendingState", "setPendingState", "vtContext", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "fetcherData", "v7_startTransition", "optInStartTransition", "setState", "isViewTransitionUnavailable", "t", "renderPromise", "navigator", "dataRouterContext", "routerFuture", "MemoizedDataRoutes", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "locationSearch", "defaultSearchParams", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "createRoutesFromChildren", "element", "Route", "AwaitRenderStatus", "DataRouterContext", "DataRouterStateContext", "NavigationContext", "LocationContext", "RouteContext", "RouteErrorContext", "useHref", "useInRouterContext", "useResolvedPath", "useLocation", "routePathnamesJson", "joinedPathname", "useIsomorphicLayoutEffect", "useNavigate", "isDataRoute", "useNavigateStable", "<PERSON><PERSON><PERSON>", "useCurrentRouteId", "activeRef", "useNavigateUnstable", "OutletContext", "useRoutesImpl", "dataRouterState", "parentMatches", "routeMatch", "parentParams", "parentPathnameBase", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parentSegments", "renderedMatches", "_renderMatches", "_dataRouterState", "_future", "errorIndex", "renderFallback", "fallbackIndex", "needsToRunLoader", "outlet", "shouldRenderHydrateFallback", "errorElement", "hydrateFallbackElement", "defaultErrorElement", "alreadyWarned", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RenderedRoute", "RenderErrorBoundary", "_state$errors", "useDataRouterState", "stack", "props", "errorInfo", "routeContext", "thisRoute", "useMatches", "Outlet", "context", "_props", "Router", "_ref5", "basenameProp", "locationProp", "navigationType", "staticProp", "navigationContext", "locationContext", "trailingPathname"], "mappings": ";0HA+BIA,EAobAC,EAnbOD,EAobAC,EA1cX,SAASC,IAYP,MAAOA,AAXPA,CAAAA,EAAWC,OAAO,MAAM,CAAGA,OAAO,MAAM,CAAC,IAAI,GAAK,SAAUC,CAAM,EAChE,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAU,MAAM,CAAED,IAAK,CACzC,IAAIE,EAASD,SAAS,CAACD,EAAE,CACzB,IAAK,IAAIG,KAAOD,EACVJ,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACI,EAAQC,IAC/CJ,CAAAA,CAAM,CAACI,EAAI,CAAGD,CAAM,CAACC,EAAI,AAAD,CAG9B,CACA,OAAOJ,CACT,GACgB,KAAK,CAAC,IAAI,CAAEE,UAC9B,iJAiBEN,CARSA,EAoBRA,GAAWA,CAAAA,EAAS,CAAC,IAZf,GAAM,CAAG,MAMhBA,EAAO,IAAO,CAAG,OAKjBA,EAAO,OAAU,CAAG,UAEtB,IAAMS,EAAoB,WAgH1B,SAASC,EAAqBC,CAAO,SAC/BA,AAAY,KAAK,IAAjBA,GACFA,CAAAA,EAAU,CAAC,GAmBNC,EAjBP,SAA+BC,CAAM,CAAEC,CAAa,EAClD,GAAI,CACFC,SAAAA,CAAQ,CACRC,OAAAA,CAAM,CACNC,KAAAA,CAAI,CACL,CAAGJ,EAAO,QAAQ,CACnB,OAAOK,EAAe,GAAI,CACxBH,SAAAA,EACAC,OAAAA,EACAC,KAAAA,CACF,EAEAH,EAAc,KAAK,EAAIA,EAAc,KAAK,CAAC,GAAG,EAAI,KAAMA,EAAc,KAAK,EAAIA,EAAc,KAAK,CAAC,GAAG,EAAI,UAC5G,EACA,SAA2BD,CAAM,CAAEM,CAAE,EACnC,MAAO,AAAc,UAAd,OAAOA,EAAkBA,EAAKC,EAAWD,EAClD,EACoE,KAAMR,EAC5E,CASA,SAASU,EAAkBV,CAAO,SAC5BA,AAAY,KAAK,IAAjBA,GACFA,CAAAA,EAAU,CAAC,GAsCNC,EApCP,SAA4BC,CAAM,CAAEC,CAAa,EAC/C,GAAI,CACFC,SAAAA,EAAW,GAAG,CACdC,OAAAA,EAAS,EAAE,CACXC,KAAAA,EAAO,EAAE,CACV,CAAGK,EAAUT,EAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAU1C,OAHI,AAACE,EAAS,UAAU,CAAC,MAASA,EAAS,UAAU,CAAC,MACpDA,CAAAA,EAAW,IAAMA,CAAO,EAEnBG,EAAe,GAAI,CACxBH,SAAAA,EACAC,OAAAA,EACAC,KAAAA,CACF,EAEAH,EAAc,KAAK,EAAIA,EAAc,KAAK,CAAC,GAAG,EAAI,KAAMA,EAAc,KAAK,EAAIA,EAAc,KAAK,CAAC,GAAG,EAAI,UAC5G,EACA,SAAwBD,CAAM,CAAEM,CAAE,EAChC,IAAII,EAAOV,EAAO,QAAQ,CAAC,aAAa,CAAC,QACrCW,EAAO,GACX,GAAID,GAAQA,EAAK,YAAY,CAAC,QAAS,CACrC,IAAIE,EAAMZ,EAAO,QAAQ,CAAC,IAAI,CAC1Ba,EAAYD,EAAI,OAAO,CAAC,KAC5BD,EAAOE,AAAc,KAAdA,EAAmBD,EAAMA,EAAI,KAAK,CAAC,EAAGC,EAC/C,CACA,OAAOF,EAAO,IAAO,CAAc,UAAd,OAAOL,EAAkBA,EAAKC,EAAWD,EAAE,CAClE,EACA,SAA8BQ,CAAQ,CAAER,CAAE,EACxCS,EAAQD,AAAgC,MAAhCA,EAAS,QAAQ,CAAC,MAAM,CAAC,GAAY,6DAA+DE,KAAK,SAAS,CAACV,GAAM,IACnI,EACoFR,EACtF,CACA,SAASmB,EAAUC,CAAK,CAAEC,CAAO,EAC/B,GAAID,AAAU,KAAVA,GAAqC,MAAlBA,EACrB,MAAM,AAAIE,MAAMD,EAEpB,CACA,SAASJ,EAAQM,CAAI,CAAEF,CAAO,EAC5B,GAAI,CAACE,EAAM,CAEL,AAAmB,aAAnB,OAAOC,SAAyBA,QAAQ,IAAI,CAACH,GACjD,GAAI,CAMF,MAAM,AAAIC,MAAMD,EAElB,CAAE,MAAOI,EAAG,CAAC,CACf,CACF,CAOA,SAASC,EAAgBV,CAAQ,CAAEW,CAAK,EACtC,MAAO,CACL,IAAKX,EAAS,KAAK,CACnB,IAAKA,EAAS,GAAG,CACjB,IAAKW,CACP,CACF,CAIA,SAASpB,EAAeqB,CAAO,CAAEpB,CAAE,CAAEqB,CAAK,CAAEhC,CAAG,EAgB7C,OAfIgC,AAAU,KAAK,IAAfA,GACFA,CAAAA,EAAQ,IAAG,EAEEtC,EAAS,CACtB,SAAU,AAAmB,UAAnB,OAAOqC,EAAuBA,EAAUA,EAAQ,QAAQ,CAClE,OAAQ,GACR,KAAM,EACR,EAAG,AAAc,UAAd,OAAOpB,EAAkBG,EAAUH,GAAMA,EAAI,CAC9CqB,MAAAA,EAKA,IAAKrB,GAAMA,EAAG,GAAG,EAAIX,GA7BhBiC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,EAAG,EA8B5C,EAEF,CAIA,SAASrB,EAAWsB,CAAI,EACtB,GAAI,CACF3B,SAAAA,EAAW,GAAG,CACdC,OAAAA,EAAS,EAAE,CACXC,KAAAA,EAAO,EAAE,CACV,CAAGyB,EAGJ,OAFI1B,GAAUA,AAAW,MAAXA,GAAgBD,CAAAA,GAAYC,AAAqB,MAArBA,EAAO,MAAM,CAAC,GAAaA,EAAS,IAAMA,CAAK,EACrFC,GAAQA,AAAS,MAATA,GAAcF,CAAAA,GAAYE,AAAmB,MAAnBA,EAAK,MAAM,CAAC,GAAaA,EAAO,IAAMA,CAAG,EACxEF,CACT,CAIA,SAASO,EAAUqB,CAAI,EACrB,IAAIC,EAAa,CAAC,EAClB,GAAID,EAAM,CACR,IAAIjB,EAAYiB,EAAK,OAAO,CAAC,KACzBjB,GAAa,IACfkB,EAAW,IAAI,CAAGD,EAAK,MAAM,CAACjB,GAC9BiB,EAAOA,EAAK,MAAM,CAAC,EAAGjB,IAExB,IAAImB,EAAcF,EAAK,OAAO,CAAC,KAC3BE,GAAe,IACjBD,EAAW,MAAM,CAAGD,EAAK,MAAM,CAACE,GAChCF,EAAOA,EAAK,MAAM,CAAC,EAAGE,IAEpBF,GACFC,CAAAA,EAAW,QAAQ,CAAGD,CAAG,CAE7B,CACA,OAAOC,CACT,CACA,SAAShC,EAAmBkC,CAAW,CAAEC,CAAU,CAAEC,CAAgB,CAAErC,CAAO,EACxEA,AAAY,KAAK,IAAjBA,GACFA,CAAAA,EAAU,CAAC,GAEb,GAAI,CACFE,OAAAA,EAASoC,SAAS,WAAW,CAC7BC,SAAAA,EAAW,EAAK,CACjB,CAAGvC,EACAG,EAAgBD,EAAO,OAAO,CAC9BsC,EAASnD,EAAO,GAAG,CACnBoD,EAAW,KACXd,EAAQe,IAUZ,SAASA,IAIP,MAAOb,AAHK1B,CAAAA,EAAc,KAAK,EAAI,CACjC,IAAK,IACP,GACa,GAAG,AAClB,CACA,SAASwC,IACPH,EAASnD,EAAO,GAAG,CACnB,IAAIuD,EAAYF,IACZG,EAAQD,AAAa,MAAbA,EAAoB,KAAOA,EAAYjB,EACnDA,EAAQiB,EACJH,GACFA,EAAS,CACPD,OAAAA,EACA,SAAUM,EAAQ,QAAQ,CAC1BD,MAAAA,CACF,EAEJ,CA+CA,SAASE,EAAUvC,CAAE,EAInB,IAAII,EAAOV,AAA2B,SAA3BA,EAAO,QAAQ,CAAC,MAAM,CAAcA,EAAO,QAAQ,CAAC,MAAM,CAAGA,EAAO,QAAQ,CAAC,IAAI,CACxFW,EAAO,AAAc,UAAd,OAAOL,EAAkBA,EAAKC,EAAWD,GAMpD,OADAW,EAAUP,EAAM,sEADhBC,CAAAA,EAAOA,EAAK,OAAO,CAAC,KAAM,MAAK,GAExB,IAAImC,IAAInC,EAAMD,EACvB,CAnFa,MAATe,IACFA,EAAQ,EACRxB,EAAc,YAAY,CAACZ,EAAS,CAAC,EAAGY,EAAc,KAAK,CAAE,CAC3D,IAAKwB,CACP,GAAI,KAgFN,IAAImB,EAAU,CACZ,IAAI,QAAS,CACX,OAAON,CACT,EACA,IAAI,UAAW,CACb,OAAOL,EAAYjC,EAAQC,EAC7B,EACA,OAAO8C,CAAE,EACP,GAAIR,EACF,MAAM,AAAInB,MAAM,8CAIlB,OAFApB,EAAO,gBAAgB,CAACJ,EAAmB6C,GAC3CF,EAAWQ,EACJ,KACL/C,EAAO,mBAAmB,CAACJ,EAAmB6C,GAC9CF,EAAW,IACb,CACF,EACA,WAAWjC,GACF4B,EAAWlC,EAAQM,GAE5BuC,UAAAA,EACA,eAAevC,CAAE,EAEf,IAAIM,EAAMiC,EAAUvC,GACpB,MAAO,CACL,SAAUM,EAAI,QAAQ,CACtB,OAAQA,EAAI,MAAM,CAClB,KAAMA,EAAI,IAAI,AAChB,CACF,EACAoC,KA1FF,SAAc1C,CAAE,CAAEqB,CAAK,EACrBW,EAASnD,EAAO,IAAI,CACpB,IAAI2B,EAAWT,EAAeuC,EAAQ,QAAQ,CAAEtC,EAAIqB,EAChDQ,CAAAA,GAAkBA,EAAiBrB,EAAUR,GAEjD,IAAI2C,EAAezB,EAAgBV,EADnCW,EAAQe,IAAa,GAEjB5B,EAAMgC,EAAQ,UAAU,CAAC9B,GAE7B,GAAI,CACFb,EAAc,SAAS,CAACgD,EAAc,GAAIrC,EAC5C,CAAE,MAAOsC,EAAO,CAKd,GAAIA,aAAiBC,cAAgBD,AAAe,mBAAfA,EAAM,IAAI,CAC7C,MAAMA,EAIRlD,EAAO,QAAQ,CAAC,MAAM,CAACY,EACzB,CACIyB,GAAYE,GACdA,EAAS,CACPD,OAAAA,EACA,SAAUM,EAAQ,QAAQ,CAC1B,MAAO,CACT,EAEJ,EA8DEQ,QA7DF,SAAiB9C,CAAE,CAAEqB,CAAK,EACxBW,EAASnD,EAAO,OAAO,CACvB,IAAI2B,EAAWT,EAAeuC,EAAQ,QAAQ,CAAEtC,EAAIqB,EAChDQ,CAAAA,GAAkBA,EAAiBrB,EAAUR,GAEjD,IAAI2C,EAAezB,EAAgBV,EADnCW,EAAQe,KAEJ5B,EAAMgC,EAAQ,UAAU,CAAC9B,GAC7Bb,EAAc,YAAY,CAACgD,EAAc,GAAIrC,GACzCyB,GAAYE,GACdA,EAAS,CACPD,OAAAA,EACA,SAAUM,EAAQ,QAAQ,CAC1B,MAAO,CACT,EAEJ,EA+CE,GAAGS,GACMpD,EAAc,EAAE,CAACoD,EAE5B,EACA,OAAOT,CACT,CAKExD,CADSA,EAKRA,GAAeA,CAAAA,EAAa,CAAC,IAJnB,IAAO,CAAG,OACrBA,EAAW,QAAW,CAAG,WACzBA,EAAW,QAAW,CAAG,WACzBA,EAAW,KAAQ,CAAG,QAExB,IAAMkE,EAAqB,IAAIC,IAAI,CAAC,OAAQ,gBAAiB,OAAQ,KAAM,QAAS,WAAW,EAM/F,SAASC,EAA0BC,CAAM,CAAEC,CAAkB,CAAEC,CAAU,CAAEC,CAAQ,EAOjF,OANID,AAAe,KAAK,IAApBA,GACFA,CAAAA,EAAa,EAAE,AAAD,EAEZC,AAAa,KAAK,IAAlBA,GACFA,CAAAA,EAAW,CAAC,GAEPH,EAAO,GAAG,CAAC,CAACI,EAAOpC,KACxB,IAAIqC,EAAW,IAAIH,EAAYI,OAAOtC,GAAO,CACzCuC,EAAK,AAAoB,UAApB,OAAOH,EAAM,EAAE,CAAgBA,EAAM,EAAE,CAAGC,EAAS,IAAI,CAAC,KAGjE,GAFA7C,EAAU4C,AAAgB,KAAhBA,EAAM,KAAK,EAAa,CAACA,EAAM,QAAQ,CAAE,6CACnD5C,EAAU,CAAC2C,CAAQ,CAACI,EAAG,CAAE,qCAAwCA,EAAxC,qEAfpBH,AAAgB,KAAhBA,AAgBYA,EAhBN,KAAK,CAgBS,CACvB,IAAII,EAAa5E,EAAS,CAAC,EAAGwE,EAAOH,EAAmBG,GAAQ,CAC9DG,GAAAA,CACF,GAEA,OADAJ,CAAQ,CAACI,EAAG,CAAGC,EACRA,CACT,CAAO,CACL,IAAIC,EAAoB7E,EAAS,CAAC,EAAGwE,EAAOH,EAAmBG,GAAQ,CACrEG,GAAAA,EACA,SAAUG,KAAAA,CACZ,GAKA,OAJAP,CAAQ,CAACI,EAAG,CAAGE,EACXL,EAAM,QAAQ,EAChBK,CAAAA,EAAkB,QAAQ,CAAGV,EAA0BK,EAAM,QAAQ,CAAEH,EAAoBI,EAAUF,EAAQ,EAExGM,CACT,CACF,EACF,CAMA,SAASE,EAAYX,CAAM,CAAEY,CAAW,CAAEC,CAAQ,EAIhD,OAHIA,AAAa,KAAK,IAAlBA,GACFA,CAAAA,EAAW,GAAE,EAERC,EAAgBd,EAAQY,EAAaC,EAAU,GACxD,CACA,SAASC,EAAgBd,CAAM,CAAEY,CAAW,CAAEC,CAAQ,CAAEE,CAAY,EAElE,IAAItE,EAAWuE,EAAc3D,AADd,CAAuB,UAAvB,OAAOuD,EAA2B5D,EAAU4D,GAAeA,CAAU,EAC9C,QAAQ,EAAI,IAAKC,GACvD,GAAIpE,AAAY,MAAZA,EACF,OAAO,KAET,IAAIwE,EAAWC,AA6BjB,SAASA,EAAclB,CAAM,CAAEiB,CAAQ,CAAEE,CAAW,CAAEjB,CAAU,EAC1De,AAAa,KAAK,IAAlBA,GACFA,CAAAA,EAAW,EAAE,AAAD,EAEVE,AAAgB,KAAK,IAArBA,GACFA,CAAAA,EAAc,EAAE,AAAD,EAEbjB,AAAe,KAAK,IAApBA,GACFA,CAAAA,EAAa,EAAC,EAEhB,IAAIkB,EAAe,CAAChB,EAAOpC,EAAOqD,SAsGdhD,EAAML,EArGxB,IAsGEsD,EACAC,EAvGEC,EAAO,CACT,aAAcH,AAAiBX,KAAAA,IAAjBW,EAA6BjB,EAAM,IAAI,EAAI,GAAKiB,EAC9D,cAAejB,AAAwB,KAAxBA,EAAM,aAAa,CAClC,cAAepC,EACfoC,MAAAA,CACF,EACIoB,EAAK,YAAY,CAAC,UAAU,CAAC,OAC/BhE,EAAUgE,EAAK,YAAY,CAAC,UAAU,CAACtB,GAAa,wBAA2BsB,EAAK,YAAY,CAA5C,wBAAiFtB,EAAjF,4GACpDsB,EAAK,YAAY,CAAGA,EAAK,YAAY,CAAC,KAAK,CAACtB,EAAW,MAAM,GAE/D,IAAI7B,EAAOoD,EAAU,CAACvB,EAAYsB,EAAK,YAAY,CAAC,EAChDE,EAAaP,EAAY,MAAM,CAACK,EAIhCpB,CAAAA,EAAM,QAAQ,EAAIA,EAAM,QAAQ,CAAC,MAAM,CAAG,IAC5C5C,EAGA4C,AAAgB,KAAhBA,EAAM,KAAK,CAAW,4FAAqG/B,EAAO,MAClI6C,EAAcd,EAAM,QAAQ,CAAEa,EAAUS,EAAYrD,IAIlD+B,CAAAA,AAAc,MAAdA,EAAM,IAAI,EAAaA,EAAM,KAAK,AAAD,GAGrCa,EAAS,IAAI,CAAC,CACZ5C,KAAAA,EACA,KAAK,EAwEWA,EAxEIA,EAwEEL,EAxEIoC,EAAM,KAAK,CA0ErCmB,EAAeD,CADfA,EAAWjD,EAAK,KAAK,CAAC,MACE,MAAM,CAC9BiD,EAAS,IAAI,CAACK,IAChBJ,CAAAA,GANiB,EAMU,EAEzBvD,GACFuD,CAAAA,GAZoB,CAYU,EAEzBD,EAAS,MAAM,CAACM,GAAK,CAACD,EAAQC,IAAI,MAAM,CAAC,CAACC,EAAOC,IAAYD,EAASE,CAAAA,EAAQ,IAAI,CAACD,GAfhE,EAeiGA,AAAY,KAAZA,EAbnG,EACC,EAYwJ,EAAIP,IAhFjLG,WAAAA,CACF,EACF,EAYA,OAXA1B,EAAO,OAAO,CAAC,CAACI,EAAOpC,KACrB,IAAIgE,EAEJ,GAAI5B,AAAe,KAAfA,EAAM,IAAI,EAAa,AAA8B,MAA7B4B,CAAAA,EAAc5B,EAAM,IAAI,AAAD,GAAc4B,EAAY,QAAQ,CAAC,KAGpF,IAAK,IAAIC,KAAYC,AAqB3B,SAASA,EAAwB7D,CAAI,EACnC,IAAIiD,EAAWjD,EAAK,KAAK,CAAC,KAC1B,GAAIiD,AAAoB,IAApBA,EAAS,MAAM,CAAQ,MAAO,EAAE,CACpC,GAAI,CAACa,EAAO,GAAGC,EAAK,CAAGd,EAEnBe,EAAaF,EAAM,QAAQ,CAAC,KAE5BG,EAAWH,EAAM,OAAO,CAAC,MAAO,IACpC,GAAIC,AAAgB,IAAhBA,EAAK,MAAM,CAGb,OAAOC,EAAa,CAACC,EAAU,GAAG,CAAG,CAACA,EAAS,CAEjD,IAAIC,EAAeL,EAAwBE,EAAK,IAAI,CAAC,MACjDI,EAAS,EAAE,CAcf,OANAA,EAAO,IAAI,IAAID,EAAa,GAAG,CAACE,GAAWA,AAAY,KAAZA,EAAiBH,EAAW,CAACA,EAAUG,EAAQ,CAAC,IAAI,CAAC,OAE5FJ,GACFG,EAAO,IAAI,IAAID,GAGVC,EAAO,GAAG,CAACP,GAAY5D,EAAK,UAAU,CAAC,MAAQ4D,AAAa,KAAbA,EAAkB,IAAMA,EAChF,EAlDmD7B,EAAM,IAAI,EACrDgB,EAAahB,EAAOpC,EAAOiE,QAH7Bb,EAAahB,EAAOpC,EAMxB,GACOiD,CACT,EArF+BjB,GAmI7BiB,AAlIkBA,EAkIT,IAAI,CAAC,CAACyB,EAAGC,SAqBID,EAAGC,SArBDD,EAAE,KAAK,GAAKC,EAAE,KAAK,CAAGA,EAAE,KAAK,CAAGD,EAAE,KAAK,EAqBzCA,EApBLA,EAAE,UAAU,CAAC,GAAG,CAAClB,GAAQA,EAAK,aAAa,EAoBnCmB,EApBsCA,EAAE,UAAU,CAAC,GAAG,CAACnB,GAAQA,EAAK,aAAa,EAsBnGoB,AADQF,EAAE,MAAM,GAAKC,EAAE,MAAM,EAAID,EAAE,KAAK,CAAC,EAAG,IAAI,KAAK,CAAC,CAAC9C,EAAG7D,IAAM6D,IAAM+C,CAAC,CAAC5G,EAAE,EAMjF2G,CAAC,CAACA,EAAE,MAAM,CAAG,EAAE,CAAGC,CAAC,CAACA,EAAE,MAAM,CAAG,EAAE,CAGjC,KAhKA,IAAIE,EAAU,KACd,IAAK,IAAI9G,EAAI,EAAG8G,AAAW,MAAXA,GAAmB9G,EAAIkF,EAAS,MAAM,CAAE,EAAElF,EAAG,CAO3D,IAAI+G,EAAUC,AAoUlB,SAAoBtF,CAAK,EACvB,GAAI,CACF,OAAOA,EAAM,KAAK,CAAC,KAAK,GAAG,CAACuF,GAAKC,mBAAmBD,GAAG,OAAO,CAAC,MAAO,QAAQ,IAAI,CAAC,IACrF,CAAE,MAAOvD,EAAO,CAEd,OADAnC,EAAQ,GAAO,iBAAoBG,EAApB,oHAA8JgC,EAAQ,MAC9KhC,CACT,CACF,EA3U6BhB,GACzBoG,EAAUK,AAyJd,SAA0BC,CAAM,CAAE1G,CAAQ,CAAEsE,CAAY,EAClDA,AAAiB,KAAK,IAAtBA,GACFA,CAAAA,EAAe,EAAI,EAErB,GAAI,CACFW,WAAAA,CAAU,CACX,CAAGyB,EACAC,EAAgB,CAAC,EACjBC,EAAkB,IAClBR,EAAU,EAAE,CAChB,IAAK,IAAI9G,EAAI,EAAGA,EAAI2F,EAAW,MAAM,CAAE,EAAE3F,EAAG,CAC1C,IAAIyF,EAAOE,CAAU,CAAC3F,EAAE,CACpBuH,EAAMvH,IAAM2F,EAAW,MAAM,CAAG,EAChC6B,EAAoBF,AAAoB,MAApBA,EAA0B5G,EAAWA,EAAS,KAAK,CAAC4G,EAAgB,MAAM,GAAK,IACnGG,EAAQC,EAAU,CACpB,KAAMjC,EAAK,YAAY,CACvB,cAAeA,EAAK,aAAa,CACjC8B,IAAAA,CACF,EAAGC,GACCnD,EAAQoB,EAAK,KAAK,CAQtB,GAPI,CAACgC,GAASF,GAAOvC,GAAgB,CAACW,CAAU,CAACA,EAAW,MAAM,CAAG,EAAE,CAAC,KAAK,CAAC,KAAK,EACjF8B,CAAAA,EAAQC,EAAU,CAChB,KAAMjC,EAAK,YAAY,CACvB,cAAeA,EAAK,aAAa,CACjC,IAAK,EACP,EAAG+B,EAAiB,EAElB,CAACC,EACH,OAAO,KAET3H,OAAO,MAAM,CAACuH,EAAeI,EAAM,MAAM,EACzCX,EAAQ,IAAI,CAAC,CAEX,OAAQO,EACR,SAAU3B,EAAU,CAAC4B,EAAiBG,EAAM,QAAQ,CAAC,EACrD,aAAcE,EAAkBjC,EAAU,CAAC4B,EAAiBG,EAAM,YAAY,CAAC,GAC/EpD,MAAAA,CACF,GACIoD,AAAuB,MAAvBA,EAAM,YAAY,EACpBH,CAAAA,EAAkB5B,EAAU,CAAC4B,EAAiBG,EAAM,YAAY,CAAC,EAErE,CACA,OAAOX,CACT,EApM+B5B,CAAQ,CAAClF,EAAE,CAAE+G,EAAS/B,EACnD,CACA,OAAO8B,CACT,CACA,SAASc,EAA2BH,CAAK,CAAEI,CAAU,EACnD,GAAI,CACFxD,MAAAA,CAAK,CACL3D,SAAAA,CAAQ,CACRoH,OAAAA,CAAM,CACP,CAAGL,EACJ,MAAO,CACL,GAAIpD,EAAM,EAAE,CACZ3D,SAAAA,EACAoH,OAAAA,EACA,KAAMD,CAAU,CAACxD,EAAM,EAAE,CAAC,CAC1B,OAAQA,EAAM,MAAM,AACtB,CACF,CA0GA,IAAM2B,EAAU,YAMVJ,EAAUC,GAAKA,AAAM,MAANA,EAiHrB,SAAS6B,EAAUK,CAAO,CAAErH,CAAQ,MAwCf4B,EAAM0F,EAAeT,MAQpCO,EACAG,CAhDA,AAAmB,WAAnB,OAAOF,GACTA,CAAAA,EAAU,CACR,KAAMA,EACN,cAAe,GACf,IAAK,EACP,GAEF,GAAI,CAACG,EAASC,EAAe,EAgCV7F,EAhCyByF,EAAQ,IAAI,CAgC/BC,EAhCiCD,EAAQ,aAAa,CAgCvCR,EAhCyCQ,EAAQ,GAAG,CAiCxFC,AAAkB,KAAK,IAAvBA,GACFA,CAAAA,EAAgB,EAAI,EAElBT,AAAQ,KAAK,IAAbA,GACFA,CAAAA,EAAM,EAAG,EAEXhG,EAAQe,AAAS,MAATA,GAAgB,CAACA,EAAK,QAAQ,CAAC,MAAQA,EAAK,QAAQ,CAAC,MAAO,eAAkBA,EAAlB,oCAAuEA,EAAK,OAAO,CAAC,MAAO,MAA3F,qIAAwPA,EAAK,OAAO,CAAC,MAAO,MAAQ,MACpVwF,EAAS,EAAE,CACXG,EAAe,IAAM3F,EAAK,OAAO,CAAC,UAAW,IAChD,OAAO,CAAC,OAAQ,KAChB,OAAO,CAAC,qBAAsB,QAC9B,OAAO,CAAC,oBAAqB,CAAC8F,EAAGC,EAAW/B,KAC3CwB,EAAO,IAAI,CAAC,CACVO,UAAAA,EACA,WAAY/B,AAAc,MAAdA,CACd,GACOA,EAAa,eAAiB,eAEnChE,EAAK,QAAQ,CAAC,MAChBwF,EAAO,IAAI,CAAC,CACV,UAAW,GACb,GACAG,GAAgB3F,AAAS,MAATA,GAAgBA,AAAS,OAATA,EAAgB,QAC9C,qBACOiF,EAETU,GAAgB,QACE,KAAT3F,GAAeA,AAAS,MAATA,GAQxB2F,CAAAA,GAAgB,eAAc,EAGzB,CADO,IAAIK,OAAOL,EAAcD,EAAgBrD,KAAAA,EAAY,KAClDmD,EAAO,EAtEpBL,EAAQ/G,EAAS,KAAK,CAACwH,GAC3B,GAAI,CAACT,EAAO,OAAO,KACnB,IAAIH,EAAkBG,CAAK,CAAC,EAAE,CAC1Bc,EAAejB,EAAgB,OAAO,CAAC,UAAW,MAClDkB,EAAgBf,EAAM,KAAK,CAAC,GAoBhC,MAAO,CACLK,OApBWK,EAAe,MAAM,CAAC,CAACM,EAAMpG,EAAMJ,KAC9C,GAAI,CACFoG,UAAAA,CAAS,CACT/B,WAAAA,CAAU,CACX,CAAGjE,EAGJ,GAAIgG,AAAc,MAAdA,EAAmB,CACrB,IAAIK,EAAaF,CAAa,CAACvG,EAAM,EAAI,GACzCsG,EAAejB,EAAgB,KAAK,CAAC,EAAGA,EAAgB,MAAM,CAAGoB,EAAW,MAAM,EAAE,OAAO,CAAC,UAAW,KACzG,CACA,IAAMhH,EAAQ8G,CAAa,CAACvG,EAAM,CAMlC,OALIqE,GAAc,CAAC5E,EACjB+G,CAAI,CAACJ,EAAU,CAAG1D,KAAAA,EAElB8D,CAAI,CAACJ,EAAU,CAAG,AAAC3G,CAAAA,GAAS,EAAC,EAAG,OAAO,CAAC,OAAQ,KAE3C+G,CACT,EAAG,CAAC,GAGF,SAAUnB,EACViB,aAAAA,EACAR,QAAAA,CACF,CACF,CAqDA,SAAS9C,EAAcvE,CAAQ,CAAEoE,CAAQ,EACvC,GAAIA,AAAa,MAAbA,EAAkB,OAAOpE,EAC7B,GAAI,CAACA,EAAS,WAAW,GAAG,UAAU,CAACoE,EAAS,WAAW,IACzD,OAAO,KAIT,IAAI6D,EAAa7D,EAAS,QAAQ,CAAC,KAAOA,EAAS,MAAM,CAAG,EAAIA,EAAS,MAAM,CAC3E8D,EAAWlI,EAAS,MAAM,CAACiI,UAC/B,AAAIC,GAAYA,AAAa,MAAbA,EAEP,KAEFlI,EAAS,KAAK,CAACiI,IAAe,GACvC,CAmCA,SAASE,EAAoBC,CAAI,CAAEC,CAAK,CAAEC,CAAI,CAAE1G,CAAI,EAClD,MAAO,qBAAuBwG,EAAO,uCAA0C,QAASC,EAAQ,WAAU,EAAIvH,KAAK,SAAS,CAACc,GAAtH,yCAAgL0G,EAAhL,2HACT,CAwBA,SAASC,EAA2BnC,CAAO,EACzC,OAAOA,EAAQ,MAAM,CAAC,CAACW,EAAOxF,IAAUA,AAAU,IAAVA,GAAewF,EAAM,KAAK,CAAC,IAAI,EAAIA,EAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAG,EACvG,CAGA,SAASyB,EAAoBpC,CAAO,CAAEqC,CAAoB,EACxD,IAAIC,EAAcH,EAA2BnC,UAI7C,AAAIqC,EACKC,EAAY,GAAG,CAAC,CAAC3B,EAAO4B,IAAQA,IAAQD,EAAY,MAAM,CAAG,EAAI3B,EAAM,QAAQ,CAAGA,EAAM,YAAY,EAEtG2B,EAAY,GAAG,CAAC3B,GAASA,EAAM,YAAY,CACpD,CAIA,SAAS6B,EAAUC,CAAK,CAAEC,CAAc,CAAEC,CAAgB,CAAEC,CAAc,MAIpE5I,EAWA6I,CAdAD,AAAmB,MAAK,IAAxBA,GACFA,CAAAA,EAAiB,EAAI,EAGnB,AAAiB,UAAjB,OAAOH,EACTzI,EAAKG,EAAUsI,IAGf9H,EAAU,CAACX,AADXA,CAAAA,EAAKjB,EAAS,CAAC,EAAG0J,EAAK,EACT,QAAQ,EAAI,CAACzI,EAAG,QAAQ,CAAC,QAAQ,CAAC,KAAM+H,EAAoB,IAAK,WAAY,SAAU/H,IACrGW,EAAU,CAACX,EAAG,QAAQ,EAAI,CAACA,EAAG,QAAQ,CAAC,QAAQ,CAAC,KAAM+H,EAAoB,IAAK,WAAY,OAAQ/H,IACnGW,EAAU,CAACX,EAAG,MAAM,EAAI,CAACA,EAAG,MAAM,CAAC,QAAQ,CAAC,KAAM+H,EAAoB,IAAK,SAAU,OAAQ/H,KAE/F,IAAI8I,EAAcL,AAAU,KAAVA,GAAgBzI,AAAgB,KAAhBA,EAAG,QAAQ,CACzC+I,EAAaD,EAAc,IAAM9I,EAAG,QAAQ,CAWhD,GAAI+I,AAAc,MAAdA,EACFF,EAAOF,MACF,CACL,IAAIK,EAAqBN,EAAe,MAAM,CAAG,EAKjD,GAAI,CAACE,GAAkBG,EAAW,UAAU,CAAC,MAAO,CAClD,IAAIE,EAAaF,EAAW,KAAK,CAAC,KAClC,KAAOE,AAAkB,OAAlBA,CAAU,CAAC,EAAE,EAClBA,EAAW,KAAK,GAChBD,GAAsB,CAExBhJ,CAAAA,EAAG,QAAQ,CAAGiJ,EAAW,IAAI,CAAC,IAChC,CACAJ,EAAOG,GAAsB,EAAIN,CAAc,CAACM,EAAmB,CAAG,GACxE,CACA,IAAIxH,EAAO0H,AApHb,SAAqBlJ,CAAE,CAAEmJ,CAAY,MAgBZ3E,MACnBC,CAhBA0E,AAAiB,MAAK,IAAtBA,GACFA,CAAAA,EAAe,GAAE,EAEnB,GAAI,CACF,SAAUJ,CAAU,CACpBlJ,OAAAA,EAAS,EAAE,CACXC,KAAAA,EAAO,EAAE,CACV,CAAG,AAAc,UAAd,OAAOE,EAAkBG,EAAUH,GAAMA,EAE7C,MAAO,CACLJ,SAFamJ,EAAaA,EAAW,UAAU,CAAC,KAAOA,GAOlCvE,EAP+DuE,EAQlFtE,EAAW0E,AARmFA,EAQtE,OAAO,CAAC,OAAQ,IAAI,KAAK,CAAC,KAEtDC,AADuB5E,EAAa,KAAK,CAAC,KACzB,OAAO,CAACS,IACnBA,AAAY,OAAZA,EAEER,EAAS,MAAM,CAAG,GAAGA,EAAS,GAAG,GAC5BQ,AAAY,MAAZA,GACTR,EAAS,IAAI,CAACQ,EAElB,GACOR,EAAS,MAAM,CAAG,EAAIA,EAAS,IAAI,CAAC,KAAO,KAlBgE0E,EAGhH,OAAQE,EAAgBxJ,GACxB,KAAMyJ,EAAcxJ,EACtB,CACF,EAqGyBE,EAAI6I,GAEvBU,EAA2BR,GAAcA,AAAe,MAAfA,GAAsBA,EAAW,QAAQ,CAAC,KAEnFS,EAA0B,AAACV,CAAAA,GAAeC,AAAe,MAAfA,CAAiB,GAAMJ,EAAiB,QAAQ,CAAC,KAI/F,MAHI,CAACnH,EAAK,QAAQ,CAAC,QAAQ,CAAC,MAAS+H,CAAAA,GAA4BC,CAAsB,GACrFhI,CAAAA,EAAK,QAAQ,EAAI,GAAE,EAEdA,CACT,CAWA,IAAMoD,EAAY6E,GAASA,EAAM,IAAI,CAAC,KAAK,OAAO,CAAC,SAAU,KAIvD5C,EAAoBjH,GAAYA,EAAS,OAAO,CAAC,OAAQ,IAAI,OAAO,CAAC,OAAQ,KAI7EyJ,EAAkBxJ,GAAU,AAACA,GAAUA,AAAW,MAAXA,EAAsBA,EAAO,UAAU,CAAC,KAAOA,EAAS,IAAMA,EAA7C,GAIxDyJ,EAAgBxJ,GAAQ,AAACA,GAAQA,AAAS,MAATA,EAAoBA,EAAK,UAAU,CAAC,KAAOA,EAAO,IAAMA,EAAzC,EAoCtD,OAAM4J,UAA6B5I,MAAO,CAoM1C,MAAM6I,EACJ,YAAYC,CAAM,CAAEC,CAAU,CAAEC,CAAI,CAAEC,CAAQ,CAAE,CAC1CA,AAAa,KAAK,IAAlBA,GACFA,CAAAA,EAAW,EAAI,EAEjB,IAAI,CAAC,MAAM,CAAGH,EACd,IAAI,CAAC,UAAU,CAAGC,GAAc,GAChC,IAAI,CAAC,QAAQ,CAAGE,EACZD,aAAgBhJ,OAClB,IAAI,CAAC,IAAI,CAAGgJ,EAAK,QAAQ,GACzB,IAAI,CAAC,KAAK,CAAGA,GAEb,IAAI,CAAC,IAAI,CAAGA,CAEhB,CACF,CAKA,SAASE,EAAqBpH,CAAK,EACjC,OAAOA,AAAS,MAATA,GAAiB,AAAwB,UAAxB,OAAOA,EAAM,MAAM,EAAiB,AAA4B,UAA5B,OAAOA,EAAM,UAAU,EAAiB,AAA0B,WAA1B,OAAOA,EAAM,QAAQ,EAAkB,SAAUA,CACvJ,CAEA,IAAMqH,EAA0B,CAAC,OAAQ,MAAO,QAAS,SAAS,CAC5DC,EAAuB,IAAIjH,IAAIgH,GAE/BE,EAAsB,IAAIlH,IADD,CAAC,SAAUgH,EAAwB,EAE5DG,EAAsB,IAAInH,IAAI,CAAC,IAAK,IAAK,IAAK,IAAK,IAAI,EACvDoH,EAAoC,IAAIpH,IAAI,CAAC,IAAK,IAAI,EACtDqH,EAAkB,CACtB,MAAO,OACP,SAAUzG,KAAAA,EACV,WAAYA,KAAAA,EACZ,WAAYA,KAAAA,EACZ,YAAaA,KAAAA,EACb,SAAUA,KAAAA,EACV,KAAMA,KAAAA,EACN,KAAMA,KAAAA,CACR,EACM0G,EAAe,CACnB,MAAO,OACP,KAAM1G,KAAAA,EACN,WAAYA,KAAAA,EACZ,WAAYA,KAAAA,EACZ,YAAaA,KAAAA,EACb,SAAUA,KAAAA,EACV,KAAMA,KAAAA,EACN,KAAMA,KAAAA,CACR,EACM2G,EAAe,CACnB,MAAO,YACP,QAAS3G,KAAAA,EACT,MAAOA,KAAAA,EACP,SAAUA,KAAAA,CACZ,EACM4G,EAAqB,gCACrBC,EAA4BnH,GAAU,EAC1C,iBAAkBoH,CAAAA,CAAQpH,EAAM,gBAAgB,AAClD,GACMqH,EAA0B,2BAQhC,SAASC,EAAaC,CAAI,EACxB,IAII1H,EAgBA2H,EA2DAC,EAsCAC,EAwBAC,EAkDAC,EA/LEC,EAAeN,EAAK,MAAM,CAAGA,EAAK,MAAM,CAAG,AAAkB,aAAlB,OAAOpL,OAAyBA,OAASmE,KAAAA,EACpFwH,EAAY,AAAwB,SAAjBD,GAAgC,AAAiC,SAA1BA,EAAa,QAAQ,EAAoB,AAA+C,SAAxCA,EAAa,QAAQ,CAAC,aAAa,CAC7IE,EAAW,CAACD,EAGlB,GAFA1K,EAAUmK,EAAK,MAAM,CAAC,MAAM,CAAG,EAAG,6DAE9BA,EAAK,kBAAkB,CACzB1H,EAAqB0H,EAAK,kBAAkB,MACvC,GAAIA,EAAK,mBAAmB,CAAE,CAEnC,IAAIS,EAAsBT,EAAK,mBAAmB,CAClD1H,EAAqBG,GAAU,EAC7B,iBAAkBgI,EAAoBhI,EACxC,EACF,MACEH,EAAqBsH,EAGvB,IAAIpH,EAAW,CAAC,EAEZkI,EAAatI,EAA0B4H,EAAK,MAAM,CAAE1H,EAAoBS,KAAAA,EAAWP,GAEnFU,EAAW8G,EAAK,QAAQ,EAAI,IAC5BW,EAAmBX,EAAK,YAAY,EAAIY,GACxCC,EAA8Bb,EAAK,uBAAuB,CAE1Dc,EAAS7M,EAAS,CACpB,kBAAmB,GACnB,uBAAwB,GACxB,oBAAqB,GACrB,mBAAoB,GACpB,qBAAsB,GACtB,+BAAgC,EAClC,EAAG+L,EAAK,MAAM,EAEVe,EAAkB,KAElBC,EAAc,IAAI7I,IAElB8I,EAAuB,KAEvBC,EAA0B,KAE1BC,EAAoB,KAOpBC,EAAwBpB,AAAsB,MAAtBA,EAAK,aAAa,CAC1CqB,EAAiBrI,EAAY0H,EAAYV,EAAK,OAAO,CAAC,QAAQ,CAAE9G,GAChEoI,EAAgB,KACpB,GAAID,AAAkB,MAAlBA,GAA0B,CAACR,EAA6B,CAG1D,IAAI/I,EAAQyJ,GAAuB,IAAK,CACtC,SAAUvB,EAAK,OAAO,CAAC,QAAQ,CAAC,QAAQ,AAC1C,GACI,CACF9E,QAAAA,CAAO,CACPzC,MAAAA,CAAK,CACN,CAAG+I,GAAuBd,GAC3BW,EAAiBnG,EACjBoG,EAAgB,CACd,CAAC7I,EAAM,EAAE,CAAC,CAAEX,CACd,CACF,CAcA,GAPIuJ,GAAkB,CAACrB,EAAK,aAAa,EAEnCyB,AADWC,GAAcL,EAAgBX,EAAYV,EAAK,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAC1E,MAAM,EACjBqB,CAAAA,EAAiB,IAAG,EAInBA,EAYE,GAAIA,EAAe,IAAI,CAACM,GAAKA,EAAE,KAAK,CAAC,IAAI,EAG9CzB,EAAc,QACT,GAAKmB,EAAe,IAAI,CAACM,GAAKA,EAAE,KAAK,CAAC,MAAM,EAG5C,GAAIb,EAAO,mBAAmB,CAAE,CAIrC,IAAI7E,EAAa+D,EAAK,aAAa,CAAGA,EAAK,aAAa,CAAC,UAAU,CAAG,KAClE4B,EAAS5B,EAAK,aAAa,CAAGA,EAAK,aAAa,CAAC,MAAM,CAAG,KAE9D,GAAI4B,EAAQ,CACV,IAAInE,EAAM4D,EAAe,SAAS,CAACM,GAAKC,AAAuB7I,KAAAA,IAAvB6I,CAAM,CAACD,EAAE,KAAK,CAAC,EAAE,CAAC,EAC1DzB,EAAcmB,EAAe,KAAK,CAAC,EAAG5D,EAAM,GAAG,KAAK,CAACkE,GAAK,CAACE,GAA2BF,EAAE,KAAK,CAAE1F,EAAY2F,GAC7G,MACE1B,EAAcmB,EAAe,KAAK,CAACM,GAAK,CAACE,GAA2BF,EAAE,KAAK,CAAE1F,EAAY2F,GAE7F,MAGE1B,EAAcF,AAAsB,MAAtBA,EAAK,aAAa,MAjBhCE,EAAc,QAZd,GALAA,EAAc,GACdmB,EAAiB,EAAE,CAIfP,EAAO,mBAAmB,CAAE,CAC9B,IAAIW,EAAWC,GAAc,KAAMhB,EAAYV,EAAK,OAAO,CAAC,QAAQ,CAAC,QAAQ,CACzEyB,CAAAA,EAAS,MAAM,EAAIA,EAAS,OAAO,EACrCJ,CAAAA,EAAiBI,EAAS,OAAO,AAAD,CAEpC,CA2BF,IAAIlL,EAAQ,CACV,cAAeyJ,EAAK,OAAO,CAAC,MAAM,CAClC,SAAUA,EAAK,OAAO,CAAC,QAAQ,CAC/B,QAASqB,EACTnB,YAAAA,EACA,WAAYV,EAEZ,sBAAuBQ,AAAsB,MAAtBA,EAAK,aAAa,EAAmB,KAC5D,mBAAoB,GACpB,aAAc,OACd,WAAYA,EAAK,aAAa,EAAIA,EAAK,aAAa,CAAC,UAAU,EAAI,CAAC,EACpE,WAAYA,EAAK,aAAa,EAAIA,EAAK,aAAa,CAAC,UAAU,EAAI,KACnE,OAAQA,EAAK,aAAa,EAAIA,EAAK,aAAa,CAAC,MAAM,EAAIsB,EAC3D,SAAU,IAAIQ,IACd,SAAU,IAAIA,GAChB,EAGIC,EAAgBhO,EAAO,GAAG,CAG1BiO,EAA4B,GAI5BC,EAA+B,GAE/BC,EAAyB,IAAIJ,IAE7BK,EAA8B,KAG9BC,EAA8B,GAK9BC,GAAyB,GAGzBC,GAA0B,EAAE,CAG5BC,GAAwB,IAAIpK,IAE5BqK,GAAmB,IAAIV,IAEvBW,GAAqB,EAIrBC,GAA0B,GAE1BC,GAAiB,IAAIb,IAErBc,GAAmB,IAAIzK,IAEvB0K,GAAmB,IAAIf,IAEvBgB,GAAiB,IAAIhB,IAGrBiB,GAAkB,IAAI5K,IAKtB6K,GAAkB,IAAIlB,IAGtBmB,GAAmB,IAAInB,IAsG3B,SAASoB,GAAYC,CAAQ,CAAEC,CAAI,EAC7BA,AAAS,KAAK,IAAdA,GACFA,CAAAA,EAAO,CAAC,GAEV7M,EAAQtC,EAAS,CAAC,EAAGsC,EAAO4M,GAG5B,IAAIE,EAAoB,EAAE,CACtBC,EAAsB,EAAE,AACxBxC,CAAAA,EAAO,iBAAiB,EAC1BvK,EAAM,QAAQ,CAAC,OAAO,CAAC,CAACgN,EAAShP,KACT,SAAlBgP,EAAQ,KAAK,GACXR,GAAgB,GAAG,CAACxO,GAEtB+O,EAAoB,IAAI,CAAC/O,GAIzB8O,EAAkB,IAAI,CAAC9O,GAG7B,GAKF,IAAIyM,EAAY,CAAC,OAAO,CAACwC,GAAcA,EAAWjN,EAAO,CACvD,gBAAiB+M,EACjB,mBAAoBF,EAAK,kBAAkB,CAC3C,UAAWA,AAAmB,KAAnBA,EAAK,SAAS,AAC3B,IAEItC,EAAO,iBAAiB,GAC1BuC,EAAkB,OAAO,CAAC9O,GAAOgC,EAAM,QAAQ,CAAC,MAAM,CAAChC,IACvD+O,EAAoB,OAAO,CAAC/O,GAAOkP,GAAclP,IAErD,CAMA,SAASmP,GAAmBhO,CAAQ,CAAEyN,CAAQ,CAAEQ,CAAK,MAC/CC,EAAiBC,MAUjBC,EAqCAC,EA9CA,CACFC,UAAAA,CAAS,CACV,CAAGL,AAAU,KAAK,IAAfA,EAAmB,CAAC,EAAIA,EAMxBM,EAAiB1N,AAAoB,MAApBA,EAAM,UAAU,EAAYA,AAA+B,MAA/BA,EAAM,UAAU,CAAC,UAAU,EAAY2N,GAAiB3N,EAAM,UAAU,CAAC,UAAU,GAAKA,AAA2B,YAA3BA,EAAM,UAAU,CAAC,KAAK,EAAkB,AAAC,CAAsC,MAArCqN,CAAAA,EAAkBlO,EAAS,KAAK,AAAD,EAAa,KAAK,EAAIkO,EAAgB,WAAW,AAAD,IAAO,GAIrQE,EAFAX,EAAS,UAAU,CACjBjP,OAAO,IAAI,CAACiP,EAAS,UAAU,EAAE,MAAM,CAAG,EAC/BA,EAAS,UAAU,CAGnB,KAENc,EAEI1N,EAAM,UAAU,CAGhB,KAGf,IAAI0F,EAAakH,EAAS,UAAU,CAAGgB,GAAgB5N,EAAM,UAAU,CAAE4M,EAAS,UAAU,CAAEA,EAAS,OAAO,EAAI,EAAE,CAAEA,EAAS,MAAM,EAAI5M,EAAM,UAAU,CAGrJ6N,EAAW7N,EAAM,QAAQ,AACzB6N,CAAAA,EAAS,IAAI,CAAG,GAElBA,AADAA,CAAAA,EAAW,IAAItC,IAAIsC,EAAQ,EAClB,OAAO,CAAC,CAAC5H,EAAG6H,IAAMD,EAAS,GAAG,CAACC,EAAG3E,IAI7C,IAAI4E,EAAqBtC,AAA8B,KAA9BA,GAAsCzL,AAA+B,MAA/BA,EAAM,UAAU,CAAC,UAAU,EAAY2N,GAAiB3N,EAAM,UAAU,CAAC,UAAU,GAAK,AAAC,CAAuC,MAAtCsN,CAAAA,EAAmBnO,EAAS,KAAK,AAAD,EAAa,KAAK,EAAImO,EAAiB,WAAW,AAAD,IAAO,GAajP,GAXI5D,IACFS,EAAaT,EACbA,EAAqBlH,KAAAA,GAEnBqJ,GAAwCL,IAAkBhO,EAAO,GAAG,GAAagO,IAAkBhO,EAAO,IAAI,CAChHiM,EAAK,OAAO,CAAC,IAAI,CAACtK,EAAUA,EAAS,KAAK,EACjCqM,IAAkBhO,EAAO,OAAO,EACzCiM,EAAK,OAAO,CAAC,OAAO,CAACtK,EAAUA,EAAS,KAAK,GAI3CqM,IAAkBhO,EAAO,GAAG,CAAE,CAEhC,IAAIwQ,EAAarC,EAAuB,GAAG,CAAC3L,EAAM,QAAQ,CAAC,QAAQ,CAC/DgO,CAAAA,GAAcA,EAAW,GAAG,CAAC7O,EAAS,QAAQ,EAChDqO,EAAqB,CACnB,gBAAiBxN,EAAM,QAAQ,CAC/B,aAAcb,CAChB,EACSwM,EAAuB,GAAG,CAACxM,EAAS,QAAQ,GAGrDqO,CAAAA,EAAqB,CACnB,gBAAiBrO,EACjB,aAAca,EAAM,QAAQ,AAC9B,EAEJ,MAAO,GAAI0L,EAA8B,CAEvC,IAAIuC,EAAUtC,EAAuB,GAAG,CAAC3L,EAAM,QAAQ,CAAC,QAAQ,EAC5DiO,EACFA,EAAQ,GAAG,CAAC9O,EAAS,QAAQ,GAE7B8O,EAAU,IAAIrM,IAAI,CAACzC,EAAS,QAAQ,CAAC,EACrCwM,EAAuB,GAAG,CAAC3L,EAAM,QAAQ,CAAC,QAAQ,CAAEiO,IAEtDT,EAAqB,CACnB,gBAAiBxN,EAAM,QAAQ,CAC/B,aAAcb,CAChB,CACF,CACAwN,GAAYjP,EAAS,CAAC,EAAGkP,EAAU,CACjCW,WAAAA,EACA7H,WAAAA,EACA,cAAe8F,EACfrM,SAAAA,EACA,YAAa,GACb,WAAY8J,EACZ,aAAc,OACd,sBAAuBiF,GAAuB/O,EAAUyN,EAAS,OAAO,EAAI5M,EAAM,OAAO,EACzF+N,mBAAAA,EACAF,SAAAA,CACF,GAAI,CACFL,mBAAAA,EACA,UAAWC,AAAc,KAAdA,CACb,GAEAjC,EAAgBhO,EAAO,GAAG,CAC1BiO,EAA4B,GAC5BC,EAA+B,GAC/BG,EAA8B,GAC9BC,GAAyB,GACzBC,GAA0B,EAAE,AAC9B,CAGA,eAAeoC,GAASxP,CAAE,CAAEkO,CAAI,EAC9B,GAAI,AAAc,UAAd,OAAOlO,EAAiB,YAC1B8K,EAAK,OAAO,CAAC,EAAE,CAAC9K,GAGlB,IAAIyP,EAAiBC,EAAYrO,EAAM,QAAQ,CAAEA,EAAM,OAAO,CAAE2C,EAAU4H,EAAO,kBAAkB,CAAE5L,EAAI4L,EAAO,oBAAoB,CAAEsC,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,WAAW,CAAEA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,QAAQ,EACjN,CACF1M,KAAAA,CAAI,CACJmO,WAAAA,CAAU,CACV/M,MAAAA,CAAK,CACN,CAAGgN,EAAyBhE,EAAO,sBAAsB,CAAE,GAAO6D,EAAgBvB,GAC/E2B,EAAkBxO,EAAM,QAAQ,CAChCyO,EAAe/P,EAAesB,EAAM,QAAQ,CAAEG,EAAM0M,GAAQA,EAAK,KAAK,EAM1E4B,EAAe/Q,EAAS,CAAC,EAAG+Q,EAAchF,EAAK,OAAO,CAAC,cAAc,CAACgF,IACtE,IAAIC,EAAc7B,GAAQA,AAAgB,MAAhBA,EAAK,OAAO,CAAWA,EAAK,OAAO,CAAGrK,KAAAA,EAC5DmM,EAAgBnR,EAAO,IAAI,AAC3BkR,AAAgB,MAAhBA,EACFC,EAAgBnR,EAAO,OAAO,CACL,KAAhBkR,GAAkCJ,AAAc,MAAdA,GAAsBX,GAAiBW,EAAW,UAAU,GAAKA,EAAW,UAAU,GAAKtO,EAAM,QAAQ,CAAC,QAAQ,CAAGA,EAAM,QAAQ,CAAC,MAAM,EAKrL2O,CAAAA,EAAgBnR,EAAO,OAAO,AAAD,EAE/B,IAAIuQ,EAAqBlB,GAAQ,uBAAwBA,EAAOA,AAA4B,KAA5BA,EAAK,kBAAkB,CAAYrK,KAAAA,EAC/FiL,EAAY,AAA6B,KAA5BZ,CAAAA,GAAQA,EAAK,SAAS,AAAD,EAClC+B,EAAaC,GAAsB,CACrCL,gBAAAA,EACAC,aAAAA,EACAE,cAAAA,CACF,UACA,AAAIC,OAEFE,GAAcF,EAAY,CACxB,MAAO,UACP,SAAUH,EACV,UACEK,GAAcF,EAAY,CACxB,MAAO,aACP,QAASpM,KAAAA,EACT,MAAOA,KAAAA,EACP,SAAUiM,CACZ,GAEAN,GAASxP,EAAIkO,EACf,EACA,QACE,IAAIgB,EAAW,IAAItC,IAAIvL,EAAM,QAAQ,EACrC6N,EAAS,GAAG,CAACe,EAAYzF,GACzBwD,GAAY,CACVkB,SAAAA,CACF,EACF,CACF,GAGK,MAAMkB,GAAgBJ,EAAeF,EAAc,CACxDH,WAAAA,EAGA,aAAc/M,EACdwM,mBAAAA,EACA,QAASlB,GAAQA,EAAK,OAAO,CAC7B,qBAAsBA,GAAQA,EAAK,cAAc,CACjDY,UAAAA,CACF,EACF,CAmCA,eAAesB,GAAgBJ,CAAa,CAAExP,CAAQ,CAAE0N,CAAI,MAupChC1N,EAAUwF,EAylDdH,EAAGC,MAxrFrBuK,CApDJnF,CAAAA,GAA+BA,EAA4B,KAAK,GAChEA,EAA8B,KAC9B2B,EAAgBmD,EAChB9C,EAA8B,AAAkD,KAAjDgB,CAAAA,GAAQA,EAAK,8BAA8B,AAAD,EAgpC/C1N,EA7oCPa,EAAM,QAAQ,CA6oCG2E,EA7oCD3E,EAAM,OAAO,CA8oC5C0K,GAAwBE,GAE1BF,CAAAA,CAAoB,CADVuE,GAAa9P,EAAUwF,GACR,CAAGiG,GAAkB,EA/oChDa,EAA4B,AAAsC,KAArCoB,CAAAA,GAAQA,EAAK,kBAAkB,AAAD,EAC3DnB,EAA+B,AAAwC,KAAvCmB,CAAAA,GAAQA,EAAK,oBAAoB,AAAD,EAChE,IAAIqC,EAAcxF,GAAsBS,EACpCgF,EAAoBtC,GAAQA,EAAK,kBAAkB,CACnDlI,EAAUlC,EAAYyM,EAAa/P,EAAUwD,GAC7C8K,EAAY,AAA6B,KAA5BZ,CAAAA,GAAQA,EAAK,SAAS,AAAD,EAClC3B,EAAWC,GAAcxG,EAASuK,EAAa/P,EAAS,QAAQ,EAKpE,GAJI+L,EAAS,MAAM,EAAIA,EAAS,OAAO,EACrCvG,CAAAA,EAAUuG,EAAS,OAAO,AAAD,EAGvB,CAACvG,EAAS,CACZ,GAAI,CACFpD,MAAAA,CAAK,CACL6N,gBAAAA,CAAe,CACflN,MAAAA,CAAK,CACN,CAAGmN,GAAsBlQ,EAAS,QAAQ,EAC3CgO,GAAmBhO,EAAU,CAC3B,QAASiQ,EACT,WAAY,CAAC,EACb,OAAQ,CACN,CAAClN,EAAM,EAAE,CAAC,CAAEX,CACd,CACF,EAAG,CACDkM,UAAAA,CACF,GACA,MACF,CAOA,GAAIzN,EAAM,WAAW,EAAI,CAAC8L,KAmsFJtH,EAnsF+CxE,EAAM,QAAQ,CAmsF1DyE,EAnsF4DtF,EAosFvF,AAAIqF,EAAE,QAAQ,GAAKC,EAAE,QAAQ,EAAID,EAAE,MAAM,GAAKC,EAAE,MAAM,GAGlDD,AAAW,KAAXA,EAAE,IAAI,CAEDC,AAAW,KAAXA,EAAE,IAAI,CACJD,EAAE,IAAI,GAAKC,EAAE,IAAI,EAGjBA,AAAW,KAAXA,EAAE,IAAI,EAMV,MAntF6F,CAAEoI,CAAAA,GAAQA,EAAK,UAAU,EAAIc,GAAiBd,EAAK,UAAU,CAAC,UAAU,GAAI,YAC5KM,GAAmBhO,EAAU,CAC3BwF,QAAAA,CACF,EAAG,CACD8I,UAAAA,CACF,GAIF5D,EAA8B,IAAIyF,gBAClC,IAAIC,EAAUC,GAAwB/F,EAAK,OAAO,CAAEtK,EAAU0K,EAA4B,MAAM,CAAEgD,GAAQA,EAAK,UAAU,EAEzH,GAAIA,GAAQA,EAAK,YAAY,CAK3BmC,EAAsB,CAACS,GAAoB9K,GAAS,KAAK,CAAC,EAAE,CAAE,CAC5D,KAAMlH,EAAW,KAAK,CACtB,MAAOoP,EAAK,YAAY,AAC1B,EAAE,MACG,GAAIA,GAAQA,EAAK,UAAU,EAAIc,GAAiBd,EAAK,UAAU,CAAC,UAAU,EAAG,CAElF,IAAI6C,EAAe,MAAMC,GAAaJ,EAASpQ,EAAU0N,EAAK,UAAU,CAAElI,EAASuG,EAAS,MAAM,CAAE,CAClG,QAAS2B,EAAK,OAAO,CACrBY,UAAAA,CACF,GACA,GAAIiC,EAAa,cAAc,CAC7B,OAIF,GAAIA,EAAa,mBAAmB,CAAE,CACpC,GAAI,CAACE,EAAStL,EAAO,CAAGoL,EAAa,mBAAmB,CACxD,GAAIG,GAAcvL,IAAWqE,EAAqBrE,EAAO,KAAK,GAAKA,AAAwB,MAAxBA,EAAO,KAAK,CAAC,MAAM,CAAU,CAC9FuF,EAA8B,KAC9BsD,GAAmBhO,EAAU,CAC3B,QAASuQ,EAAa,OAAO,CAC7B,WAAY,CAAC,EACb,OAAQ,CACN,CAACE,EAAQ,CAAEtL,EAAO,KAAK,AACzB,CACF,GACA,MACF,CACF,CACAK,EAAU+K,EAAa,OAAO,EAAI/K,EAClCqK,EAAsBU,EAAa,mBAAmB,CACtDP,EAAoBW,GAAqB3Q,EAAU0N,EAAK,UAAU,EAClEY,EAAY,GAEZvC,EAAS,MAAM,CAAG,GAElBqE,EAAUC,GAAwB/F,EAAK,OAAO,CAAE8F,EAAQ,GAAG,CAAEA,EAAQ,MAAM,CAC7E,CAEA,GAAI,CACFQ,eAAAA,CAAc,CACd,QAASC,CAAc,CACvBtK,WAAAA,CAAU,CACV2F,OAAAA,CAAM,CACP,CAAG,MAAM4E,GAAcV,EAASpQ,EAAUwF,EAASuG,EAAS,MAAM,CAAEiE,EAAmBtC,GAAQA,EAAK,UAAU,CAAEA,GAAQA,EAAK,iBAAiB,CAAEA,GAAQA,EAAK,OAAO,CAAEA,GAAQA,AAA0B,KAA1BA,EAAK,gBAAgB,CAAWY,EAAWuB,GACtNe,IAMJlG,EAA8B,KAC9BsD,GAAmBhO,EAAUzB,EAAS,CACpC,QAASsS,GAAkBrL,CAC7B,EAAGuL,GAAuBlB,GAAsB,CAC9CtJ,WAAAA,EACA2F,OAAAA,CACF,IACF,CAGA,eAAesE,GAAaJ,CAAO,CAAEpQ,CAAQ,CAAEmP,CAAU,CAAE3J,CAAO,CAAEwL,CAAU,CAAEtD,CAAI,MAg1F3CyB,MAnyFnChK,EAjCJ,GAXIuI,AAAS,KAAK,IAAdA,GACFA,CAAAA,EAAO,CAAC,GAEVuD,KAGAzD,GAAY,CACV0D,WAy0Fa,CACf,MAAO,aACPlR,SA70FyCA,EA80FzC,WAAYmP,CAJ2BA,EA10FYA,GA80F5B,UAAU,CACjC,WAAYA,EAAW,UAAU,CACjC,YAAaA,EAAW,WAAW,CACnC,SAAUA,EAAW,QAAQ,CAC7B,KAAMA,EAAW,IAAI,CACrB,KAAMA,EAAW,IAAI,AACvB,CAj1FE,EAAG,CACD,UAAWzB,AAAmB,KAAnBA,EAAK,SAAS,AAC3B,GACIsD,EAAY,CACd,IAAIG,EAAiB,MAAMC,GAAe5L,EAASxF,EAAS,QAAQ,CAAEoQ,EAAQ,MAAM,EACpF,GAAIe,AAAwB,YAAxBA,EAAe,IAAI,CACrB,MAAO,CACL,eAAgB,EAClB,EACK,GAAIA,AAAwB,UAAxBA,EAAe,IAAI,CAAc,CAC1C,IAAIE,EAAaf,GAAoBa,EAAe,cAAc,EAAE,KAAK,CAAC,EAAE,CAC5E,MAAO,CACL,QAASA,EAAe,cAAc,CACtC,oBAAqB,CAACE,EAAY,CAChC,KAAM/S,EAAW,KAAK,CACtB,MAAO6S,EAAe,KAAK,AAC7B,EAAE,AACJ,CACF,CAAO,GAAKA,EAAe,OAAO,CAchC3L,EAAU2L,EAAe,OAAO,KAdE,CAClC,GAAI,CACFlB,gBAAAA,CAAe,CACf7N,MAAAA,CAAK,CACLW,MAAAA,CAAK,CACN,CAAGmN,GAAsBlQ,EAAS,QAAQ,EAC3C,MAAO,CACL,QAASiQ,EACT,oBAAqB,CAAClN,EAAM,EAAE,CAAE,CAC9B,KAAMzE,EAAW,KAAK,CACtB8D,MAAAA,CACF,EAAE,AACJ,CACF,CAGF,CAGA,IAAIkP,EAAcC,GAAe/L,EAASxF,GAC1C,GAAI,AAACsR,EAAY,KAAK,CAAC,MAAM,EAAKA,EAAY,KAAK,CAAC,IAAI,CAYtD,IADAnM,EAASqM,AADK,OAAMC,GAAiB,SAAU5Q,EAAOuP,EAAS,CAACkB,EAAY,CAAE9L,EAAS,KAAI,CAC3E,CAAC8L,EAAY,KAAK,CAAC,EAAE,CAAC,CAClClB,EAAQ,MAAM,CAAC,OAAO,CACxB,MAAO,CACL,eAAgB,EAClB,CACF,MAfAjL,EAAS,CACP,KAAM7G,EAAW,KAAK,CACtB,MAAOuN,GAAuB,IAAK,CACjC,OAAQuE,EAAQ,MAAM,CACtB,SAAUpQ,EAAS,QAAQ,CAC3B,QAASsR,EAAY,KAAK,CAAC,EAAE,AAC/B,EACF,EAUF,GAAII,GAAiBvM,GAAS,CAC5B,IAAI7C,EAcJ,OAZEA,EADEoL,GAAQA,AAAgB,MAAhBA,EAAK,OAAO,CACZA,EAAK,OAAO,CAMZ1N,AADK2R,GAA0BxM,EAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAa,IAAInD,IAAIoO,EAAQ,GAAG,EAAG5M,KACjF3C,EAAM,QAAQ,CAAC,QAAQ,CAAGA,EAAM,QAAQ,CAAC,MAAM,CAExE,MAAM+Q,GAAwBxB,EAASjL,EAAQ,GAAM,CACnDgK,WAAAA,EACA7M,QAAAA,CACF,GACO,CACL,eAAgB,EAClB,CACF,CACA,GAAIuP,GAAiB1M,GACnB,MAAM0G,GAAuB,IAAK,CAChC,KAAM,cACR,GAEF,GAAI6E,GAAcvL,GAAS,CAGzB,IAAI2M,EAAgBxB,GAAoB9K,EAAS8L,EAAY,KAAK,CAAC,EAAE,EASrE,MAHI,AAA2B,KAA1B5D,CAAAA,GAAQA,EAAK,OAAO,AAAD,GACtBrB,CAAAA,EAAgBhO,EAAO,IAAI,AAAD,EAErB,CACLmH,QAAAA,EACA,oBAAqB,CAACsM,EAAc,KAAK,CAAC,EAAE,CAAE3M,EAAO,AACvD,CACF,CACA,MAAO,CACLK,QAAAA,EACA,oBAAqB,CAAC8L,EAAY,KAAK,CAAC,EAAE,CAAEnM,EAAO,AACrD,CACF,CAGA,eAAe2L,GAAcV,CAAO,CAAEpQ,CAAQ,CAAEwF,CAAO,CAAEwL,CAAU,CAAEe,CAAkB,CAAE5C,CAAU,CAAE6C,CAAiB,CAAE1P,CAAO,CAAE2P,CAAgB,CAAE3D,CAAS,CAAEuB,CAAmB,EAE/K,IAAIG,EAAoB+B,GAAsBpB,GAAqB3Q,EAAUmP,GAGzE+C,EAAmB/C,GAAc6C,GAAqBG,GAA4BnC,GAOlFoC,EAA8B,CAAC1F,GAAgC,EAACtB,EAAO,mBAAmB,EAAI,CAAC6G,CAAe,EAMlH,GAAIjB,EAAY,CACd,GAAIoB,EAA6B,CAC/B,IAAIhE,EAAaiE,GAAqBxC,GACtCrC,GAAYjP,EAAS,CACnB,WAAYyR,CACd,EAAG5B,AAAe/K,KAAAA,IAAf+K,EAA2B,CAC5BA,WAAAA,CACF,EAAI,CAAC,GAAI,CACPE,UAAAA,CACF,EACF,CACA,IAAI6C,EAAiB,MAAMC,GAAe5L,EAASxF,EAAS,QAAQ,CAAEoQ,EAAQ,MAAM,EACpF,GAAIe,AAAwB,YAAxBA,EAAe,IAAI,CACrB,MAAO,CACL,eAAgB,EAClB,EACK,GAAIA,AAAwB,UAAxBA,EAAe,IAAI,CAAc,CAC1C,IAAIE,EAAaf,GAAoBa,EAAe,cAAc,EAAE,KAAK,CAAC,EAAE,CAC5E,MAAO,CACL,QAASA,EAAe,cAAc,CACtC,WAAY,CAAC,EACb,OAAQ,CACN,CAACE,EAAW,CAAEF,EAAe,KAAK,AACpC,CACF,CACF,CAAO,GAAKA,EAAe,OAAO,CAchC3L,EAAU2L,EAAe,OAAO,KAdE,CAClC,GAAI,CACF/O,MAAAA,CAAK,CACL6N,gBAAAA,CAAe,CACflN,MAAAA,CAAK,CACN,CAAGmN,GAAsBlQ,EAAS,QAAQ,EAC3C,MAAO,CACL,QAASiQ,EACT,WAAY,CAAC,EACb,OAAQ,CACN,CAAClN,EAAM,EAAE,CAAC,CAAEX,CACd,CACF,CACF,CAGF,CACA,IAAI2N,EAAcxF,GAAsBS,EACpC,CAACsH,EAAeC,EAAqB,CAAGC,EAAiBlI,EAAK,OAAO,CAAEzJ,EAAO2E,EAAS0M,EAAkBlS,EAAUoL,EAAO,mBAAmB,EAAI6G,AAAqB,KAArBA,EAA2B7G,EAAO,8BAA8B,CAAEuB,GAAwBC,GAAyBC,GAAuBQ,GAAiBF,GAAkBD,GAAkB6C,EAAavM,EAAUqM,GAO3W,GAHA4C,GAAsBhC,GAAW,CAAEjL,CAAAA,GAAWA,EAAQ,IAAI,CAACyG,GAAKA,EAAE,KAAK,CAAC,EAAE,GAAKwE,EAAO,GAAM6B,GAAiBA,EAAc,IAAI,CAACrG,GAAKA,EAAE,KAAK,CAAC,EAAE,GAAKwE,IACpJzD,GAA0B,EAAED,GAExBuF,AAAyB,IAAzBA,EAAc,MAAM,EAAUC,AAAgC,IAAhCA,EAAqB,MAAM,CAAQ,CACnE,IAAIG,EAAkBC,KAatB,OAZA3E,GAAmBhO,EAAUzB,EAAS,CACpCiH,QAAAA,EACA,WAAY,CAAC,EAEb,OAAQqK,GAAuBa,GAAcb,CAAmB,CAAC,EAAE,EAAI,CACrE,CAACA,CAAmB,CAAC,EAAE,CAAC,CAAEA,CAAmB,CAAC,EAAE,CAAC,KAAK,AACxD,EAAI,IACN,EAAGkB,GAAuBlB,GAAsB6C,EAAkB,CAChE,SAAU,IAAItG,IAAIvL,EAAM,QAAQ,CAClC,EAAI,CAAC,GAAI,CACPyN,UAAAA,CACF,GACO,CACL,eAAgB,EAClB,CACF,CACA,GAAI8D,EAA6B,CAC/B,IAAIQ,EAAU,CAAC,EACf,GAAI,CAAC5B,EAAY,CAEf4B,EAAQ,UAAU,CAAG5C,EACrB,IAAI5B,EAAaiE,GAAqBxC,EAClCzB,AAAe/K,MAAAA,IAAf+K,GACFwE,CAAAA,EAAQ,UAAU,CAAGxE,CAAS,CAElC,CACImE,EAAqB,MAAM,CAAG,IA4GpCA,AA3GsDA,EA2GjC,OAAO,CAACM,IAC3B,IAAIhF,EAAUhN,EAAM,QAAQ,CAAC,GAAG,CAACgS,EAAG,GAAG,EACnCC,EAAsBC,GAAkB1P,KAAAA,EAAWwK,EAAUA,EAAQ,IAAI,CAAGxK,KAAAA,GAChFxC,EAAM,QAAQ,CAAC,GAAG,CAACgS,EAAG,GAAG,CAAEC,EAC7B,GA/GIF,EAAQ,QAAQ,CAgHb,IAAIxG,IAAIvL,EAAM,QAAQ,GA9G3B2M,GAAYoF,EAAS,CACnBtE,UAAAA,CACF,EACF,CACAiE,EAAqB,OAAO,CAACM,IAC3BG,GAAaH,EAAG,GAAG,EACfA,EAAG,UAAU,EAIf/F,GAAiB,GAAG,CAAC+F,EAAG,GAAG,CAAEA,EAAG,UAAU,CAE9C,GAEA,IAAII,EAAiC,IAAMV,EAAqB,OAAO,CAACW,GAAKF,GAAaE,EAAE,GAAG,EAC3FxI,CAAAA,GACFA,EAA4B,MAAM,CAAC,gBAAgB,CAAC,QAASuI,GAE/D,GAAI,CACFE,cAAAA,CAAa,CACbC,eAAAA,CAAc,CACf,CAAG,MAAMC,GAA+BxS,EAAO2E,EAAS8M,EAAeC,EAAsBnC,GAC9F,GAAIA,EAAQ,MAAM,CAAC,OAAO,CACxB,MAAO,CACL,eAAgB,EAClB,CAKE1F,CAAAA,GACFA,EAA4B,MAAM,CAAC,mBAAmB,CAAC,QAASuI,GAElEV,EAAqB,OAAO,CAACM,GAAM/F,GAAiB,MAAM,CAAC+F,EAAG,GAAG,GAEjE,IAAIS,EAAWC,GAAaJ,GAC5B,GAAIG,EAIF,OAHA,MAAM1B,GAAwBxB,EAASkD,EAAS,MAAM,CAAE,GAAM,CAC5DhR,QAAAA,CACF,GACO,CACL,eAAgB,EAClB,EAGF,GADAgR,EAAWC,GAAaH,GAStB,OAJAlG,GAAiB,GAAG,CAACoG,EAAS,GAAG,EACjC,MAAM1B,GAAwBxB,EAASkD,EAAS,MAAM,CAAE,GAAM,CAC5DhR,QAAAA,CACF,GACO,CACL,eAAgB,EAClB,EAGF,GAAI,CACFiE,WAAAA,CAAU,CACV2F,OAAAA,CAAM,CACP,CAAGsH,GAAkB3S,EAAO2E,EAAS2N,EAAetD,EAAqB0C,EAAsBa,EAAgB9F,IAEhHA,GAAgB,OAAO,CAAC,CAACmG,EAAchD,KACrCgD,EAAa,SAAS,CAACC,IAIjBA,CAAAA,GAAWD,EAAa,IAAI,AAAD,GAC7BnG,GAAgB,MAAM,CAACmD,EAE3B,EACF,GAEIrF,EAAO,mBAAmB,EAAI6G,GAAoBpR,EAAM,MAAM,EAChEqL,CAAAA,EAAS3N,EAAS,CAAC,EAAGsC,EAAM,MAAM,CAAEqL,EAAM,EAE5C,IAAIwG,EAAkBC,KAClBgB,EAAqBC,GAAqB5G,IAC1C6G,EAAuBnB,GAAmBiB,GAAsBpB,EAAqB,MAAM,CAAG,EAClG,OAAOhU,EAAS,CACdiH,QAAAA,EACAe,WAAAA,EACA2F,OAAAA,CACF,EAAG2H,EAAuB,CACxB,SAAU,IAAIzH,IAAIvL,EAAM,QAAQ,CAClC,EAAI,CAAC,EACP,CACA,SAASwR,GAAqBxC,CAAmB,EAC/C,GAAIA,GAAuB,CAACa,GAAcb,CAAmB,CAAC,EAAE,EAI9D,MAAO,CACL,CAACA,CAAmB,CAAC,EAAE,CAAC,CAAEA,CAAmB,CAAC,EAAE,CAAC,IAAI,AACvD,EACK,GAAIhP,EAAM,UAAU,CACzB,GAAIrC,AAAyC,IAAzCA,OAAO,IAAI,CAACqC,EAAM,UAAU,EAAE,MAAM,CACtC,OAAO,UAEP,OAAOA,EAAM,UAAU,AAG7B,CA0DA,eAAeiT,GAAoBjV,CAAG,CAAE4R,CAAO,CAAEzP,CAAI,CAAEmF,CAAK,CAAE4N,CAAc,CAAE/C,CAAU,CAAE1C,CAAS,CAAEM,CAAkB,CAAEO,CAAU,MAmgFvGA,EAAY6E,EAhgFtC,SAASC,EAAwBhI,CAAC,EAChC,GAAI,CAACA,EAAE,KAAK,CAAC,MAAM,EAAI,CAACA,EAAE,KAAK,CAAC,IAAI,CAAE,CACpC,IAAI7J,EAAQyJ,GAAuB,IAAK,CACtC,OAAQsD,EAAW,UAAU,CAC7B,SAAUnO,EACV,QAASyP,CACX,GAIA,OAHAyD,GAAgBrV,EAAK4R,EAASrO,EAAO,CACnCkM,UAAAA,CACF,GACO,EACT,CACA,MAAO,EACT,CACA,GAhBA2C,KACA9D,GAAiB,MAAM,CAACtO,GAepB,CAACmS,GAAciD,EAAwB9N,GACzC,OAGF,IAAI6N,EAAkBnT,EAAM,QAAQ,CAAC,GAAG,CAAChC,GACzCsV,GAAmBtV,GA6+EOsQ,EA7+EmBA,EA6+EP6E,EA7+EmBA,EA8+E7C,CACZ,MAAO,aACP,WAAY7E,EAAW,UAAU,CACjC,WAAYA,EAAW,UAAU,CACjC,YAAaA,EAAW,WAAW,CACnC,SAAUA,EAAW,QAAQ,CAC7B,KAAMA,EAAW,IAAI,CACrB,KAAMA,EAAW,IAAI,CACrB,KAAM6E,EAAkBA,EAAgB,IAAI,CAAG3Q,KAAAA,CACjD,GAv/E6E,CACzEiL,UAAAA,CACF,GACA,IAAI8F,EAAkB,IAAIjE,gBACtBkE,EAAehE,GAAwB/F,EAAK,OAAO,CAAEtJ,EAAMoT,EAAgB,MAAM,CAAEjF,GACvF,GAAI6B,EAAY,CACd,IAAIG,EAAiB,MAAMC,GAAe2C,EAAgB/S,EAAMqT,EAAa,MAAM,EACnF,GAAIlD,AAAwB,YAAxBA,EAAe,IAAI,CACrB,OACK,GAAIA,AAAwB,UAAxBA,EAAe,IAAI,CAAc,YAC1C+C,GAAgBrV,EAAK4R,EAASU,EAAe,KAAK,CAAE,CAClD7C,UAAAA,CACF,GAEK,GAAI,CAAC6C,EAAe,OAAO,CAAE,YAClC+C,GAAgBrV,EAAK4R,EAAS5E,GAAuB,IAAK,CACxD,SAAU7K,CACZ,GAAI,CACFsN,UAAAA,CACF,GAKA,GAAI2F,EADJ9N,EAAQoL,GADRwC,EAAiB5C,EAAe,OAAO,CACAnQ,IAErC,MAGN,CAEA8L,GAAiB,GAAG,CAACjO,EAAKuV,GAC1B,IAAIE,EAAoBvH,GAEpBwD,EAAegE,AADC,OAAM9C,GAAiB,SAAU5Q,EAAOwT,EAAc,CAAClO,EAAM,CAAE4N,EAAgBlV,EAAG,CACtE,CAACsH,EAAM,KAAK,CAAC,EAAE,CAAC,CAChD,GAAIkO,EAAa,MAAM,CAAC,OAAO,CAAE,CAG3BvH,GAAiB,GAAG,CAACjO,KAASuV,GAChCtH,GAAiB,MAAM,CAACjO,GAE1B,MACF,CAIA,GAAIuM,EAAO,iBAAiB,EAAIiC,GAAgB,GAAG,CAACxO,GAClD,IAAI6S,GAAiBnB,IAAiBG,GAAcH,GAAe,YACjE4D,GAAmBtV,EAAK2V,GAAenR,KAAAA,GAEzC,KAEK,CACL,GAAIqO,GAAiBnB,SAEnB,CADAzD,GAAiB,MAAM,CAACjO,GACpBmO,GAA0BsH,QAK5BH,GAAmBtV,EAAK2V,GAAenR,KAAAA,KAGvC6J,GAAiB,GAAG,CAACrO,GACrBsV,GAAmBtV,EAAKkU,GAAkB5D,IACnCyC,GAAwByC,EAAc9D,EAAc,GAAO,CAChE,kBAAmBpB,EACnBP,mBAAAA,CACF,IAIJ,GAAI8B,GAAcH,GAAe,YAC/B2D,GAAgBrV,EAAK4R,EAASF,EAAa,KAAK,CAGpD,CACA,GAAIsB,GAAiBtB,GACnB,MAAM1E,GAAuB,IAAK,CAChC,KAAM,cACR,GAIF,IAAIyD,EAAezO,EAAM,UAAU,CAAC,QAAQ,EAAIA,EAAM,QAAQ,CAC1D4T,EAAsBpE,GAAwB/F,EAAK,OAAO,CAAEgF,EAAc8E,EAAgB,MAAM,EAChGrE,EAAcxF,GAAsBS,EACpCxF,EAAU3E,AAA2B,SAA3BA,EAAM,UAAU,CAAC,KAAK,CAAcyC,EAAYyM,EAAalP,EAAM,UAAU,CAAC,QAAQ,CAAE2C,GAAY3C,EAAM,OAAO,CAC/HV,EAAUqF,EAAS,gDACnB,IAAIkP,EAAS,EAAE3H,GACfE,GAAe,GAAG,CAACpO,EAAK6V,GACxB,IAAIC,EAAc5B,GAAkB5D,EAAYoB,EAAa,IAAI,EACjE1P,EAAM,QAAQ,CAAC,GAAG,CAAChC,EAAK8V,GACxB,GAAI,CAACrC,EAAeC,EAAqB,CAAGC,EAAiBlI,EAAK,OAAO,CAAEzJ,EAAO2E,EAAS2J,EAAYG,EAAc,GAAOlE,EAAO,8BAA8B,CAAEuB,GAAwBC,GAAyBC,GAAuBQ,GAAiBF,GAAkBD,GAAkB6C,EAAavM,EAAU,CAAC2C,EAAM,KAAK,CAAC,EAAE,CAAEoK,EAAa,EAIrVgC,EAAqB,MAAM,CAACM,GAAMA,EAAG,GAAG,GAAKhU,GAAK,OAAO,CAACgU,IACxD,IAAI+B,EAAW/B,EAAG,GAAG,CACjBmB,EAAkBnT,EAAM,QAAQ,CAAC,GAAG,CAAC+T,GACrC9B,EAAsBC,GAAkB1P,KAAAA,EAAW2Q,EAAkBA,EAAgB,IAAI,CAAG3Q,KAAAA,GAChGxC,EAAM,QAAQ,CAAC,GAAG,CAAC+T,EAAU9B,GAC7BE,GAAa4B,GACT/B,EAAG,UAAU,EACf/F,GAAiB,GAAG,CAAC8H,EAAU/B,EAAG,UAAU,CAEhD,GACArF,GAAY,CACV,SAAU,IAAIpB,IAAIvL,EAAM,QAAQ,CAClC,GACA,IAAIoS,EAAiC,IAAMV,EAAqB,OAAO,CAACM,GAAMG,GAAaH,EAAG,GAAG,GACjGuB,EAAgB,MAAM,CAAC,gBAAgB,CAAC,QAASnB,GACjD,GAAI,CACFE,cAAAA,CAAa,CACbC,eAAAA,CAAc,CACf,CAAG,MAAMC,GAA+BxS,EAAO2E,EAAS8M,EAAeC,EAAsBkC,GAC9F,GAAIL,EAAgB,MAAM,CAAC,OAAO,CAChC,OAEFA,EAAgB,MAAM,CAAC,mBAAmB,CAAC,QAASnB,GACpDhG,GAAe,MAAM,CAACpO,GACtBiO,GAAiB,MAAM,CAACjO,GACxB0T,EAAqB,OAAO,CAACsC,GAAK/H,GAAiB,MAAM,CAAC+H,EAAE,GAAG,GAC/D,IAAIvB,EAAWC,GAAaJ,GAC5B,GAAIG,EACF,OAAO1B,GAAwB6C,EAAqBnB,EAAS,MAAM,CAAE,GAAO,CAC1E1E,mBAAAA,CACF,GAGF,GADA0E,EAAWC,GAAaH,GAMtB,OADAlG,GAAiB,GAAG,CAACoG,EAAS,GAAG,EAC1B1B,GAAwB6C,EAAqBnB,EAAS,MAAM,CAAE,GAAO,CAC1E1E,mBAAAA,CACF,GAGF,GAAI,CACFrI,WAAAA,CAAU,CACV2F,OAAAA,CAAM,CACP,CAAGsH,GAAkB3S,EAAO2E,EAAS2N,EAAe9P,KAAAA,EAAWkP,EAAsBa,EAAgB9F,IAGtG,GAAIzM,EAAM,QAAQ,CAAC,GAAG,CAAChC,GAAM,CAC3B,IAAIiW,EAAcN,GAAejE,EAAa,IAAI,EAClD1P,EAAM,QAAQ,CAAC,GAAG,CAAChC,EAAKiW,EAC1B,CACAlB,GAAqBc,GAIjB7T,AAA2B,YAA3BA,EAAM,UAAU,CAAC,KAAK,EAAkB6T,EAAS1H,IACnD7M,EAAUkM,EAAe,2BACzB3B,GAA+BA,EAA4B,KAAK,GAChEsD,GAAmBnN,EAAM,UAAU,CAAC,QAAQ,CAAE,CAC5C2E,QAAAA,EACAe,WAAAA,EACA2F,OAAAA,EACA,SAAU,IAAIE,IAAIvL,EAAM,QAAQ,CAClC,KAKA2M,GAAY,CACVtB,OAAAA,EACA,WAAYuC,GAAgB5N,EAAM,UAAU,CAAE0F,EAAYf,EAAS0G,GACnE,SAAU,IAAIE,IAAIvL,EAAM,QAAQ,CAClC,GACA8L,GAAyB,GAE7B,CAEA,eAAeoI,GAAoBlW,CAAG,CAAE4R,CAAO,CAAEzP,CAAI,CAAEmF,CAAK,CAAEX,CAAO,CAAEwL,CAAU,CAAE1C,CAAS,CAAEM,CAAkB,CAAEO,CAAU,EAC1H,IAAI6E,EAAkBnT,EAAM,QAAQ,CAAC,GAAG,CAAChC,GACzCsV,GAAmBtV,EAAKkU,GAAkB5D,EAAY6E,EAAkBA,EAAgB,IAAI,CAAG3Q,KAAAA,GAAY,CACzGiL,UAAAA,CACF,GACA,IAAI8F,EAAkB,IAAIjE,gBACtBkE,EAAehE,GAAwB/F,EAAK,OAAO,CAAEtJ,EAAMoT,EAAgB,MAAM,EACrF,GAAIpD,EAAY,CACd,IAAIG,EAAiB,MAAMC,GAAe5L,EAASxE,EAAMqT,EAAa,MAAM,EAC5E,GAAIlD,AAAwB,YAAxBA,EAAe,IAAI,CACrB,OACK,GAAIA,AAAwB,UAAxBA,EAAe,IAAI,CAAc,YAC1C+C,GAAgBrV,EAAK4R,EAASU,EAAe,KAAK,CAAE,CAClD7C,UAAAA,CACF,GAEK,GAAI,CAAC6C,EAAe,OAAO,CAAE,YAClC+C,GAAgBrV,EAAK4R,EAAS5E,GAAuB,IAAK,CACxD,SAAU7K,CACZ,GAAI,CACFsN,UAAAA,CACF,GAIAnI,EAAQoL,GADR/L,EAAU2L,EAAe,OAAO,CACAnQ,EAEpC,CAEA8L,GAAiB,GAAG,CAACjO,EAAKuV,GAC1B,IAAIE,EAAoBvH,GAEpB5H,EAASqM,AADC,OAAMC,GAAiB,SAAU5Q,EAAOwT,EAAc,CAAClO,EAAM,CAAEX,EAAS3G,EAAG,CACrE,CAACsH,EAAM,KAAK,CAAC,EAAE,CAAC,CAapC,GARI0L,GAAiB1M,IACnBA,CAAAA,EAAS,AAAC,MAAM6P,GAAoB7P,EAAQkP,EAAa,MAAM,CAAE,KAAUlP,CAAK,EAI9E2H,GAAiB,GAAG,CAACjO,KAASuV,GAChCtH,GAAiB,MAAM,CAACjO,IAEtBwV,EAAa,MAAM,CAAC,OAAO,EAK/B,GAAIhH,GAAgB,GAAG,CAACxO,GAAM,YAC5BsV,GAAmBtV,EAAK2V,GAAenR,KAAAA,IAIzC,GAAIqO,GAAiBvM,GACnB,GAAI6H,GAA0BsH,EAAmB,YAG/CH,GAAmBtV,EAAK2V,GAAenR,KAAAA,QAElC,CACL6J,GAAiB,GAAG,CAACrO,GACrB,MAAM+S,GAAwByC,EAAclP,EAAQ,GAAO,CACzDyJ,mBAAAA,CACF,GACA,MACF,CAGF,GAAI8B,GAAcvL,GAAS,YACzB+O,GAAgBrV,EAAK4R,EAAStL,EAAO,KAAK,EAG5ChF,EAAU,CAAC0R,GAAiB1M,GAAS,mCAErCgP,GAAmBtV,EAAK2V,GAAerP,EAAO,IAAI,GACpD,CAoBA,eAAeyM,GAAwBxB,CAAO,CAAEkD,CAAQ,CAAE2B,CAAY,CAAEC,CAAM,EAC5E,GAAI,CACF/F,WAAAA,CAAU,CACV6C,kBAAAA,CAAiB,CACjBpD,mBAAAA,CAAkB,CAClBtM,QAAAA,CAAO,CACR,CAAG4S,AAAW,KAAK,IAAhBA,EAAoB,CAAC,EAAIA,CACzB5B,CAAAA,EAAS,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAChC3G,CAAAA,GAAyB,EAAG,EAE9B,IAAI3M,EAAWsT,EAAS,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAC7CnT,EAAUH,EAAU,uDACpBA,EAAW2R,GAA0B3R,EAAU,IAAIgC,IAAIoO,EAAQ,GAAG,EAAG5M,GACrE,IAAI2R,EAAmB5V,EAAesB,EAAM,QAAQ,CAAEb,EAAU,CAC9D,YAAa,EACf,GACA,GAAI6K,EAAW,CACb,IAAIuK,EAAmB,GACvB,GAAI9B,EAAS,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,2BAEhC8B,EAAmB,QACd,GAAInL,EAAmB,IAAI,CAACjK,GAAW,CAC5C,IAAMF,EAAMwK,EAAK,OAAO,CAAC,SAAS,CAACtK,GACnCoV,EAEAtV,EAAI,MAAM,GAAK8K,EAAa,QAAQ,CAAC,MAAM,EAE3CjH,AAAyC,MAAzCA,EAAc7D,EAAI,QAAQ,CAAE0D,EAC9B,CACA,GAAI4R,EAAkB,YAChB9S,EACFsI,EAAa,QAAQ,CAAC,OAAO,CAAC5K,GAE9B4K,EAAa,QAAQ,CAAC,MAAM,CAAC5K,GAInC,CAGA0K,EAA8B,KAC9B,IAAI2K,EAAwB/S,AAAY,KAAZA,GAAoBgR,EAAS,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAqBjV,EAAO,OAAO,CAAGA,EAAO,IAAI,CAG3H,CACFiX,WAAAA,CAAU,CACVC,WAAAA,CAAU,CACVC,YAAAA,CAAW,CACZ,CAAG3U,EAAM,UAAU,AAChB,EAACsO,GAAc,CAAC6C,GAAqBsD,GAAcC,GAAcC,GACnErG,CAAAA,EAAagD,GAA4BtR,EAAM,UAAU,GAK3D,IAAIqR,EAAmB/C,GAAc6C,EACrC,GAAInI,EAAkC,GAAG,CAACyJ,EAAS,QAAQ,CAAC,MAAM,GAAKpB,GAAoB1D,GAAiB0D,EAAiB,UAAU,EACrI,MAAMtC,GAAgByF,EAAuBF,EAAkB,CAC7D,WAAY5W,EAAS,CAAC,EAAG2T,EAAkB,CACzC,WAAYlS,CACd,GAEA,mBAAoB4O,GAAsBtC,EAC1C,qBAAsB2I,EAAe1I,EAA+BlJ,KAAAA,CACtE,OACK,CAGL,IAAI0O,EAAqBpB,GAAqBwE,EAAkBhG,EAChE,OAAMS,GAAgByF,EAAuBF,EAAkB,CAC7DpD,mBAAAA,EAEAC,kBAAAA,EAEA,mBAAoBpD,GAAsBtC,EAC1C,qBAAsB2I,EAAe1I,EAA+BlJ,KAAAA,CACtE,EACF,CACF,CAGA,eAAeoO,GAAiBgE,CAAI,CAAE5U,CAAK,CAAEuP,CAAO,CAAEkC,CAAa,CAAE9M,CAAO,CAAEkQ,CAAU,EAEtF,IADIlE,EACAmE,EAAc,CAAC,EACnB,GAAI,CACFnE,EAAU,MAAMoE,GAAqB3K,EAAkBwK,EAAM5U,EAAOuP,EAASkC,EAAe9M,EAASkQ,EAAY5S,EAAUF,EAC7H,CAAE,MAAOnC,EAAG,CASV,OANA6R,EAAc,OAAO,CAACrG,IACpB0J,CAAW,CAAC1J,EAAE,KAAK,CAAC,EAAE,CAAC,CAAG,CACxB,KAAM3N,EAAW,KAAK,CACtB,MAAOmC,CACT,CACF,GACOkV,CACT,CACA,IAAK,GAAI,CAAClF,EAAStL,EAAO,GAAI3G,OAAO,OAAO,CAACgT,GAC3C,KA64DsCrM,EA74DtC,GA84DG0Q,GAAW1Q,CADwBA,EA74DCA,GA84DlB,MAAM,GAAKyE,EAAoB,GAAG,CAACzE,EAAO,MAAM,CAAC,MAAM,EA94D5B,CAC9C,IAAI2Q,EAAW3Q,EAAO,MAAM,AAC5BwQ,CAAAA,CAAW,CAAClF,EAAQ,CAAG,CACrB,KAAMnS,EAAW,QAAQ,CACzB,SAAUyX,AAyjDpB,SAAkDD,CAAQ,CAAE1F,CAAO,CAAEK,CAAO,CAAEjL,CAAO,CAAEhC,CAAQ,CAAEqE,CAAoB,EACnH,IAAI7H,EAAW8V,EAAS,OAAO,CAAC,GAAG,CAAC,YAEpC,GADA3V,EAAUH,EAAU,8EAChB,CAACiK,EAAmB,IAAI,CAACjK,GAAW,CACtC,IAAIgW,EAAiBxQ,EAAQ,KAAK,CAAC,EAAGA,EAAQ,SAAS,CAACyG,GAAKA,EAAE,KAAK,CAAC,EAAE,GAAKwE,GAAW,GACvFzQ,EAAWkP,EAAY,IAAIlN,IAAIoO,EAAQ,GAAG,EAAG4F,EAAgBxS,EAAU,GAAMxD,EAAU6H,GACvFiO,EAAS,OAAO,CAAC,GAAG,CAAC,WAAY9V,EACnC,CACA,OAAO8V,CACT,EAlkD6DA,EAAU1F,EAASK,EAASjL,EAAShC,EAAU4H,EAAO,oBAAoB,CAC/H,CACF,MACEuK,CAAW,CAAClF,EAAQ,CAAG,MAAMwF,GAAsC9Q,EACrE,CAEF,OAAOwQ,CACT,CACA,eAAetC,GAA+BxS,CAAK,CAAE2E,CAAO,CAAE8M,CAAa,CAAE4D,CAAc,CAAE9F,CAAO,EAClG,IAAI+F,EAAiBtV,EAAM,OAAO,CAE9BuV,EAAuB3E,GAAiB,SAAU5Q,EAAOuP,EAASkC,EAAe9M,EAAS,MAC1F6Q,EAAwBC,QAAQ,GAAG,CAACJ,EAAe,GAAG,CAAC,MAAMhD,IAC/D,GAAIA,CAAAA,EAAE,OAAO,GAAIA,EAAE,KAAK,GAAIA,EAAE,UAAU,CAQtC,OAAOoD,QAAQ,OAAO,CAAC,CACrB,CAACpD,EAAE,GAAG,CAAC,CAAE,CACP,KAAM5U,EAAW,KAAK,CACtB,MAAOuN,GAAuB,IAAK,CACjC,SAAUqH,EAAE,IAAI,AAClB,EACF,CACF,EAfwC,EAExC,IAAI/N,EAASqM,AADC,OAAMC,GAAiB,SAAU5Q,EAAOwP,GAAwB/F,EAAK,OAAO,CAAE4I,EAAE,IAAI,CAAEA,EAAE,UAAU,CAAC,MAAM,EAAG,CAACA,EAAE,KAAK,CAAC,CAAEA,EAAE,OAAO,CAAEA,EAAE,GAAG,EACjI,CAACA,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAEtC,MAAO,CACL,CAACA,EAAE,GAAG,CAAC,CAAE/N,CACX,CACF,CAUF,IACIgO,EAAgB,MAAMiD,EACtBhD,EAAiB,AAAC,OAAMiD,CAAoB,EAAG,MAAM,CAAC,CAACE,EAAK1B,IAAMrW,OAAO,MAAM,CAAC+X,EAAK1B,GAAI,CAAC,GAE9F,OADA,MAAMyB,QAAQ,GAAG,CAAC,CAACE,GAAiChR,EAAS2N,EAAe/C,EAAQ,MAAM,CAAE+F,EAAgBtV,EAAM,UAAU,EAAG4V,GAA8BjR,EAAS4N,EAAgB8C,GAAgB,EAC/L,CACL/C,cAAAA,EACAC,eAAAA,CACF,CACF,CACA,SAASnC,KAEPtE,GAAyB,GAGzBC,GAAwB,IAAI,IAAI6F,MAEhCtF,GAAiB,OAAO,CAAC,CAACrG,EAAGjI,KACvBiO,GAAiB,GAAG,CAACjO,IACvBgO,GAAsB,GAAG,CAAChO,GAE5BmU,GAAanU,EACf,EACF,CACA,SAASsV,GAAmBtV,CAAG,CAAEgP,CAAO,CAAEH,CAAI,EACxCA,AAAS,KAAK,IAAdA,GACFA,CAAAA,EAAO,CAAC,GAEV7M,EAAM,QAAQ,CAAC,GAAG,CAAChC,EAAKgP,GACxBL,GAAY,CACV,SAAU,IAAIpB,IAAIvL,EAAM,QAAQ,CAClC,EAAG,CACD,UAAW,AAA6B,KAA5B6M,CAAAA,GAAQA,EAAK,SAAS,AAAD,CACnC,EACF,CACA,SAASwG,GAAgBrV,CAAG,CAAE4R,CAAO,CAAErO,CAAK,CAAEsL,CAAI,EAC5CA,AAAS,KAAK,IAAdA,GACFA,CAAAA,EAAO,CAAC,GAEV,IAAIoE,EAAgBxB,GAAoBzP,EAAM,OAAO,CAAE4P,GACvD1C,GAAclP,GACd2O,GAAY,CACV,OAAQ,CACN,CAACsE,EAAc,KAAK,CAAC,EAAE,CAAC,CAAE1P,CAC5B,EACA,SAAU,IAAIgK,IAAIvL,EAAM,QAAQ,CAClC,EAAG,CACD,UAAW,AAA6B,KAA5B6M,CAAAA,GAAQA,EAAK,SAAS,AAAD,CACnC,EACF,CACA,SAASgJ,GAAW7X,CAAG,EASrB,OARIuM,EAAO,iBAAiB,GAC1BgC,GAAe,GAAG,CAACvO,EAAK,AAACuO,CAAAA,GAAe,GAAG,CAACvO,IAAQ,GAAK,GAGrDwO,GAAgB,GAAG,CAACxO,IACtBwO,GAAgB,MAAM,CAACxO,IAGpBgC,EAAM,QAAQ,CAAC,GAAG,CAAChC,IAAQkL,CACpC,CACA,SAASgE,GAAclP,CAAG,EACxB,IAAIgP,EAAUhN,EAAM,QAAQ,CAAC,GAAG,CAAChC,EAI7BiO,CAAAA,GAAiB,GAAG,CAACjO,IAAQ,CAAEgP,CAAAA,GAAWA,AAAkB,YAAlBA,EAAQ,KAAK,EAAkBZ,GAAe,GAAG,CAACpO,EAAG,GACjGmU,GAAanU,GAEfsO,GAAiB,MAAM,CAACtO,GACxBoO,GAAe,MAAM,CAACpO,GACtBqO,GAAiB,MAAM,CAACrO,GACxBwO,GAAgB,MAAM,CAACxO,GACvBgO,GAAsB,MAAM,CAAChO,GAC7BgC,EAAM,QAAQ,CAAC,MAAM,CAAChC,EACxB,CAiBA,SAASmU,GAAanU,CAAG,EACvB,IAAI8X,EAAa7J,GAAiB,GAAG,CAACjO,GAClC8X,IACFA,EAAW,KAAK,GAChB7J,GAAiB,MAAM,CAACjO,GAE5B,CACA,SAAS+X,GAAiBC,CAAI,EAC5B,IAAK,IAAIhY,KAAOgY,EAAM,CAEpB,IAAI/B,EAAcN,GAAe3G,AADnB6I,GAAW7X,GACgB,IAAI,EAC7CgC,EAAM,QAAQ,CAAC,GAAG,CAAChC,EAAKiW,EAC1B,CACF,CACA,SAASnC,KACP,IAAImE,EAAW,EAAE,CACbpE,EAAkB,GACtB,IAAK,IAAI7T,KAAOqO,GAAkB,CAChC,IAAIW,EAAUhN,EAAM,QAAQ,CAAC,GAAG,CAAChC,GACjCsB,EAAU0N,EAAS,qBAAuBhP,GACpB,YAAlBgP,EAAQ,KAAK,GACfX,GAAiB,MAAM,CAACrO,GACxBiY,EAAS,IAAI,CAACjY,GACd6T,EAAkB,GAEtB,CAEA,OADAkE,GAAiBE,GACVpE,CACT,CACA,SAASkB,GAAqBmD,CAAQ,EACpC,IAAIC,EAAa,EAAE,CACnB,IAAK,GAAI,CAACnY,EAAKqE,EAAG,GAAI+J,GACpB,GAAI/J,EAAK6T,EAAU,CACjB,IAAIlJ,EAAUhN,EAAM,QAAQ,CAAC,GAAG,CAAChC,GACjCsB,EAAU0N,EAAS,qBAAuBhP,GACpB,YAAlBgP,EAAQ,KAAK,GACfmF,GAAanU,GACboO,GAAe,MAAM,CAACpO,GACtBmY,EAAW,IAAI,CAACnY,GAEpB,CAGF,OADA+X,GAAiBI,GACVA,EAAW,MAAM,CAAG,CAC7B,CAQA,SAASC,GAAcpY,CAAG,EACxBgC,EAAM,QAAQ,CAAC,MAAM,CAAChC,GACtB0O,GAAiB,MAAM,CAAC1O,EAC1B,CAEA,SAAS8Q,GAAc9Q,CAAG,CAAEqY,CAAU,EACpC,IAAIC,EAAUtW,EAAM,QAAQ,CAAC,GAAG,CAAChC,IAAQmL,EAGzC7J,EAAUgX,AAAkB,cAAlBA,EAAQ,KAAK,EAAoBD,AAAqB,YAArBA,EAAW,KAAK,EAAkBC,AAAkB,YAAlBA,EAAQ,KAAK,EAAkBD,AAAqB,YAArBA,EAAW,KAAK,EAAkBC,AAAkB,YAAlBA,EAAQ,KAAK,EAAkBD,AAAqB,eAArBA,EAAW,KAAK,EAAqBC,AAAkB,YAAlBA,EAAQ,KAAK,EAAkBD,AAAqB,cAArBA,EAAW,KAAK,EAAoBC,AAAkB,eAAlBA,EAAQ,KAAK,EAAqBD,AAAqB,cAArBA,EAAW,KAAK,CAAkB,qCAAuCC,EAAQ,KAAK,CAAG,OAASD,EAAW,KAAK,EACza,IAAIxI,EAAW,IAAItC,IAAIvL,EAAM,QAAQ,EACrC6N,EAAS,GAAG,CAAC7P,EAAKqY,GAClB1J,GAAY,CACVkB,SAAAA,CACF,EACF,CACA,SAASgB,GAAsB0H,CAAK,EAClC,GAAI,CACF/H,gBAAAA,CAAe,CACfC,aAAAA,CAAY,CACZE,cAAAA,CAAa,CACd,CAAG4H,EACJ,GAAI7J,AAA0B,IAA1BA,GAAiB,IAAI,CACvB,MAIEA,CAAAA,GAAiB,IAAI,CAAG,GAC1BtN,EAAQ,GAAO,gDAEjB,IAAIoX,EAAUC,MAAM,IAAI,CAAC/J,GAAiB,OAAO,IAC7C,CAACkC,EAAY8H,EAAgB,CAAGF,CAAO,CAACA,EAAQ,MAAM,CAAG,EAAE,CAC3DF,EAAUtW,EAAM,QAAQ,CAAC,GAAG,CAAC4O,GACjC,GAAI0H,CAAAA,CAAAA,GAAWA,AAAkB,eAAlBA,EAAQ,KAAK,AAAgB,GAOxCI,EAAgB,CAClBlI,gBAAAA,EACAC,aAAAA,EACAE,cAAAA,CACF,GACE,OAAOC,CAEX,CACA,SAASS,GAAsB9Q,CAAQ,EACrC,IAAIgD,EAAQyJ,GAAuB,IAAK,CACtCzM,SAAAA,CACF,GAEI,CACFoG,QAAAA,CAAO,CACPzC,MAAAA,CAAK,CACN,CAAG+I,GAJcvB,GAAsBS,GAOxC,OADAyH,KACO,CACL,gBAAiBjN,EACjBzC,MAAAA,EACAX,MAAAA,CACF,CACF,CACA,SAASqQ,GAAsB+E,CAAS,EACtC,IAAIC,EAAoB,EAAE,CAW1B,OAVAnK,GAAgB,OAAO,CAAC,CAACoK,EAAKjH,KACxB,EAAC+G,GAAaA,EAAU/G,EAAO,IAIjCiH,EAAI,MAAM,GACVD,EAAkB,IAAI,CAAChH,GACvBnD,GAAgB,MAAM,CAACmD,GAE3B,GACOgH,CACT,CAyBA,SAAS3H,GAAa9P,CAAQ,CAAEwF,CAAO,SACrC,AAAIgG,GACQA,EAAwBxL,EAAUwF,EAAQ,GAAG,CAACyG,GAAK3F,EAA2B2F,EAAGpL,EAAM,UAAU,KAC7Fb,EAAS,GAAG,AAG9B,CAOA,SAAS+O,GAAuB/O,CAAQ,CAAEwF,CAAO,EAC/C,GAAI+F,EAAsB,CAExB,IAAIoM,EAAIpM,CAAoB,CADlBuE,GAAa9P,EAAUwF,GACA,CACjC,GAAI,AAAa,UAAb,OAAOmS,EACT,OAAOA,CAEX,CACA,OAAO,IACT,CACA,SAAS3L,GAAcxG,CAAO,CAAEuK,CAAW,CAAE3Q,CAAQ,EACnD,GAAI+L,EACF,IAAI,CAAC3F,EAEH,MAAO,CACL,OAAQ,GACR,QAASoS,AAHMnU,EAAgBsM,EAAa3Q,EAAUoE,EAAU,KAGzC,EAAE,AAC3B,OAEA,GAAIhF,OAAO,IAAI,CAACgH,CAAO,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAG,EAK1C,MAAO,CACL,OAAQ,GACR,QAHmB/B,EAAgBsM,EAAa3Q,EAAUoE,EAAU,GAItE,CAEJ,CAEF,MAAO,CACL,OAAQ,GACR,QAAS,IACX,CACF,CACA,eAAe4N,GAAe5L,CAAO,CAAEpG,CAAQ,CAAEyY,CAAM,EACrD,GAAI,CAAC1M,EACH,MAAO,CACL,KAAM,UACN3F,QAAAA,CACF,EAEF,IAAIsS,EAAiBtS,EACrB,OAAa,CACX,IAAIuS,EAAWxN,AAAsB,MAAtBA,EACXwF,EAAcxF,GAAsBS,EACpCgN,EAAgBlV,EACpB,GAAI,CACF,MAAMqI,EAA4B,CAChC,KAAM/L,EACN,QAAS0Y,EACT,MAAO,CAACrH,EAASwH,KACXJ,EAAO,OAAO,EAClBK,GAAgBzH,EAASwH,EAAUlI,EAAaiI,EAAepV,EACjE,CACF,EACF,CAAE,MAAOnC,EAAG,CACV,MAAO,CACL,KAAM,QACN,MAAOA,EACPqX,eAAAA,CACF,CACF,QAAU,CAOJC,GAAY,CAACF,EAAO,OAAO,EAC7B7M,CAAAA,EAAa,IAAIA,EAAW,AAAD,CAE/B,CACA,GAAI6M,EAAO,OAAO,CAChB,MAAO,CACL,KAAM,SACR,EAEF,IAAIM,EAAa7U,EAAYyM,EAAa3Q,EAAUoE,GACpD,GAAI2U,EACF,MAAO,CACL,KAAM,UACN,QAASA,CACX,EAEF,IAAIC,EAAoB3U,EAAgBsM,EAAa3Q,EAAUoE,EAAU,IAEzE,GAAI,CAAC4U,GAAqBN,EAAe,MAAM,GAAKM,EAAkB,MAAM,EAAIN,EAAe,KAAK,CAAC,CAAC7L,EAAGvN,IAAMuN,EAAE,KAAK,CAAC,EAAE,GAAKmM,CAAiB,CAAC1Z,EAAE,CAAC,KAAK,CAAC,EAAE,EACzJ,MAAO,CACL,KAAM,UACN,QAAS,IACX,EAEFoZ,EAAiBM,CACnB,CACF,CAyDA,OAtCA3N,EAAS,CACP,IAAI,UAAW,CACb,OAAOjH,CACT,EACA,IAAI,QAAS,CACX,OAAO4H,CACT,EACA,IAAI,OAAQ,CACV,OAAOvK,CACT,EACA,IAAI,QAAS,CACX,OAAOmK,CACT,EACA,IAAI,QAAS,CACX,OAAOJ,CACT,EACAyN,WA7nDF,WAwDE,GArDAhN,EAAkBf,EAAK,OAAO,CAAC,MAAM,CAACvJ,IACpC,GAAI,CACF,OAAQyO,CAAa,CACrBxP,SAAAA,CAAQ,CACR6B,MAAAA,CAAK,CACN,CAAGd,EAGJ,GAAI4J,EAA6B,CAC/BA,IACAA,EAA8BtH,KAAAA,EAC9B,MACF,CACApD,EAAQsN,AAA0B,IAA1BA,GAAiB,IAAI,EAAU1L,AAAS,MAATA,EAAe,8YACtD,IAAI4N,EAAaC,GAAsB,CACrC,gBAAiB7O,EAAM,QAAQ,CAC/B,aAAcb,EACdwP,cAAAA,CACF,GACA,GAAIC,GAAc5N,AAAS,MAATA,EAAe,CAE/B,IAAIyW,EAA2B,IAAIhC,QAAQiC,IACzC5N,EAA8B4N,CAChC,GACAjO,EAAK,OAAO,CAAC,EAAE,CAACzI,AAAQ,GAARA,GAEhB8N,GAAcF,EAAY,CACxB,MAAO,UACPzP,SAAAA,EACA,UACE2P,GAAcF,EAAY,CACxB,MAAO,aACP,QAASpM,KAAAA,EACT,MAAOA,KAAAA,EACPrD,SAAAA,CACF,GAIAsY,EAAyB,IAAI,CAAC,IAAMhO,EAAK,OAAO,CAAC,EAAE,CAACzI,GACtD,EACA,QACE,IAAI6M,EAAW,IAAItC,IAAIvL,EAAM,QAAQ,EACrC6N,EAAS,GAAG,CAACe,EAAYzF,GACzBwD,GAAY,CACVkB,SAAAA,CACF,EACF,CACF,GACA,MACF,CACA,OAAOkB,GAAgBJ,EAAexP,EACxC,GACI6K,EAAW,KAkzGgB2N,EA/yGH5N,EA+yGY6N,EA/yGEjM,EAgzG5C,GAAI,CACF,IAAIkM,EAAmBF,EAAQ,cAAc,CAAC,OAAO,CAACpO,GACtD,GAAIsO,EAAkB,CACpB,IAAIC,EAAOzY,KAAK,KAAK,CAACwY,GACtB,IAAK,GAAI,CAAC/J,EAAGhJ,EAAE,GAAInH,OAAO,OAAO,CAACma,GAAQ,CAAC,GACrChT,GAAK2R,MAAM,OAAO,CAAC3R,IACrB8S,EAAY,GAAG,CAAC9J,EAAG,IAAIlM,IAAIkD,GAAK,EAAE,EAGxC,CACF,CAAE,MAAOlF,EAAG,CAEZ,CA3zGI,IAAImY,EAA0B,IAAMC,AA6zG1C,UAAmCL,CAAO,CAAEC,CAAW,EACrD,GAAIA,EAAY,IAAI,CAAG,EAAG,CACxB,IAAIE,EAAO,CAAC,EACZ,IAAK,GAAI,CAAChK,EAAGhJ,EAAE,GAAI8S,EACjBE,CAAI,CAAChK,EAAE,CAAG,IAAIhJ,EAAE,CAElB,GAAI,CACF6S,EAAQ,cAAc,CAAC,OAAO,CAACpO,EAAyBlK,KAAK,SAAS,CAACyY,GACzE,CAAE,MAAOvW,EAAO,CACdnC,EAAQ,GAAO,8DAAgEmC,EAAQ,KACzF,CACF,CACF,GAz0GoEwI,EAAc4B,GAC5E5B,EAAa,gBAAgB,CAAC,WAAYgO,GAC1CnM,EAA8B,IAAM7B,EAAa,mBAAmB,CAAC,WAAYgO,EACnF,CAWA,OALI,AAAC/X,EAAM,WAAW,EACpB+O,GAAgBvR,EAAO,GAAG,CAAEwC,EAAM,QAAQ,CAAE,CAC1C,iBAAkB,EACpB,GAEK4J,CACT,EAmjDEqO,UApiDF,SAAmB7W,CAAE,EAEnB,OADAqJ,EAAY,GAAG,CAACrJ,GACT,IAAMqJ,EAAY,MAAM,CAACrJ,EAClC,EAkiDE8W,wBAxKF,SAAiCC,CAAS,CAAEC,CAAW,CAAEC,CAAM,EAO7D,GANA3N,EAAuByN,EACvBvN,EAAoBwN,EACpBzN,EAA0B0N,GAAU,KAIhC,CAACxN,GAAyB7K,EAAM,UAAU,GAAKiJ,EAAiB,CAClE4B,EAAwB,GACxB,IAAIiM,EAAI5I,GAAuBlO,EAAM,QAAQ,CAAEA,EAAM,OAAO,CACxD8W,AAAK,OAALA,GACFnK,GAAY,CACV,sBAAuBmK,CACzB,EAEJ,CACA,MAAO,KACLpM,EAAuB,KACvBE,EAAoB,KACpBD,EAA0B,IAC5B,CACF,EAoJEwD,SAAAA,GACAmK,MAp2BF,SAAeta,CAAG,CAAE4R,CAAO,CAAE5Q,CAAI,CAAE6N,CAAI,EACrC,GAAI5C,EACF,MAAM,AAAIxK,MAAM,oMAElB0S,GAAanU,GACb,IAAIyP,EAAY,AAA6B,KAA5BZ,CAAAA,GAAQA,EAAK,SAAS,AAAD,EAClCqC,EAAcxF,GAAsBS,EACpCiE,EAAiBC,EAAYrO,EAAM,QAAQ,CAAEA,EAAM,OAAO,CAAE2C,EAAU4H,EAAO,kBAAkB,CAAEvL,EAAMuL,EAAO,oBAAoB,CAAEqF,EAAS/C,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,QAAQ,EAClLlI,EAAUlC,EAAYyM,EAAad,EAAgBzL,GACnDuI,EAAWC,GAAcxG,EAASuK,EAAad,GAInD,GAHIlD,EAAS,MAAM,EAAIA,EAAS,OAAO,EACrCvG,CAAAA,EAAUuG,EAAS,OAAO,AAAD,EAEvB,CAACvG,EAAS,YACZ0O,GAAgBrV,EAAK4R,EAAS5E,GAAuB,IAAK,CACxD,SAAUoD,CACZ,GAAI,CACFX,UAAAA,CACF,GAGF,GAAI,CACFtN,KAAAA,CAAI,CACJmO,WAAAA,CAAU,CACV/M,MAAAA,CAAK,CACN,CAAGgN,EAAyBhE,EAAO,sBAAsB,CAAE,GAAM6D,EAAgBvB,GAClF,GAAItL,EAAO,YACT8R,GAAgBrV,EAAK4R,EAASrO,EAAO,CACnCkM,UAAAA,CACF,GAGF,IAAInI,EAAQoL,GAAe/L,EAASxE,GAChC4N,EAAqB,AAAsC,KAArClB,CAAAA,GAAQA,EAAK,kBAAkB,AAAD,EACxD,GAAIyB,GAAcX,GAAiBW,EAAW,UAAU,EAAG,YACzD2E,GAAoBjV,EAAK4R,EAASzP,EAAMmF,EAAOX,EAASuG,EAAS,MAAM,CAAEuC,EAAWM,EAAoBO,GAK1GhC,GAAiB,GAAG,CAACtO,EAAK,CACxB4R,QAAAA,EACAzP,KAAAA,CACF,GACA+T,GAAoBlW,EAAK4R,EAASzP,EAAMmF,EAAOX,EAASuG,EAAS,MAAM,CAAEuC,EAAWM,EAAoBO,EAC1G,EAwzBEiK,WAp0CF,WAOE,GANAnI,KACAzD,GAAY,CACV,aAAc,SAChB,GAGI3M,AAA2B,eAA3BA,EAAM,UAAU,CAAC,KAAK,EAM1B,GAAIA,AAA2B,SAA3BA,EAAM,UAAU,CAAC,KAAK,CAAa,YACrC+O,GAAgB/O,EAAM,aAAa,CAAEA,EAAM,QAAQ,CAAE,CACnD,+BAAgC,EAClC,GAMF+O,GAAgBvD,GAAiBxL,EAAM,aAAa,CAAEA,EAAM,UAAU,CAAC,QAAQ,CAAE,CAC/E,mBAAoBA,EAAM,UAAU,CAEpC,qBAAsB0L,AAAiC,KAAjCA,CACxB,GACF,EA4yCE,WAAY/M,GAAM8K,EAAK,OAAO,CAAC,UAAU,CAAC9K,GAC1C,eAAgBA,GAAM8K,EAAK,OAAO,CAAC,cAAc,CAAC9K,GAClDkX,WAAAA,GACA,cAtUF,SAAqC7X,CAAG,EACtC,GAAIuM,EAAO,iBAAiB,CAAE,CAC5B,IAAIiO,EAAQ,AAACjM,CAAAA,GAAe,GAAG,CAACvO,IAAQ,GAAK,CACzCwa,CAAAA,GAAS,GACXjM,GAAe,MAAM,CAACvO,GACtBwO,GAAgB,GAAG,CAACxO,IAEpBuO,GAAe,GAAG,CAACvO,EAAKwa,EAE5B,MACEtL,GAAclP,GAEhB2O,GAAY,CACV,SAAU,IAAIpB,IAAIvL,EAAM,QAAQ,CAClC,EACF,EAwTEyY,QA5jDF,WACMjO,GACFA,IAEEoB,GACFA,IAEFnB,EAAY,KAAK,GACjBZ,GAA+BA,EAA4B,KAAK,GAChE7J,EAAM,QAAQ,CAAC,OAAO,CAAC,CAACiG,EAAGjI,IAAQkP,GAAclP,IACjDgC,EAAM,QAAQ,CAAC,OAAO,CAAC,CAACiG,EAAGjI,IAAQoY,GAAcpY,GACnD,EAkjDE0a,WA3QF,SAAoB1a,CAAG,CAAEoD,CAAE,EACzB,IAAIkV,EAAUtW,EAAM,QAAQ,CAAC,GAAG,CAAChC,IAAQmL,EAIzC,OAHIuD,GAAiB,GAAG,CAAC1O,KAASoD,GAChCsL,GAAiB,GAAG,CAAC1O,EAAKoD,GAErBkV,CACT,EAsQEF,cAAAA,GACAuC,YA7CF,SAAqB/I,CAAO,CAAEwH,CAAQ,EACpC,IAAIF,EAAWxN,AAAsB,MAAtBA,EAEf2N,GAAgBzH,EAASwH,EADP1N,GAAsBS,EACQlI,EAAUF,GAMtDmV,IACF/M,EAAa,IAAIA,EAAW,CAC5BwC,GAAY,CAAC,GAEjB,EAiCE,0BAA2BV,GAC3B,yBAA0BQ,GAG1BmM,mBAtDF,SAA4BC,CAAS,EAEnCnP,EAAqB7H,EAA0BgX,EAAW9W,EAAoBS,KAAAA,EAD9EP,EAAW,CAAC,EAEd,CAoDA,CAEF,CAwbA,SAASoM,EAAYlP,CAAQ,CAAEwF,CAAO,CAAEhC,CAAQ,CAAEmW,CAAe,CAAEna,CAAE,CAAEqI,CAAoB,CAAE+R,CAAW,CAAEC,CAAQ,MAC5GC,EACAC,EACJ,GAAIH,EAIF,KAAK,IAAIzT,KADT2T,EAAoB,EAAE,CACJtU,GAEhB,GADAsU,EAAkB,IAAI,CAAC3T,GACnBA,EAAM,KAAK,CAAC,EAAE,GAAKyT,EAAa,CAClCG,EAAmB5T,EACnB,KACF,CACF,MAEA2T,EAAoBtU,EACpBuU,EAAmBvU,CAAO,CAACA,EAAQ,MAAM,CAAG,EAAE,CAGhD,IAAIxE,EAAOgH,EAAUxI,GAAU,IAAKoI,EAAoBkS,EAAmBjS,GAAuBlE,EAAc3D,EAAS,QAAQ,CAAEwD,IAAaxD,EAAS,QAAQ,CAAE6Z,AAAa,SAAbA,GASnK,GALU,MAANra,IACFwB,EAAK,MAAM,CAAGhB,EAAS,MAAM,CAC7BgB,EAAK,IAAI,CAAGhB,EAAS,IAAI,EAGvB,AAACR,CAAAA,AAAM,MAANA,GAAcA,AAAO,KAAPA,GAAaA,AAAO,MAAPA,CAAS,GAAMua,EAAkB,CAC/D,IAAIC,EAAaC,GAAmBjZ,EAAK,MAAM,EAC/C,GAAI+Y,EAAiB,KAAK,CAAC,KAAK,EAAI,CAACC,EAEnChZ,EAAK,MAAM,CAAGA,EAAK,MAAM,CAAGA,EAAK,MAAM,CAAC,OAAO,CAAC,MAAO,WAAa,cAC/D,GAAI,CAAC+Y,EAAiB,KAAK,CAAC,KAAK,EAAIC,EAAY,CAEtD,IAAIxT,EAAS,IAAI0T,gBAAgBlZ,EAAK,MAAM,EACxCmZ,EAAc3T,EAAO,MAAM,CAAC,SAChCA,EAAO,MAAM,CAAC,SACd2T,EAAY,MAAM,CAACxU,GAAKA,GAAG,OAAO,CAACA,GAAKa,EAAO,MAAM,CAAC,QAASb,IAC/D,IAAIyU,EAAK5T,EAAO,QAAQ,EACxBxF,CAAAA,EAAK,MAAM,CAAGoZ,EAAK,IAAMA,EAAK,EAChC,CACF,CAQA,OAHIT,GAAmBnW,AAAa,MAAbA,GACrBxC,CAAAA,EAAK,QAAQ,CAAGA,AAAkB,MAAlBA,EAAK,QAAQ,CAAWwC,EAAWY,EAAU,CAACZ,EAAUxC,EAAK,QAAQ,CAAC,GAEjFvB,EAAWuB,EACpB,CAGA,SAASoO,EAAyBiL,CAAmB,CAAEC,CAAS,CAAEtZ,CAAI,CAAE0M,CAAI,MA8/BrD6M,MAt7BjBC,EACAC,EAvEJ,GAAI,CAAC/M,GAAQ,CA1DNA,CAAAA,AAAQ,MA0DsBA,GA1Db,cA0DaA,GA1DSA,AAAiB,MAAjBA,AA0DTA,EA1Dc,QAAQ,EAAY,SA0DlCA,GA1DoDA,AAAcrK,KAAAA,IAAdqK,AA0DpDA,EA1DyD,IAAI,AAAa,CAAC,EA2D9G,MAAO,CACL1M,KAAAA,CACF,EAEF,GAAI0M,EAAK,UAAU,GAu/BE6M,EAv/BiB7M,EAAK,UAAU,EAw/B9C/D,EAAoB,GAAG,CAAC4Q,EAAO,WAAW,KAv/B/C,MAAO,CACLvZ,KAAAA,EACA,MAAO6K,GAAuB,IAAK,CACjC,OAAQ6B,EAAK,UAAU,AACzB,EACF,EAEF,IAAIgN,EAAsB,IAAO,EAC/B1Z,KAAAA,EACA,MAAO6K,GAAuB,IAAK,CACjC,KAAM,cACR,EACF,GAEI8O,EAAgBjN,EAAK,UAAU,EAAI,MACnC4H,EAAa+E,EAAsBM,EAAc,WAAW,GAAKA,EAAc,WAAW,GAC1FpF,EAAaqF,GAAkB5Z,GACnC,GAAI0M,AAAcrK,KAAAA,IAAdqK,EAAK,IAAI,CACX,IAAIA,AAAqB,eAArBA,EAAK,WAAW,CAAmB,CAErC,GAAI,CAACc,GAAiB8G,GACpB,OAAOoF,IAET,IAAIG,EAAO,AAAqB,UAArB,OAAOnN,EAAK,IAAI,CAAgBA,EAAK,IAAI,CAAGA,EAAK,IAAI,YAAYoN,UAAYpN,EAAK,IAAI,YAAYwM,gBAE7G5C,MAAM,IAAI,CAAC5J,EAAK,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC6I,EAAKwE,KAC3C,GAAI,CAACC,EAAM5a,EAAM,CAAG2a,EACpB,MAAO,GAAKxE,EAAMyE,EAAO,IAAM5a,EAAQ,IACzC,EAAG,IAAM6C,OAAOyK,EAAK,IAAI,EACzB,MAAO,CACL1M,KAAAA,EACA,WAAY,CACVsU,WAAAA,EACAC,WAAAA,EACA,YAAa7H,EAAK,WAAW,CAC7B,SAAUrK,KAAAA,EACV,KAAMA,KAAAA,EACNwX,KAAAA,CACF,CACF,CACF,MAAO,GAAInN,AAAqB,qBAArBA,EAAK,WAAW,CAAyB,CAElD,GAAI,CAACc,GAAiB8G,GACpB,OAAOoF,IAET,GAAI,CACF,IAAI/B,EAAO,AAAqB,UAArB,OAAOjL,EAAK,IAAI,CAAgBxN,KAAK,KAAK,CAACwN,EAAK,IAAI,EAAIA,EAAK,IAAI,CAC5E,MAAO,CACL1M,KAAAA,EACA,WAAY,CACVsU,WAAAA,EACAC,WAAAA,EACA,YAAa7H,EAAK,WAAW,CAC7B,SAAUrK,KAAAA,EACVsV,KAAAA,EACA,KAAMtV,KAAAA,CACR,CACF,CACF,CAAE,MAAO5C,EAAG,CACV,OAAOia,GACT,CACF,EAKF,GAHAva,EAAU,AAAoB,YAApB,OAAO2a,SAAyB,iDAGtCpN,EAAK,QAAQ,CACf8M,EAAeS,GAA8BvN,EAAK,QAAQ,EAC1D+M,EAAW/M,EAAK,QAAQ,MACnB,GAAIA,EAAK,IAAI,YAAYoN,SAC9BN,EAAeS,GAA8BvN,EAAK,IAAI,EACtD+M,EAAW/M,EAAK,IAAI,MACf,GAAIA,EAAK,IAAI,YAAYwM,gBAE9BO,EAAWS,GADXV,EAAe9M,EAAK,IAAI,OAEnB,GAAIA,AAAa,MAAbA,EAAK,IAAI,CAClB8M,EAAe,IAAIN,gBACnBO,EAAW,IAAIK,cAEf,GAAI,CACFN,EAAe,IAAIN,gBAAgBxM,EAAK,IAAI,EAC5C+M,EAAWS,GAA8BV,EAC3C,CAAE,MAAO/Z,EAAG,CACV,OAAOia,GACT,CAEF,IAAIvL,EAAa,CACfmG,WAAAA,EACAC,WAAAA,EACA,YAAa7H,GAAQA,EAAK,WAAW,EAAI,oCACzC+M,SAAAA,EACA,KAAMpX,KAAAA,EACN,KAAMA,KAAAA,CACR,EACA,GAAImL,GAAiBW,EAAW,UAAU,EACxC,MAAO,CACLnO,KAAAA,EACAmO,WAAAA,CACF,EAGF,IAAIlO,EAAatB,EAAUqB,GAQ3B,OAJIsZ,GAAarZ,EAAW,MAAM,EAAIgZ,GAAmBhZ,EAAW,MAAM,GACxEuZ,EAAa,MAAM,CAAC,QAAS,IAE/BvZ,EAAW,MAAM,CAAG,IAAMuZ,EACnB,CACL,KAAM/a,EAAWwB,GACjBkO,WAAAA,CACF,CACF,CAGA,SAASgM,EAA8B3V,CAAO,CAAE6L,CAAU,CAAE+J,CAAe,EACrEA,AAAoB,KAAK,IAAzBA,GACFA,CAAAA,EAAkB,EAAI,EAExB,IAAIza,EAAQ6E,EAAQ,SAAS,CAACyG,GAAKA,EAAE,KAAK,CAAC,EAAE,GAAKoF,UAClD,AAAI1Q,GAAS,EACJ6E,EAAQ,KAAK,CAAC,EAAG4V,EAAkBza,EAAQ,EAAIA,GAEjD6E,CACT,CACA,SAASgN,EAAiB1Q,CAAO,CAAEjB,CAAK,CAAE2E,CAAO,CAAE2J,CAAU,CAAEnP,CAAQ,CAAEiS,CAAgB,CAAEoJ,CAA2B,CAAE1O,CAAsB,CAAEC,CAAuB,CAAEC,CAAqB,CAAEQ,CAAe,CAAEF,CAAgB,CAAED,CAAgB,CAAE6C,CAAW,CAAEvM,CAAQ,CAAEqM,CAAmB,EAC7R,IAAIU,EAAeV,EAAsBa,GAAcb,CAAmB,CAAC,EAAE,EAAIA,CAAmB,CAAC,EAAE,CAAC,KAAK,CAAGA,CAAmB,CAAC,EAAE,CAAC,IAAI,CAAGxM,KAAAA,EAC1IiY,EAAaxZ,EAAQ,SAAS,CAACjB,EAAM,QAAQ,EAC7C0a,EAAUzZ,EAAQ,SAAS,CAAC9B,GAE5Bwb,EAAkBhW,CAClByM,CAAAA,GAAoBpR,EAAM,MAAM,CAMlC2a,EAAkBL,EAA8B3V,EAAShH,OAAO,IAAI,CAACqC,EAAM,MAAM,CAAC,CAAC,EAAE,CAAE,IAC9EgP,GAAuBa,GAAcb,CAAmB,CAAC,EAAE,GAGpE2L,CAAAA,EAAkBL,EAA8B3V,EAASqK,CAAmB,CAAC,EAAE,GAKjF,IAAI4L,EAAe5L,EAAsBA,CAAmB,CAAC,EAAE,CAAC,UAAU,CAAGxM,KAAAA,EACzEqY,EAAyBL,GAA+BI,GAAgBA,GAAgB,IACxFE,EAAoBH,EAAgB,MAAM,CAAC,CAACrV,EAAOxF,SAqIpCib,EAAmBC,EAAc1V,EApIlD,IAqIE2V,EAOAC,EA5IE,CACFhZ,MAAAA,CAAK,CACN,CAAGoD,EACJ,GAAIpD,EAAM,IAAI,CAEZ,MAAO,GAET,GAAIA,AAAgB,MAAhBA,EAAM,MAAM,CACd,MAAO,GAET,GAAIkP,EACF,OAAO9F,GAA2BpJ,EAAOlC,EAAM,UAAU,CAAEA,EAAM,MAAM,EAGzE,GAAImb,AAsHaJ,EAtHD/a,EAAM,UAAU,CAsHIgb,EAtHFhb,EAAM,OAAO,CAACF,EAAM,CAsHJwF,EAtHMA,EAuHtD2V,EAEJ,CAACD,GAED1V,EAAM,KAAK,CAAC,EAAE,GAAK0V,EAAa,KAAK,CAAC,EAAE,CAGpCE,EAAgBH,AAAsCvY,KAAAA,IAAtCuY,CAAiB,CAACzV,EAAM,KAAK,CAAC,EAAE,CAAC,CAE9C2V,GAASC,GAhIoDnP,EAAwB,IAAI,CAAC1J,GAAMA,IAAOiD,EAAM,KAAK,CAAC,EAAE,EACxH,MAAO,GAMT,IAAI8V,EAAoBpb,EAAM,OAAO,CAACF,EAAM,CAE5C,OAAOub,GAAuB/V,EAAO5H,EAAS,CAC5C+c,WAAAA,EACA,cAAeW,EAAkB,MAAM,CACvCV,QAAAA,EACA,WAAYY,AALOhW,EAKQ,MAAM,AACnC,EAAGgJ,EAAY,CACboB,aAAAA,EACAkL,aAAAA,EACA,wBAAyBC,CAAAA,GAEzB/O,CAAAA,GAA0B2O,EAAW,QAAQ,CAAGA,EAAW,MAAM,GAAKC,EAAQ,QAAQ,CAAGA,EAAQ,MAAM,EAEvGD,EAAW,MAAM,GAAKC,EAAQ,MAAM,EAAIa,GAAmBH,EAbxC9V,EAayE,CAC9F,GACF,GAEIoM,EAAuB,EAAE,CAqE7B,OApEApF,EAAiB,OAAO,CAAC,CAAC+F,EAAGrU,KAM3B,GAAIoT,GAAoB,CAACzM,EAAQ,IAAI,CAACyG,GAAKA,EAAE,KAAK,CAAC,EAAE,GAAKiH,EAAE,OAAO,GAAK7F,EAAgB,GAAG,CAACxO,GAC1F,OAEF,IAAIwd,EAAiB/Y,EAAYyM,EAAamD,EAAE,IAAI,CAAE1P,GAKtD,GAAI,CAAC6Y,EAAgB,YACnB9J,EAAqB,IAAI,CAAC,CACxB1T,IAAAA,EACA,QAASqU,EAAE,OAAO,CAClB,KAAMA,EAAE,IAAI,CACZ,QAAS,KACT,MAAO,KACP,WAAY,IACd,GAMF,IAAIrF,EAAUhN,EAAM,QAAQ,CAAC,GAAG,CAAChC,GAC7Byd,EAAe/K,GAAe8K,EAAgBnJ,EAAE,IAAI,EACpDqJ,EAAmB,GACnBrP,EAAiB,GAAG,CAACrO,GAEvB0d,EAAmB,GACV1P,EAAsB,GAAG,CAAChO,IAEnCgO,EAAsB,MAAM,CAAChO,GAC7B0d,EAAmB,IAKnBA,EAJS1O,GAAWA,AAAkB,SAAlBA,EAAQ,KAAK,EAAeA,AAAiBxK,KAAAA,IAAjBwK,EAAQ,IAAI,CAIzClB,EAIAuP,GAAuBI,EAAc/d,EAAS,CAC/D+c,WAAAA,EACA,cAAeza,EAAM,OAAO,CAACA,EAAM,OAAO,CAAC,MAAM,CAAG,EAAE,CAAC,MAAM,CAC7D0a,QAAAA,EACA,WAAY/V,CAAO,CAACA,EAAQ,MAAM,CAAG,EAAE,CAAC,MAAM,AAChD,EAAG2J,EAAY,CACboB,aAAAA,EACAkL,aAAAA,EACA,wBAAyBC,CAAAA,GAAiC/O,CAC5D,IAEE4P,GACFhK,EAAqB,IAAI,CAAC,CACxB1T,IAAAA,EACA,QAASqU,EAAE,OAAO,CAClB,KAAMA,EAAE,IAAI,CACZ,QAASmJ,EACT,MAAOC,EACP,WAAY,IAAInM,eAClB,EAEJ,GACO,CAACwL,EAAmBpJ,EAAqB,AAClD,CACA,SAASpG,GAA2BpJ,CAAK,CAAEwD,CAAU,CAAE2F,CAAM,EAE3D,GAAInJ,EAAM,IAAI,CACZ,MAAO,GAGT,GAAI,CAACA,EAAM,MAAM,CACf,MAAO,GAET,IAAIyZ,EAAUjW,AAAc,MAAdA,GAAsBA,AAAyBlD,KAAAA,IAAzBkD,CAAU,CAACxD,EAAM,EAAE,CAAC,CACpD0Z,EAAWvQ,AAAU,MAAVA,GAAkBA,AAAqB7I,KAAAA,IAArB6I,CAAM,CAACnJ,EAAM,EAAE,CAAC,OAEjD,AAAI,GAACyZ,IAAWC,CAAO,IAIK,YAAxB,OAAO1Z,EAAM,MAAM,EAAmBA,AAAyB,KAAzBA,EAAM,MAAM,CAAC,OAAO,EAIvD,CAACyZ,GAAW,CAACC,EACtB,CAaA,SAASL,GAAmBP,CAAY,CAAE1V,CAAK,EAC7C,IAAIuW,EAAcb,EAAa,KAAK,CAAC,IAAI,CACzC,OAEEA,EAAa,QAAQ,GAAK1V,EAAM,QAAQ,EAGxCuW,AAAe,MAAfA,GAAuBA,EAAY,QAAQ,CAAC,MAAQb,EAAa,MAAM,CAAC,IAAI,GAAK1V,EAAM,MAAM,CAAC,IAAI,AAEtG,CACA,SAAS+V,GAAuBS,CAAW,CAAEC,CAAG,EAC9C,GAAID,EAAY,KAAK,CAAC,gBAAgB,CAAE,CACtC,IAAIE,EAAcF,EAAY,KAAK,CAAC,gBAAgB,CAACC,GACrD,GAAI,AAAuB,WAAvB,OAAOC,EACT,OAAOA,CAEX,CACA,OAAOD,EAAI,uBAAuB,AACpC,CACA,SAAS1E,GAAgBzH,CAAO,CAAEwH,CAAQ,CAAElI,CAAW,CAAEjN,CAAQ,CAAEF,CAAkB,MAC/Eka,MACAC,EACJ,GAAItM,EAAS,CACX,IAAI1N,EAAQD,CAAQ,CAAC2N,EAAQ,CAC7BtQ,EAAU4C,EAAO,oDAAsD0N,GACnE,AAAC1N,EAAM,QAAQ,EACjBA,CAAAA,EAAM,QAAQ,CAAG,EAAE,AAAD,EAEpBga,EAAkBha,EAAM,QAAQ,AAClC,MACEga,EAAkBhN,EAMpB,IAAI2J,EAAYhX,EADKuV,EAAS,MAAM,CAAC+E,GAAY,CAACD,EAAgB,IAAI,CAACE,GAAiBC,AAI1F,UAASA,EAAYF,CAAQ,CAAEC,CAAa,QAE1C,AAAI,OAAQD,GAAY,OAAQC,GAAiBD,EAAS,EAAE,GAAKC,EAAc,EAAE,EAI3ED,EAAS,KAAK,GAAKC,EAAc,KAAK,EAAID,EAAS,IAAI,GAAKC,EAAc,IAAI,EAAID,EAAS,aAAa,GAAKC,EAAc,aAAa,GAKzI,EAACD,EAAS,QAAQ,EAAIA,AAA6B,IAA7BA,EAAS,QAAQ,CAAC,MAAM,AAAK,GAAO,EAACC,EAAc,QAAQ,EAAIA,AAAkC,IAAlCA,EAAc,QAAQ,CAAC,MAAM,AAAK,GAKrHD,EAAS,QAAQ,CAAC,KAAK,CAAC,CAACG,EAAQze,KACtC,IAAI0e,EACJ,OAAO,AAAoD,MAAnDA,CAAAA,EAAwBH,EAAc,QAAQ,AAAD,EAAa,KAAK,EAAIG,EAAsB,IAAI,CAACC,GAAUH,EAAYC,EAAQE,GACtI,GACF,GAxBsGL,EAAUC,KACpDra,EAAoB,CAAC6N,GAAW,IAAK,QAASxN,OAAO,AAAC,CAAwC,MAAvC6Z,CAAAA,EAAmBC,CAAc,EAAa,KAAK,EAAID,EAAiB,MAAM,AAAD,GAAM,KAAK,CAAEha,GAC3Mia,EAAgB,IAAI,IAAIrD,EAC1B,CA2BA,eAAe4D,GAAoBva,CAAK,CAAEH,CAAkB,CAAEE,CAAQ,EACpE,GAAI,CAACC,EAAM,IAAI,CACb,OAEF,IAAIwa,EAAY,MAAMxa,EAAM,IAAI,GAIhC,GAAI,CAACA,EAAM,IAAI,CACb,OAEF,IAAIya,EAAgB1a,CAAQ,CAACC,EAAM,EAAE,CAAC,CACtC5C,EAAUqd,EAAe,8BASzB,IAAIC,EAAe,CAAC,EACpB,IAAK,IAAIC,KAAqBH,EAAW,CAEvC,IAAII,EAA8BC,AAAqBva,KAAAA,IADhCma,CAAa,CAACE,EAAkB,EAIvDA,AAAsB,qBAAtBA,EACAzd,EAAQ,CAAC0d,EAA6B,UAAaH,EAAc,EAAE,CAAG,4BAAgCE,EAAhE,yGAA4MA,EAAoB,sBAClQ,AAACC,GAAgCnb,EAAmB,GAAG,CAACkb,IAC1DD,CAAAA,CAAY,CAACC,EAAkB,CAAGH,CAAS,CAACG,EAAkB,AAAD,CAEjE,CAGAlf,OAAO,MAAM,CAACgf,EAAeC,GAI7Bjf,OAAO,MAAM,CAACgf,EAAejf,EAAS,CAAC,EAAGqE,EAAmB4a,GAAgB,CAC3E,KAAMna,KAAAA,CACR,GACF,CAEA,eAAe6H,GAAoB2S,CAAK,EACtC,GAAI,CACFrY,QAAAA,CAAO,CACR,CAAGqY,EACAvL,EAAgB9M,EAAQ,MAAM,CAACyG,GAAKA,EAAE,UAAU,EAEpD,MAAOuF,AADO,OAAM8E,QAAQ,GAAG,CAAChE,EAAc,GAAG,CAACrG,GAAKA,EAAE,OAAO,IAAG,EACpD,MAAM,CAAC,CAACsK,EAAKpR,EAAQzG,IAAMF,OAAO,MAAM,CAAC+X,EAAK,CAC3D,CAACjE,CAAa,CAAC5T,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAEyG,CAC/B,GAAI,CAAC,EACP,CACA,eAAeyQ,GAAqB3K,CAAgB,CAAEwK,CAAI,CAAE5U,CAAK,CAAEuP,CAAO,CAAEkC,CAAa,CAAE9M,CAAO,CAAEkQ,CAAU,CAAE5S,CAAQ,CAAEF,CAAkB,CAAEkb,CAAc,EAC1J,IAAIC,EAA+BvY,EAAQ,GAAG,CAACyG,GAAKA,EAAE,KAAK,CAAC,IAAI,CAAGqR,GAAoBrR,EAAE,KAAK,CAAErJ,EAAoBE,GAAYO,KAAAA,GAC5H2a,EAAYxY,EAAQ,GAAG,CAAC,CAACW,EAAOzH,KAClC,IAAIuf,EAAmBF,CAA4B,CAACrf,EAAE,CAClDwf,EAAa5L,EAAc,IAAI,CAACrG,GAAKA,EAAE,KAAK,CAAC,EAAE,GAAK9F,EAAM,KAAK,CAAC,EAAE,EAKlEoS,EAAU,MAAM4F,IACdA,GAAmB/N,AAAmB,QAAnBA,EAAQ,MAAM,EAAejK,CAAAA,EAAM,KAAK,CAAC,IAAI,EAAIA,EAAM,KAAK,CAAC,MAAM,AAAD,GACvF+X,CAAAA,EAAa,EAAG,EAEXA,EAAaE,GAAmB3I,EAAMrF,EAASjK,EAAO8X,EAAkBE,EAAiBL,GAAkBxH,QAAQ,OAAO,CAAC,CAChI,KAAMhY,EAAW,IAAI,CACrB,OAAQ+E,KAAAA,CACV,IAEF,OAAO9E,EAAS,CAAC,EAAG4H,EAAO,CACzB+X,WAAAA,EACA3F,QAAAA,CACF,EACF,GAII/G,EAAU,MAAMvG,EAAiB,CACnC,QAAS+S,EACT5N,QAAAA,EACA,OAAQ5K,CAAO,CAAC,EAAE,CAAC,MAAM,CACzBkQ,WAAAA,EACA,QAASoI,CACX,GAIA,GAAI,CACF,MAAMxH,QAAQ,GAAG,CAACyH,EACpB,CAAE,MAAOtd,EAAG,CAEZ,CACA,OAAO+Q,CACT,CAEA,eAAe4M,GAAmB3I,CAAI,CAAErF,CAAO,CAAEjK,CAAK,CAAE8X,CAAgB,CAAEE,CAAe,CAAEE,CAAa,EAGtG,IAFIlZ,EACAmZ,EACAC,EAAaC,IAKf,IAHIC,EAGAC,EAAe,IAAIpI,QAAQ,CAACxP,EAAG+N,IAAM4J,EAAS5J,GAClDyJ,EAAW,IAAMG,IACjBrO,EAAQ,MAAM,CAAC,gBAAgB,CAAC,QAASkO,GACzC,IAAIK,EAAgBC,GAClB,AAAI,AAAmB,YAAnB,OAAOJ,EACFlI,QAAQ,MAAM,CAAC,AAAIhW,MAAM,mEAAsE,KAAOmV,EAAO,cAAc,EAAItP,EAAM,KAAK,CAAC,EAAE,CAAG,MAElJqY,EAAQ,CACbpO,QAAAA,EACA,OAAQjK,EAAM,MAAM,CACpB,QAASkY,CACX,KAAOO,AAAQvb,KAAAA,IAARub,EAAoB,CAACA,EAAI,CAAG,EAAE,EAgBvC,OAAOtI,QAAQ,IAAI,CAAC,CAdC,AAAC,WACpB,GAAI,CACF,IAAIuI,EAAM,MAAOV,CAAAA,EAAkBA,EAAgBS,GAAOD,EAAcC,IAAQD,GAAc,EAC9F,MAAO,CACL,KAAM,OACN,OAAQE,CACV,CACF,CAAE,MAAOpe,EAAG,CACV,MAAO,CACL,KAAM,QACN,OAAQA,CACV,CACF,CACF,KACqCie,EAAa,CACpD,EACA,GAAI,CACF,IAAIF,EAAUrY,EAAM,KAAK,CAACsP,EAAK,CAE/B,GAAIwI,EACF,GAAIO,EAAS,CAGX,IADIM,EACA,CAAC1e,EAAM,CAAG,MAAMkW,QAAQ,GAAG,CAAC,CAIhCiI,EAAWC,GAAS,KAAK,CAAC/d,IACxBqe,EAAere,CACjB,GAAIwd,EAAiB,EACrB,GAAIa,AAAiBzb,KAAAA,IAAjByb,EACF,MAAMA,EAER3Z,EAAS/E,CACX,MAIE,GAFA,MAAM6d,EACNO,EAAUrY,EAAM,KAAK,CAACsP,EAAK,CAKzBtQ,EAAS,MAAMoZ,EAAWC,OACE,CAAvB,GAAI/I,AAAS,WAATA,EAWT,MAAO,CACL,KAAMnX,EAAW,IAAI,CACrB,OAAQ+E,KAAAA,CACV,EAbA,IAAIvD,EAAM,IAAIkC,IAAIoO,EAAQ,GAAG,EACzBhR,EAAWU,EAAI,QAAQ,CAAGA,EAAI,MAAM,AACxC,OAAM+L,GAAuB,IAAK,CAChC,OAAQuE,EAAQ,MAAM,CACtBhR,SAAAA,EACA,QAAS+G,EAAM,KAAK,CAAC,EAAE,AACzB,EACF,MASG,GAAKqY,EAOVrZ,EAAS,MAAMoZ,EAAWC,OAPP,CACnB,IAAI1e,EAAM,IAAIkC,IAAIoO,EAAQ,GAAG,EACzBhR,EAAWU,EAAI,QAAQ,CAAGA,EAAI,MAAM,AACxC,OAAM+L,GAAuB,IAAK,CAChCzM,SAAAA,CACF,EACF,CAGAe,EAAUgF,AAAkB9B,KAAAA,IAAlB8B,EAAO,MAAM,CAAgB,eAAkBsQ,CAAAA,AAAS,WAATA,EAAoB,YAAc,UAAS,EAAK,cAAiB,KAAOtP,EAAM,KAAK,CAAC,EAAE,CAAG,2CAA0C,EAAIsP,EAAzJ,+CACzC,CAAE,MAAOhV,EAAG,CAIV,MAAO,CACL,KAAMnC,EAAW,KAAK,CACtB,OAAQmC,CACV,CACF,QAAU,CACJ6d,GACFlO,EAAQ,MAAM,CAAC,mBAAmB,CAAC,QAASkO,EAEhD,CACA,OAAOnZ,CACT,CACA,eAAe8Q,GAAsC8I,CAAkB,MA2C7DC,EAEEC,EAiBJC,EAAeC,EASfC,EAAeC,EA6WCjf,EAnbtB,GAAI,CACF+E,OAAAA,CAAM,CACNsQ,KAAAA,CAAI,CACL,CAAGsJ,EACJ,GAAIlJ,GAAW1Q,GAAS,CACtB,IAAImE,EACJ,GAAI,CACF,IAAIgW,EAAcna,EAAO,OAAO,CAAC,GAAG,CAAC,gBAKjCmE,EAFAgW,GAAe,wBAAwB,IAAI,CAACA,GAC1Cna,AAAe,MAAfA,EAAO,IAAI,CACN,KAEA,MAAMA,EAAO,IAAI,GAGnB,MAAMA,EAAO,IAAI,EAE5B,CAAE,MAAO1E,EAAG,CACV,MAAO,CACL,KAAMnC,EAAW,KAAK,CACtB,MAAOmC,CACT,CACF,QACA,AAAIgV,IAASnX,EAAW,KAAK,CACpB,CACL,KAAMA,EAAW,KAAK,CACtB,MAAO,IAAI6K,EAAkBhE,EAAO,MAAM,CAAEA,EAAO,UAAU,CAAEmE,GAC/D,WAAYnE,EAAO,MAAM,CACzB,QAASA,EAAO,OAAO,AACzB,EAEK,CACL,KAAM7G,EAAW,IAAI,CACrBgL,KAAAA,EACA,WAAYnE,EAAO,MAAM,CACzB,QAASA,EAAO,OAAO,AACzB,CACF,CACA,GAAIsQ,IAASnX,EAAW,KAAK,CAAE,CAC7B,GAAIihB,GAAuBpa,GAAS,CAElC,GAAIA,EAAO,IAAI,YAAY7E,MAEzB,MAAO,CACL,KAAMhC,EAAW,KAAK,CACtB,MAAO6G,EAAO,IAAI,CAClB,WAAY,AAAgC,MAA/B8Z,CAAAA,EAAe9Z,EAAO,IAAI,AAAD,EAAa,KAAK,EAAI8Z,EAAa,MAAM,AACjF,EAGF9Z,EAAS,IAAIgE,EAAkB,AAAC,CAAiC,MAAhC6V,CAAAA,EAAgB7Z,EAAO,IAAI,AAAD,EAAa,KAAK,EAAI6Z,EAAc,MAAM,AAAD,GAAM,IAAK3b,KAAAA,EAAW8B,EAAO,IAAI,CACvI,CACA,MAAO,CACL,KAAM7G,EAAW,KAAK,CACtB,MAAO6G,EACP,WAAYqE,EAAqBrE,GAAUA,EAAO,MAAM,CAAG9B,KAAAA,CAC7D,CACF,OACA,AAyXOmc,CAFepf,EAvXH+E,IAyXA,AAAoB,UAApB,OADJ/E,GACoC,AAAyB,UAAzB,OAAOof,AAD3Cpf,EACoD,IAAI,EAAiB,AAA8B,YAA9B,OAAOof,AADhFpf,EACyF,SAAS,EAAmB,AAA2B,YAA3B,OAAOof,AAD5Hpf,EACqI,MAAM,EAAmB,AAAgC,YAAhC,OAAOof,AADrKpf,EAC8K,WAAW,CAvX/L,CACL,KAAM9B,EAAW,QAAQ,CACzB,aAAc6G,EACd,WAAY,AAAiC,MAAhC+Z,CAAAA,EAAgB/Z,EAAO,IAAI,AAAD,EAAa,KAAK,EAAI+Z,EAAc,MAAM,CACjF,QAAS,AAAC,CAAiC,MAAhCC,CAAAA,EAAgBha,EAAO,IAAI,AAAD,EAAa,KAAK,EAAIga,EAAc,OAAO,AAAD,GAAM,IAAIM,QAAQta,EAAO,IAAI,CAAC,OAAO,CACtH,EAEEoa,GAAuBpa,GAElB,CACL,KAAM7G,EAAW,IAAI,CACrB,KAAM6G,EAAO,IAAI,CACjB,WAAY,AAAiC,MAAhCia,CAAAA,EAAgBja,EAAO,IAAI,AAAD,EAAa,KAAK,EAAIia,EAAc,MAAM,CACjF,QAAS,AAAiC,MAAhCC,CAAAA,EAAgBla,EAAO,IAAI,AAAD,GAAcka,EAAc,OAAO,CAAG,IAAII,QAAQta,EAAO,IAAI,CAAC,OAAO,EAAI9B,KAAAA,CAC/G,EAEK,CACL,KAAM/E,EAAW,IAAI,CACrB,KAAM6G,CACR,CACF,CAYA,SAASwM,GAA0B3R,CAAQ,CAAEsb,CAAU,CAAE9X,CAAQ,EAC/D,GAAIyG,EAAmB,IAAI,CAACjK,GAAW,CAGrC,IAAIF,MAAgDkC,IAA1C0d,AADe1f,EACI,UAAU,CAAC,MAAgBsb,EAAW,QAAQ,CADlDtb,EAAAA,GAErB2f,EAAiBhc,AAAyC,MAAzCA,EAAc7D,EAAI,QAAQ,CAAE0D,GACjD,GAAI1D,EAAI,MAAM,GAAKwb,EAAW,MAAM,EAAIqE,EACtC,OAAO7f,EAAI,QAAQ,CAAGA,EAAI,MAAM,CAAGA,EAAI,IAAI,AAE/C,CACA,OAAOE,CACT,CAIA,SAASqQ,GAAwBvO,CAAO,CAAE9B,CAAQ,CAAE6X,CAAM,CAAE1I,CAAU,EACpE,IAAIrP,EAAMgC,EAAQ,SAAS,CAAC8Y,GAAkB5a,IAAW,QAAQ,GAC7DsK,EAAO,CACTuN,OAAAA,CACF,EACA,GAAI1I,GAAcX,GAAiBW,EAAW,UAAU,EAAG,CACzD,GAAI,CACFmG,WAAAA,CAAU,CACVE,YAAAA,CAAW,CACZ,CAAGrG,CAIJ7E,CAAAA,EAAK,MAAM,CAAGgL,EAAW,WAAW,GAChCE,AAAgB,qBAAhBA,GACFlL,EAAK,OAAO,CAAG,IAAImV,QAAQ,CACzB,eAAgBjK,CAClB,GACAlL,EAAK,IAAI,CAAGpK,KAAK,SAAS,CAACiP,EAAW,IAAI,GACjCqG,AAAgB,eAAhBA,EAETlL,EAAK,IAAI,CAAG6E,EAAW,IAAI,CAClBqG,AAAgB,sCAAhBA,GAAuDrG,EAAW,QAAQ,CAEnF7E,EAAK,IAAI,CAAG2Q,GAA8B9L,EAAW,QAAQ,EAG7D7E,EAAK,IAAI,CAAG6E,EAAW,QAAQ,AAEnC,CACA,OAAO,IAAIyQ,QAAQ9f,EAAKwK,EAC1B,CACA,SAAS2Q,GAA8BR,CAAQ,EAC7C,IAAID,EAAe,IAAIN,gBACvB,IAAK,GAAI,CAACrb,EAAKuB,EAAM,GAAIqa,EAAS,OAAO,GAEvCD,EAAa,MAAM,CAAC3b,EAAK,AAAiB,UAAjB,OAAOuB,EAAqBA,EAAQA,EAAM,IAAI,EAEzE,OAAOoa,CACT,CACA,SAASU,GAA8BV,CAAY,EACjD,IAAIC,EAAW,IAAIK,SACnB,IAAK,GAAI,CAACjc,EAAKuB,EAAM,GAAIoa,EAAa,OAAO,GAC3CC,EAAS,MAAM,CAAC5b,EAAKuB,GAEvB,OAAOqa,CACT,CA0FA,SAASjH,GAAkB3S,CAAK,CAAE2E,CAAO,CAAEgM,CAAO,CAAE3B,CAAmB,CAAE0C,CAAoB,CAAEa,CAAc,CAAE9F,CAAe,MArFxHuS,EAFAtZ,EACA2F,EAEA4T,EACAC,EACAC,EAmFA,CACFzZ,WAAAA,CAAU,CACV2F,OAAAA,CAAM,CACP,EA3FG3F,EAAa,CAAC,EACd2F,EAAS,KAET4T,EAAa,GACbC,EAAgB,CAAC,EACjBC,EAAenQ,AAsF0BA,GAtFHa,GAAcb,AAsFXA,CAtF8B,CAAC,EAAE,EAAIA,AAsFrCA,CAtFwD,CAAC,EAAE,CAAC,KAAK,CAAGxM,KAAAA,EAEjHmC,AAoF2BA,EApFnB,OAAO,CAACW,IACd,GAAI,CAAEA,CAAAA,EAAM,KAAK,CAAC,EAAE,IAmFcqL,CAnFJ,EAC5B,OAEF,IAAItO,EAAKiD,EAAM,KAAK,CAAC,EAAE,CACnBhB,EAASqM,AA+EqBA,CA/Ed,CAACtO,EAAG,CAExB,GADA/C,EAAU,CAACuR,GAAiBvM,GAAS,uDACjCuL,GAAcvL,GAAS,CACzB,IAAI/C,EAAQ+C,EAAO,KAAK,AAIH9B,MAAAA,IAAjB2c,IACF5d,EAAQ4d,EACRA,EAAe3c,KAAAA,GAEjB6I,EAASA,GAAU,CAAC,EAoE2D,CAjExE,EAIL,IAAI4F,EAAgBxB,GA6DC9K,EA7D4BtC,EAC7CgJ,AAAkC,OAAlCA,CAAM,CAAC4F,EAAc,KAAK,CAAC,EAAE,CAAC,EAChC5F,CAAAA,CAAM,CAAC4F,EAAc,KAAK,CAAC,EAAE,CAAC,CAAG1P,CAAI,CAEzC,CAEAmE,CAAU,CAACrD,EAAG,CAAGG,KAAAA,EAGZyc,IACHA,EAAa,GACbD,EAAarW,EAAqBrE,EAAO,KAAK,EAAIA,EAAO,KAAK,CAAC,MAAM,CAAG,KAEtEA,EAAO,OAAO,EAChB4a,CAAAA,CAAa,CAAC7c,EAAG,CAAGiC,EAAO,OAAO,AAAD,CAErC,MACM0M,GAAiB1M,IACnBmI,AA2C4DA,EA3C5C,GAAG,CAACpK,EAAIiC,EAAO,YAAY,EAC3CoB,CAAU,CAACrD,EAAG,CAAGiC,EAAO,YAAY,CAAC,IAAI,CAGrCA,AAAqB,MAArBA,EAAO,UAAU,EAAYA,AAAsB,MAAtBA,EAAO,UAAU,EAAa2a,GAC7DD,CAAAA,EAAa1a,EAAO,UAAU,AAAD,IAM/BoB,CAAU,CAACrD,EAAG,CAAGiC,EAAO,IAAI,CAGxBA,EAAO,UAAU,EAAIA,AAAsB,MAAtBA,EAAO,UAAU,EAAY,CAAC2a,GACrDD,CAAAA,EAAa1a,EAAO,UAAU,AAAD,GAE3BA,EAAO,OAAO,EAChB4a,CAAAA,CAAa,CAAC7c,EAAG,CAAGiC,EAAO,OAAO,AAAD,CAIzC,GAIqB9B,KAAAA,IAAjB2c,GAiByCnQ,IAhB3C3D,EAAS,CACP,CAAC2D,AAewCA,CAfrB,CAAC,EAAE,CAAC,CAAEmQ,CAC5B,EACAzZ,CAAU,CAACsJ,AAagCA,CAbb,CAAC,EAAE,CAAC,CAAGxM,KAAAA,GAEhC,CACLkD,WAAAA,EACA2F,OAAAA,EACA,WAAY2T,GAAc,IAC1BE,cAAAA,CACF,GA0CA,OAjCAxN,EAAqB,OAAO,CAACM,IAC3B,GAAI,CACFhU,IAAAA,CAAG,CACHsH,MAAAA,CAAK,CACLwQ,WAAAA,CAAU,CACX,CAAG9D,EACA1N,EAASiO,CAAc,CAACvU,EAAI,CAGhC,GAFAsB,EAAUgF,EAAQ,6CAEdwR,CAAAA,IAAcA,EAAW,MAAM,CAAC,OAAO,CAGpC,GAAIjG,GAAcvL,GAAS,CAChC,IAAI2M,EAAgBxB,GAAoBzP,EAAM,OAAO,CAAEsF,AAAS,MAATA,EAAgB,KAAK,EAAIA,EAAM,KAAK,CAAC,EAAE,CAC1F,CAAE+F,GAAUA,CAAM,CAAC4F,EAAc,KAAK,CAAC,EAAE,CAAC,EAC5C5F,CAAAA,EAAS3N,EAAS,CAAC,EAAG2N,EAAQ,CAC5B,CAAC4F,EAAc,KAAK,CAAC,EAAE,CAAC,CAAE3M,EAAO,KAAK,AACxC,EAAC,EAEHtE,EAAM,QAAQ,CAAC,MAAM,CAAChC,EACxB,MAAO,GAAI6S,GAAiBvM,GAG1BhF,EAAU,GAAO,gDACZ,GAAI0R,GAAiB1M,GAG1BhF,EAAU,GAAO,uCACZ,CACL,IAAI2U,EAAcN,GAAerP,EAAO,IAAI,EAC5CtE,EAAM,QAAQ,CAAC,GAAG,CAAChC,EAAKiW,EAC1B,CACF,GACO,CACLvO,WAAAA,EACA2F,OAAAA,CACF,CACF,CACA,SAASuC,GAAgBlI,CAAU,CAAE0Z,CAAa,CAAEza,CAAO,CAAE0G,CAAM,EACjE,IAAIgU,EAAmB3hB,EAAS,CAAC,EAAG0hB,GACpC,IAAK,IAAI9Z,KAASX,EAAS,CACzB,IAAItC,EAAKiD,EAAM,KAAK,CAAC,EAAE,CAUvB,GATI8Z,EAAc,cAAc,CAAC/c,GAC3B+c,AAAsB5c,KAAAA,IAAtB4c,CAAa,CAAC/c,EAAG,EACnBgd,CAAAA,CAAgB,CAAChd,EAAG,CAAG+c,CAAa,CAAC/c,EAAG,AAAD,EAEhCqD,AAAmBlD,KAAAA,IAAnBkD,CAAU,CAACrD,EAAG,EAAkBiD,EAAM,KAAK,CAAC,MAAM,EAG3D+Z,CAAAA,CAAgB,CAAChd,EAAG,CAAGqD,CAAU,CAACrD,EAAG,AAAD,EAElCgJ,GAAUA,EAAO,cAAc,CAAChJ,GAElC,KAEJ,CACA,OAAOgd,CACT,CACA,SAASnP,GAAuBlB,CAAmB,SACjD,AAAKA,EAGEa,GAAcb,CAAmB,CAAC,EAAE,EAAI,CAE7C,WAAY,CAAC,CACf,EAAI,CACF,WAAY,CACV,CAACA,CAAmB,CAAC,EAAE,CAAC,CAAEA,CAAmB,CAAC,EAAE,CAAC,IAAI,AACvD,CACF,EATS,CAAC,CAUZ,CAIA,SAASS,GAAoB9K,CAAO,CAAEiL,CAAO,EAE3C,MAAO0P,AADe1P,CAAAA,EAAUjL,EAAQ,KAAK,CAAC,EAAGA,EAAQ,SAAS,CAACyG,GAAKA,EAAE,KAAK,CAAC,EAAE,GAAKwE,GAAW,GAAK,IAAIjL,EAAQ,AAAD,EAC3F,OAAO,GAAG,IAAI,CAACyG,GAAKA,AAA6B,KAA7BA,EAAE,KAAK,CAAC,gBAAgB,GAAczG,CAAO,CAAC,EAAE,AAC7F,CACA,SAASsG,GAAuBnJ,CAAM,EAEpC,IAAII,EAAQJ,AAAkB,IAAlBA,EAAO,MAAM,CAASA,CAAM,CAAC,EAAE,CAAGA,EAAO,IAAI,CAACkS,GAAKA,EAAE,KAAK,EAAI,CAACA,EAAE,IAAI,EAAIA,AAAW,MAAXA,EAAE,IAAI,GAAa,CACtG,GAAI,sBACN,EACA,MAAO,CACL,QAAS,CAAC,CACR,OAAQ,CAAC,EACT,SAAU,GACV,aAAc,GACd9R,MAAAA,CACF,EAAE,CACFA,MAAAA,CACF,CACF,CACA,SAAS8I,GAAuBzC,CAAM,CAAEgX,CAAM,EAC5C,GAAI,CACFhhB,SAAAA,CAAQ,CACRqR,QAAAA,CAAO,CACP8J,OAAAA,CAAM,CACN9E,KAAAA,CAAI,CACJpV,QAAAA,CAAO,CACR,CAAG+f,AAAW,KAAK,IAAhBA,EAAoB,CAAC,EAAIA,EACzB/W,EAAa,uBACbgX,EAAe,kCAwBnB,OAvBIjX,AAAW,MAAXA,GACFC,EAAa,cACTkR,GAAUnb,GAAYqR,EACxB4P,EAAe,cAAgB9F,EAAS,gBAAmBnb,EAA5C,+CAAgHqR,EAAhH,+CACNgF,AAAS,iBAATA,EACT4K,EAAe,sCACN5K,AAAS,iBAATA,GACT4K,CAAAA,EAAe,kCAAiC,GAEzCjX,AAAW,MAAXA,GACTC,EAAa,YACbgX,EAAe,UAAa5P,EAAU,yBAA6BrR,EAAW,KACrEgK,AAAW,MAAXA,GACTC,EAAa,YACbgX,EAAe,yBAA4BjhB,EAAW,KAClC,MAAXgK,IACTC,EAAa,qBACTkR,GAAUnb,GAAYqR,EACxB4P,EAAe,cAAgB9F,EAAO,WAAW,GAAK,gBAAmBnb,EAA1D,gDAA+HqR,EAA/H,+CACN8J,GACT8F,CAAAA,EAAe,2BAA8B9F,EAAO,WAAW,GAAK,GAAG,GAGpE,IAAIpR,EAAkBC,GAAU,IAAKC,EAAY,AAAI/I,MAAM+f,GAAe,GACnF,CAEA,SAAS9M,GAAa/B,CAAO,EAC3B,IAAI6F,EAAU7Y,OAAO,OAAO,CAACgT,GAC7B,IAAK,IAAI9S,EAAI2Y,EAAQ,MAAM,CAAG,EAAG3Y,GAAK,EAAGA,IAAK,CAC5C,GAAI,CAACG,EAAKsG,EAAO,CAAGkS,CAAO,CAAC3Y,EAAE,CAC9B,GAAIgT,GAAiBvM,GACnB,MAAO,CACLtG,IAAAA,EACAsG,OAAAA,CACF,CAEJ,CACF,CACA,SAASyV,GAAkB5Z,CAAI,EAC7B,IAAIC,EAAa,AAAgB,UAAhB,OAAOD,EAAoBrB,EAAUqB,GAAQA,EAC9D,OAAOvB,EAAWlB,EAAS,CAAC,EAAG0C,EAAY,CACzC,KAAM,EACR,GACF,CAyBA,SAAS4Q,GAAiB1M,CAAM,EAC9B,OAAOA,EAAO,IAAI,GAAK7G,EAAW,QAAQ,AAC5C,CACA,SAASoS,GAAcvL,CAAM,EAC3B,OAAOA,EAAO,IAAI,GAAK7G,EAAW,KAAK,AACzC,CACA,SAASoT,GAAiBvM,CAAM,EAC9B,MAAO,AAACA,CAAAA,GAAUA,EAAO,IAAI,AAAD,IAAO7G,EAAW,QAAQ,AACxD,CACA,SAASihB,GAAuBnf,CAAK,EACnC,MAAO,AAAiB,UAAjB,OAAOA,GAAsBA,AAAS,MAATA,GAAiB,SAAUA,GAAS,SAAUA,GAAS,SAAUA,GAASA,AAAe,yBAAfA,EAAM,IAAI,AAC1H,CAKA,SAASyV,GAAWzV,CAAK,EACvB,OAAOA,AAAS,MAATA,GAAiB,AAAwB,UAAxB,OAAOA,EAAM,MAAM,EAAiB,AAA4B,UAA5B,OAAOA,EAAM,UAAU,EAAiB,AAAyB,UAAzB,OAAOA,EAAM,OAAO,EAAiB,AAAsB,SAAfA,EAAM,IAAI,AAC5J,CAYA,SAASoO,GAAiB+L,CAAM,EAC9B,OAAO7Q,EAAqB,GAAG,CAAC6Q,EAAO,WAAW,GACpD,CACA,eAAe/D,GAAiChR,CAAO,CAAEgM,CAAO,CAAEqG,CAAM,CAAE1B,CAAc,CAAEyF,CAAiB,EACzG,IAAIvE,EAAU7Y,OAAO,OAAO,CAACgT,GAC7B,IAAK,IAAI7Q,EAAQ,EAAGA,EAAQ0W,EAAQ,MAAM,CAAE1W,IAAS,CACnD,GAAI,CAAC8P,EAAStL,EAAO,CAAGkS,CAAO,CAAC1W,EAAM,CAClCwF,EAAQX,EAAQ,IAAI,CAACyG,GAAK,AAACA,CAAAA,AAAK,MAALA,EAAY,KAAK,EAAIA,EAAE,KAAK,CAAC,EAAE,AAAD,IAAOwE,GAIpE,GAAI,CAACtK,EACH,SAEF,IAAI0V,EAAe1F,EAAe,IAAI,CAAClK,GAAKA,EAAE,KAAK,CAAC,EAAE,GAAK9F,EAAM,KAAK,CAAC,EAAE,EACrEma,EAAuBzE,AAAgB,MAAhBA,GAAwB,CAACO,GAAmBP,EAAc1V,IAAU,AAACyV,CAAAA,GAAqBA,CAAiB,CAACzV,EAAM,KAAK,CAAC,EAAE,CAAC,AAAD,IAAO9C,KAAAA,CACxJwO,CAAAA,GAAiB1M,IAAWmb,GAI9B,MAAMtL,GAAoB7P,EAAQ0S,EAAQ,IAAO,IAAI,CAAC1S,IAChDA,GACFqM,CAAAA,CAAO,CAACf,EAAQ,CAAGtL,CAAK,CAE5B,EAEJ,CACF,CACA,eAAesR,GAA8BjR,CAAO,CAAEgM,CAAO,CAAEe,CAAoB,EACjF,IAAK,IAAI5R,EAAQ,EAAGA,EAAQ4R,EAAqB,MAAM,CAAE5R,IAAS,CAChE,GAAI,CACF9B,IAAAA,CAAG,CACH4R,QAAAA,CAAO,CACPkG,WAAAA,CAAU,CACX,CAAGpE,CAAoB,CAAC5R,EAAM,CAC3BwE,EAASqM,CAAO,CAAC3S,EAAI,CACb2G,EAAQ,IAAI,CAACyG,GAAK,AAACA,CAAAA,AAAK,MAALA,EAAY,KAAK,EAAIA,EAAE,KAAK,CAAC,EAAE,AAAD,IAAOwE,IAOhEoB,GAAiB1M,KAInBhF,EAAUwW,EAAY,wEACtB,MAAM3B,GAAoB7P,EAAQwR,EAAW,MAAM,CAAE,IAAM,IAAI,CAACxR,IAC1DA,GACFqM,CAAAA,CAAO,CAAC3S,EAAI,CAAGsG,CAAK,CAExB,GAEJ,CACF,CACA,eAAe6P,GAAoB7P,CAAM,CAAE0S,CAAM,CAAE0I,CAAM,EAKvD,GAJIA,AAAW,KAAK,IAAhBA,GACFA,CAAAA,EAAS,EAAI,GAED,MAAMpb,EAAO,YAAY,CAAC,WAAW,CAAC0S,IAIpD,GAAI0I,EACF,GAAI,CACF,MAAO,CACL,KAAMjiB,EAAW,IAAI,CACrB,KAAM6G,EAAO,YAAY,CAAC,aAAa,AACzC,CACF,CAAE,MAAO1E,EAAG,CAEV,MAAO,CACL,KAAMnC,EAAW,KAAK,CACtB,MAAOmC,CACT,CACF,CAEF,MAAO,CACL,KAAMnC,EAAW,IAAI,CACrB,KAAM6G,EAAO,YAAY,CAAC,IAAI,AAChC,EACF,CACA,SAAS8U,GAAmB5a,CAAM,EAChC,OAAO,IAAI6a,gBAAgB7a,GAAQ,MAAM,CAAC,SAAS,IAAI,CAACsG,GAAKA,AAAM,KAANA,EAC/D,CACA,SAAS4L,GAAe/L,CAAO,CAAExF,CAAQ,EACvC,IAAIX,EAAS,AAAoB,UAApB,OAAOW,EAAwBL,EAAUK,GAAU,MAAM,CAAGA,EAAS,MAAM,CACxF,GAAIwF,CAAO,CAACA,EAAQ,MAAM,CAAG,EAAE,CAAC,KAAK,CAAC,KAAK,EAAIyU,GAAmB5a,GAAU,IAE1E,OAAOmG,CAAO,CAACA,EAAQ,MAAM,CAAG,EAAE,CAIpC,IAAIsC,EAAcH,EAA2BnC,GAC7C,OAAOsC,CAAW,CAACA,EAAY,MAAM,CAAG,EAAE,AAC5C,CACA,SAASqK,GAA4BjB,CAAU,EAC7C,GAAI,CACFoE,WAAAA,CAAU,CACVC,WAAAA,CAAU,CACVC,YAAAA,CAAW,CACXqF,KAAAA,CAAI,CACJJ,SAAAA,CAAQ,CACR9B,KAAAA,CAAI,CACL,CAAGzH,EACJ,GAAI,AAACoE,GAAeC,GAAeC,EAGnC,IAAIqF,AAAQ,MAARA,EACF,MAAO,CACLvF,WAAAA,EACAC,WAAAA,EACAC,YAAAA,EACA,SAAUnS,KAAAA,EACV,KAAMA,KAAAA,EACNwX,KAAAA,CACF,OACK,GAAIJ,AAAY,MAAZA,EACT,MAAO,CACLnF,WAAAA,EACAC,WAAAA,EACAC,YAAAA,EACAiF,SAAAA,EACA,KAAMpX,KAAAA,EACN,KAAMA,KAAAA,CACR,OACK,GAAIsV,AAAStV,KAAAA,IAATsV,EACT,MAAO,CACLrD,WAAAA,EACAC,WAAAA,EACAC,YAAAA,EACA,SAAUnS,KAAAA,EACVsV,KAAAA,EACA,KAAMtV,KAAAA,CACR,CACF,CACF,CACA,SAASsN,GAAqB3Q,CAAQ,CAAEmP,CAAU,SAChD,AAAIA,EACe,CACf,MAAO,UACPnP,SAAAA,EACA,WAAYmP,EAAW,UAAU,CACjC,WAAYA,EAAW,UAAU,CACjC,YAAaA,EAAW,WAAW,CACnC,SAAUA,EAAW,QAAQ,CAC7B,KAAMA,EAAW,IAAI,CACrB,KAAMA,EAAW,IAAI,AACvB,EAGiB,CACf,MAAO,UACPnP,SAAAA,EACA,WAAYqD,KAAAA,EACZ,WAAYA,KAAAA,EACZ,YAAaA,KAAAA,EACb,SAAUA,KAAAA,EACV,KAAMA,KAAAA,EACN,KAAMA,KAAAA,CACR,CAGJ,CAcA,SAAS0P,GAAkB5D,CAAU,CAAE7F,CAAI,SACzC,AAAI6F,EACY,CACZ,MAAO,UACP,WAAYA,EAAW,UAAU,CACjC,WAAYA,EAAW,UAAU,CACjC,YAAaA,EAAW,WAAW,CACnC,SAAUA,EAAW,QAAQ,CAC7B,KAAMA,EAAW,IAAI,CACrB,KAAMA,EAAW,IAAI,CACrB7F,KAAAA,CACF,EAGc,CACZ,MAAO,UACP,WAAYjG,KAAAA,EACZ,WAAYA,KAAAA,EACZ,YAAaA,KAAAA,EACb,SAAUA,KAAAA,EACV,KAAMA,KAAAA,EACN,KAAMA,KAAAA,EACNiG,KAAAA,CACF,CAGJ,CAcA,SAASkL,GAAelL,CAAI,EAW1B,MAVc,CACZ,MAAO,OACP,WAAYjG,KAAAA,EACZ,WAAYA,KAAAA,EACZ,YAAaA,KAAAA,EACb,SAAUA,KAAAA,EACV,KAAMA,KAAAA,EACN,KAAMA,KAAAA,EACNiG,KAAAA,CACF,CAEF,CAhtD+BkX,OAAO,oFC/tE3BC,EAQAC,MATPD,EAQAC,8CAj7BJ,SAASniB,IAYP,MAAOA,AAXPA,CAAAA,EAAWC,OAAO,MAAM,CAAGA,OAAO,MAAM,CAAC,IAAI,GAAK,SAAUC,CAAM,EAChE,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAU,MAAM,CAAED,IAAK,CACzC,IAAIE,EAASD,SAAS,CAACD,EAAE,CACzB,IAAK,IAAIG,KAAOD,EACVJ,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACI,EAAQC,IAC/CJ,CAAAA,CAAM,CAACI,EAAI,CAAGD,CAAM,CAACC,EAAI,AAAD,CAG9B,CACA,OAAOJ,CACT,GACgB,KAAK,CAAC,IAAI,CAAEE,UAC9B,CA4DA,SAASgiB,EAAmBrW,CAAI,EAI9B,OAHIA,AAAS,KAAK,IAAdA,GACFA,CAAAA,EAAO,EAAC,EAEH,IAAI4P,gBAAgB,AAAgB,UAAhB,OAAO5P,GAAqBgN,MAAM,OAAO,CAAChN,IAASA,aAAgB4P,gBAAkB5P,EAAO9L,OAAO,IAAI,CAAC8L,GAAM,MAAM,CAAC,CAACnD,EAAMtI,KACrJ,IAAIuB,EAAQkK,CAAI,CAACzL,EAAI,CACrB,OAAOsI,EAAK,MAAM,CAACmQ,MAAM,OAAO,CAAClX,GAASA,EAAM,GAAG,CAACuF,GAAK,CAAC9G,EAAK8G,EAAE,EAAI,CAAC,CAAC9G,EAAKuB,EAAM,CAAC,CACrF,EAAG,EAAE,EACP,CA6HA,GAAI,CACFlB,OAAO,oBAAoB,CAFA,GAG7B,CAAE,MAAOuB,EAAG,CAEZ,CACA,SAASmgB,EAAoBje,CAAM,CAAE+K,CAAI,EACvC,MAAO,SAAa,CAClB,SAAUA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,QAAQ,CAC/C,OAAQnP,EAAS,CAAC,EAAGmP,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,MAAM,CAAE,CACxD,mBAAoB,EACtB,GACA,QAAS,SAAqB,CAC5B,OAAQA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,MAAM,AAC7C,GACA,cAAe,AAACA,CAAAA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,aAAa,AAAD,GAAMmT,IAC/Dle,OAAAA,EACA,mBAAoB,IAAyB,CAC7C,aAAc+K,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,YAAY,CACvD,wBAAyBA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,uBAAuB,CAC7E,OAAQA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,MAAM,AAC7C,GAAG,UAAU,EACf,CACA,SAASoT,EAAiBne,CAAM,CAAE+K,CAAI,EACpC,MAAO,SAAa,CAClB,SAAUA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,QAAQ,CAC/C,OAAQnP,EAAS,CAAC,EAAGmP,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,MAAM,CAAE,CACxD,mBAAoB,EACtB,GACA,QAAS,SAAkB,CACzB,OAAQA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,MAAM,AAC7C,GACA,cAAe,AAACA,CAAAA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,aAAa,AAAD,GAAMmT,IAC/Dle,OAAAA,EACA,mBAAoB,IAAyB,CAC7C,aAAc+K,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,YAAY,CACvD,wBAAyBA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,uBAAuB,CAC7E,OAAQA,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,MAAM,AAC7C,GAAG,UAAU,EACf,CACA,SAASmT,IACP,IAAIrI,EACJ,IAAI3X,EAAQ,AAAsB,MAArB2X,CAAAA,EAAUtZ,MAAK,EAAa,KAAK,EAAIsZ,EAAQ,2BAA2B,CAMrF,OALI3X,GAASA,EAAM,MAAM,EACvBA,CAAAA,EAAQtC,EAAS,CAAC,EAAGsC,EAAO,CAC1B,OAAQkgB,AAKd,SAA2B7U,CAAM,EAC/B,GAAI,CAACA,EAAQ,OAAO,KACpB,IAAImL,EAAU7Y,OAAO,OAAO,CAAC0N,GACzB8U,EAAa,CAAC,EAClB,IAAK,GAAI,CAACniB,EAAKggB,EAAI,GAAIxH,EAGrB,GAAIwH,GAAOA,AAAe,uBAAfA,EAAI,MAAM,CACnBmC,CAAU,CAACniB,EAAI,CAAG,IAAI,IAAwB,CAACggB,EAAI,MAAM,CAAEA,EAAI,UAAU,CAAEA,EAAI,IAAI,CAAEA,AAAiB,KAAjBA,EAAI,QAAQ,OAC5F,GAAIA,GAAOA,AAAe,UAAfA,EAAI,MAAM,CAAc,CAExC,GAAIA,EAAI,SAAS,CAAE,CACjB,IAAIoC,EAAmB/hB,MAAM,CAAC2f,EAAI,SAAS,CAAC,CAC5C,GAAI,AAA4B,YAA5B,OAAOoC,EACT,GAAI,CAEF,IAAI7e,EAAQ,IAAI6e,EAAiBpC,EAAI,OAAO,CAG5Czc,CAAAA,EAAM,KAAK,CAAG,GACd4e,CAAU,CAACniB,EAAI,CAAGuD,CACpB,CAAE,MAAO3B,EAAG,CAEZ,CAEJ,CACA,GAAIugB,AAAmB,MAAnBA,CAAU,CAACniB,EAAI,CAAU,CAC3B,IAAIuD,EAAQ,AAAI9B,MAAMue,EAAI,OAAO,CAGjCzc,CAAAA,EAAM,KAAK,CAAG,GACd4e,CAAU,CAACniB,EAAI,CAAGuD,CACpB,CACF,MACE4e,CAAU,CAACniB,EAAI,CAAGggB,EAGtB,OAAOmC,CACT,EA3CgCngB,EAAM,MAAM,CACxC,EAAC,EAEIA,CACT,CAwCA,IAAMqgB,EAAqC,eAAmB,CAAC,CAC7D,gBAAiB,EACnB,GAIMC,EAA+B,eAAmB,CAAC,IAAI/U,KA8BvDgV,EAAsB,kBADH,eAC0B,CAE7CC,EAAgB,kBADH,SACuB,CAU1C,SAASC,EAAcC,CAAE,EACnBF,EACFA,EAAcE,GAEdA,GAEJ,CAdkB,kBADH,KACgB,AAe/B,OAAMC,EACJ,aAAc,CACZ,IAAI,CAAC,MAAM,CAAG,UACd,IAAI,CAAC,OAAO,CAAG,IAAIlL,QAAQ,CAACiC,EAASkG,KACnC,IAAI,CAAC,OAAO,CAAGre,IACO,YAAhB,IAAI,CAAC,MAAM,GACb,IAAI,CAAC,MAAM,CAAG,WACdmY,EAAQnY,GAEZ,EACA,IAAI,CAAC,MAAM,CAAGqhB,IACQ,YAAhB,IAAI,CAAC,MAAM,GACb,IAAI,CAAC,MAAM,CAAG,WACdhD,EAAOgD,GAEX,CACF,EACF,CACF,CAIA,SAASC,EAAe3gB,CAAI,EAC1B,GAAI,CACF4gB,gBAAAA,CAAe,CACflX,OAAAA,CAAM,CACNW,OAAAA,CAAM,CACP,CAAGrK,EACA,CAACF,EAAO+gB,EAAa,CAAG,UAAc,CAACnX,EAAO,KAAK,EACnD,CAACoX,EAAcC,EAAgB,CAAG,UAAc,GAChD,CAACC,EAAWC,EAAa,CAAG,UAAc,CAAC,CAC7C,gBAAiB,EACnB,GACI,CAACC,EAAWC,EAAa,CAAG,UAAc,GAC1C,CAACC,EAAYC,EAAc,CAAG,UAAc,GAC5C,CAACC,EAAcC,EAAgB,CAAG,UAAc,GAChDC,EAAc,QAAY,CAAC,IAAInW,KAC/B,CACFoW,mBAAAA,CAAkB,CACnB,CAAGpX,GAAU,CAAC,EACXqX,EAAuB,aAAiB,CAAClB,IAC3C,GAAIiB,EAtDFpB,EACFA,EAsDsBG,GApDtBA,AAoDsBA,SAEpBA,GAEJ,EAAG,CAACiB,EAAmB,EACnBE,EAAW,aAAiB,CAAC,CAACjV,EAAU2J,KAC1C,GAAI,CACF/J,gBAAAA,CAAe,CACf,UAAWiB,CAAS,CACpB,mBAAoBD,CAAkB,CACvC,CAAG+I,EACJ/J,EAAgB,OAAO,CAACxO,GAAO0jB,EAAY,OAAO,CAAC,MAAM,CAAC1jB,IAC1D4O,EAAS,QAAQ,CAAC,OAAO,CAAC,CAACI,EAAShP,KAC9BgP,AAAiBxK,KAAAA,IAAjBwK,EAAQ,IAAI,EACd0U,EAAY,OAAO,CAAC,GAAG,CAAC1jB,EAAKgP,EAAQ,IAAI,CAE7C,GACA,IAAI8U,EAA8BlY,AAAiB,MAAjBA,EAAO,MAAM,EAAYA,AAA0B,MAA1BA,EAAO,MAAM,CAAC,QAAQ,EAAY,AAAsD,YAAtD,OAAOA,EAAO,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAG9I,GAAI,CAAC4D,GAAsBsU,EAA6B,YAClDrU,EACFgT,EAAc,IAAMM,EAAanU,IAEjCgV,EAAqB,IAAMb,EAAanU,KAK5C,GAAIa,EAAW,CAEbgT,EAAc,KAERa,IACFF,GAAaA,EAAU,OAAO,GAC9BE,EAAW,cAAc,IAE3BH,EAAa,CACX,gBAAiB,GACjB,UAAW,GACX,gBAAiB3T,EAAmB,eAAe,CACnD,aAAcA,EAAmB,YAAY,AAC/C,EACF,GAEA,IAAIuU,EAAInY,EAAO,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KACjD6W,EAAc,IAAMM,EAAanU,GACnC,GAEAmV,EAAE,QAAQ,CAAC,OAAO,CAAC,KACjBtB,EAAc,KACZY,EAAa7e,KAAAA,GACb+e,EAAc/e,KAAAA,GACdye,EAAgBze,KAAAA,GAChB2e,EAAa,CACX,gBAAiB,EACnB,EACF,EACF,GACAV,EAAc,IAAMc,EAAcQ,IAClC,MACF,CAEIT,GAGFF,GAAaA,EAAU,OAAO,GAC9BE,EAAW,cAAc,GACzBG,EAAgB,CACd,MAAO7U,EACP,gBAAiBY,EAAmB,eAAe,CACnD,aAAcA,EAAmB,YAAY,AAC/C,KAGAyT,EAAgBrU,GAChBuU,EAAa,CACX,gBAAiB,GACjB,UAAW,GACX,gBAAiB3T,EAAmB,eAAe,CACnD,aAAcA,EAAmB,YAAY,AAC/C,GAEJ,EAAG,CAAC5D,EAAO,MAAM,CAAE0X,EAAYF,EAAWM,EAAaE,EAAqB,EAG5E,iBAAqB,CAAC,IAAMhY,EAAO,SAAS,CAACiY,GAAW,CAACjY,EAAQiY,EAAS,EAG1E,WAAe,CAAC,KACVX,EAAU,eAAe,EAAI,CAACA,EAAU,SAAS,EACnDG,EAAa,IAAIV,EAErB,EAAG,CAACO,EAAU,EAId,WAAe,CAAC,KACd,GAAIE,GAAaJ,GAAgBpX,EAAO,MAAM,CAAE,CAE9C,IAAIoY,EAAgBZ,EAAU,OAAO,CACjCE,EAAa1X,EAAO,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC,UAC1DgY,EAAqB,IAAMb,EAHdC,IAIb,MAAMgB,CACR,GACAV,EAAW,QAAQ,CAAC,OAAO,CAAC,KAC1BD,EAAa7e,KAAAA,GACb+e,EAAc/e,KAAAA,GACdye,EAAgBze,KAAAA,GAChB2e,EAAa,CACX,gBAAiB,EACnB,EACF,GACAI,EAAcD,EAChB,CACF,EAAG,CAACM,EAAsBZ,EAAcI,EAAWxX,EAAO,MAAM,CAAC,EAGjE,WAAe,CAAC,KACVwX,GAAaJ,GAAgBhhB,EAAM,QAAQ,CAAC,GAAG,GAAKghB,EAAa,QAAQ,CAAC,GAAG,EAC/EI,EAAU,OAAO,EAErB,EAAG,CAACA,EAAWE,EAAYthB,EAAM,QAAQ,CAAEghB,EAAa,EAGxD,WAAe,CAAC,KACV,CAACE,EAAU,eAAe,EAAIM,IAChCP,EAAgBO,EAAa,KAAK,EAClCL,EAAa,CACX,gBAAiB,GACjB,UAAW,GACX,gBAAiBK,EAAa,eAAe,CAC7C,aAAcA,EAAa,YAAY,AACzC,GACAC,EAAgBjf,KAAAA,GAEpB,EAAG,CAAC0e,EAAU,eAAe,CAAEM,EAAa,EAC5C,WAAe,CAAC,KAIhB,EAAG,EAAE,EACL,IAAIS,EAAY,SAAa,CAAC,IACrB,EACL,WAAYrY,EAAO,UAAU,CAC7B,eAAgBA,EAAO,cAAc,CACrC,GAAIlI,GAAKkI,EAAO,QAAQ,CAAClI,GACzB,KAAM,CAAC/C,EAAIqB,EAAO6M,IAASjD,EAAO,QAAQ,CAACjL,EAAI,CAC7CqB,MAAAA,EACA,mBAAoB6M,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,kBAAkB,AACrE,GACA,QAAS,CAAClO,EAAIqB,EAAO6M,IAASjD,EAAO,QAAQ,CAACjL,EAAI,CAChD,QAAS,GACTqB,MAAAA,EACA,mBAAoB6M,AAAQ,MAARA,EAAe,KAAK,EAAIA,EAAK,kBAAkB,AACrE,EACF,GACC,CAACjD,EAAO,EACPjH,EAAWiH,EAAO,QAAQ,EAAI,IAC9BsY,EAAoB,SAAa,CAAC,IAAO,EAC3CtY,OAAAA,EACAqY,UAAAA,EACA,OAAQ,GACRtf,SAAAA,CACF,GAAI,CAACiH,EAAQqY,EAAWtf,EAAS,EAC7Bwf,EAAe,SAAa,CAAC,IAAO,EACtC,qBAAsBvY,EAAO,MAAM,CAAC,oBAAoB,AAC1D,GAAI,CAACA,EAAO,MAAM,CAAC,oBAAoB,CAAC,EAOxC,OAAoB,eAAmB,CAAC,UAAc,CAAE,KAAmB,eAAmB,CAAC,aAAiC,CAAE,CAChI,MAAOsY,CACT,EAAgB,eAAmB,CAAC,aAAsC,CAAE,CAC1E,MAAOliB,CACT,EAAgB,eAAmB,CAACsgB,EAAgB,QAAQ,CAAE,CAC5D,MAAOoB,EAAY,OAAO,AAC5B,EAAgB,eAAmB,CAACrB,EAAsB,QAAQ,CAAE,CAClE,MAAOa,CACT,EAAgB,eAAmB,CAAC,IAAM,CAAE,CAC1C,SAAUve,EACV,SAAU3C,EAAM,QAAQ,CACxB,eAAgBA,EAAM,aAAa,CACnC,UAAWiiB,EACX,OAAQE,CACV,EAAGniB,EAAM,WAAW,EAAI4J,EAAO,MAAM,CAAC,mBAAmB,CAAgB,eAAmB,CAACwY,EAAoB,CAC/G,OAAQxY,EAAO,MAAM,CACrB,OAAQA,EAAO,MAAM,CACrB,MAAO5J,CACT,GAAK8gB,OAAsB,KAC7B,CAEA,IAAMsB,EAAkC,MAAU,CAClD,SAAoBlI,CAAK,EACvB,GAAI,CACFpY,OAAAA,CAAM,CACNyI,OAAAA,CAAM,CACNvK,MAAAA,CAAK,CACN,CAAGka,EACJ,MAAO,SAAqBpY,EAAQU,KAAAA,EAAWxC,EAAOuK,EACxD,GA2ZA,SAAS8X,EAAgBC,CAAW,EAElC,IAAIC,EAAyB,QAAY,CAACzC,EAAmBwC,IACzDE,EAAwB,QAAY,CAAC,IACrCrjB,EAAW,WACXwa,EAAe,SAAa,CAAC,SAh6BC8I,EAAgBC,MAC9C/I,SAD8B8I,EAo6BPtjB,EAAS,MAAM,CAp6BQujB,EAo6BNF,EAAsB,OAAO,CAAG,KAAOD,EAAuB,OAAO,CAn6B7G5I,EAAemG,EAAmB2C,GAClCC,GAMFA,EAAoB,OAAO,CAAC,CAACzc,EAAGjI,KAC1B,AAAC2b,EAAa,GAAG,CAAC3b,IACpB0kB,EAAoB,MAAM,CAAC1kB,GAAK,OAAO,CAACuB,IACtCoa,EAAa,MAAM,CAAC3b,EAAKuB,EAC3B,EAEJ,GAEKoa,GAo5B6G,CAACxa,EAAS,MAAM,CAAC,EACjIgP,EAAW,WACXwU,EAAkB,aAAiB,CAAC,CAACC,EAAUC,KACjD,IAAMC,EAAkBhD,EAAmB,AAAoB,YAApB,OAAO8C,EAA0BA,EAASjJ,GAAgBiJ,EACrGJ,CAAAA,EAAsB,OAAO,CAAG,GAChCrU,EAAS,IAAM2U,EAAiBD,EAClC,EAAG,CAAC1U,EAAUwL,EAAa,EAC3B,MAAO,CAACA,EAAcgJ,EAAgB,AACxC,CA1TkB,AAAkB,aAAlB,OAAOtkB,QAA0B,AAA2B,SAApBA,OAAO,QAAQ,EAA2BA,OAAO,QAAQ,CAAC,aAAa,CAuO/HuhB,CADSA,EAMRA,GAAmBA,CAAAA,EAAiB,CAAC,IALvB,oBAAuB,CAAG,uBACzCA,EAAe,SAAY,CAAG,YAC9BA,EAAe,gBAAmB,CAAG,mBACrCA,EAAe,UAAa,CAAG,aAC/BA,EAAe,sBAAyB,CAAG,yBAI3CC,CADSA,EAIRA,GAAwBA,CAAAA,EAAsB,CAAC,IAH5B,UAAa,CAAG,aACpCA,EAAoB,WAAc,CAAG,cACrCA,EAAoB,oBAAuB,CAAG,kHCsYhD,SAASkD,EAAyB3L,CAAQ,CAAEpV,CAAU,EAChDA,AAAe,KAAK,IAApBA,GACFA,CAAAA,EAAa,EAAE,AAAD,EAEhB,IAAIF,EAAS,EAAE,CAoCf,OAnCA,kBAAsB,CAACsV,EAAU,CAAC4L,EAASljB,KACzC,GAAI,CAAe,gBAAoB,CAACkjB,GAGtC,OAEF,IAAI7gB,EAAW,IAAIH,EAAYlC,EAAM,CACrC,GAAIkjB,EAAQ,IAAI,GAAK,UAAc,CAAE,YAEnClhB,EAAO,IAAI,CAAC,KAAK,CAACA,EAAQihB,EAAyBC,EAAQ,KAAK,CAAC,QAAQ,CAAE7gB,GAG7E,AAAE6gB,CAAAA,EAAQ,IAAI,GAAKC,GAAmQ,SAAiB,IACvS,AAAGD,EAAQ,KAAK,CAAC,KAAK,EAAKA,EAAQ,KAAK,CAAC,QAAQ,EAAkH,SAAiB,IACpL,IAAI9gB,EAAQ,CACV,GAAI8gB,EAAQ,KAAK,CAAC,EAAE,EAAI7gB,EAAS,IAAI,CAAC,KACtC,cAAe6gB,EAAQ,KAAK,CAAC,aAAa,CAC1C,QAASA,EAAQ,KAAK,CAAC,OAAO,CAC9B,UAAWA,EAAQ,KAAK,CAAC,SAAS,CAClC,MAAOA,EAAQ,KAAK,CAAC,KAAK,CAC1B,KAAMA,EAAQ,KAAK,CAAC,IAAI,CACxB,OAAQA,EAAQ,KAAK,CAAC,MAAM,CAC5B,OAAQA,EAAQ,KAAK,CAAC,MAAM,CAC5B,aAAcA,EAAQ,KAAK,CAAC,YAAY,CACxC,cAAeA,EAAQ,KAAK,CAAC,aAAa,CAC1C,iBAAkBA,AAA+B,MAA/BA,EAAQ,KAAK,CAAC,aAAa,EAAYA,AAA8B,MAA9BA,EAAQ,KAAK,CAAC,YAAY,CACnF,iBAAkBA,EAAQ,KAAK,CAAC,gBAAgB,CAChD,OAAQA,EAAQ,KAAK,CAAC,MAAM,CAC5B,KAAMA,EAAQ,KAAK,CAAC,IAAI,AAC1B,CACIA,CAAAA,EAAQ,KAAK,CAAC,QAAQ,EACxB9gB,CAAAA,EAAM,QAAQ,CAAG6gB,EAAyBC,EAAQ,KAAK,CAAC,QAAQ,CAAE7gB,EAAQ,EAE5EL,EAAO,IAAI,CAACI,EACd,GACOJ,CACT,qDArtB4C8d,EAMKC,EA+iBFqD,0BAvsC/C,SAASxlB,IAYP,MAAOA,AAXPA,CAAAA,EAAWC,OAAO,MAAM,CAAGA,OAAO,MAAM,CAAC,IAAI,GAAK,SAAUC,CAAM,EAChE,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAU,MAAM,CAAED,IAAK,CACzC,IAAIE,EAASD,SAAS,CAACD,EAAE,CACzB,IAAK,IAAIG,KAAOD,EACVJ,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACI,EAAQC,IAC/CJ,CAAAA,CAAM,CAACI,EAAI,CAAGD,CAAM,CAACC,EAAI,AAAD,CAG9B,CACA,OAAOJ,CACT,GACgB,KAAK,CAAC,IAAI,CAAEE,UAC9B,CAIA,IAAMqlB,EAAiC,eAAmB,CAAC,MAIrDC,EAAsC,eAAmB,CAAC,MAmB1DC,EAAiC,eAAmB,CAAC,MAIrDC,EAA+B,eAAmB,CAAC,MAInDC,EAA4B,eAAmB,CAAC,CACpD,OAAQ,KACR,QAAS,EAAE,CACX,YAAa,EACf,GAIMC,EAAiC,eAAmB,CAAC,MAW3D,SAASC,EAAQ9kB,CAAE,CAAEyO,CAAK,EACxB,GAAI,CACF4L,SAAAA,CAAQ,CACT,CAAG5L,AAAU,KAAK,IAAfA,EAAmB,CAAC,EAAIA,CAC5B,CAACsW,KAEuE,SAAiB,IACzF,GAAI,CACF/gB,SAAAA,CAAQ,CACRsf,UAAAA,CAAS,CACV,CAAG,YAAgB,CAACoB,GACjB,CACF5kB,KAAAA,CAAI,CACJF,SAAAA,CAAQ,CACRC,OAAAA,CAAM,CACP,CAAGmlB,AAsMN,SAAyBhlB,CAAE,CAAE0V,CAAM,EACjC,GAAI,CACF2E,SAAAA,CAAQ,CACT,CAAG3E,AAAW,KAAK,IAAhBA,EAAoB,CAAC,EAAIA,EACzB,CACF9J,OAAAA,CAAM,CACP,CAAG,YAAgB,CAAC8Y,GACjB,CACF1e,QAAAA,CAAO,CACR,CAAG,YAAgB,CAAC4e,GACjB,CACF,SAAUjc,CAAgB,CAC3B,CAAGsc,IACAC,EAAqBxkB,KAAK,SAAS,CAAC,SAA2BsF,EAAS4F,EAAO,oBAAoB,GACvG,OAAO,SAAa,CAAC,IAAM,SAAU5L,EAAIU,KAAK,KAAK,CAACwkB,GAAqBvc,EAAkB0R,AAAa,SAAbA,GAAsB,CAACra,EAAIklB,EAAoBvc,EAAkB0R,EAAS,CACvK,EArNsBra,EAAI,CACtBqa,SAAAA,CACF,GACI8K,EAAiBvlB,EASrB,MAHIoE,AAAa,MAAbA,GACFmhB,CAAAA,EAAiBvlB,AAAa,MAAbA,EAAmBoE,EAAW,SAAU,CAACA,EAAUpE,EAAS,GAExE0jB,EAAU,UAAU,CAAC,CAC1B,SAAU6B,EACVtlB,OAAAA,EACAC,KAAAA,CACF,EACF,CAOA,SAASilB,IACP,OAAO,AAAqC,MAArC,YAAgB,CAACJ,EAC1B,CAYA,SAASM,IAIP,OAHA,AAACF,KAE2E,SAAiB,IACtF,YAAgB,CAACJ,GAAiB,QAAQ,AACnD,CAoCA,SAASS,EAA0BrD,CAAE,EAE/B,AADW,YAAgB,CAAC2C,GAAmB,MAAM,EAKvD,iBAAqB,CAAC3C,EAE1B,CAQA,SAASsD,IACP,GAAI,CACFC,YAAAA,CAAW,CACZ,CAAG,YAAgB,CAACV,GAGrB,OAAOU,EAAcC,AAyrBvB,eAxM8BC,MACxBpG,EAwMA,CACFnU,OAAAA,CAAM,CACP,EAAwBgW,EAAe,iBAAiB,CAzMzD,CADI7B,EAAM,YAAgB,CAACoF,KACmF,SAAiB,IACxHpF,GAyMH1b,EAAK+hB,EAAkBvE,EAAoB,iBAAiB,EAC5DwE,EAAY,QAAY,CAAC,IAqB7B,OApBAN,EAA0B,KACxBM,EAAU,OAAO,CAAG,EACtB,GACe,aAAiB,CAAC,SAAU1lB,CAAE,CAAER,CAAO,EAChDA,AAAY,KAAK,IAAjBA,GACFA,CAAAA,EAAU,CAAC,GAMRkmB,EAAU,OAAO,GAClB,AAAc,UAAd,OAAO1lB,EACTiL,EAAO,QAAQ,CAACjL,GAEhBiL,EAAO,QAAQ,CAACjL,EAAIjB,EAAS,CAC3B,YAAa2E,CACf,EAAGlE,IAEP,EAAG,CAACyL,EAAQvH,EAAG,CAEjB,IAptB6CiiB,AAE7C,WACE,AAACZ,KAE2E,SAAiB,IAC7F,IAAIxB,EAAoB,YAAgB,CAACiB,GACrC,CACFxgB,SAAAA,CAAQ,CACR4H,OAAAA,CAAM,CACN0X,UAAAA,CAAS,CACV,CAAG,YAAgB,CAACoB,GACjB,CACF1e,QAAAA,CAAO,CACR,CAAG,YAAgB,CAAC4e,GACjB,CACF,SAAUjc,CAAgB,CAC3B,CAAGsc,IACAC,EAAqBxkB,KAAK,SAAS,CAAC,SAA2BsF,EAAS4F,EAAO,oBAAoB,GACnG8Z,EAAY,QAAY,CAAC,IA8B7B,OA7BAN,EAA0B,KACxBM,EAAU,OAAO,CAAG,EACtB,GACe,aAAiB,CAAC,SAAU1lB,CAAE,CAAER,CAAO,EAQpD,GAPIA,AAAY,KAAK,IAAjBA,GACFA,CAAAA,EAAU,CAAC,GAMT,CAACkmB,EAAU,OAAO,CAAE,OACxB,GAAI,AAAc,UAAd,OAAO1lB,EAAiB,YAC1BsjB,EAAU,EAAE,CAACtjB,GAGf,IAAIwB,EAAO,SAAUxB,EAAIU,KAAK,KAAK,CAACwkB,GAAqBvc,EAAkBnJ,AAAqB,SAArBA,EAAQ,QAAQ,CAQvF+jB,AAAqB,OAArBA,GAA6Bvf,AAAa,MAAbA,GAC/BxC,CAAAA,EAAK,QAAQ,CAAGA,AAAkB,MAAlBA,EAAK,QAAQ,CAAWwC,EAAW,SAAU,CAACA,EAAUxC,EAAK,QAAQ,CAAC,GAExF,AAAC,CAAEhC,EAAQ,OAAO,CAAG8jB,EAAU,OAAO,CAAGA,EAAU,IAAI,AAAD,EAAG9hB,EAAMhC,EAAQ,KAAK,CAAEA,EAChF,EAAG,CAACwE,EAAUsf,EAAW4B,EAAoBvc,EAAkB4a,EAAkB,CAEnF,GAjDA,CAkDA,IAAMqC,EAA6B,eAAmB,CAAC,MA4EvD,SAASC,EAAc1iB,CAAM,CAAEY,CAAW,CAAE+hB,CAAe,CAAEla,CAAM,MAwC7DpL,CAvCJ,CAACukB,KAEyE,SAAiB,IAC3F,GAAI,CACFzB,UAAAA,CAAS,CACV,CAAG,YAAgB,CAACoB,GACjB,CACF,QAASqB,CAAa,CACvB,CAAG,YAAgB,CAACnB,GACjBoB,EAAaD,CAAa,CAACA,EAAc,MAAM,CAAG,EAAE,CACpDE,EAAeD,EAAaA,EAAW,MAAM,CAAG,CAAC,CAChCA,CAAAA,GAAaA,EAAW,QAAQ,CACrD,IAAIE,EAAqBF,EAAaA,EAAW,YAAY,CAAG,GAC9CA,CAAAA,GAAcA,EAAW,KAAK,CAyBhD,IAAIG,EAAsBlB,IAE1B,GAAIlhB,EAAa,CACf,IAAIqiB,EACJ,IAAIC,EAAoB,AAAuB,UAAvB,OAAOtiB,EAA2B,SAAUA,GAAeA,CACnF,AAAyB,OAAvBmiB,GAA+B,CAAwD,MAAvDE,CAAAA,EAAwBC,EAAkB,QAAQ,AAAD,EAAa,KAAK,EAAID,EAAsB,UAAU,CAACF,EAAkB,GAAsb,SAAiB,IACnmB1lB,EAAW6lB,CACb,MACE7lB,EAAW2lB,EAEb,IAAIvmB,EAAWY,EAAS,QAAQ,EAAI,IAChCkG,EAAoB9G,EACxB,GAAIsmB,AAAuB,MAAvBA,EAA4B,CAe9B,IAAII,EAAiBJ,EAAmB,OAAO,CAAC,MAAO,IAAI,KAAK,CAAC,KAEjExf,EAAoB,IAAMjC,AADX7E,EAAS,OAAO,CAAC,MAAO,IAAI,KAAK,CAAC,KACd,KAAK,CAAC0mB,EAAe,MAAM,EAAE,IAAI,CAAC,IACvE,CACA,IAAItgB,EAAU,SAAY7C,EAAQ,CAChC,SAAUuD,CACZ,GAKI6f,EAAkBC,AAkIxB,SAAwBxgB,CAAO,CAAE+f,CAAa,CAAED,CAAe,CAAEla,CAAM,MACjE6a,EAWEC,EADN,GATIX,AAAkB,KAAK,IAAvBA,GACFA,CAAAA,EAAgB,EAAE,AAAD,EAEfD,AAAoB,KAAK,IAAzBA,GACFA,CAAAA,EAAkB,IAAG,EAEnBla,AAAW,KAAK,IAAhBA,GACFA,CAAAA,EAAS,IAAG,EAEV5F,AAAW,MAAXA,EAAiB,CAEnB,GAAI,CAAC8f,EACH,OAAO,KAET,GAAIA,EAAgB,MAAM,CAGxB9f,EAAU8f,EAAgB,OAAO,MAC5B,GAAI,AAAsB,MAArBY,CAAAA,EAAU9a,CAAK,IAAc8a,EAAQ,mBAAmB,EAAIX,AAAyB,IAAzBA,EAAc,MAAM,EAAWD,EAAgB,WAAW,GAAIA,CAAAA,EAAgB,OAAO,CAAC,MAAM,CAAG,GASrK,OAAO,KAFP9f,EAAU8f,EAAgB,OAAO,CAIrC,CACA,IAAIS,EAAkBvgB,EAGlB0G,EAAS,AAAwC,MAAvC+Z,CAAAA,EAAmBX,CAAc,EAAa,KAAK,EAAIW,EAAiB,MAAM,CAC5F,GAAI/Z,AAAU,MAAVA,EAAgB,CAClB,IAAIia,EAAaJ,EAAgB,SAAS,CAAC9Z,GAAKA,EAAE,KAAK,CAAC,EAAE,EAAI,AAACC,CAAAA,AAAU,MAAVA,EAAiB,KAAK,EAAIA,CAAM,CAACD,EAAE,KAAK,CAAC,EAAE,CAAC,AAAD,IAAO5I,KAAAA,EACjH,CAAE8iB,GAAc,GAAoK,SAAiB,IACrMJ,EAAkBA,EAAgB,KAAK,CAAC,EAAGjlB,KAAK,GAAG,CAACilB,EAAgB,MAAM,CAAEI,EAAa,GAC3F,CAIA,IAAIC,EAAiB,GACjBC,EAAgB,GACpB,GAAIf,GAAmBla,GAAUA,EAAO,mBAAmB,CACzD,IAAK,IAAI1M,EAAI,EAAGA,EAAIqnB,EAAgB,MAAM,CAAErnB,IAAK,CAC/C,IAAIyH,EAAQ4f,CAAe,CAACrnB,EAAE,CAK9B,GAHIyH,CAAAA,EAAM,KAAK,CAAC,eAAe,EAAIA,EAAM,KAAK,CAAC,sBAAsB,AAAD,GAClEkgB,CAAAA,EAAgB3nB,CAAAA,EAEdyH,EAAM,KAAK,CAAC,EAAE,CAAE,CAClB,GAAI,CACFI,WAAAA,CAAU,CACV2F,OAAAA,CAAM,CACP,CAAGoZ,EACAgB,EAAmBngB,EAAM,KAAK,CAAC,MAAM,EAAII,AAA+BlD,KAAAA,IAA/BkD,CAAU,CAACJ,EAAM,KAAK,CAAC,EAAE,CAAC,EAAmB,EAAC+F,GAAUA,AAA2B7I,KAAAA,IAA3B6I,CAAM,CAAC/F,EAAM,KAAK,CAAC,EAAE,CAAC,AAAa,EACxI,GAAIA,EAAM,KAAK,CAAC,IAAI,EAAImgB,EAAkB,CAIxCF,EAAiB,GAEfL,EADEM,GAAiB,EACDN,EAAgB,KAAK,CAAC,EAAGM,EAAgB,GAEzC,CAACN,CAAe,CAAC,EAAE,CAAC,CAExC,KACF,CACF,CACF,CAEF,OAAOA,EAAgB,WAAW,CAAC,CAACQ,EAAQpgB,EAAOxF,SA8ThC9B,EAAWwB,EA3T5B,IADI+B,EACAokB,EAA8B,GAC9BC,EAAe,KACfC,EAAyB,KACzBpB,IACFljB,EAAQ8J,GAAU/F,EAAM,KAAK,CAAC,EAAE,CAAG+F,CAAM,CAAC/F,EAAM,KAAK,CAAC,EAAE,CAAC,CAAG9C,KAAAA,EAC5DojB,EAAetgB,EAAM,KAAK,CAAC,YAAY,EAAIwgB,EACvCP,IACEC,EAAgB,GAAK1lB,AAAU,IAAVA,GAoTZ9B,EAnTC,iBAmTUwB,EAnTe,EAoT/BumB,CAAa,CAAC/nB,EAAI,EAC9B+nB,CAAAA,CAAa,CAAC/nB,EAAI,CAAG,EAAG,EApTlB2nB,EAA8B,GAC9BE,EAAyB,MAChBL,IAAkB1lB,IAC3B6lB,EAA8B,GAC9BE,EAAyBvgB,EAAM,KAAK,CAAC,sBAAsB,EAAI,QAIrE,IAAIX,EAAU+f,EAAc,MAAM,CAACQ,EAAgB,KAAK,CAAC,EAAGplB,EAAQ,IAChEkmB,EAAc,KAChB,IAAI5O,EAkBJ,OAhBEA,EADE7V,EACSqkB,EACFD,EACEE,EACFvgB,EAAM,KAAK,CAAC,SAAS,CAON,eAAmB,CAACA,EAAM,KAAK,CAAC,SAAS,CAAE,MAC1DA,EAAM,KAAK,CAAC,OAAO,CACjBA,EAAM,KAAK,CAAC,OAAO,CAEnBogB,EAEO,eAAmB,CAACO,EAAe,CACrD,MAAO3gB,EACP,aAAc,CACZogB,OAAAA,EACA/gB,QAAAA,EACA,YAAa8f,AAAmB,MAAnBA,CACf,EACA,SAAUrN,CACZ,EACF,EAIA,OAAOqN,GAAoBnf,CAAAA,EAAM,KAAK,CAAC,aAAa,EAAIA,EAAM,KAAK,CAAC,YAAY,EAAIxF,AAAU,IAAVA,CAAU,EAAkB,eAAmB,CAAComB,EAAqB,CACvJ,SAAUzB,EAAgB,QAAQ,CAClC,aAAcA,EAAgB,YAAY,CAC1C,UAAWmB,EACX,MAAOrkB,EACP,SAAUykB,IACV,aAAc,CACZ,OAAQ,KACRrhB,QAAAA,EACA,YAAa,EACf,CACF,GAAKqhB,GACP,EAAG,KACL,EA9QuCrhB,GAAWA,EAAQ,GAAG,CAACW,GAAS3H,OAAO,MAAM,CAAC,CAAC,EAAG2H,EAAO,CAC5F,OAAQ3H,OAAO,MAAM,CAAC,CAAC,EAAGinB,EAActf,EAAM,MAAM,EACpD,SAAU,SAAU,CAACuf,EAErB5C,EAAU,cAAc,CAAGA,EAAU,cAAc,CAAC3c,EAAM,QAAQ,EAAE,QAAQ,CAAGA,EAAM,QAAQ,CAAC,EAC9F,aAAcA,AAAuB,MAAvBA,EAAM,YAAY,CAAWuf,EAAqB,SAAU,CAACA,EAE3E5C,EAAU,cAAc,CAAGA,EAAU,cAAc,CAAC3c,EAAM,YAAY,EAAE,QAAQ,CAAGA,EAAM,YAAY,CAAC,CACxG,IAAKof,EAAeD,EAAiBla,UAKrC,AAAI7H,GAAewiB,EACG,eAAmB,CAAC5B,EAAgB,QAAQ,CAAE,CAChE,MAAO,CACL,SAAU5lB,EAAS,CACjB,SAAU,IACV,OAAQ,GACR,KAAM,GACN,MAAO,KACP,IAAK,SACP,EAAGyB,GACH,eAAgB,QAAU,AAC5B,CACF,EAAG+lB,GAEEA,CACT,CA+BA,IAAMY,EAAmC,eAAmB,CA9B5D,eA6WMK,MACA5kB,EACAvB,EACA4P,EA/WArO,GA6WAA,EAAQ,YAAgB,CAACiiB,GACzBxjB,EAAQomB,EAAmBvG,EAAoB,aAAa,EAC5DjQ,EAAUwU,EAAkBvE,EAAoB,aAAa,EAIjE,AAAIte,AAAUiB,KAAAA,IAAVjB,EACKA,EAIF,AAAkC,MAAjC4kB,CAAAA,EAAgBnmB,EAAM,MAAM,AAAD,EAAa,KAAK,EAAImmB,CAAa,CAACvW,EAAQ,EAvX3EpQ,EAAU,SAAqB+B,GAASA,EAAM,MAAM,CAAG,IAAMA,EAAM,UAAU,CAAGA,aAAiB9B,MAAQ8B,EAAM,OAAO,CAAGlC,KAAK,SAAS,CAACkC,GACxI8kB,EAAQ9kB,aAAiB9B,MAAQ8B,EAAM,KAAK,CAAG,KAmBnD,OAAoB,eAAmB,CAAC,UAAc,CAAE,KAAmB,eAAmB,CAAC,KAAM,KAAM,iCAA+C,eAAmB,CAAC,KAAM,CAClL,MAAO,CACL,UAAW,QACb,CACF,EAAG/B,GAAU6mB,EAAqB,eAAmB,CAAC,MAAO,CAC3D,MAtBc,CACd,QAAS,SACT,gBAHc,wBAIhB,CAoBA,EAAGA,GAAS,KAfE,KAgBhB,EACoF,KACpF,OAAMH,UAA4B,WAAe,CAC/C,YAAYI,CAAK,CAAE,CACjB,KAAK,CAACA,GACN,IAAI,CAAC,KAAK,CAAG,CACX,SAAUA,EAAM,QAAQ,CACxB,aAAcA,EAAM,YAAY,CAChC,MAAOA,EAAM,KAAK,AACpB,CACF,CACA,OAAO,yBAAyB/kB,CAAK,CAAE,CACrC,MAAO,CACL,MAAOA,CACT,CACF,CACA,OAAO,yBAAyB+kB,CAAK,CAAEtmB,CAAK,CAAE,QAS5C,AAAIA,EAAM,QAAQ,GAAKsmB,EAAM,QAAQ,EAAItmB,AAAuB,SAAvBA,EAAM,YAAY,EAAesmB,AAAuB,SAAvBA,EAAM,YAAY,CACnF,CACL,MAAOA,EAAM,KAAK,CAClB,SAAUA,EAAM,QAAQ,CACxB,aAAcA,EAAM,YAAY,AAClC,EAOK,CACL,MAAOA,AAAgB9jB,KAAAA,IAAhB8jB,EAAM,KAAK,CAAiBA,EAAM,KAAK,CAAGtmB,EAAM,KAAK,CAC5D,SAAUA,EAAM,QAAQ,CACxB,aAAcsmB,EAAM,YAAY,EAAItmB,EAAM,YAAY,AACxD,CACF,CACA,kBAAkBuB,CAAK,CAAEglB,CAAS,CAAE,CAClC5mB,QAAQ,KAAK,CAAC,wDAAyD4B,EAAOglB,EAChF,CACA,QAAS,CACP,OAAO,AAAqB/jB,KAAAA,IAArB,IAAI,CAAC,KAAK,CAAC,KAAK,CAA8B,eAAmB,CAAC+gB,EAAa,QAAQ,CAAE,CAC9F,MAAO,IAAI,CAAC,KAAK,CAAC,YAAY,AAChC,EAAgB,eAAmB,CAACC,EAAkB,QAAQ,CAAE,CAC9D,MAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACvB,SAAU,IAAI,CAAC,KAAK,CAAC,SAAS,AAChC,IAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,AAC3B,CACF,CACA,SAASyC,EAAc/lB,CAAI,EACzB,GAAI,CACFsmB,aAAAA,CAAY,CACZlhB,MAAAA,CAAK,CACL8R,SAAAA,CAAQ,CACT,CAAGlX,EACAgiB,EAAoB,YAAgB,CAACiB,GAOzC,OAHIjB,GAAqBA,EAAkB,MAAM,EAAIA,EAAkB,aAAa,EAAK5c,CAAAA,EAAM,KAAK,CAAC,YAAY,EAAIA,EAAM,KAAK,CAAC,aAAa,AAAD,GAC3I4c,CAAAA,EAAkB,aAAa,CAAC,0BAA0B,CAAG5c,EAAM,KAAK,CAAC,EAAE,AAAD,EAExD,eAAmB,CAACie,EAAa,QAAQ,CAAE,CAC7D,MAAOiD,CACT,EAAGpP,EACL,CA8IA,IAAIwI,GACFA,CAD0CA,EAK1CA,GAAkB,CAAC,GAJJ,UAAa,CAAG,aAC/BA,EAAe,cAAiB,CAAG,iBACnCA,EAAe,iBAAoB,CAAG,cAC/BA,GAELC,GACFA,CAD+CA,EAY/CA,GAAuB,CAAC,GAXJ,UAAa,CAAG,aACpCA,EAAoB,aAAgB,CAAG,gBACvCA,EAAoB,aAAgB,CAAG,gBACvCA,EAAoB,aAAgB,CAAG,gBACvCA,EAAoB,aAAgB,CAAG,gBACvCA,EAAoB,kBAAqB,CAAG,qBAC5CA,EAAoB,UAAa,CAAG,aACpCA,EAAoB,cAAiB,CAAG,iBACxCA,EAAoB,iBAAoB,CAAG,cAC3CA,EAAoB,UAAa,CAAG,aAC7BA,GAUT,SAASuG,EAAmBjC,CAAQ,EAClC,IAAInkB,EAAQ,YAAgB,CAACojB,GAE7B,OADA,AAACpjB,GAA+G,SAAiB,IAC1HA,CACT,CAQA,SAASokB,EAAkBD,CAAQ,MAN7BjiB,EAOAA,GANJ,CADIA,EAAQ,YAAgB,CAACqhB,KACmF,SAAiB,IAC1HrhB,GAMHukB,EAAYvkB,EAAM,OAAO,CAACA,EAAM,OAAO,CAAC,MAAM,CAAG,EAAE,CAEvD,OADA,AAACukB,EAAU,KAAK,CAAC,EAAE,EAA4I,SAAiB,IACzKA,EAAU,KAAK,CAAC,EAAE,AAC3B,CAmCA,SAASC,IACP,GAAI,CACF/hB,QAAAA,CAAO,CACPe,WAAAA,CAAU,CACX,CAAG0gB,EAAmBvG,EAAoB,UAAU,EACrD,OAAO,SAAa,CAAC,IAAMlb,EAAQ,GAAG,CAACyG,GAAK,SAAkCA,EAAG1F,IAAc,CAACf,EAASe,EAAW,CACtH,CAqKA,IAAMqgB,EAAgB,CAAC,EA+MvB,SAASY,EAAOL,CAAK,MAh2BFM,MACblB,EAg2BJ,OAj2BiBkB,EAi2BAN,EAAM,OAAO,CA/1B9B,CADIZ,EAAS,YAAgB,CAACnC,GAAc,MAAM,EAE5B,eAAmB,CAACgB,EAAc,QAAQ,CAAE,CAC9D,MAAOqC,CACT,EAAGlB,GAEEA,CA21BT,CAMA,SAASzC,EAAM4D,CAAM,EAC4L,SAAiB,GAClO,CAUA,SAASC,EAAOC,CAAK,EACnB,GAAI,CACF,SAAUC,EAAe,GAAG,CAC5B5P,SAAAA,EAAW,IAAI,CACf,SAAU6P,CAAY,CACtBC,eAAAA,EAAiB,QAAU,CAC3BjF,UAAAA,CAAS,CACT,OAAQkF,EAAa,EAAK,CAC1B5c,OAAAA,CAAM,CACP,CAAGwc,CACJ,CAAErD,KAAwM,SAAiB,IAI3N,IAAI/gB,EAAWqkB,EAAa,OAAO,CAAC,OAAQ,KACxCI,EAAoB,SAAa,CAAC,IAAO,EAC3CzkB,SAAAA,EACAsf,UAAAA,EACA,OAAQkF,EACR,OAAQzpB,EAAS,CACf,qBAAsB,EACxB,EAAG6M,EACL,GAAI,CAAC5H,EAAU4H,EAAQ0X,EAAWkF,EAAW,CACzC,AAAwB,WAAxB,OAAOF,GACTA,CAAAA,EAAe,SAAUA,EAAY,EAEvC,GAAI,CACF1oB,SAAAA,EAAW,GAAG,CACdC,OAAAA,EAAS,EAAE,CACXC,KAAAA,EAAO,EAAE,CACTuB,MAAAA,EAAQ,IAAI,CACZhC,IAAAA,EAAM,SAAS,CAChB,CAAGipB,EACAI,EAAkB,SAAa,CAAC,KAClC,IAAIC,EAAmB,SAAc/oB,EAAUoE,UAC/C,AAAI2kB,AAAoB,MAApBA,EACK,KAEF,CACL,SAAU,CACR,SAAUA,EACV9oB,OAAAA,EACAC,KAAAA,EACAuB,MAAAA,EACAhC,IAAAA,CACF,EACAkpB,eAAAA,CACF,CACF,EAAG,CAACvkB,EAAUpE,EAAUC,EAAQC,EAAMuB,EAAOhC,EAAKkpB,EAAe,SAEjE,AAAIG,AAAmB,MAAnBA,EACK,KAEW,eAAmB,CAAChE,EAAkB,QAAQ,CAAE,CAClE,MAAO+D,CACT,EAAgB,eAAmB,CAAC9D,EAAgB,QAAQ,CAAE,CAC5D,SAAUlM,EACV,MAAOiQ,CACT,GACF,CAhQ4B,kBADH,eAC0B,CA6RnD,IAAInE,GACFA,CAD6CA,EAK7CA,GAAqB,CAAC,EAJL,CAACA,EAAkB,OAAU,CAAG,EAAE,CAAG,UACtDA,CAAiB,CAACA,EAAkB,OAAU,CAAG,EAAE,CAAG,UACtDA,CAAiB,CAACA,EAAkB,KAAQ,CAAG,EAAE,CAAG,QAC7CA,GAqKT,SAASnhB,EAAmBG,CAAK,EAC/B,IAAI6P,EAAU,CAGZ,iBAAkB7P,AAAuB,MAAvBA,EAAM,aAAa,EAAYA,AAAsB,MAAtBA,EAAM,YAAY,AACrE,EAkCA,OAjCIA,EAAM,SAAS,EAMjBvE,OAAO,MAAM,CAACoU,EAAS,CACrB,QAAsB,eAAmB,CAAC7P,EAAM,SAAS,EACzD,UAAWM,KAAAA,CACb,GAEEN,EAAM,eAAe,EAMvBvE,OAAO,MAAM,CAACoU,EAAS,CACrB,uBAAqC,eAAmB,CAAC7P,EAAM,eAAe,EAC9E,gBAAiBM,KAAAA,CACnB,GAEEN,EAAM,aAAa,EAMrBvE,OAAO,MAAM,CAACoU,EAAS,CACrB,aAA2B,eAAmB,CAAC7P,EAAM,aAAa,EAClE,cAAeM,KAAAA,CACjB,GAEKuP,CACT,CA3M4B,IAAI0D,QAAQ,KAAO,GACd,WAAe"}