{"version": 3, "file": "static/js/main.559d37c1.js", "sources": ["webpack://web-dashboard/./node_modules/.modern-js/main/runtime-global-context.js", "webpack://web-dashboard/./node_modules/.modern-js/main/routes.js", "webpack://web-dashboard/./node_modules/.modern-js/main/runtime-register.js", "webpack://web-dashboard/./node_modules/.modern-js/main/index.jsx", "webpack://web-dashboard/./src/modern.runtime.ts", "webpack://web-dashboard/./src/web-dashboard/routes/layout.tsx", "webpack://web-dashboard/"], "sourcesContent": ["import { setGlobalContext } from '@modern-js/runtime/context';\nlet appConfig;\nlet appInit;\nlet layoutApp;\n\nimport { routes } from './routes';\n\nconst entryName = 'main';\nsetGlobalContext({\n  entryName,\n  layoutApp,\n  routes,\n  appInit,\n  appConfig,\n});", "\n    \n    import { lazy } from \"react\";\n    import loadable, { lazy as loadableLazy } from \"@modern-js/runtime/loadable\"\n  \n    \n    \n    import { createShouldRevalidate, handleRouteModule,  handleRouteModuleError} from '@modern-js/runtime/router';\n  \n    import RootLayout from '@_modern_js_src/web-dashboard/routes/layout'\n    \n    \n    \n    \n    \n    if(typeof document !== 'undefined'){\n      window._routeModules = {}\n    }\n  \n    \n    export const routes = [\n  {\n  \"path\": \"/\",\n  \"children\": [\n    {\n      \"_component\": \"@_modern_js_src/web-dashboard/routes/page\",\n      \"index\": true,\n      \"id\": \"page\",\n      \"type\": \"nested\",\n      \"lazyImport\": () => import(/* webpackChunkName: \"page\" */ '@_modern_js_src/web-dashboard/routes/page').then(routeModule => handleRouteModule(routeModule, \"page\")).catch(handleRouteModuleError),\n      \"component\": lazy(() => import(/* webpackChunkName: \"page\" */ '@_modern_js_src/web-dashboard/routes/page').then(routeModule => handleRouteModule(routeModule, \"page\")).catch(handleRouteModuleError))\n    }\n  ],\n  \"isRoot\": true,\n  \"_component\": \"@_modern_js_src/web-dashboard/routes/layout\",\n  \"id\": \"layout\",\n  \"type\": \"nested\",\n  \"lazyImport\": () => import('@_modern_js_src/web-dashboard/routes/layout').then(routeModule => handleRouteModule(routeModule, \"layout\")).catch(handleRouteModuleError),\n  \"component\": RootLayout\n},\n];\n  ", "import { registerPlugin, mergeConfig } from '@modern-js/runtime/plugin';\nimport { getGlobalAppConfig, getGlobalLayoutApp, getCurrentEntryName } from '@modern-js/runtime/context';\n\nimport modernRuntime from '@_modern_js_src/modern.runtime';\nconst runtimeConfig = typeof modernRuntime === 'function' ? modernRuntime(getCurrentEntryName()) : modernRuntime\n\nconst plugins = [];\n\nimport { routerPlugin } from '@modern-js/runtime/router';\n\nplugins.push(routerPlugin(mergeConfig({\"serverBase\":[\"/frontend\"]}, (runtimeConfig || {})['router'], ((runtimeConfig || {})['routerByEntries'] || {})['main'], (getGlobalAppConfig() || {})['router'])));\n\nregisterPlugin(plugins, runtimeConfig);\n", "import '@modern-js/runtime/registry/main';\nimport { createRoot } from '@modern-js/runtime/react';\nimport { render } from '@modern-js/runtime/browser';\n\n\n\n\n\n\n\n\n\nconst ModernRoot = createRoot();\n\nrender(<ModernRoot />, 'root');\n", "import { defineRuntimeConfig } from '@modern-js/runtime';\r\n\r\nexport default defineRuntimeConfig({});\r\n", "import { Outlet } from '@modern-js/runtime/router';\r\nimport './index.css'\r\nexport default function Layout() {\r\n  return (\r\n    <div>\r\n      <Outlet />\r\n    </div>\r\n  );\r\n}\r\n", "import \"core-js\";"], "names": ["appConfig", "appInit", "layoutApp", "document", "window", "routes", "routeModule", "handleRouteModule", "handleRouteModuleError", "lazy", "RootLayout", "setGlobalContext", "entryName", "runtimeConfig", "modernRuntime", "getCurrentEntryName", "plugins", "routerPlugin", "mergeConfig", "getGlobalAppConfig", "registerPlugin", "ModernRoot", "createRoot", "render", "defineRuntimeConfig", "Layout", "Outlet"], "mappings": "6IACIA,EACAC,EACAC,E,qDCYG,AAAoB,cAApB,OAAOC,UACRC,CAAAA,OAAO,aAAa,CAAG,CAAC,GAInB,IAAMC,EAAS,CACxB,CACA,KAAQ,IACR,SAAY,CACV,CACE,WAAc,4CACd,MAAS,GACT,GAAM,OACN,KAAQ,SACR,WAAc,IAAM,2DAAmF,IAAI,CAACC,AAAAA,GAAeC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAkBD,EAAa,SAAS,KAAK,CAACE,EAAAA,EAAsBA,EAC/L,UAAaC,AAAAA,GAAAA,EAAAA,IAAAA,AAAAA,EAAK,IAAM,2DAAmF,IAAI,CAACH,AAAAA,GAAeC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAkBD,EAAa,SAAS,KAAK,CAACE,EAAAA,EAAsBA,EACrM,EACD,CACD,OAAU,GACV,WAAc,8CACd,GAAM,SACN,KAAQ,SACR,WAAc,IAAM,wCAAsD,IAAI,CAACF,AAAAA,GAAeC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAkBD,EAAa,WAAW,KAAK,CAACE,EAAAA,EAAsBA,EACpK,UAAaE,EAAAA,OAAUA,AACzB,EACC,CDhCDC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAiB,CACfC,UAFgB,OAGhBV,UAAAA,EACAG,OAAMA,EACNJ,QAAAA,EACAD,UAAAA,CACF,G,qCEVMa,EAAgB,AAAyB,YAAzB,OAAOC,EAAAA,CAAaA,CAAkBA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAcC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,KAAyBD,EAAAA,CAAaA,CAE1GE,EAAU,EAAE,CAIlBA,EAAQ,IAAI,CAACC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAY,CAAC,WAAa,CAAC,YAAY,EAAIL,AAAAA,CAAAA,GAAiB,CAAC,GAAG,MAAS,CAAG,AAACA,CAAAA,CAAAA,GAAiB,CAAC,GAAG,eAAkB,EAAI,CAAC,GAAG,IAAO,CAAGM,AAAAA,CAAAA,GAAAA,EAAAA,EAAAA,AAAAA,KAAwB,CAAC,GAAG,MAAS,IAErMC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAeJ,EAASH,G,0BCAlBQ,EAAaC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,IAEnBC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAO,UAACF,EAAAA,CAAAA,GAAe,O,wBCZvB,IAAeG,AAAAA,GAAAA,A,SAAAA,EAAAA,AAAAA,EAAoB,CAAC,E,gFCArB,SAASC,IACtB,MACE,UAAC,O,SACC,UAACC,EAAAA,EAAMA,CAAAA,CAAAA,E,EAGb,C,gsFCRAtB,OAAO,eAAe,CAAG,W"}