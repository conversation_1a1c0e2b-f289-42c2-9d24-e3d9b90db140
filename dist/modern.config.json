[{"runtime": "1", "runtimeByEntries": "2", "source": "3", "tools": "4", "output": "5", "server": "6", "dev": "7", "html": "8", "plugins": "9", "builderPlugins": "10", "_raw": "11", "autoLoadPlugins": false, "bff": "12", "testing": "13", "deploy": "14", "performance": "15"}, {"router": true}, {}, {"alias": "16", "globalVars": "17", "mainEntryName": "18", "enableAsyncEntry": false, "enableCustomEntry": false, "disableDefaultEntries": false, "entriesDir": "19", "configDir": "20", "include": "21"}, {"styledComponents": "22", "bundlerChain": "23", "tsChecker": "24", "devServer": "25"}, {"distPath": "26", "cleanDistPath": true, "disableNodePolyfill": true, "enableInlineRouteManifests": true, "disableInlineRouteManifests": false, "assetPrefix": "27"}, {"baseUrl": "27", "port": 8080}, {"cliShortcuts": "28"}, {"title": "29", "mountId": "30", "meta": "31", "favicon": "32"}, ["33", "34"], ["35"], {"runtime": "36", "server": "37", "output": "38", "plugins": "39", "tools": "40"}, {}, {}, {}, {"buildCache": false}, {"styled-components": "41", "@modern-js/runtime/plugins": "42", "@meta/runtime/browser$": "43", "@meta/runtime/react$": "44", "@meta/runtime/context$": "45", "@meta/runtime$": "46", "@_modern_js_internal": "47", "@_modern_js_src": "48", "@": "48", "@shared": "49", "@modern-js/runtime-utils/node$": "50"}, {"process.env.IS_REACT18": "51", "process.env.MODERN_JS_VERSION": "52"}, "main", "./src", "./config", ["53", "54", "55", "47"], {"topLevelImportPaths": "56"}, [null, null], {"issue": "57"}, {"proxy": "58"}, {"root": "59", "html": "60", "js": "61", "css": "62", "server": "63", "worker": "64"}, "/frontend", {"help": false}, "", "root", {"charset": "65", "viewport": "66", "http-equiv": "67", "renderer": "68", "layoutmode": "69", "imagemode": "70", "wap-font-scale": "71", "format-detection": "72"}, "D:\\workspace\\auto-project\\app\\web-dashboard\\config\\favicon.ico", {"name": "73", "usePlugins": "74", "post": "75", "registryHooks": "76"}, {"name": "77", "usePlugins": "78"}, {"name": "79"}, {"router": true}, {"baseUrl": "27"}, {"assetPrefix": "27", "distPath": "80"}, ["33", "34"], {"devServer": "81"}, "D:\\workspace\\auto-project\\node_modules\\.pnpm\\styled-components@5.3.11_@b_b2c58dcc0f52d3bf8dbbb16bcae7e4b1\\node_modules\\styled-components\\dist\\styled-components.cjs.js", "D:\\workspace\\auto-project\\app\\web-dashboard\\node_modules\\.modern-js\\.runtime-exports\\plugins.js", "D:\\workspace\\auto-project\\node_modules\\.pnpm\\@modern-js+runtime@2.67.1_r_1e7dfa6baa23377df6a085d32e9326ac\\node_modules\\@modern-js\\runtime\\dist\\esm\\core\\browser\\index.js", "D:\\workspace\\auto-project\\node_modules\\.pnpm\\@modern-js+runtime@2.67.1_r_1e7dfa6baa23377df6a085d32e9326ac\\node_modules\\@modern-js\\runtime\\dist\\esm\\core\\react\\index.js", "D:\\workspace\\auto-project\\node_modules\\.pnpm\\@modern-js+runtime@2.67.1_r_1e7dfa6baa23377df6a085d32e9326ac\\node_modules\\@modern-js\\runtime\\dist\\esm\\core\\context\\index.js", "D:\\workspace\\auto-project\\node_modules\\.pnpm\\@modern-js+runtime@2.67.1_r_1e7dfa6baa23377df6a085d32e9326ac\\node_modules\\@modern-js\\runtime\\dist\\esm\\index.js", "D:\\workspace\\auto-project\\app\\web-dashboard\\node_modules\\.modern-js", "D:\\workspace\\auto-project\\app\\web-dashboard\\src", "D:\\workspace\\auto-project\\app\\web-dashboard\\shared", "D:\\workspace\\auto-project\\node_modules\\.pnpm\\@modern-js+runtime-utils@2._1352f4881980124c6b42a3da81722bf2\\node_modules\\@modern-js\\runtime-utils\\dist\\esm\\node\\index.js", "true", "2.67.1", {}, {}, {}, ["82"], {"exclude": "83"}, {"/api": "84"}, "D:\\workspace\\auto-project\\frontend_static\\dist", "html", "static/js", "static/css", "bundles", "worker", {"charset": "85"}, "width=device-width, initial-scale=1.0, shrink-to-fit=no, viewport-fit=cover, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no", {"http-equiv": "86", "content": "87"}, "webkit", "standard", "force", "no", "telephone=no", "@modern-js/app-tools", ["88", "89", "90", "91", "92"], ["93", "94", "95", "96", "97", "98", "99", "100"], {"onAfterPrepare": "101", "deploy": "102", "checkEntryPoint": "103", "modifyEntrypoints": "104", "modifyFileSystemRoutes": "105", "generateEntryCode": "106", "onBeforeGenerateRoutes": "107", "onBeforePrintInstructions": "108", "registerDev": "109", "registerBuildPlatform": "110", "addRuntimeExports": "111"}, "@modern-js/plugin-tailwindcss", ["112"], "@modern-js/builder-plugin-ssr", {"root": "59"}, {"proxy": "113"}, "@modern-js/runtime/styled", ["114"], {"target": "115", "changeOrigin": true, "secure": false, "pathRewrite": "116"}, "utf-8", "x-ua-compatible", "ie=edge", {"name": "117", "registryHooks": "118"}, {"name": "93", "post": "119"}, {"name": "94", "post": "120"}, {"name": "121"}, {"name": "122"}, "@modern-js/plugin-initialize", "@modern-js/plugin-analyze", "@modern-js/plugin-ssr", "@modern-js/plugin-document", "@modern-js/plugin-state", "@modern-js/plugin-router", "@modern-js/plugin-router-v5", "@modern-js/plugin-polyfill", {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {"name": "123"}, {"/api": "124"}, {"file": "125"}, "http://172.16.101.91:8000/", {"^/api": "126"}, "@modern-js/app-tools-compat", {"jestConfig": "127", "afterTest": "128"}, ["95", "96", "97", "98", "99", "100"], ["129"], "@modern-js/server-build", "@modern-js/plugin-deploy", "@modern-js/plugin-design-token", {"target": "115", "changeOrigin": true, "secure": false, "pathRewrite": "130"}, "**/api/lambda/**/*", "/api", {}, {}, "@modern-js/runtime", {"^/api": "126"}]