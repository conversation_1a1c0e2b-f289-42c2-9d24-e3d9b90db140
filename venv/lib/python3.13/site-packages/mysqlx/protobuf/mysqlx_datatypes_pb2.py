# Copyright (c) 2017, 2020, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0, as
# published by the Free Software Foundation.
#
# This program is also distributed with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an
# additional permission to link the program and your derivative works
# with the separately licensed software that they have included with
# MySQL.
#
# Without limiting anything contained in the foregoing, this file,
# which is part of MySQL Connector/Python, is also subject to the
# Universal FOSS Exception, version 1.0, a copy of which can be found at
# http://oss.oracle.com/licenses/universal-foss-exception.
#
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
# See the GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mysqlx_datatypes.proto

# type: ignore

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='mysqlx_datatypes.proto',
  package='Mysqlx.Datatypes',
  syntax='proto2',
  serialized_pb=_b('\n\x16mysqlx_datatypes.proto\x12\x10Mysqlx.Datatypes\"\xc6\x03\n\x06Scalar\x12+\n\x04type\x18\x01 \x02(\x0e\x32\x1d.Mysqlx.Datatypes.Scalar.Type\x12\x14\n\x0cv_signed_int\x18\x02 \x01(\x12\x12\x16\n\x0ev_unsigned_int\x18\x03 \x01(\x04\x12\x31\n\x08v_octets\x18\x05 \x01(\x0b\x32\x1f.Mysqlx.Datatypes.Scalar.Octets\x12\x10\n\x08v_double\x18\x06 \x01(\x01\x12\x0f\n\x07v_float\x18\x07 \x01(\x02\x12\x0e\n\x06v_bool\x18\x08 \x01(\x08\x12\x31\n\x08v_string\x18\t \x01(\x0b\x32\x1f.Mysqlx.Datatypes.Scalar.String\x1a*\n\x06String\x12\r\n\x05value\x18\x01 \x02(\x0c\x12\x11\n\tcollation\x18\x02 \x01(\x04\x1a-\n\x06Octets\x12\r\n\x05value\x18\x01 \x02(\x0c\x12\x14\n\x0c\x63ontent_type\x18\x02 \x01(\r\"m\n\x04Type\x12\n\n\x06V_SINT\x10\x01\x12\n\n\x06V_UINT\x10\x02\x12\n\n\x06V_NULL\x10\x03\x12\x0c\n\x08V_OCTETS\x10\x04\x12\x0c\n\x08V_DOUBLE\x10\x05\x12\x0b\n\x07V_FLOAT\x10\x06\x12\n\n\x06V_BOOL\x10\x07\x12\x0c\n\x08V_STRING\x10\x08\"}\n\x06Object\x12\x31\n\x03\x66ld\x18\x01 \x03(\x0b\x32$.Mysqlx.Datatypes.Object.ObjectField\x1a@\n\x0bObjectField\x12\x0b\n\x03key\x18\x01 \x02(\t\x12$\n\x05value\x18\x02 \x02(\x0b\x32\x15.Mysqlx.Datatypes.Any\"-\n\x05\x41rray\x12$\n\x05value\x18\x01 \x03(\x0b\x32\x15.Mysqlx.Datatypes.Any\"\xd3\x01\n\x03\x41ny\x12(\n\x04type\x18\x01 \x02(\x0e\x32\x1a.Mysqlx.Datatypes.Any.Type\x12(\n\x06scalar\x18\x02 \x01(\x0b\x32\x18.Mysqlx.Datatypes.Scalar\x12%\n\x03obj\x18\x03 \x01(\x0b\x32\x18.Mysqlx.Datatypes.Object\x12&\n\x05\x61rray\x18\x04 \x01(\x0b\x32\x17.Mysqlx.Datatypes.Array\")\n\x04Type\x12\n\n\x06SCALAR\x10\x01\x12\n\n\x06OBJECT\x10\x02\x12\t\n\x05\x41RRAY\x10\x03\x42\x1b\n\x17\x63om.mysql.cj.x.protobufH\x03')
)
_sym_db.RegisterFileDescriptor(DESCRIPTOR)



_SCALAR_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='Mysqlx.Datatypes.Scalar.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='V_SINT', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='V_UINT', index=1, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='V_NULL', index=2, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='V_OCTETS', index=3, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='V_DOUBLE', index=4, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='V_FLOAT', index=5, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='V_BOOL', index=6, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='V_STRING', index=7, number=8,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=390,
  serialized_end=499,
)
_sym_db.RegisterEnumDescriptor(_SCALAR_TYPE)

_ANY_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='Mysqlx.Datatypes.Any.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SCALAR', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OBJECT', index=1, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ARRAY', index=2, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=846,
  serialized_end=887,
)
_sym_db.RegisterEnumDescriptor(_ANY_TYPE)


_SCALAR_STRING = _descriptor.Descriptor(
  name='String',
  full_name='Mysqlx.Datatypes.Scalar.String',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='Mysqlx.Datatypes.Scalar.String.value', index=0,
      number=1, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='collation', full_name='Mysqlx.Datatypes.Scalar.String.collation', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=299,
  serialized_end=341,
)

_SCALAR_OCTETS = _descriptor.Descriptor(
  name='Octets',
  full_name='Mysqlx.Datatypes.Scalar.Octets',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='Mysqlx.Datatypes.Scalar.Octets.value', index=0,
      number=1, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='content_type', full_name='Mysqlx.Datatypes.Scalar.Octets.content_type', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=343,
  serialized_end=388,
)

_SCALAR = _descriptor.Descriptor(
  name='Scalar',
  full_name='Mysqlx.Datatypes.Scalar',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='Mysqlx.Datatypes.Scalar.type', index=0,
      number=1, type=14, cpp_type=8, label=2,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='v_signed_int', full_name='Mysqlx.Datatypes.Scalar.v_signed_int', index=1,
      number=2, type=18, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='v_unsigned_int', full_name='Mysqlx.Datatypes.Scalar.v_unsigned_int', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='v_octets', full_name='Mysqlx.Datatypes.Scalar.v_octets', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='v_double', full_name='Mysqlx.Datatypes.Scalar.v_double', index=4,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='v_float', full_name='Mysqlx.Datatypes.Scalar.v_float', index=5,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='v_bool', full_name='Mysqlx.Datatypes.Scalar.v_bool', index=6,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='v_string', full_name='Mysqlx.Datatypes.Scalar.v_string', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[_SCALAR_STRING, _SCALAR_OCTETS, ],
  enum_types=[
    _SCALAR_TYPE,
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=45,
  serialized_end=499,
)


_OBJECT_OBJECTFIELD = _descriptor.Descriptor(
  name='ObjectField',
  full_name='Mysqlx.Datatypes.Object.ObjectField',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='Mysqlx.Datatypes.Object.ObjectField.key', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='Mysqlx.Datatypes.Object.ObjectField.value', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=562,
  serialized_end=626,
)

_OBJECT = _descriptor.Descriptor(
  name='Object',
  full_name='Mysqlx.Datatypes.Object',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fld', full_name='Mysqlx.Datatypes.Object.fld', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[_OBJECT_OBJECTFIELD, ],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=501,
  serialized_end=626,
)


_ARRAY = _descriptor.Descriptor(
  name='Array',
  full_name='Mysqlx.Datatypes.Array',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='Mysqlx.Datatypes.Array.value', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=628,
  serialized_end=673,
)


_ANY = _descriptor.Descriptor(
  name='Any',
  full_name='Mysqlx.Datatypes.Any',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='Mysqlx.Datatypes.Any.type', index=0,
      number=1, type=14, cpp_type=8, label=2,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='scalar', full_name='Mysqlx.Datatypes.Any.scalar', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obj', full_name='Mysqlx.Datatypes.Any.obj', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='array', full_name='Mysqlx.Datatypes.Any.array', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ANY_TYPE,
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=676,
  serialized_end=887,
)

_SCALAR_STRING.containing_type = _SCALAR
_SCALAR_OCTETS.containing_type = _SCALAR
_SCALAR.fields_by_name['type'].enum_type = _SCALAR_TYPE
_SCALAR.fields_by_name['v_octets'].message_type = _SCALAR_OCTETS
_SCALAR.fields_by_name['v_string'].message_type = _SCALAR_STRING
_SCALAR_TYPE.containing_type = _SCALAR
_OBJECT_OBJECTFIELD.fields_by_name['value'].message_type = _ANY
_OBJECT_OBJECTFIELD.containing_type = _OBJECT
_OBJECT.fields_by_name['fld'].message_type = _OBJECT_OBJECTFIELD
_ARRAY.fields_by_name['value'].message_type = _ANY
_ANY.fields_by_name['type'].enum_type = _ANY_TYPE
_ANY.fields_by_name['scalar'].message_type = _SCALAR
_ANY.fields_by_name['obj'].message_type = _OBJECT
_ANY.fields_by_name['array'].message_type = _ARRAY
_ANY_TYPE.containing_type = _ANY
DESCRIPTOR.message_types_by_name['Scalar'] = _SCALAR
DESCRIPTOR.message_types_by_name['Object'] = _OBJECT
DESCRIPTOR.message_types_by_name['Array'] = _ARRAY
DESCRIPTOR.message_types_by_name['Any'] = _ANY

Scalar = _reflection.GeneratedProtocolMessageType('Scalar', (_message.Message,), dict(

  String = _reflection.GeneratedProtocolMessageType('String', (_message.Message,), dict(
    DESCRIPTOR = _SCALAR_STRING,
    __module__ = 'mysqlx_datatypes_pb2'
    # @@protoc_insertion_point(class_scope:Mysqlx.Datatypes.Scalar.String)
    ))
  ,

  Octets = _reflection.GeneratedProtocolMessageType('Octets', (_message.Message,), dict(
    DESCRIPTOR = _SCALAR_OCTETS,
    __module__ = 'mysqlx_datatypes_pb2'
    # @@protoc_insertion_point(class_scope:Mysqlx.Datatypes.Scalar.Octets)
    ))
  ,
  DESCRIPTOR = _SCALAR,
  __module__ = 'mysqlx_datatypes_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Datatypes.Scalar)
  ))
_sym_db.RegisterMessage(Scalar)
_sym_db.RegisterMessage(Scalar.String)
_sym_db.RegisterMessage(Scalar.Octets)

Object = _reflection.GeneratedProtocolMessageType('Object', (_message.Message,), dict(

  ObjectField = _reflection.GeneratedProtocolMessageType('ObjectField', (_message.Message,), dict(
    DESCRIPTOR = _OBJECT_OBJECTFIELD,
    __module__ = 'mysqlx_datatypes_pb2'
    # @@protoc_insertion_point(class_scope:Mysqlx.Datatypes.Object.ObjectField)
    ))
  ,
  DESCRIPTOR = _OBJECT,
  __module__ = 'mysqlx_datatypes_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Datatypes.Object)
  ))
_sym_db.RegisterMessage(Object)
_sym_db.RegisterMessage(Object.ObjectField)

Array = _reflection.GeneratedProtocolMessageType('Array', (_message.Message,), dict(
  DESCRIPTOR = _ARRAY,
  __module__ = 'mysqlx_datatypes_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Datatypes.Array)
  ))
_sym_db.RegisterMessage(Array)

Any = _reflection.GeneratedProtocolMessageType('Any', (_message.Message,), dict(
  DESCRIPTOR = _ANY,
  __module__ = 'mysqlx_datatypes_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Datatypes.Any)
  ))
_sym_db.RegisterMessage(Any)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\027com.mysql.cj.x.protobufH\003'))
# @@protoc_insertion_point(module_scope)
