# Copyright (c) 2017, 2020, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0, as
# published by the Free Software Foundation.
#
# This program is also distributed with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an
# additional permission to link the program and your derivative works
# with the separately licensed software that they have included with
# MySQL.
#
# Without limiting anything contained in the foregoing, this file,
# which is part of MySQL Connector/Python, is also subject to the
# Universal FOSS Exception, version 1.0, a copy of which can be found at
# http://oss.oracle.com/licenses/universal-foss-exception.
#
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
# See the GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mysqlx_expr.proto

# type: ignore

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mysqlx.protobuf import mysqlx_datatypes_pb2 as mysqlx__datatypes__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mysqlx_expr.proto',
  package='Mysqlx.Expr',
  syntax='proto2',
  serialized_pb=_b('\n\x11mysqlx_expr.proto\x12\x0bMysqlx.Expr\x1a\x16mysqlx_datatypes.proto\"\xc4\x03\n\x04\x45xpr\x12$\n\x04type\x18\x01 \x02(\x0e\x32\x16.Mysqlx.Expr.Expr.Type\x12\x31\n\nidentifier\x18\x02 \x01(\x0b\x32\x1d.Mysqlx.Expr.ColumnIdentifier\x12\x10\n\x08variable\x18\x03 \x01(\t\x12)\n\x07literal\x18\x04 \x01(\x0b\x32\x18.Mysqlx.Datatypes.Scalar\x12\x30\n\rfunction_call\x18\x05 \x01(\x0b\x32\x19.Mysqlx.Expr.FunctionCall\x12\'\n\x08operator\x18\x06 \x01(\x0b\x32\x15.Mysqlx.Expr.Operator\x12\x10\n\x08position\x18\x07 \x01(\r\x12#\n\x06object\x18\x08 \x01(\x0b\x32\x13.Mysqlx.Expr.Object\x12!\n\x05\x61rray\x18\t \x01(\x0b\x32\x12.Mysqlx.Expr.Array\"q\n\x04Type\x12\t\n\x05IDENT\x10\x01\x12\x0b\n\x07LITERAL\x10\x02\x12\x0c\n\x08VARIABLE\x10\x03\x12\r\n\tFUNC_CALL\x10\x04\x12\x0c\n\x08OPERATOR\x10\x05\x12\x0f\n\x0bPLACEHOLDER\x10\x06\x12\n\n\x06OBJECT\x10\x07\x12\t\n\x05\x41RRAY\x10\x08\"/\n\nIdentifier\x12\x0c\n\x04name\x18\x01 \x02(\t\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\"\xcb\x01\n\x10\x44ocumentPathItem\x12\x30\n\x04type\x18\x01 \x02(\x0e\x32\".Mysqlx.Expr.DocumentPathItem.Type\x12\r\n\x05value\x18\x02 \x01(\t\x12\r\n\x05index\x18\x03 \x01(\r\"g\n\x04Type\x12\n\n\x06MEMBER\x10\x01\x12\x13\n\x0fMEMBER_ASTERISK\x10\x02\x12\x0f\n\x0b\x41RRAY_INDEX\x10\x03\x12\x18\n\x14\x41RRAY_INDEX_ASTERISK\x10\x04\x12\x13\n\x0f\x44OUBLE_ASTERISK\x10\x05\"\x7f\n\x10\x43olumnIdentifier\x12\x34\n\rdocument_path\x18\x01 \x03(\x0b\x32\x1d.Mysqlx.Expr.DocumentPathItem\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x12\n\ntable_name\x18\x03 \x01(\t\x12\x13\n\x0bschema_name\x18\x04 \x01(\t\"W\n\x0c\x46unctionCall\x12%\n\x04name\x18\x01 \x02(\x0b\x32\x17.Mysqlx.Expr.Identifier\x12 \n\x05param\x18\x02 \x03(\x0b\x32\x11.Mysqlx.Expr.Expr\":\n\x08Operator\x12\x0c\n\x04name\x18\x01 \x02(\t\x12 \n\x05param\x18\x02 \x03(\x0b\x32\x11.Mysqlx.Expr.Expr\"t\n\x06Object\x12,\n\x03\x66ld\x18\x01 \x03(\x0b\x32\x1f.Mysqlx.Expr.Object.ObjectField\x1a<\n\x0bObjectField\x12\x0b\n\x03key\x18\x01 \x02(\t\x12 \n\x05value\x18\x02 \x02(\x0b\x32\x11.Mysqlx.Expr.Expr\")\n\x05\x41rray\x12 \n\x05value\x18\x01 \x03(\x0b\x32\x11.Mysqlx.Expr.ExprB\x1b\n\x17\x63om.mysql.cj.x.protobufH\x03')
  ,
  dependencies=[mysqlx__datatypes__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)



_EXPR_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='Mysqlx.Expr.Expr.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='IDENT', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LITERAL', index=1, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='VARIABLE', index=2, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FUNC_CALL', index=3, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OPERATOR', index=4, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PLACEHOLDER', index=5, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OBJECT', index=6, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ARRAY', index=7, number=8,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=398,
  serialized_end=511,
)
_sym_db.RegisterEnumDescriptor(_EXPR_TYPE)

_DOCUMENTPATHITEM_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='Mysqlx.Expr.DocumentPathItem.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='MEMBER', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MEMBER_ASTERISK', index=1, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ARRAY_INDEX', index=2, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ARRAY_INDEX_ASTERISK', index=3, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DOUBLE_ASTERISK', index=4, number=5,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=663,
  serialized_end=766,
)
_sym_db.RegisterEnumDescriptor(_DOCUMENTPATHITEM_TYPE)


_EXPR = _descriptor.Descriptor(
  name='Expr',
  full_name='Mysqlx.Expr.Expr',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='Mysqlx.Expr.Expr.type', index=0,
      number=1, type=14, cpp_type=8, label=2,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='identifier', full_name='Mysqlx.Expr.Expr.identifier', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='variable', full_name='Mysqlx.Expr.Expr.variable', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='literal', full_name='Mysqlx.Expr.Expr.literal', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='function_call', full_name='Mysqlx.Expr.Expr.function_call', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='operator', full_name='Mysqlx.Expr.Expr.operator', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='position', full_name='Mysqlx.Expr.Expr.position', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='object', full_name='Mysqlx.Expr.Expr.object', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='array', full_name='Mysqlx.Expr.Expr.array', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _EXPR_TYPE,
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=59,
  serialized_end=511,
)


_IDENTIFIER = _descriptor.Descriptor(
  name='Identifier',
  full_name='Mysqlx.Expr.Identifier',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='Mysqlx.Expr.Identifier.name', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='Mysqlx.Expr.Identifier.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=513,
  serialized_end=560,
)


_DOCUMENTPATHITEM = _descriptor.Descriptor(
  name='DocumentPathItem',
  full_name='Mysqlx.Expr.DocumentPathItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='Mysqlx.Expr.DocumentPathItem.type', index=0,
      number=1, type=14, cpp_type=8, label=2,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='Mysqlx.Expr.DocumentPathItem.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='index', full_name='Mysqlx.Expr.DocumentPathItem.index', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _DOCUMENTPATHITEM_TYPE,
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=563,
  serialized_end=766,
)


_COLUMNIDENTIFIER = _descriptor.Descriptor(
  name='ColumnIdentifier',
  full_name='Mysqlx.Expr.ColumnIdentifier',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='document_path', full_name='Mysqlx.Expr.ColumnIdentifier.document_path', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='name', full_name='Mysqlx.Expr.ColumnIdentifier.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='table_name', full_name='Mysqlx.Expr.ColumnIdentifier.table_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='Mysqlx.Expr.ColumnIdentifier.schema_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=768,
  serialized_end=895,
)


_FUNCTIONCALL = _descriptor.Descriptor(
  name='FunctionCall',
  full_name='Mysqlx.Expr.FunctionCall',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='Mysqlx.Expr.FunctionCall.name', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='param', full_name='Mysqlx.Expr.FunctionCall.param', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=897,
  serialized_end=984,
)


_OPERATOR = _descriptor.Descriptor(
  name='Operator',
  full_name='Mysqlx.Expr.Operator',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='Mysqlx.Expr.Operator.name', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='param', full_name='Mysqlx.Expr.Operator.param', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=986,
  serialized_end=1044,
)


_OBJECT_OBJECTFIELD = _descriptor.Descriptor(
  name='ObjectField',
  full_name='Mysqlx.Expr.Object.ObjectField',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='Mysqlx.Expr.Object.ObjectField.key', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='Mysqlx.Expr.Object.ObjectField.value', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1102,
  serialized_end=1162,
)

_OBJECT = _descriptor.Descriptor(
  name='Object',
  full_name='Mysqlx.Expr.Object',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fld', full_name='Mysqlx.Expr.Object.fld', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[_OBJECT_OBJECTFIELD, ],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1046,
  serialized_end=1162,
)


_ARRAY = _descriptor.Descriptor(
  name='Array',
  full_name='Mysqlx.Expr.Array',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='Mysqlx.Expr.Array.value', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1164,
  serialized_end=1205,
)

_EXPR.fields_by_name['type'].enum_type = _EXPR_TYPE
_EXPR.fields_by_name['identifier'].message_type = _COLUMNIDENTIFIER
_EXPR.fields_by_name['literal'].message_type = mysqlx__datatypes__pb2._SCALAR
_EXPR.fields_by_name['function_call'].message_type = _FUNCTIONCALL
_EXPR.fields_by_name['operator'].message_type = _OPERATOR
_EXPR.fields_by_name['object'].message_type = _OBJECT
_EXPR.fields_by_name['array'].message_type = _ARRAY
_EXPR_TYPE.containing_type = _EXPR
_DOCUMENTPATHITEM.fields_by_name['type'].enum_type = _DOCUMENTPATHITEM_TYPE
_DOCUMENTPATHITEM_TYPE.containing_type = _DOCUMENTPATHITEM
_COLUMNIDENTIFIER.fields_by_name['document_path'].message_type = _DOCUMENTPATHITEM
_FUNCTIONCALL.fields_by_name['name'].message_type = _IDENTIFIER
_FUNCTIONCALL.fields_by_name['param'].message_type = _EXPR
_OPERATOR.fields_by_name['param'].message_type = _EXPR
_OBJECT_OBJECTFIELD.fields_by_name['value'].message_type = _EXPR
_OBJECT_OBJECTFIELD.containing_type = _OBJECT
_OBJECT.fields_by_name['fld'].message_type = _OBJECT_OBJECTFIELD
_ARRAY.fields_by_name['value'].message_type = _EXPR
DESCRIPTOR.message_types_by_name['Expr'] = _EXPR
DESCRIPTOR.message_types_by_name['Identifier'] = _IDENTIFIER
DESCRIPTOR.message_types_by_name['DocumentPathItem'] = _DOCUMENTPATHITEM
DESCRIPTOR.message_types_by_name['ColumnIdentifier'] = _COLUMNIDENTIFIER
DESCRIPTOR.message_types_by_name['FunctionCall'] = _FUNCTIONCALL
DESCRIPTOR.message_types_by_name['Operator'] = _OPERATOR
DESCRIPTOR.message_types_by_name['Object'] = _OBJECT
DESCRIPTOR.message_types_by_name['Array'] = _ARRAY

Expr = _reflection.GeneratedProtocolMessageType('Expr', (_message.Message,), dict(
  DESCRIPTOR = _EXPR,
  __module__ = 'mysqlx_expr_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Expr.Expr)
  ))
_sym_db.RegisterMessage(Expr)

Identifier = _reflection.GeneratedProtocolMessageType('Identifier', (_message.Message,), dict(
  DESCRIPTOR = _IDENTIFIER,
  __module__ = 'mysqlx_expr_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Expr.Identifier)
  ))
_sym_db.RegisterMessage(Identifier)

DocumentPathItem = _reflection.GeneratedProtocolMessageType('DocumentPathItem', (_message.Message,), dict(
  DESCRIPTOR = _DOCUMENTPATHITEM,
  __module__ = 'mysqlx_expr_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Expr.DocumentPathItem)
  ))
_sym_db.RegisterMessage(DocumentPathItem)

ColumnIdentifier = _reflection.GeneratedProtocolMessageType('ColumnIdentifier', (_message.Message,), dict(
  DESCRIPTOR = _COLUMNIDENTIFIER,
  __module__ = 'mysqlx_expr_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Expr.ColumnIdentifier)
  ))
_sym_db.RegisterMessage(ColumnIdentifier)

FunctionCall = _reflection.GeneratedProtocolMessageType('FunctionCall', (_message.Message,), dict(
  DESCRIPTOR = _FUNCTIONCALL,
  __module__ = 'mysqlx_expr_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Expr.FunctionCall)
  ))
_sym_db.RegisterMessage(FunctionCall)

Operator = _reflection.GeneratedProtocolMessageType('Operator', (_message.Message,), dict(
  DESCRIPTOR = _OPERATOR,
  __module__ = 'mysqlx_expr_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Expr.Operator)
  ))
_sym_db.RegisterMessage(Operator)

Object = _reflection.GeneratedProtocolMessageType('Object', (_message.Message,), dict(

  ObjectField = _reflection.GeneratedProtocolMessageType('ObjectField', (_message.Message,), dict(
    DESCRIPTOR = _OBJECT_OBJECTFIELD,
    __module__ = 'mysqlx_expr_pb2'
    # @@protoc_insertion_point(class_scope:Mysqlx.Expr.Object.ObjectField)
    ))
  ,
  DESCRIPTOR = _OBJECT,
  __module__ = 'mysqlx_expr_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Expr.Object)
  ))
_sym_db.RegisterMessage(Object)
_sym_db.RegisterMessage(Object.ObjectField)

Array = _reflection.GeneratedProtocolMessageType('Array', (_message.Message,), dict(
  DESCRIPTOR = _ARRAY,
  __module__ = 'mysqlx_expr_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Expr.Array)
  ))
_sym_db.RegisterMessage(Array)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\027com.mysql.cj.x.protobufH\003'))
# @@protoc_insertion_point(module_scope)
