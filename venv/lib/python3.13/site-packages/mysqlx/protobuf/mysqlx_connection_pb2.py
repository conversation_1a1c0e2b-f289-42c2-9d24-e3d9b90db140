# Copyright (c) 2017, 2020, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0, as
# published by the Free Software Foundation.
#
# This program is also distributed with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an
# additional permission to link the program and your derivative works
# with the separately licensed software that they have included with
# MySQL.
#
# Without limiting anything contained in the foregoing, this file,
# which is part of MySQL Connector/Python, is also subject to the
# Universal FOSS Exception, version 1.0, a copy of which can be found at
# http://oss.oracle.com/licenses/universal-foss-exception.
#
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
# See the GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mysqlx_connection.proto

# type: ignore

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mysqlx.protobuf import mysqlx_datatypes_pb2 as mysqlx__datatypes__pb2
from mysqlx.protobuf import mysqlx_pb2 as mysqlx__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mysqlx_connection.proto',
  package='Mysqlx.Connection',
  syntax='proto2',
  serialized_pb=_b('\n\x17mysqlx_connection.proto\x12\x11Mysqlx.Connection\x1a\x16mysqlx_datatypes.proto\x1a\x0cmysqlx.proto\"@\n\nCapability\x12\x0c\n\x04name\x18\x01 \x02(\t\x12$\n\x05value\x18\x02 \x02(\x0b\x32\x15.Mysqlx.Datatypes.Any\"C\n\x0c\x43\x61pabilities\x12\x33\n\x0c\x63\x61pabilities\x18\x01 \x03(\x0b\x32\x1d.Mysqlx.Connection.Capability\"\x11\n\x0f\x43\x61pabilitiesGet\"H\n\x0f\x43\x61pabilitiesSet\x12\x35\n\x0c\x63\x61pabilities\x18\x01 \x02(\x0b\x32\x1f.Mysqlx.Connection.Capabilities\"\x07\n\x05\x43lose\"\xa5\x01\n\x0b\x43ompression\x12\x19\n\x11uncompressed_size\x18\x01 \x01(\x04\x12\x34\n\x0fserver_messages\x18\x02 \x01(\x0e\x32\x1b.Mysqlx.ServerMessages.Type\x12\x34\n\x0f\x63lient_messages\x18\x03 \x01(\x0e\x32\x1b.Mysqlx.ClientMessages.Type\x12\x0f\n\x07payload\x18\x04 \x02(\x0c\x42\x1b\n\x17\x63om.mysql.cj.x.protobufH\x03')
  ,
  dependencies=[mysqlx__datatypes__pb2.DESCRIPTOR,mysqlx__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_CAPABILITY = _descriptor.Descriptor(
  name='Capability',
  full_name='Mysqlx.Connection.Capability',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='Mysqlx.Connection.Capability.name', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='Mysqlx.Connection.Capability.value', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=84,
  serialized_end=148,
)


_CAPABILITIES = _descriptor.Descriptor(
  name='Capabilities',
  full_name='Mysqlx.Connection.Capabilities',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='capabilities', full_name='Mysqlx.Connection.Capabilities.capabilities', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=150,
  serialized_end=217,
)


_CAPABILITIESGET = _descriptor.Descriptor(
  name='CapabilitiesGet',
  full_name='Mysqlx.Connection.CapabilitiesGet',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=219,
  serialized_end=236,
)


_CAPABILITIESSET = _descriptor.Descriptor(
  name='CapabilitiesSet',
  full_name='Mysqlx.Connection.CapabilitiesSet',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='capabilities', full_name='Mysqlx.Connection.CapabilitiesSet.capabilities', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=238,
  serialized_end=310,
)


_CLOSE = _descriptor.Descriptor(
  name='Close',
  full_name='Mysqlx.Connection.Close',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=312,
  serialized_end=319,
)


_COMPRESSION = _descriptor.Descriptor(
  name='Compression',
  full_name='Mysqlx.Connection.Compression',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='uncompressed_size', full_name='Mysqlx.Connection.Compression.uncompressed_size', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='server_messages', full_name='Mysqlx.Connection.Compression.server_messages', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='client_messages', full_name='Mysqlx.Connection.Compression.client_messages', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='payload', full_name='Mysqlx.Connection.Compression.payload', index=3,
      number=4, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=322,
  serialized_end=487,
)

_CAPABILITY.fields_by_name['value'].message_type = mysqlx__datatypes__pb2._ANY
_CAPABILITIES.fields_by_name['capabilities'].message_type = _CAPABILITY
_CAPABILITIESSET.fields_by_name['capabilities'].message_type = _CAPABILITIES
_COMPRESSION.fields_by_name['server_messages'].enum_type = mysqlx__pb2._SERVERMESSAGES_TYPE
_COMPRESSION.fields_by_name['client_messages'].enum_type = mysqlx__pb2._CLIENTMESSAGES_TYPE
DESCRIPTOR.message_types_by_name['Capability'] = _CAPABILITY
DESCRIPTOR.message_types_by_name['Capabilities'] = _CAPABILITIES
DESCRIPTOR.message_types_by_name['CapabilitiesGet'] = _CAPABILITIESGET
DESCRIPTOR.message_types_by_name['CapabilitiesSet'] = _CAPABILITIESSET
DESCRIPTOR.message_types_by_name['Close'] = _CLOSE
DESCRIPTOR.message_types_by_name['Compression'] = _COMPRESSION

Capability = _reflection.GeneratedProtocolMessageType('Capability', (_message.Message,), dict(
  DESCRIPTOR = _CAPABILITY,
  __module__ = 'mysqlx_connection_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Connection.Capability)
  ))
_sym_db.RegisterMessage(Capability)

Capabilities = _reflection.GeneratedProtocolMessageType('Capabilities', (_message.Message,), dict(
  DESCRIPTOR = _CAPABILITIES,
  __module__ = 'mysqlx_connection_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Connection.Capabilities)
  ))
_sym_db.RegisterMessage(Capabilities)

CapabilitiesGet = _reflection.GeneratedProtocolMessageType('CapabilitiesGet', (_message.Message,), dict(
  DESCRIPTOR = _CAPABILITIESGET,
  __module__ = 'mysqlx_connection_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Connection.CapabilitiesGet)
  ))
_sym_db.RegisterMessage(CapabilitiesGet)

CapabilitiesSet = _reflection.GeneratedProtocolMessageType('CapabilitiesSet', (_message.Message,), dict(
  DESCRIPTOR = _CAPABILITIESSET,
  __module__ = 'mysqlx_connection_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Connection.CapabilitiesSet)
  ))
_sym_db.RegisterMessage(CapabilitiesSet)

Close = _reflection.GeneratedProtocolMessageType('Close', (_message.Message,), dict(
  DESCRIPTOR = _CLOSE,
  __module__ = 'mysqlx_connection_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Connection.Close)
  ))
_sym_db.RegisterMessage(Close)

Compression = _reflection.GeneratedProtocolMessageType('Compression', (_message.Message,), dict(
  DESCRIPTOR = _COMPRESSION,
  __module__ = 'mysqlx_connection_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Connection.Compression)
  ))
_sym_db.RegisterMessage(Compression)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\027com.mysql.cj.x.protobufH\003'))
# @@protoc_insertion_point(module_scope)
