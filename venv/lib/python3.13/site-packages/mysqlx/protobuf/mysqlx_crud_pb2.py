# Copyright (c) 2017, 2020, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0, as
# published by the Free Software Foundation.
#
# This program is also distributed with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an
# additional permission to link the program and your derivative works
# with the separately licensed software that they have included with
# MySQL.
#
# Without limiting anything contained in the foregoing, this file,
# which is part of MySQL Connector/Python, is also subject to the
# Universal FOSS Exception, version 1.0, a copy of which can be found at
# http://oss.oracle.com/licenses/universal-foss-exception.
#
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
# See the GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mysqlx_crud.proto

# type: ignore

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mysqlx.protobuf import mysqlx_expr_pb2 as mysqlx__expr__pb2
from mysqlx.protobuf import mysqlx_datatypes_pb2 as mysqlx__datatypes__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mysqlx_crud.proto',
  package='Mysqlx.Crud',
  syntax='proto2',
  serialized_pb=_b('\n\x11mysqlx_crud.proto\x12\x0bMysqlx.Crud\x1a\x11mysqlx_expr.proto\x1a\x16mysqlx_datatypes.proto\"[\n\x06\x43olumn\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x61lias\x18\x02 \x01(\t\x12\x34\n\rdocument_path\x18\x03 \x03(\x0b\x32\x1d.Mysqlx.Expr.DocumentPathItem\">\n\nProjection\x12!\n\x06source\x18\x01 \x02(\x0b\x32\x11.Mysqlx.Expr.Expr\x12\r\n\x05\x61lias\x18\x02 \x01(\t\"*\n\nCollection\x12\x0c\n\x04name\x18\x01 \x02(\t\x12\x0e\n\x06schema\x18\x02 \x01(\t\"*\n\x05Limit\x12\x11\n\trow_count\x18\x01 \x02(\x04\x12\x0e\n\x06offset\x18\x02 \x01(\x04\"T\n\tLimitExpr\x12$\n\trow_count\x18\x01 \x02(\x0b\x32\x11.Mysqlx.Expr.Expr\x12!\n\x06offset\x18\x02 \x01(\x0b\x32\x11.Mysqlx.Expr.Expr\"~\n\x05Order\x12\x1f\n\x04\x65xpr\x18\x01 \x02(\x0b\x32\x11.Mysqlx.Expr.Expr\x12\x34\n\tdirection\x18\x02 \x01(\x0e\x32\x1c.Mysqlx.Crud.Order.Direction:\x03\x41SC\"\x1e\n\tDirection\x12\x07\n\x03\x41SC\x10\x01\x12\x08\n\x04\x44\x45SC\x10\x02\"\xac\x02\n\x0fUpdateOperation\x12-\n\x06source\x18\x01 \x02(\x0b\x32\x1d.Mysqlx.Expr.ColumnIdentifier\x12:\n\toperation\x18\x02 \x02(\x0e\x32\'.Mysqlx.Crud.UpdateOperation.UpdateType\x12 \n\x05value\x18\x03 \x01(\x0b\x32\x11.Mysqlx.Expr.Expr\"\x8b\x01\n\nUpdateType\x12\x07\n\x03SET\x10\x01\x12\x0f\n\x0bITEM_REMOVE\x10\x02\x12\x0c\n\x08ITEM_SET\x10\x03\x12\x10\n\x0cITEM_REPLACE\x10\x04\x12\x0e\n\nITEM_MERGE\x10\x05\x12\x10\n\x0c\x41RRAY_INSERT\x10\x06\x12\x10\n\x0c\x41RRAY_APPEND\x10\x07\x12\x0f\n\x0bMERGE_PATCH\x10\x08\"\xe4\x04\n\x04\x46ind\x12+\n\ncollection\x18\x02 \x02(\x0b\x32\x17.Mysqlx.Crud.Collection\x12*\n\ndata_model\x18\x03 \x01(\x0e\x32\x16.Mysqlx.Crud.DataModel\x12+\n\nprojection\x18\x04 \x03(\x0b\x32\x17.Mysqlx.Crud.Projection\x12&\n\x04\x61rgs\x18\x0b \x03(\x0b\x32\x18.Mysqlx.Datatypes.Scalar\x12#\n\x08\x63riteria\x18\x05 \x01(\x0b\x32\x11.Mysqlx.Expr.Expr\x12!\n\x05limit\x18\x06 \x01(\x0b\x32\x12.Mysqlx.Crud.Limit\x12!\n\x05order\x18\x07 \x03(\x0b\x32\x12.Mysqlx.Crud.Order\x12#\n\x08grouping\x18\x08 \x03(\x0b\x32\x11.Mysqlx.Expr.Expr\x12,\n\x11grouping_criteria\x18\t \x01(\x0b\x32\x11.Mysqlx.Expr.Expr\x12*\n\x07locking\x18\x0c \x01(\x0e\x32\x19.Mysqlx.Crud.Find.RowLock\x12\x39\n\x0flocking_options\x18\r \x01(\x0e\x32 .Mysqlx.Crud.Find.RowLockOptions\x12*\n\nlimit_expr\x18\x0e \x01(\x0b\x32\x16.Mysqlx.Crud.LimitExpr\".\n\x07RowLock\x12\x0f\n\x0bSHARED_LOCK\x10\x01\x12\x12\n\x0e\x45XCLUSIVE_LOCK\x10\x02\"-\n\x0eRowLockOptions\x12\n\n\x06NOWAIT\x10\x01\x12\x0f\n\x0bSKIP_LOCKED\x10\x02\"\xa2\x02\n\x06Insert\x12+\n\ncollection\x18\x01 \x02(\x0b\x32\x17.Mysqlx.Crud.Collection\x12*\n\ndata_model\x18\x02 \x01(\x0e\x32\x16.Mysqlx.Crud.DataModel\x12\'\n\nprojection\x18\x03 \x03(\x0b\x32\x13.Mysqlx.Crud.Column\x12)\n\x03row\x18\x04 \x03(\x0b\x32\x1c.Mysqlx.Crud.Insert.TypedRow\x12&\n\x04\x61rgs\x18\x05 \x03(\x0b\x32\x18.Mysqlx.Datatypes.Scalar\x12\x15\n\x06upsert\x18\x06 \x01(\x08:\x05\x66\x61lse\x1a,\n\x08TypedRow\x12 \n\x05\x66ield\x18\x01 \x03(\x0b\x32\x11.Mysqlx.Expr.Expr\"\xd1\x02\n\x06Update\x12+\n\ncollection\x18\x02 \x02(\x0b\x32\x17.Mysqlx.Crud.Collection\x12*\n\ndata_model\x18\x03 \x01(\x0e\x32\x16.Mysqlx.Crud.DataModel\x12#\n\x08\x63riteria\x18\x04 \x01(\x0b\x32\x11.Mysqlx.Expr.Expr\x12!\n\x05limit\x18\x05 \x01(\x0b\x32\x12.Mysqlx.Crud.Limit\x12!\n\x05order\x18\x06 \x03(\x0b\x32\x12.Mysqlx.Crud.Order\x12/\n\toperation\x18\x07 \x03(\x0b\x32\x1c.Mysqlx.Crud.UpdateOperation\x12&\n\x04\x61rgs\x18\x08 \x03(\x0b\x32\x18.Mysqlx.Datatypes.Scalar\x12*\n\nlimit_expr\x18\t \x01(\x0b\x32\x16.Mysqlx.Crud.LimitExpr\"\xa0\x02\n\x06\x44\x65lete\x12+\n\ncollection\x18\x01 \x02(\x0b\x32\x17.Mysqlx.Crud.Collection\x12*\n\ndata_model\x18\x02 \x01(\x0e\x32\x16.Mysqlx.Crud.DataModel\x12#\n\x08\x63riteria\x18\x03 \x01(\x0b\x32\x11.Mysqlx.Expr.Expr\x12!\n\x05limit\x18\x04 \x01(\x0b\x32\x12.Mysqlx.Crud.Limit\x12!\n\x05order\x18\x05 \x03(\x0b\x32\x12.Mysqlx.Crud.Order\x12&\n\x04\x61rgs\x18\x06 \x03(\x0b\x32\x18.Mysqlx.Datatypes.Scalar\x12*\n\nlimit_expr\x18\x07 \x01(\x0b\x32\x16.Mysqlx.Crud.LimitExpr\"\xbc\x02\n\nCreateView\x12+\n\ncollection\x18\x01 \x02(\x0b\x32\x17.Mysqlx.Crud.Collection\x12\x0f\n\x07\x64\x65\x66iner\x18\x02 \x01(\t\x12\x38\n\talgorithm\x18\x03 \x01(\x0e\x32\x1a.Mysqlx.Crud.ViewAlgorithm:\tUNDEFINED\x12\x37\n\x08security\x18\x04 \x01(\x0e\x32\x1c.Mysqlx.Crud.ViewSqlSecurity:\x07\x44\x45\x46INER\x12+\n\x05\x63heck\x18\x05 \x01(\x0e\x32\x1c.Mysqlx.Crud.ViewCheckOption\x12\x0e\n\x06\x63olumn\x18\x06 \x03(\t\x12\x1f\n\x04stmt\x18\x07 \x02(\x0b\x32\x11.Mysqlx.Crud.Find\x12\x1f\n\x10replace_existing\x18\x08 \x01(\x08:\x05\x66\x61lse\"\x87\x02\n\nModifyView\x12+\n\ncollection\x18\x01 \x02(\x0b\x32\x17.Mysqlx.Crud.Collection\x12\x0f\n\x07\x64\x65\x66iner\x18\x02 \x01(\t\x12-\n\talgorithm\x18\x03 \x01(\x0e\x32\x1a.Mysqlx.Crud.ViewAlgorithm\x12.\n\x08security\x18\x04 \x01(\x0e\x32\x1c.Mysqlx.Crud.ViewSqlSecurity\x12+\n\x05\x63heck\x18\x05 \x01(\x0e\x32\x1c.Mysqlx.Crud.ViewCheckOption\x12\x0e\n\x06\x63olumn\x18\x06 \x03(\t\x12\x1f\n\x04stmt\x18\x07 \x01(\x0b\x32\x11.Mysqlx.Crud.Find\"Q\n\x08\x44ropView\x12+\n\ncollection\x18\x01 \x02(\x0b\x32\x17.Mysqlx.Crud.Collection\x12\x18\n\tif_exists\x18\x02 \x01(\x08:\x05\x66\x61lse*$\n\tDataModel\x12\x0c\n\x08\x44OCUMENT\x10\x01\x12\t\n\x05TABLE\x10\x02*8\n\rViewAlgorithm\x12\r\n\tUNDEFINED\x10\x01\x12\t\n\x05MERGE\x10\x02\x12\r\n\tTEMPTABLE\x10\x03*+\n\x0fViewSqlSecurity\x12\x0b\n\x07INVOKER\x10\x01\x12\x0b\n\x07\x44\x45\x46INER\x10\x02**\n\x0fViewCheckOption\x12\t\n\x05LOCAL\x10\x01\x12\x0c\n\x08\x43\x41SCADED\x10\x02\x42\x1b\n\x17\x63om.mysql.cj.x.protobufH\x03')
  ,
  dependencies=[mysqlx__expr__pb2.DESCRIPTOR,mysqlx__datatypes__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

_DATAMODEL = _descriptor.EnumDescriptor(
  name='DataModel',
  full_name='Mysqlx.Crud.DataModel',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DOCUMENT', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TABLE', index=1, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=3046,
  serialized_end=3082,
)
_sym_db.RegisterEnumDescriptor(_DATAMODEL)

DataModel = enum_type_wrapper.EnumTypeWrapper(_DATAMODEL)
_VIEWALGORITHM = _descriptor.EnumDescriptor(
  name='ViewAlgorithm',
  full_name='Mysqlx.Crud.ViewAlgorithm',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNDEFINED', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MERGE', index=1, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TEMPTABLE', index=2, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=3084,
  serialized_end=3140,
)
_sym_db.RegisterEnumDescriptor(_VIEWALGORITHM)

ViewAlgorithm = enum_type_wrapper.EnumTypeWrapper(_VIEWALGORITHM)
_VIEWSQLSECURITY = _descriptor.EnumDescriptor(
  name='ViewSqlSecurity',
  full_name='Mysqlx.Crud.ViewSqlSecurity',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INVOKER', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEFINER', index=1, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=3142,
  serialized_end=3185,
)
_sym_db.RegisterEnumDescriptor(_VIEWSQLSECURITY)

ViewSqlSecurity = enum_type_wrapper.EnumTypeWrapper(_VIEWSQLSECURITY)
_VIEWCHECKOPTION = _descriptor.EnumDescriptor(
  name='ViewCheckOption',
  full_name='Mysqlx.Crud.ViewCheckOption',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LOCAL', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CASCADED', index=1, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=3187,
  serialized_end=3229,
)
_sym_db.RegisterEnumDescriptor(_VIEWCHECKOPTION)

ViewCheckOption = enum_type_wrapper.EnumTypeWrapper(_VIEWCHECKOPTION)
DOCUMENT = 1
TABLE = 2
UNDEFINED = 1
MERGE = 2
TEMPTABLE = 3
INVOKER = 1
DEFINER = 2
LOCAL = 1
CASCADED = 2


_ORDER_DIRECTION = _descriptor.EnumDescriptor(
  name='Direction',
  full_name='Mysqlx.Crud.Order.Direction',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ASC', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DESC', index=1, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=504,
  serialized_end=534,
)
_sym_db.RegisterEnumDescriptor(_ORDER_DIRECTION)

_UPDATEOPERATION_UPDATETYPE = _descriptor.EnumDescriptor(
  name='UpdateType',
  full_name='Mysqlx.Crud.UpdateOperation.UpdateType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SET', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ITEM_REMOVE', index=1, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ITEM_SET', index=2, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ITEM_REPLACE', index=3, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ITEM_MERGE', index=4, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ARRAY_INSERT', index=5, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ARRAY_APPEND', index=6, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MERGE_PATCH', index=7, number=8,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=698,
  serialized_end=837,
)
_sym_db.RegisterEnumDescriptor(_UPDATEOPERATION_UPDATETYPE)

_FIND_ROWLOCK = _descriptor.EnumDescriptor(
  name='RowLock',
  full_name='Mysqlx.Crud.Find.RowLock',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SHARED_LOCK', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='EXCLUSIVE_LOCK', index=1, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1359,
  serialized_end=1405,
)
_sym_db.RegisterEnumDescriptor(_FIND_ROWLOCK)

_FIND_ROWLOCKOPTIONS = _descriptor.EnumDescriptor(
  name='RowLockOptions',
  full_name='Mysqlx.Crud.Find.RowLockOptions',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NOWAIT', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SKIP_LOCKED', index=1, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1407,
  serialized_end=1452,
)
_sym_db.RegisterEnumDescriptor(_FIND_ROWLOCKOPTIONS)


_COLUMN = _descriptor.Descriptor(
  name='Column',
  full_name='Mysqlx.Crud.Column',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='Mysqlx.Crud.Column.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='alias', full_name='Mysqlx.Crud.Column.alias', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='document_path', full_name='Mysqlx.Crud.Column.document_path', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=77,
  serialized_end=168,
)


_PROJECTION = _descriptor.Descriptor(
  name='Projection',
  full_name='Mysqlx.Crud.Projection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source', full_name='Mysqlx.Crud.Projection.source', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='alias', full_name='Mysqlx.Crud.Projection.alias', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=170,
  serialized_end=232,
)


_COLLECTION = _descriptor.Descriptor(
  name='Collection',
  full_name='Mysqlx.Crud.Collection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='Mysqlx.Crud.Collection.name', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='schema', full_name='Mysqlx.Crud.Collection.schema', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=234,
  serialized_end=276,
)


_LIMIT = _descriptor.Descriptor(
  name='Limit',
  full_name='Mysqlx.Crud.Limit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_count', full_name='Mysqlx.Crud.Limit.row_count', index=0,
      number=1, type=4, cpp_type=4, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='offset', full_name='Mysqlx.Crud.Limit.offset', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=278,
  serialized_end=320,
)


_LIMITEXPR = _descriptor.Descriptor(
  name='LimitExpr',
  full_name='Mysqlx.Crud.LimitExpr',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_count', full_name='Mysqlx.Crud.LimitExpr.row_count', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='offset', full_name='Mysqlx.Crud.LimitExpr.offset', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=322,
  serialized_end=406,
)


_ORDER = _descriptor.Descriptor(
  name='Order',
  full_name='Mysqlx.Crud.Order',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='expr', full_name='Mysqlx.Crud.Order.expr', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='direction', full_name='Mysqlx.Crud.Order.direction', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ORDER_DIRECTION,
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=408,
  serialized_end=534,
)


_UPDATEOPERATION = _descriptor.Descriptor(
  name='UpdateOperation',
  full_name='Mysqlx.Crud.UpdateOperation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source', full_name='Mysqlx.Crud.UpdateOperation.source', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='operation', full_name='Mysqlx.Crud.UpdateOperation.operation', index=1,
      number=2, type=14, cpp_type=8, label=2,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='Mysqlx.Crud.UpdateOperation.value', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _UPDATEOPERATION_UPDATETYPE,
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=537,
  serialized_end=837,
)


_FIND = _descriptor.Descriptor(
  name='Find',
  full_name='Mysqlx.Crud.Find',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='collection', full_name='Mysqlx.Crud.Find.collection', index=0,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='data_model', full_name='Mysqlx.Crud.Find.data_model', index=1,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='projection', full_name='Mysqlx.Crud.Find.projection', index=2,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='args', full_name='Mysqlx.Crud.Find.args', index=3,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='criteria', full_name='Mysqlx.Crud.Find.criteria', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='limit', full_name='Mysqlx.Crud.Find.limit', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='order', full_name='Mysqlx.Crud.Find.order', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='grouping', full_name='Mysqlx.Crud.Find.grouping', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='grouping_criteria', full_name='Mysqlx.Crud.Find.grouping_criteria', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='locking', full_name='Mysqlx.Crud.Find.locking', index=9,
      number=12, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='locking_options', full_name='Mysqlx.Crud.Find.locking_options', index=10,
      number=13, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='limit_expr', full_name='Mysqlx.Crud.Find.limit_expr', index=11,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _FIND_ROWLOCK,
    _FIND_ROWLOCKOPTIONS,
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=840,
  serialized_end=1452,
)


_INSERT_TYPEDROW = _descriptor.Descriptor(
  name='TypedRow',
  full_name='Mysqlx.Crud.Insert.TypedRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='field', full_name='Mysqlx.Crud.Insert.TypedRow.field', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1701,
  serialized_end=1745,
)

_INSERT = _descriptor.Descriptor(
  name='Insert',
  full_name='Mysqlx.Crud.Insert',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='collection', full_name='Mysqlx.Crud.Insert.collection', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='data_model', full_name='Mysqlx.Crud.Insert.data_model', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='projection', full_name='Mysqlx.Crud.Insert.projection', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='row', full_name='Mysqlx.Crud.Insert.row', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='args', full_name='Mysqlx.Crud.Insert.args', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='upsert', full_name='Mysqlx.Crud.Insert.upsert', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[_INSERT_TYPEDROW, ],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1455,
  serialized_end=1745,
)


_UPDATE = _descriptor.Descriptor(
  name='Update',
  full_name='Mysqlx.Crud.Update',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='collection', full_name='Mysqlx.Crud.Update.collection', index=0,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='data_model', full_name='Mysqlx.Crud.Update.data_model', index=1,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='criteria', full_name='Mysqlx.Crud.Update.criteria', index=2,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='limit', full_name='Mysqlx.Crud.Update.limit', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='order', full_name='Mysqlx.Crud.Update.order', index=4,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='operation', full_name='Mysqlx.Crud.Update.operation', index=5,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='args', full_name='Mysqlx.Crud.Update.args', index=6,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='limit_expr', full_name='Mysqlx.Crud.Update.limit_expr', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1748,
  serialized_end=2085,
)


_DELETE = _descriptor.Descriptor(
  name='Delete',
  full_name='Mysqlx.Crud.Delete',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='collection', full_name='Mysqlx.Crud.Delete.collection', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='data_model', full_name='Mysqlx.Crud.Delete.data_model', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='criteria', full_name='Mysqlx.Crud.Delete.criteria', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='limit', full_name='Mysqlx.Crud.Delete.limit', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='order', full_name='Mysqlx.Crud.Delete.order', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='args', full_name='Mysqlx.Crud.Delete.args', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='limit_expr', full_name='Mysqlx.Crud.Delete.limit_expr', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2088,
  serialized_end=2376,
)


_CREATEVIEW = _descriptor.Descriptor(
  name='CreateView',
  full_name='Mysqlx.Crud.CreateView',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='collection', full_name='Mysqlx.Crud.CreateView.collection', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='definer', full_name='Mysqlx.Crud.CreateView.definer', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='algorithm', full_name='Mysqlx.Crud.CreateView.algorithm', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='security', full_name='Mysqlx.Crud.CreateView.security', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=2,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='check', full_name='Mysqlx.Crud.CreateView.check', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='column', full_name='Mysqlx.Crud.CreateView.column', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='stmt', full_name='Mysqlx.Crud.CreateView.stmt', index=6,
      number=7, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='replace_existing', full_name='Mysqlx.Crud.CreateView.replace_existing', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2379,
  serialized_end=2695,
)


_MODIFYVIEW = _descriptor.Descriptor(
  name='ModifyView',
  full_name='Mysqlx.Crud.ModifyView',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='collection', full_name='Mysqlx.Crud.ModifyView.collection', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='definer', full_name='Mysqlx.Crud.ModifyView.definer', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='algorithm', full_name='Mysqlx.Crud.ModifyView.algorithm', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='security', full_name='Mysqlx.Crud.ModifyView.security', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='check', full_name='Mysqlx.Crud.ModifyView.check', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='column', full_name='Mysqlx.Crud.ModifyView.column', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='stmt', full_name='Mysqlx.Crud.ModifyView.stmt', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2698,
  serialized_end=2961,
)


_DROPVIEW = _descriptor.Descriptor(
  name='DropView',
  full_name='Mysqlx.Crud.DropView',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='collection', full_name='Mysqlx.Crud.DropView.collection', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='if_exists', full_name='Mysqlx.Crud.DropView.if_exists', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2963,
  serialized_end=3044,
)

_COLUMN.fields_by_name['document_path'].message_type = mysqlx__expr__pb2._DOCUMENTPATHITEM
_PROJECTION.fields_by_name['source'].message_type = mysqlx__expr__pb2._EXPR
_LIMITEXPR.fields_by_name['row_count'].message_type = mysqlx__expr__pb2._EXPR
_LIMITEXPR.fields_by_name['offset'].message_type = mysqlx__expr__pb2._EXPR
_ORDER.fields_by_name['expr'].message_type = mysqlx__expr__pb2._EXPR
_ORDER.fields_by_name['direction'].enum_type = _ORDER_DIRECTION
_ORDER_DIRECTION.containing_type = _ORDER
_UPDATEOPERATION.fields_by_name['source'].message_type = mysqlx__expr__pb2._COLUMNIDENTIFIER
_UPDATEOPERATION.fields_by_name['operation'].enum_type = _UPDATEOPERATION_UPDATETYPE
_UPDATEOPERATION.fields_by_name['value'].message_type = mysqlx__expr__pb2._EXPR
_UPDATEOPERATION_UPDATETYPE.containing_type = _UPDATEOPERATION
_FIND.fields_by_name['collection'].message_type = _COLLECTION
_FIND.fields_by_name['data_model'].enum_type = _DATAMODEL
_FIND.fields_by_name['projection'].message_type = _PROJECTION
_FIND.fields_by_name['args'].message_type = mysqlx__datatypes__pb2._SCALAR
_FIND.fields_by_name['criteria'].message_type = mysqlx__expr__pb2._EXPR
_FIND.fields_by_name['limit'].message_type = _LIMIT
_FIND.fields_by_name['order'].message_type = _ORDER
_FIND.fields_by_name['grouping'].message_type = mysqlx__expr__pb2._EXPR
_FIND.fields_by_name['grouping_criteria'].message_type = mysqlx__expr__pb2._EXPR
_FIND.fields_by_name['locking'].enum_type = _FIND_ROWLOCK
_FIND.fields_by_name['locking_options'].enum_type = _FIND_ROWLOCKOPTIONS
_FIND.fields_by_name['limit_expr'].message_type = _LIMITEXPR
_FIND_ROWLOCK.containing_type = _FIND
_FIND_ROWLOCKOPTIONS.containing_type = _FIND
_INSERT_TYPEDROW.fields_by_name['field'].message_type = mysqlx__expr__pb2._EXPR
_INSERT_TYPEDROW.containing_type = _INSERT
_INSERT.fields_by_name['collection'].message_type = _COLLECTION
_INSERT.fields_by_name['data_model'].enum_type = _DATAMODEL
_INSERT.fields_by_name['projection'].message_type = _COLUMN
_INSERT.fields_by_name['row'].message_type = _INSERT_TYPEDROW
_INSERT.fields_by_name['args'].message_type = mysqlx__datatypes__pb2._SCALAR
_UPDATE.fields_by_name['collection'].message_type = _COLLECTION
_UPDATE.fields_by_name['data_model'].enum_type = _DATAMODEL
_UPDATE.fields_by_name['criteria'].message_type = mysqlx__expr__pb2._EXPR
_UPDATE.fields_by_name['limit'].message_type = _LIMIT
_UPDATE.fields_by_name['order'].message_type = _ORDER
_UPDATE.fields_by_name['operation'].message_type = _UPDATEOPERATION
_UPDATE.fields_by_name['args'].message_type = mysqlx__datatypes__pb2._SCALAR
_UPDATE.fields_by_name['limit_expr'].message_type = _LIMITEXPR
_DELETE.fields_by_name['collection'].message_type = _COLLECTION
_DELETE.fields_by_name['data_model'].enum_type = _DATAMODEL
_DELETE.fields_by_name['criteria'].message_type = mysqlx__expr__pb2._EXPR
_DELETE.fields_by_name['limit'].message_type = _LIMIT
_DELETE.fields_by_name['order'].message_type = _ORDER
_DELETE.fields_by_name['args'].message_type = mysqlx__datatypes__pb2._SCALAR
_DELETE.fields_by_name['limit_expr'].message_type = _LIMITEXPR
_CREATEVIEW.fields_by_name['collection'].message_type = _COLLECTION
_CREATEVIEW.fields_by_name['algorithm'].enum_type = _VIEWALGORITHM
_CREATEVIEW.fields_by_name['security'].enum_type = _VIEWSQLSECURITY
_CREATEVIEW.fields_by_name['check'].enum_type = _VIEWCHECKOPTION
_CREATEVIEW.fields_by_name['stmt'].message_type = _FIND
_MODIFYVIEW.fields_by_name['collection'].message_type = _COLLECTION
_MODIFYVIEW.fields_by_name['algorithm'].enum_type = _VIEWALGORITHM
_MODIFYVIEW.fields_by_name['security'].enum_type = _VIEWSQLSECURITY
_MODIFYVIEW.fields_by_name['check'].enum_type = _VIEWCHECKOPTION
_MODIFYVIEW.fields_by_name['stmt'].message_type = _FIND
_DROPVIEW.fields_by_name['collection'].message_type = _COLLECTION
DESCRIPTOR.message_types_by_name['Column'] = _COLUMN
DESCRIPTOR.message_types_by_name['Projection'] = _PROJECTION
DESCRIPTOR.message_types_by_name['Collection'] = _COLLECTION
DESCRIPTOR.message_types_by_name['Limit'] = _LIMIT
DESCRIPTOR.message_types_by_name['LimitExpr'] = _LIMITEXPR
DESCRIPTOR.message_types_by_name['Order'] = _ORDER
DESCRIPTOR.message_types_by_name['UpdateOperation'] = _UPDATEOPERATION
DESCRIPTOR.message_types_by_name['Find'] = _FIND
DESCRIPTOR.message_types_by_name['Insert'] = _INSERT
DESCRIPTOR.message_types_by_name['Update'] = _UPDATE
DESCRIPTOR.message_types_by_name['Delete'] = _DELETE
DESCRIPTOR.message_types_by_name['CreateView'] = _CREATEVIEW
DESCRIPTOR.message_types_by_name['ModifyView'] = _MODIFYVIEW
DESCRIPTOR.message_types_by_name['DropView'] = _DROPVIEW
DESCRIPTOR.enum_types_by_name['DataModel'] = _DATAMODEL
DESCRIPTOR.enum_types_by_name['ViewAlgorithm'] = _VIEWALGORITHM
DESCRIPTOR.enum_types_by_name['ViewSqlSecurity'] = _VIEWSQLSECURITY
DESCRIPTOR.enum_types_by_name['ViewCheckOption'] = _VIEWCHECKOPTION

Column = _reflection.GeneratedProtocolMessageType('Column', (_message.Message,), dict(
  DESCRIPTOR = _COLUMN,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.Column)
  ))
_sym_db.RegisterMessage(Column)

Projection = _reflection.GeneratedProtocolMessageType('Projection', (_message.Message,), dict(
  DESCRIPTOR = _PROJECTION,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.Projection)
  ))
_sym_db.RegisterMessage(Projection)

Collection = _reflection.GeneratedProtocolMessageType('Collection', (_message.Message,), dict(
  DESCRIPTOR = _COLLECTION,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.Collection)
  ))
_sym_db.RegisterMessage(Collection)

Limit = _reflection.GeneratedProtocolMessageType('Limit', (_message.Message,), dict(
  DESCRIPTOR = _LIMIT,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.Limit)
  ))
_sym_db.RegisterMessage(Limit)

LimitExpr = _reflection.GeneratedProtocolMessageType('LimitExpr', (_message.Message,), dict(
  DESCRIPTOR = _LIMITEXPR,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.LimitExpr)
  ))
_sym_db.RegisterMessage(LimitExpr)

Order = _reflection.GeneratedProtocolMessageType('Order', (_message.Message,), dict(
  DESCRIPTOR = _ORDER,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.Order)
  ))
_sym_db.RegisterMessage(Order)

UpdateOperation = _reflection.GeneratedProtocolMessageType('UpdateOperation', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEOPERATION,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.UpdateOperation)
  ))
_sym_db.RegisterMessage(UpdateOperation)

Find = _reflection.GeneratedProtocolMessageType('Find', (_message.Message,), dict(
  DESCRIPTOR = _FIND,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.Find)
  ))
_sym_db.RegisterMessage(Find)

Insert = _reflection.GeneratedProtocolMessageType('Insert', (_message.Message,), dict(

  TypedRow = _reflection.GeneratedProtocolMessageType('TypedRow', (_message.Message,), dict(
    DESCRIPTOR = _INSERT_TYPEDROW,
    __module__ = 'mysqlx_crud_pb2'
    # @@protoc_insertion_point(class_scope:Mysqlx.Crud.Insert.TypedRow)
    ))
  ,
  DESCRIPTOR = _INSERT,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.Insert)
  ))
_sym_db.RegisterMessage(Insert)
_sym_db.RegisterMessage(Insert.TypedRow)

Update = _reflection.GeneratedProtocolMessageType('Update', (_message.Message,), dict(
  DESCRIPTOR = _UPDATE,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.Update)
  ))
_sym_db.RegisterMessage(Update)

Delete = _reflection.GeneratedProtocolMessageType('Delete', (_message.Message,), dict(
  DESCRIPTOR = _DELETE,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.Delete)
  ))
_sym_db.RegisterMessage(Delete)

CreateView = _reflection.GeneratedProtocolMessageType('CreateView', (_message.Message,), dict(
  DESCRIPTOR = _CREATEVIEW,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.CreateView)
  ))
_sym_db.RegisterMessage(CreateView)

ModifyView = _reflection.GeneratedProtocolMessageType('ModifyView', (_message.Message,), dict(
  DESCRIPTOR = _MODIFYVIEW,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.ModifyView)
  ))
_sym_db.RegisterMessage(ModifyView)

DropView = _reflection.GeneratedProtocolMessageType('DropView', (_message.Message,), dict(
  DESCRIPTOR = _DROPVIEW,
  __module__ = 'mysqlx_crud_pb2'
  # @@protoc_insertion_point(class_scope:Mysqlx.Crud.DropView)
  ))
_sym_db.RegisterMessage(DropView)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\027com.mysql.cj.x.protobufH\003'))
# @@protoc_insertion_point(module_scope)
