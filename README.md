# 企业微信命令桥接服务

这是一个专门用于接收企业微信消息并处理命令的服务，基于 FastAPI 开发。

## 功能

- 验证企业微信服务器的回调请求
- 接收并解密企业微信消息
- 处理文本消息并返回回复
- **自动提取B站视频链接，创建视频处理任务**
- **提供任务队列API供第三方系统获取和更新任务状态**
- **支持任务完成后自动删除队列中的任务**

## 技术架构

本项目采用现代FastAPI框架开发，使用了依赖注入、Pydantic数据模型和异步编程等特性，具有以下特点：

- **高性能**: 基于Starlette和Pydantic，性能出色
- **API文档**: 自动生成OpenAPI文档（可访问`/docs`和`/redoc`）
- **类型提示**: 完整的Python类型提示支持
- **依赖注入**: 使用dependency-injector实现服务的松耦合
- **异步支持**: 所有API端点支持异步处理
- **模块化**: 按功能分层的清晰架构

项目结构采用领域驱动设计思想，代码分为以下几层：

- API层（`app/api`）: 处理HTTP请求和路由
- 服务层（`app/services`）: 实现业务逻辑
- 仓储层（`app/repositories`）: 负责数据存储和访问
- 模型层（`app/schemas`）: 定义数据结构和验证规则

## 配置环境变量

运行服务前，需要设置以下环境变量：

```env
WECOM_TOKEN=你的企业微信Token
WECOM_ENCODING_AES_KEY=你的企业微信EncodingAESKey
WECOM_CORP_ID=你的企业微信CorpID
WECOM_AGENT_ID=你的企业微信应用ID
WECOM_SECRET=你的企业微信应用密钥
```

## 快速使用

项目提供了便捷的部署和管理脚本`deploy.sh`，可以快速启动、停止和管理服务。

### 开发环境

#### 方式一：Python直接运行（适合快速开发调试）

```bash
# 添加脚本执行权限
chmod +x deploy.sh

# 启动本地Python开发服务
./deploy.sh start-python

# 代码修改后重启服务
./deploy.sh restart-python

# 停止本地服务
./deploy.sh stop-python

# 运行健康检查测试
./deploy.sh test
```

#### 方式二：本地Docker开发环境（推荐，与生产环境相同配置）

本项目已配置Docker卷挂载，本地修改代码后**无需重新构建镜像**即可生效。

```bash
# 添加脚本执行权限
chmod +x deploy.sh

# 启动本地Docker开发环境
./deploy.sh dev

# 本地修改代码后重载服务（无需重新构建）
./deploy.sh reload

# 修改依赖或配置文件后重新构建
./deploy.sh rebuild

# 查看Docker服务日志
./deploy.sh logs

# 停止Docker服务
./deploy.sh stop
```

> **说明**：我们使用了Docker卷挂载技术，将本地代码目录直接挂载到Docker容器中，因此修改Python代码后只需执行`./deploy.sh reload`即可立即生效，无需重新构建镜像。这大大提高了开发效率。但是，如果修改了`requirements.txt`依赖或Dockerfile等配置文件，则需要使用`./deploy.sh rebuild`重新构建镜像。

### 生产环境部署

```bash
# 部署生产环境
./deploy.sh deploy

# 重启服务
./deploy.sh restart

# 查看日志
./deploy.sh logs

# 停止服务
./deploy.sh stop
```

## 本地运行

1. 安装依赖

```bash
pip install -r requirements.txt
```

1. 运行服务

```bash
python run.py
```

## 调试模式与详细日志

为了方便排查问题，本项目提供了调试模式，可以输出详细的日志信息：

1. 使用调试脚本启动服务

```bash
python debug_run.py
```

这将以DEBUG日志级别启动服务，所有详细信息都会记录到`logs/debug.log`文件中。

调试模式特点：
- 开启详细日志记录，包括企业微信回调接口的完整处理过程
- 记录加解密操作的详细步骤
- 输出XML解析与处理的全流程信息
- 显示所有HTTP请求的详细处理过程

当遇到企业微信回调问题时，建议使用调试模式运行并查看详细日志。

## Docker 运行

1. 构建镜像

```bash
docker build -t wecom-message-receiver .
```

1. 运行容器

```bash
docker run -d -p 5001:5001 \
  -e WECOM_TOKEN=你的企业微信Token \
  -e WECOM_ENCODING_AES_KEY=你的企业微信EncodingAESKey \
  -e WECOM_CORP_ID=你的企业微信CorpID \
  -e WECOM_AGENT_ID=你的企业微信应用ID \
  -e WECOM_SECRET=你的企业微信应用密钥 \
  wecom-message-receiver
```

## API 接口概述

本服务提供以下API接口类型，详细API文档可通过启动服务后访问 `/docs` 或 `/redoc` 查看：

1. **健康检查接口** - 检查服务是否正常运行
2. **企业微信回调接口** - 接收和处理企业微信消息推送
3. **任务管理接口** - 包括获取任务、更新任务状态、上传任务文件等功能
4. **测试接口** - 用于测试系统与企业微信的连接

## 测试服务

可以使用以下命令进行测试，确认服务是否正常工作：

```bash
./deploy.sh test
```

该命令会测试以下内容：

- 健康检查接口（`/health`）
- 企业微信测试接口（`/test`）

## 任务处理流程

当企业微信中收到包含B站视频链接的消息（如 `https://www.bilibili.com/video/BV1fkoyY3ERd`）时：

1. 系统自动提取视频BV号并创建任务
1. 第三方系统通过 API 接口获取任务（获取成功即视为任务已被接收，状态自动更新为"已接收"）
1. 第三方系统处理任务，可以通过 API 接口上传处理结果文件
1. 第三方系统通过 API 接口更新任务状态（成功或失败）
1. 系统根据任务状态向企业微信用户发送相应的通知消息，如果任务成功且有上传文件，文件会一并发送给用户
1. 当任务处理成功或失败时，系统自动从队列中删除该任务

## 任务状态说明

- `0`: 未开始（初始状态）
- `1`: 已接收（第三方系统已接收任务）
- `2`: 成功（任务处理成功，任务会被删除）
- `3`: 失败（任务处理失败，任务会被删除）

## 文件处理流程

当第三方系统需要上传文件并发送给企业微信用户时，流程如下：

1. 第三方系统通过 API 接口获取任务
1. 第三方系统处理任务，生成处理结果文件
1. 通过 API 接口上传文件，文件会保存在服务器并关联到指定任务
1. 通过 API 接口将任务状态更新为成功(2)
1. 系统会自动：
   - 将文件上传到企业微信临时素材库，获取媒体ID
   - 发送文本消息通知用户任务处理完成
   - 发送文件消息，将处理结果文件发送给用户
   - 从队列中删除该任务

注意：文件上传到企业微信后是临时素材，有效期为3天。如果需要长期保存，建议将文件上传到其他存储服务并分享链接。

## 项目结构

详细的项目目录结构请参见 [DIRECTORY_STRUCTURE.md](DIRECTORY_STRUCTURE.md)

## 开发规范

为确保代码质量和一致性，请遵循以下开发规范：

### 代码风格规范

#### 命名规范

- **文件名**：使用小写字母，单词间用下划线连接（如 `wecom_service.py`）
- **类名**：使用驼峰命名法（如 `WecomService`）
- **函数名和变量名**：使用小写字母，单词间用下划线连接（如 `verify_url`）
- **常量**：使用全大写字母，单词间用下划线连接（如 `API_V1_STR`）

#### 文档字符串
所有模块、类和函数都应该有文档字符串，使用以下格式：

```python
"""
模块简短描述

详细描述（可选）
"""

class MyClass:
    """类的简短描述
    
    类的详细描述（可选）
    """
    
    def my_method(self, arg1: str, arg2: int) -> bool:
        """方法的简短描述
        
        Args:
            arg1: 参数1的描述
            arg2: 参数2的描述
            
        Returns:
            返回值的描述
        """
```

#### 类型注解
所有函数参数和返回值应使用类型注解，提高代码可读性：

```python
def my_function(param1: str, param2: int = 0) -> dict:
    """函数简短描述"""
    return {"result": True}
```

### API开发规范

#### 路由定义

- 使用FastAPI的APIRouter进行路由注册
- 按功能模块组织路由文件
- 在main.py中统一注册路由

```python
# 在app/api/routes/my_route.py中
router = APIRouter()

@router.get("/endpoint")
async def get_endpoint():
    """端点功能描述"""
    return {"result": "success"}

# 在app/main.py中注册
app.include_router(
    my_route.router,
    prefix=f"{settings.API_V1_STR}/my-route",
    tags=["功能标签"]
)
```

#### 依赖注入
使用FastAPI的Depends机制实现依赖注入，尤其是服务注入：

```python
@router.get("/endpoint")
async def get_endpoint(
    service: Annotated[MyService, Depends(get_my_service)]
):
    result = service.do_something()
    return result
```

#### 请求模型验证
使用Pydantic模型定义请求数据结构，实现自动验证：

```python
from pydantic import BaseModel

class MyRequest(BaseModel):
    name: str
    age: int
    
@router.post("/users")
async def create_user(user: MyRequest):
    # 处理验证后的数据
    return {"status": "success"}
```

### 日志规范

#### 日志器配置

- 使用项目的日志配置模块配置日志
- 日志文件保存在`logs/`目录下

#### 日志器命名
按模块路径命名日志器：

```python
# 在app/services/my_service.py中
logger = logging.getLogger("app.services.my_service")
```

#### 日志级别使用

- **DEBUG**：详细调试信息，仅在开发环境使用
- **INFO**：常规运行信息，记录重要操作
- **WARNING**：警告信息，不影响正常运行但需注意
- **ERROR**：错误信息，影响功能但不导致程序崩溃

```python
logger.info(f"操作成功: {result}")
logger.error(f"操作失败: {str(error)}")
```

### 错误处理规范

#### 统一错误响应
API错误应返回统一格式的JSON响应：

```python
{
    "status": "error",
    "message": "错误描述信息"
}
```

#### 异常捕获
正确处理异常并转换为适当的HTTP响应：

```python
try:
    result = service.process_data(data)
    return {"status": "success", "data": result}
except ValueError as e:
    logger.warning(f"数据验证失败: {str(e)}")
    return JSONResponse(
        status_code=400,
        content={"status": "error", "message": str(e)}
    )
```

### 数据库操作规范

#### 使用SQLAlchemy ORM
避免直接SQL查询，使用ORM操作数据库：

```python
# 创建记录
db_item = MyModel(name="示例", value=123)
db.add(db_item)
db.commit()
db.refresh(db_item)

# 查询记录
items = db.query(MyModel).filter(MyModel.name == "示例").all()
```

#### 会话管理
通过依赖注入获取数据库会话，使用完后确保关闭：

```python
def get_db():
    db = DBService().get_db()
    try:
        yield db
    finally:
        db.close()
```

### 代码提交规范

#### 提交信息格式
```
<类型>: <简短描述>

<详细描述（可选）>
```

类型包括：
- `feat`：新功能
- `fix`：bug修复
- `docs`：文档更改
- `style`：格式调整（不影响代码运行）
- `refactor`：代码重构
- `test`：添加或修改测试

#### 分支管理

- `main/master`：主分支，保持稳定
- `develop`：开发分支
- `feature/<功能名>`：功能分支
- `bugfix/<问题描述>`：修复分支

# 企业微信回调测试工具

这个项目提供了一个本地工具，用于测试企业微信的回调接口配置。

## 背景

企业微信要求应用配置回调URL时，必须通过它们的验证机制。这个过程包括：

1. 企业微信向配置的URL发送一个GET请求，包含加密的回显字符串
1. 应用需要解密这个字符串并原样返回
1. 只有成功解密并返回原字符串，企业微信才会认为配置有效

本工具可以在本地模拟这个过程，方便开发和测试。

## 功能

- 提供本地服务器来处理企业微信回调验证请求
- 测试脚本可以模拟企业微信发送的验证请求
- 验证整个加密、解密和认证流程是否正确

## 快速开始

### 环境准备

1. 确保已安装Python 3.x
1. 安装依赖包：

```bash
pip install -r requirements-test.txt
```

---

# 部署脚本优化版本迁移指南

## 🎯 问题解决

原始脚本 `simple-deploy.sh` 存在以下问题已在优化版本中解决：

### ✅ 已修复的问题
- **安全漏洞**：移除硬编码密码和敏感信息
- **macOS兼容性**：解决bash 3.2版本不支持关联数组的问题
- **代码结构**：重构长函数，提高可维护性
- **错误处理**：完善错误处理和恢复机制
- **配置管理**：统一配置文件管理

## 📋 迁移步骤

### 1. 选择合适的版本

| 系统环境 | 推荐版本 | 说明 |
|----------|----------|------|
| **macOS (默认bash)** | `simple-deploy-optimized-v3.sh` | 兼容bash 3.2+ |
| **Linux (bash 4.0+)** | `simple-deploy-optimized.sh` | 使用关联数组，功能更完整 |
| **生产环境** | `simple-deploy-optimized-v3.sh` | 兼容性最好，最稳定 |

### 2. 快速开始

```bash
# 1. 赋予执行权限
chmod +x simple-deploy-optimized-v3.sh

# 2. 初始化配置
./simple-deploy-optimized-v3.sh init

# 3. 编辑配置文件
nano .deploy.env

# 4. 启动服务
./simple-deploy-optimized-v3.sh start
```

### 3. 配置迁移

#### 原脚本硬编码配置
```bash
# 原脚本中的硬编码配置
REGISTRY_SERVER="*************:5000"
REGISTRY_USERNAME="root"
REGISTRY_PASSWORD="123qq123"
MYSQL_PASSWORD="rootpassword"
```

#### 新脚本配置文件 `.deploy.env`
```bash
# 新脚本的安全配置
REGISTRY_SERVER="*************:5000"
REGISTRY_USERNAME="root"
REGISTRY_PASSWORD="123qq123"
MYSQL_PASSWORD="rootpassword"
```

## 🔄 命令对照表

| 功能 | 原脚本 | 优化脚本 |
|------|--------|----------|
| 启动服务 | `./simple-deploy.sh start` | `./simple-deploy-optimized-v3.sh start` |
| 私有仓库部署 | `./simple-deploy.sh local` | `./simple-deploy-optimized-v3.sh pull-and-start` |
| 指定镜像启动 | `./simple-deploy.sh use-registry-image <镜像>` | `./simple-deploy-optimized-v3.sh start <镜像>` |
| 网络连接 | `./simple-deploy.sh connect-network <网络>` | `./simple-deploy-optimized-v3.sh connect-network <网络>` |
| 查看日志 | `./simple-deploy.sh logs` | `./simple-deploy-optimized-v3.sh logs` |
| **新增功能** | ❌ | `./simple-deploy-optimized-v3.sh backup/rollback/health` |

## 🆕 新增功能

### 1. 配置管理
```bash
# 初始化配置文件
./simple-deploy-optimized-v3.sh init
```

### 2. 备份和回滚
```bash
# 备份当前部署
./simple-deploy-optimized-v3.sh backup

# 回滚到上次备份
./simple-deploy-optimized-v3.sh rollback
```

### 3. 健康检查
```bash
# 执行服务健康检查
./simple-deploy-optimized-v3.sh health
```

### 4. 模拟模式
```bash
# 模拟执行，不实际操作
./simple-deploy-optimized-v3.sh --dry-run start
```

### 5. 详细模式
```bash
# 显示详细的调试信息
./simple-deploy-optimized-v3.sh -v start
```

## 🛡️ 安全改进

### 1. 配置文件权限
```bash
# 设置配置文件安全权限
chmod 600 .deploy.env

# 避免提交敏感配置到版本控制
echo ".deploy.env" >> .gitignore
```

### 2. 环境变量支持
```bash
# 使用环境变量而不是配置文件
export MYSQL_PASSWORD="secure-password"
./simple-deploy-optimized-v3.sh start
```

## 🔧 故障排除

### 常见问题

#### 1. bash版本兼容性问题
```
错误: declare: -A: invalid option
```
**解决方案**: 使用 `simple-deploy-optimized-v3.sh` (macOS兼容版本)

#### 2. 配置验证失败
```
错误: 缺少必需配置: MYSQL_PASSWORD
```
**解决方案**: 编辑 `.deploy.env` 文件，设置 `MYSQL_PASSWORD`

#### 3. Docker daemon未运行
```
错误: Docker daemon未运行
```
**解决方案**: 
```bash
# macOS
open /Applications/Docker.app

# Linux
sudo systemctl start docker
```

## 📝 最佳实践

### 1. 生产环境部署流程
```bash
# 1. 备份当前环境
./simple-deploy-optimized-v3.sh backup

# 2. 模拟新版本部署
./simple-deploy-optimized-v3.sh --dry-run start new-version:latest

# 3. 实际部署
./simple-deploy-optimized-v3.sh start new-version:latest

# 4. 健康检查
./simple-deploy-optimized-v3.sh health

# 5. 如有问题，立即回滚
./simple-deploy-optimized-v3.sh rollback
```

### 2. 开发环境使用
```bash
# 1. 初始化开发配置
./simple-deploy-optimized-v3.sh init

# 2. 编辑配置，设置开发数据库
# 3. 启动开发环境
./simple-deploy-optimized-v3.sh start

# 4. 查看实时日志
./simple-deploy-optimized-v3.sh logs 100 true
```

## 🔗 相关文档

- [完整使用指南](README-optimized.md)
- [原始脚本](simple-deploy.sh)
- [优化版本 v2.0](simple-deploy-optimized.sh) - Linux/bash 4.0+
- [优化版本 v2.1](simple-deploy-optimized-v3.sh) - macOS兼容

---

**注意**: 建议在生产环境使用前在测试环境充分验证新脚本的功能。

---

# 企业微信命令桥接服务部署脚本 v2.0 使用指南

## 🚀 主要优化内容

### 🔒 安全性增强
- ✅ 移除硬编码密码，使用配置文件和环境变量管理敏感信息
- ✅ 安全的Docker登录机制
- ✅ 配置文件权限控制

### 🏗️ 代码结构优化
- ✅ 模块化函数设计，单一职责原则
- ✅ 统一的配置管理系统
- ✅ 完善的错误处理和异常恢复
- ✅ 严格模式 (`set -euo pipefail`)

### 🔧 功能增强
- ✅ 自动备份和回滚功能
- ✅ 服务健康检查
- ✅ 进度指示器和详细日志
- ✅ 模拟执行模式 (dry-run)
- ✅ 详细的系统环境检查

### 📊 用户体验改进
- ✅ 彩色输出和统一的日志格式
- ✅ 详细的帮助信息和使用示例
- ✅ 操作确认和状态显示

## 📋 快速开始

### 1. 初始化配置
```bash
# 赋予执行权限
chmod +x simple-deploy-optimized.sh

# 初始化配置文件
./simple-deploy-optimized.sh init
```

### 2. 编辑配置文件
编辑生成的 `.deploy.env` 文件：
```bash
# 企业微信命令桥接服务配置文件

# 私有仓库配置
REGISTRY_SERVER="*************:5000"
REGISTRY_USERNAME="root"
REGISTRY_PASSWORD="123qq123"

# 数据库配置
MYSQL_PASSWORD="your-secure-password"

# 其他配置保持默认或根据需要修改...
```

### 3. 启动服务
```bash
# 使用默认镜像启动
./simple-deploy-optimized.sh start

# 或指定特定镜像
./simple-deploy-optimized.sh start my-registry.com/wecom-app:v2.0
```

## 🛠️ 命令详解

### 基础命令

| 命令 | 说明 | 示例 |
|------|------|------|
| `init` | 初始化配置文件 | `./simple-deploy-optimized.sh init` |
| `start [镜像]` | 启动服务 | `./simple-deploy-optimized.sh start` |
| `stop` | 停止服务 | `./simple-deploy-optimized.sh stop` |
| `restart` | 重启服务 | `./simple-deploy-optimized.sh restart` |
| `status` | 显示服务状态 | `./simple-deploy-optimized.sh status` |

### 高级命令

| 命令 | 说明 | 示例 |
|------|------|------|
| `pull-and-start [镜像]` | 拉取镜像并启动 | `./simple-deploy-optimized.sh pull-and-start` |
| `logs [行数] [follow]` | 查看日志 | `./simple-deploy-optimized.sh logs 100 true` |
| `connect-network <网络>` | 连接到外部网络 | `./simple-deploy-optimized.sh connect-network deploy_default` |
| `backup` | 备份当前部署 | `./simple-deploy-optimized.sh backup` |
| `rollback` | 回滚到上次备份 | `./simple-deploy-optimized.sh rollback` |
| `health` | 执行健康检查 | `./simple-deploy-optimized.sh health` |

### 选项参数

| 选项 | 说明 | 示例 |
|------|------|------|
| `-v, --verbose` | 详细输出模式 | `./simple-deploy-optimized.sh -v start` |
| `-n, --dry-run` | 模拟执行模式 | `./simple-deploy-optimized.sh -n start` |
| `-h, --help` | 显示帮助信息 | `./simple-deploy-optimized.sh --help` |
| `--version` | 显示版本信息 | `./simple-deploy-optimized.sh --version` |

## 📁 配置文件说明

### `.deploy.env` - 主配置文件
```bash
# 私有仓库配置
REGISTRY_SERVER="your-registry.com:5000"    # 私有仓库地址
REGISTRY_USERNAME="username"                 # 仓库用户名
REGISTRY_PASSWORD="password"                 # 仓库密码

# 镜像配置
DEFAULT_IMAGE="wecom-app:latest"             # 默认镜像
LOCAL_IMAGE_NAME="wecom-app:local"           # 本地镜像名称

# 数据库配置
MYSQL_HOST="shared-mysql"                    # MySQL主机地址
MYSQL_PORT="3306"                           # MySQL端口
MYSQL_USER="root"                           # MySQL用户名
MYSQL_PASSWORD="your-password"               # MySQL密码 (必需)
MYSQL_DB="wecom_bridge"                     # 数据库名

# Redis配置
REDIS_HOST="shared-redis"                    # Redis主机地址
REDIS_PORT="6379"                           # Redis端口

# 应用配置
APP_PORT="8000"                             # 应用端口
CONTAINER_NAME="wecom-bridge-app"           # 容器名称
HEALTH_CHECK_TIMEOUT="60"                   # 健康检查超时时间(秒)
```

## 🔍 使用场景示例

### 场景1：开发环境快速部署
```bash
# 1. 初始化配置
./simple-deploy-optimized.sh init

# 2. 编辑配置文件（设置测试数据库密码）
# 3. 启动服务
./simple-deploy-optimized.sh start

# 4. 查看服务状态
./simple-deploy-optimized.sh status
```

### 场景2：生产环境部署
```bash
# 1. 使用详细模式启动，从私有仓库拉取最新镜像
./simple-deploy-optimized.sh -v pull-and-start

# 2. 连接到生产网络
./simple-deploy-optimized.sh connect-network prod_network

# 3. 执行健康检查
./simple-deploy-optimized.sh health

# 4. 查看运行日志
./simple-deploy-optimized.sh logs 200 true
```

### 场景3：版本更新和回滚
```bash
# 1. 备份当前部署
./simple-deploy-optimized.sh backup

# 2. 部署新版本
./simple-deploy-optimized.sh start my-registry.com/wecom-app:v2.1

# 3. 如果出现问题，执行回滚
./simple-deploy-optimized.sh rollback
```

### 场景4：问题诊断
```bash
# 1. 模拟执行，检查配置
./simple-deploy-optimized.sh --dry-run start

# 2. 详细模式查看启动过程
./simple-deploy-optimized.sh -v restart

# 3. 查看详细日志
./simple-deploy-optimized.sh logs 500 true
```

## 🛡️ 安全最佳实践

### 1. 配置文件安全
```bash
# 设置配置文件权限，仅所有者可读写
chmod 600 .deploy.env

# 避免将配置文件提交到版本控制
echo ".deploy.env" >> .gitignore
```

### 2. 密码管理
```bash
# 使用环境变量设置密码
export MYSQL_PASSWORD="your-secure-password"
./simple-deploy-optimized.sh start

# 或使用密码管理工具
MYSQL_PASSWORD=$(vault kv get -field=password secret/mysql)
```

### 3. 网络安全
```bash
# 确保容器连接到正确的网络
./simple-deploy-optimized.sh connect-network secure_network

# 检查网络配置
docker network inspect secure_network
```

## 🐛 故障排除

### 常见问题

#### 1. 配置验证失败
```
错误: 缺少必需配置: MYSQL_PASSWORD
```
**解决方案**: 编辑 `.deploy.env` 文件，设置 `MYSQL_PASSWORD`

#### 2. Docker daemon未运行
```
错误: Docker daemon未运行
```
**解决方案**: 启动Docker服务
```bash
sudo systemctl start docker
```

#### 3. 镜像拉取失败
```
错误: 镜像拉取失败，已达最大重试次数
```
**解决方案**: 
- 检查网络连接
- 验证镜像名称和标签
- 确认Docker注册表凭据

#### 4. 健康检查超时
```
错误: 服务健康检查超时
```
**解决方案**: 
- 检查应用是否正确启动
- 验证健康检查端点
- 增加超时时间配置

### 日志查看
```bash
# 查看部署日志
cat deploy.log

# 查看应用日志
./simple-deploy-optimized.sh logs 100

# 查看Docker日志
docker logs wecom-bridge-app
```

## 📈 版本升级指南

### 从旧版本迁移
1. 备份现有部署
2. 使用新脚本初始化配置
3. 迁移旧配置到新的配置文件
4. 测试新脚本功能
5. 逐步替换旧脚本

### 配置兼容性
- 大部分配置保持向后兼容
- 新增配置项有合理的默认值
- 详细的迁移日志和提示

## 🤝 贡献和反馈

如果您发现问题或有改进建议，请：
1. 查看故障排除部分
2. 检查日志文件 `deploy.log`
3. 提供详细的错误信息和环境描述

---

**注意**: 请确保在生产环境使用前充分测试该脚本，并根据实际环境调整配置。

---

# 评论数据批量插入脚本使用说明

## 功能描述
这个Python脚本用于批量插入评论数据到`wecom_bridge.comment_records`数据库表中。脚本会根据您提供的评论内容列表，自动生成相应的数据库插入记录。

## 文件说明
- `insert_comments_simple.py` - 主要的插入脚本
- `requirements.txt` - Python依赖包列表
- `README_insert_comments.md` - 本使用说明文档

## 安装依赖

首先安装所需的Python包：

```bash
pip install -r requirements.txt
```

或者直接安装MySQL连接器：

```bash
pip install mysql-connector-python
```

## 配置数据库连接

在运行脚本之前，请修改`insert_comments_simple.py`文件中的数据库配置：

```python
DB_CONFIG = {
    'host': 'localhost',        # 数据库主机地址
    'database': 'wecom_bridge', # 数据库名称
    'user': 'your_username',    # 数据库用户名
    'password': 'your_password', # 数据库密码
    'charset': 'utf8mb4',
    'autocommit': False
}
```

## 数据库表结构

确保您的数据库中存在`comment_records`表，包含以下字段：

- `id` - 主键ID
- `source_id` - 源ID
- `video_id` - 视频ID
- `source_content` - 评论内容
- `root_id` - 根评论ID
- `root_reply_content` - 根评论内容
- `target_id` - 目标ID
- `target_reply_content` - 目标回复内容
- `reply_time` - 回复时间
- `uri` - 链接地址
- `user_mid` - 用户ID
- `user_nickname` - 用户昵称
- `path` - 路径
- `comment_type` - 评论类型
- `reply_count` - 回复数量
- `like_count` - 点赞数量
- `importance_level` - 重要程度
- `status` - 状态
- `is_top` - 是否置顶
- `is_blocked` - 是否被屏蔽
- `created_at` - 创建时间
- `updated_at` - 更新时间

## 运行脚本

1. 确保数据库连接配置正确
2. 运行脚本：

```bash
python insert_comments_simple.py
```

3. 脚本会提示您确认配置，输入`y`或`yes`继续执行

## 脚本功能

- **自动生成ID**: 脚本从ID 272开始，为每条评论自动分配递增的ID和source_id
- **批量插入**: 使用`executemany()`方法进行高效的批量插入
- **事务处理**: 支持事务回滚，确保数据一致性
- **日志记录**: 详细的日志输出，便于跟踪执行过程
- **错误处理**: 完善的异常处理机制

## 插入的数据范围

- **ID范围**: 272 - 361 (共90条记录)
- **source_id范围**: 4026748505 - 4026748594
- **评论内容**: 根据您提供的评论列表

## 注意事项

1. **备份数据**: 在运行脚本前，建议备份您的数据库
2. **检查重复**: 确保要插入的ID范围不会与现有数据冲突
3. **权限确认**: 确保数据库用户有INSERT权限
4. **网络连接**: 确保能够正常连接到数据库服务器

## 故障排除

### 常见错误

1. **连接失败**: 检查数据库配置信息是否正确
2. **权限不足**: 确保数据库用户有相应的操作权限
3. **表不存在**: 确认数据库中存在`comment_records`表
4. **字段不匹配**: 检查表结构是否与脚本中的字段对应

### 日志信息

脚本会输出详细的日志信息，包括：
- 数据库连接状态
- 插入记录数量
- 插入的ID范围
- 错误信息（如果有）

## 自定义修改

如果需要修改插入的数据：

1. **修改评论内容**: 编辑`COMMENTS`列表
2. **修改基础数据**: 编辑`BASE_DATA`字典
3. **修改起始ID**: 修改`start_id`和`start_source_id`变量

## 联系支持

如果在使用过程中遇到问题，请检查：
1. 数据库连接配置
2. 表结构是否正确
3. 权限设置
4. 日志输出的错误信息

---

# 数据库初始化说明

## 快速开始

项目启动时会自动检查并创建 `wecom_bridge` 数据库和相关表。

### 自动初始化
当您启动应用时，系统会自动：
1. 检查 `wecom_bridge` 数据库是否存在，不存在则创建
2. 检查必需的表是否存在，不存在则创建
3. 执行 `sql/table.sql` 文件中的SQL语句（如果文件存在）

### 应用启动方式
支持两种启动方式，都会自动进行数据库初始化：

```bash
# 方式1：使用启动脚本
python run.py

# 方式2：作为Python模块启动
python -m app
```

### 手动初始化
如果需要手动初始化数据库，可以运行：

```bash
python scripts/init_database.py
```

## 数据库配置

确保以下环境变量已正确设置：

```bash
DB_HOST=shared-mysql        # 默认: shared-mysql
DB_PORT=3306               # 默认: 3306
DB_USER=root               # 默认: root
DB_PASSWORD=rootpassword   # 默认: rootpassword
DB_NAME=wecom_bridge       # 默认: wecom_bridge
```

## 创建的表

- `bili_users` - B站用户信息
- `comment_records` - 评论记录
- `comment_replies` - 评论回复任务
- `video_details` - 视频详情

## 注意事项

- 初始化过程是安全的，不会删除现有数据
- 可以多次运行，具有幂等性
- 如果遇到权限问题，请确保数据库用户有创建数据库和表的权限

详细文档请参考：[docs/database_setup.md](docs/database_setup.md)

---

# 企业微信命令桥接服务 - 简化部署指南

本文档介绍了企业微信命令桥接服务的简化部署方法，使用直接的docker-compose命令替代复杂的脚本。

## 前提条件

- 已安装 Docker 和 Docker Compose
- 服务器可以访问互联网
- 已配置好企业微信的相关环境变量

## 快速开始

我们提供了简化版的部署脚本，只需几个简单命令即可完成部署和管理：

### 1. 启动服务

```bash
# 标准启动方式（使用默认私有仓库镜像）
./simple-deploy.sh start

# 从私有仓库拉取最新镜像并启动
./simple-deploy.sh local

# 使用指定镜像启动服务
./simple-deploy.sh use-registry-image 127.0.0.1:5000/wecom-app:v1.2
```

### 2. 停止服务

```bash
./simple-deploy.sh stop
```

### 3. 重启服务

```bash
./simple-deploy.sh restart
```

### 4. 查看日志

```bash
./simple-deploy.sh logs
```

## 网络连接

如果应用需要连接到其他服务（如MySQL或Redis）所在的网络，可以使用以下命令：

```bash
# 将应用容器连接到指定网络
./simple-deploy.sh connect-network <网络名称>

# 例如连接到默认部署网络
./simple-deploy.sh connect-network deploy_default
```

## 环境配置

部署脚本会自动创建并配置必要的环境变量和目录：

1. 自动创建 `.env` 文件（如果不存在）
2. 配置数据库和Redis连接信息
3. 创建必要的上传和日志目录

## 故障排除

### 1. 镜像拉取失败

如果遇到镜像拉取失败，请检查：

- 私有仓库地址是否正确
- 登录凭证是否有效
- 网络连接是否正常

### 2. 服务启动后无法访问

请确认：

- 应用容器是否正常运行：`docker-compose ps`
- 是否已连接到正确的网络：`./simple-deploy.sh connect-network <网络名称>`
- 端口映射是否正确：`docker-compose logs -f`

### 3. 如何确认服务是否正常运行？

您可以访问 http://localhost/health 检查服务健康状态。

### 4. 企业微信回调验证失败怎么办？

检查日志并确认：

```bash
./simple-deploy.sh logs
```

## 使用说明

新的部署方案使用了两个配置文件：

1. `docker-compose.yml` - 标准配置，用于生产环境
2. `docker-compose.override.yml` - 由脚本自动生成，包含镜像和环境变量配置

## 常见问题

### 1. 如何切换到特定版本的镜像？

使用以下命令：

```bash
./simple-deploy.sh use-registry-image <仓库地址>/<镜像名称>:<标签>
```

### 2. 如何查看容器的网络配置？

连接网络后，脚本会自动显示容器的网络配置信息。您也可以通过以下命令查看：

```bash
docker inspect -f '{{range $k, $v := .NetworkSettings.Networks}}{{$k}}: {{$v.IPAddress}}{{printf "\n"}}{{end}}' wecom-bridge-app
```

### 3. 想要保留旧的部署方式怎么办？

旧的部署脚本仍然可用，您可以继续使用 `./deploy.sh` 命令。