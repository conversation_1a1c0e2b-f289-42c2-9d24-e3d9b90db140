# 数据库级别锁机制实现文档

## 概述

为了防止同一个回复任务被重复处理，在 `process_pending_replies` 方法中添加了数据库级别的锁机制。这个机制与现有的应用层任务执行状态检查（`_task_execution_status`）形成双重保护。

## 实现原理

### 1. 锁机制层次

- **应用层保护**: `_task_execution_status` - 防止整个批处理任务重复执行
- **数据库层保护**: `SELECT FOR UPDATE NOWAIT` - 防止单个回复任务重复处理

### 2. 核心实现

#### 2.1 数据库锁获取方法

```python
@contextmanager
def _acquire_reply_lock(self, reply_id: int):
    """获取回复任务的数据库级别锁"""
    try:
        # 使用 SELECT FOR UPDATE NOWAIT 尝试获取行级锁
        locked_reply = self.db_session.query(CommentReply).filter(
            CommentReply.id == reply_id
        ).with_for_update(nowait=True).first()
        
        if locked_reply:
            yield locked_reply
        else:
            yield None
    except OperationalError:
        # 锁获取失败，其他进程正在处理该任务
        yield None
```

#### 2.2 带锁的任务处理方法

```python
def _process_single_reply_with_lock(self, reply: CommentReply) -> bool:
    """使用数据库锁处理单个评论回复任务"""
    try:
        with self._acquire_reply_lock(reply.id) as locked_reply:
            if locked_reply is None:
                # 无法获取锁，跳过该任务
                return False
            
            # 检查任务状态是否仍为pending
            if locked_reply.status != ReplyStatus.pending:
                return False
            
            # 处理任务
            return self._process_single_reply(locked_reply)
    except Exception as e:
        logger.error(f"使用锁处理回复任务时发生异常: {str(e)}")
        return False
```

## 修改的方法

### 1. process_pending_replies

- **串行模式**: 使用 `_process_single_reply_with_lock` 替代原来的 `_process_single_reply`
- **并行模式**: 使用 `_process_single_reply_with_new_session_and_lock` 确保线程安全

### 2. process_pending_replies_thread_safe

- **串行模式**: 使用 `_process_single_reply_with_lock`
- **并行模式**: 使用 `_process_single_reply_with_new_session_and_lock`

## 锁机制特点

### 1. 非阻塞锁

- 使用 `NOWAIT` 选项，如果无法立即获取锁则立即返回
- 避免线程阻塞等待，提高系统响应性

### 2. 自动释放

- 锁在事务提交或回滚时自动释放
- 无需手动管理锁的生命周期

### 3. 异常安全

- 即使在异常情况下，锁也会在事务结束时自动释放
- 防止死锁情况

## 工作流程

### 1. 串行处理模式

```
1. 获取待处理的回复任务列表
2. 对每个任务:
   a. 尝试获取数据库锁
   b. 如果获取失败，跳过该任务
   c. 如果获取成功，检查任务状态
   d. 处理任务
   e. 锁在事务结束时自动释放
```

### 2. 并行处理模式

```
1. 获取待处理的回复任务列表
2. 为每个任务创建独立线程:
   a. 创建独立的数据库会话
   b. 尝试获取数据库锁
   c. 如果获取失败，跳过该任务
   d. 如果获取成功，检查任务状态
   e. 处理任务
   f. 关闭数据库会话（锁自动释放）
```

## 日志输出

### 成功获取锁
```
DEBUG: 成功获取回复任务锁: reply_id=123
```

### 无法获取锁
```
INFO: 无法获取回复任务锁，任务可能正在被其他进程处理: reply_id=123
INFO: 跳过回复任务（无法获取锁）: reply_id=123
```

### 状态已变更
```
INFO: 跳过回复任务（状态已变更）: reply_id=123, status=processing
```

## 测试验证

提供了测试脚本 `test_reply_locking.py` 用于验证锁机制的正确性：

```bash
python test_reply_locking.py
```

测试会创建多个线程同时处理同一个任务，验证只有一个线程能够成功处理。

## 兼容性

- 保持与现有代码的完全兼容
- 不影响现有的应用层保护机制
- 可以与现有的串行和并行处理模式无缝集成

## 性能影响

- 锁获取操作非常快速（微秒级别）
- 使用 NOWAIT 避免阻塞等待
- 对整体性能影响极小

## 注意事项

1. **数据库支持**: 需要数据库支持 `SELECT FOR UPDATE NOWAIT` 语法（MySQL、PostgreSQL 等）
2. **事务管理**: 锁的生命周期与数据库事务绑定
3. **错误处理**: 需要正确处理 `OperationalError` 异常
4. **监控**: 建议监控跳过的任务数量，以评估并发冲突情况
