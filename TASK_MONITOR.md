# 定时任务监控指南

## 🛡️ 防重复执行机制

系统已内置多层防重复执行保护：

### 1. APScheduler 级别保护
- `max_instances: 1` - 同一任务同时只能有1个实例
- `coalesce: True` - 合并等待中的重复任务

### 2. 应用层保护
- 全局状态跟踪：`_task_execution_status`
- 任务开始时检查状态，如果正在执行则跳过

### 3. 数据库层保护
- 状态字段管理：`pending` → `processing` → `completed/failed`
- 串行处理模式避免并发冲突

## 📊 任务执行监控

### 关键指标
- **未回复评论任务**：建议执行时间 < 4分钟
- **待回复任务**：建议执行时间 < 9分钟
- **任务间隔**：当前配置 300秒/600秒

### 日志监控关键词
```bash
# 监控任务重叠
grep "正在执行中，跳过本次触发" logs/app.log

# 监控执行时间过长
grep "执行时间过长" logs/app.log

# 监控任务执行情况
grep "任务开始时间\|执行时间=" logs/app.log
```

## ⚠️ 故障排除

### 如果每分钟执行一次会发生什么？

1. **第1分钟**：任务A开始执行
2. **第2分钟**：
   - APScheduler检测到任务A还在执行
   - 由于`max_instances=1`，任务B被**自动跳过**
   - 日志显示：`Execution of job "xxx" skipped: maximum number of running instances reached (1)`

3. **应用层双重保护**：
   - 即使APScheduler保护失效，应用层状态检查也会跳过重复执行
   - 日志显示：`任务 xxx 正在执行中，跳过本次触发`

### 问题场景与解决方案

| 场景 | 现象 | 解决方案 |
|------|------|----------|
| 任务执行时间过长 | 频繁出现跳过执行日志 | 增加定时间隔或优化任务逻辑 |
| 数据库连接超时 | 任务执行失败 | 检查数据库连接池配置 |
| Coze API限流 | 回复生成失败 | 增加重试机制或降低并发度 |

## 🔧 配置调优建议

### 安全的任务间隔设置
```env
# 保守配置（推荐）
COMMENT_UNREPLIED_INTERVAL=300    # 5分钟
COMMENT_PENDING_INTERVAL=600      # 10分钟

# 高频配置（需监控）
COMMENT_UNREPLIED_INTERVAL=120    # 2分钟  
COMMENT_PENDING_INTERVAL=180      # 3分钟

# 测试配置（需密切监控）
COMMENT_UNREPLIED_INTERVAL=60     # 1分钟
COMMENT_PENDING_INTERVAL=60       # 1分钟
```

### 性能优化建议
1. **批量处理**：每次处理的任务数量适中（当前limit=10）
2. **超时设置**：避免单个任务执行时间过长
3. **错误重试**：合理的重试机制避免任务卡死
4. **监控告警**：设置执行时间阈值告警

## 📈 监控脚本示例

```bash
#!/bin/bash
# task_monitor.sh - 定时任务监控脚本

LOG_FILE="logs/app.log"
DATE=$(date '+%Y-%m-%d %H:%M')

echo "[$DATE] 定时任务监控报告"
echo "================================"

# 检查任务重叠情况
OVERLAP_COUNT=$(grep -c "正在执行中，跳过本次触发" $LOG_FILE)
echo "任务重叠次数: $OVERLAP_COUNT"

# 检查执行时间过长
TIMEOUT_COUNT=$(grep -c "执行时间过长" $LOG_FILE)
echo "执行超时次数: $TIMEOUT_COUNT"

# 最近的任务执行时间
echo "最近任务执行时间:"
grep "执行时间=" $LOG_FILE | tail -5

if [ $OVERLAP_COUNT -gt 10 ] || [ $TIMEOUT_COUNT -gt 3 ]; then
    echo "⚠️  警告：检测到异常情况，建议检查任务配置"
fi
```

## 💡 最佳实践总结

1. **现有保护已足够**：系统已有完善的防重复机制
2. **监控执行时间**：通过日志监控任务性能
3. **合理设置间隔**：根据实际执行时间调整定时间隔
4. **定期检查日志**：关注异常和性能指标
5. **渐进式调优**：从保守配置开始，逐步优化 