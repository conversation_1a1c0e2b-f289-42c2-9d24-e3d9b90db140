#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import base64
from pprint import pprint
import time

# 设置基础URL - 根据app/main.py中的配置，users路由添加了API_V1_STR前缀
BASE_URL = "http://localhost:8000/api/v1/users"

# 设置测试的用户ID（用于测试后续的API）
TEST_USER_ID = "123456789"
TEST_MACHINE_CODE = "MACHINE_CODE_TEST_123"

# 打印分隔符的函数
def print_separator(title):
    print("\n" + "="*50)
    print(f"测试接口: {title}")
    print("="*50)

def test_create_user():
    """测试创建用户接口"""
    print_separator("创建用户 (POST /)")
    
    # 准备测试数据
    user_data = {
        "user_id": TEST_USER_ID,
        "username": "测试用户",
        "avatar_url": "https://example.com/avatar.jpg",
        "login_status": "已登录",
        "machine_code": TEST_MACHINE_CODE,
        "machine_name": "测试机器",
        "is_disabled": False
    }
    
    # 发送POST请求
    response = requests.post(f"{BASE_URL}/", json=user_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)
    
    return response.json() if response.status_code == 201 else None

def test_get_users():
    """测试获取用户列表接口"""
    print_separator("获取用户列表 (GET /)")
    
    # 发送GET请求 - 不带任何过滤条件
    response = requests.get(f"{BASE_URL}/")
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)
    
    # 发送GET请求 - 带过滤条件
    print("\n带过滤条件的请求:")
    params = {
        "username": "测试",
        "login_status": "已登录",
        "page": 1,
        "page_size": 10
    }
    
    response = requests.get(f"{BASE_URL}/", params=params)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)

def test_get_user_detail():
    """测试获取用户详情接口"""
    print_separator(f"获取用户详情 (GET /{TEST_USER_ID})")
    
    # 发送GET请求
    response = requests.get(f"{BASE_URL}/{TEST_USER_ID}")
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)

def test_update_user():
    """测试更新用户信息接口"""
    print_separator(f"更新用户信息 (PUT /{TEST_USER_ID})")
    
    # 准备测试数据
    update_data = {
        "username": "更新后的测试用户",
        "avatar_url": "https://example.com/new_avatar.jpg"
    }
    
    # 发送PUT请求
    response = requests.put(f"{BASE_URL}/{TEST_USER_ID}", json=update_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)

def test_update_login_status():
    """测试更新用户登录状态接口"""
    print_separator(f"更新用户登录状态 (PUT /{TEST_USER_ID}/login-status)")
    
    # 准备测试数据
    status_data = {
        "login_status": "离线",
        "machine_code": TEST_MACHINE_CODE,
        "machine_name": "新测试机器"
    }
    
    # 发送PUT请求
    response = requests.put(f"{BASE_URL}/{TEST_USER_ID}/login-status", json=status_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)

def test_toggle_user_disable():
    """测试禁用/启用用户接口"""
    print_separator(f"禁用用户 (PUT /{TEST_USER_ID}/disable?is_disabled=true)")
    
    # 发送PUT请求 - 禁用用户
    response = requests.put(f"{BASE_URL}/{TEST_USER_ID}/disable", params={"is_disabled": True})
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)
    
    print("\n启用用户 (PUT /{TEST_USER_ID}/disable?is_disabled=false)")
    
    # 发送PUT请求 - 启用用户
    response = requests.put(f"{BASE_URL}/{TEST_USER_ID}/disable", params={"is_disabled": False})
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)

def test_upload_qrcode():
    """测试上传二维码接口"""
    print_separator("上传二维码 (POST /qrcode/upload)")
    
    # 创建一个假的base64编码的二维码数据
    fake_qrcode = base64.b64encode(b"This is a fake QR code image").decode('utf-8')
    
    # 准备测试数据
    qrcode_data = {
        "machine_code": TEST_MACHINE_CODE,
        "qrcode_base64": fake_qrcode
    }
    
    # 发送POST请求
    response = requests.post(f"{BASE_URL}/qrcode/upload", json=qrcode_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)

def test_get_qrcode():
    """测试获取二维码接口"""
    print_separator(f"获取二维码 (GET /qrcode/{TEST_MACHINE_CODE})")
    
    # 发送GET请求
    response = requests.get(f"{BASE_URL}/qrcode/{TEST_MACHINE_CODE}")
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)

def test_delete_user():
    """测试删除用户接口"""
    print_separator(f"删除用户 (DELETE /{TEST_USER_ID})")
    
    # 发送DELETE请求
    response = requests.delete(f"{BASE_URL}/{TEST_USER_ID}")
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    if response.status_code == 204:
        print("用户删除成功")
    else:
        try:
            pprint(response.json())
        except Exception:
            print(response.text)

if __name__ == "__main__":
    # 执行所有测试
    print("开始测试B站用户API...")
    
    # 1. 创建用户
    created_user = test_create_user()
    
    # 等待一会以确保数据已经写入
    time.sleep(1)
    
    # 2. 获取用户列表
    test_get_users()
    
    # 3. 获取用户详情
    test_get_user_detail()
    
    # 4. 更新用户信息
    test_update_user()
    
    # 5. 更新用户登录状态
    test_update_login_status()
    
    # 6. 禁用/启用用户
    test_toggle_user_disable()
    
    # 7. 上传二维码
    test_upload_qrcode()
    
    # 8. 获取二维码
    test_get_qrcode()
    
    # 9. 删除用户
    # test_delete_user()
    
    print("\n所有接口测试完成!")