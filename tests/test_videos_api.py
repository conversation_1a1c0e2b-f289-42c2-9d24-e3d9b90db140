#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
from pprint import pprint

# 设置基础URL - 根据app/main.py中的配置，videos路由添加了API_V1_STR前缀
BASE_URL = "http://localhost:8000/api/v1/videos"
USER_BASE_URL = "http://localhost:8000/api/v1/users"

# 设置测试的视频ID和用户ID（用于测试后续的API）
TEST_VIDEO_ID = "BV12345678"
TEST_USER_BUSINESS_ID = "123456789"  # B站用户的业务ID
TEST_USER_ID = None  # 数据库中用户的主键ID，将在测试前获取


# 打印分隔符的函数
def print_separator(title):
    print("\n" + "="*50)
    print(f"测试接口: {title}")
    print("="*50)


# 确保用户存在的辅助函数
def ensure_test_user_exists():
    """确保测试用户存在，并返回用户的主键ID"""
    print_separator("确保测试用户存在")
    
    # 先检查用户是否已存在
    response = requests.get(f"{USER_BASE_URL}/{TEST_USER_BUSINESS_ID}")
    if response.status_code == 200:
        user_data = response.json()
        print("用户已存在")
        return user_data.get("id")
    
    # 如果用户不存在，则创建新用户
    print("用户不存在，创建新用户")
    user_data = {
        "user_id": TEST_USER_BUSINESS_ID,
        "username": "测试用户",
        "avatar_url": "https://example.com/avatar.jpg",
        "login_status": "已登录",
        "machine_code": "MACHINE_CODE_TEST_123",
        "machine_name": "测试机器",
        "is_disabled": False
    }
    
    # 发送POST请求创建用户
    response = requests.post(f"{USER_BASE_URL}/", json=user_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        result = response.json()
        pprint(result)
        # 如果用户创建成功，返回用户ID
        if response.status_code == 201:
            return result.get("id")
    except Exception:
        print(response.text)
    
    return None


def test_create_video():
    """测试创建视频接口"""
    print_separator("创建视频 (POST /)")
    
    # 确保已设置了全局用户ID
    if TEST_USER_ID is None:
        print("错误：尚未设置用户ID，无法创建视频")
        return None
    
    # 准备测试数据 - 使用正确的字段名称
    video_data = {
        "video_id": TEST_VIDEO_ID,
        "title": "测试视频标题",
        "user_id": TEST_USER_ID,  # 使用数据库中的用户主键ID
        "is_disabled": False,
        "video_url": "https://example.com/video.mp4",
        "video_content": "这是一个测试视频的描述内容"  # 正确的字段名称
    }
    
    # 发送POST请求
    response = requests.post(f"{BASE_URL}/", json=video_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)
    
    return response.json() if response.status_code == 201 else None


def test_create_video_default_disabled():
    """测试创建视频时默认是禁用状态"""
    print_separator("创建视频（默认禁用状态）(POST /)")
    
    # 确保已设置了全局用户ID
    if TEST_USER_ID is None:
        print("错误：尚未设置用户ID，无法创建视频")
        return None
    
    # 准备测试数据 - 不设置is_disabled字段
    video_data = {
        "video_id": "BV_DEFAULT_TEST",
        "title": "默认禁用测试视频",
        "user_id": TEST_USER_ID,
        "video_url": "https://example.com/default_video.mp4",
        "video_content": "这是一个测试默认禁用状态的视频"
        # 注意：这里没有设置 is_disabled 字段
    }
    
    # 发送POST请求
    response = requests.post(f"{BASE_URL}/", json=video_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        result = response.json()
        pprint(result)
        # 验证默认值是True（禁用状态）
        if response.status_code == 201:
            print(f"验证默认禁用状态: {result.get('is_disabled')}")
            assert result.get('is_disabled') is True, "创建视频时应该默认是禁用状态"
            print("✓ 默认禁用状态验证通过")
    except Exception as e:
        print(f"响应解析失败: {e}")
        print(response.text)
    
    return response.json() if response.status_code == 201 else None


def test_get_videos():
    """测试获取视频列表接口"""
    print_separator("获取视频列表 (GET /)")
    
    # 发送GET请求 - 不带任何过滤条件
    response = requests.get(f"{BASE_URL}/")
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)
    
    # 发送GET请求 - 带分页条件
    print("\n带分页条件的请求:")
    params = {
        "skip": 0,
        "limit": 5
    }
    
    response = requests.get(f"{BASE_URL}/", params=params)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)


def test_get_video_detail():
    """测试获取视频详情接口"""
    print_separator(f"获取视频详情 (GET /{TEST_VIDEO_ID})")
    
    # 发送GET请求
    response = requests.get(f"{BASE_URL}/{TEST_VIDEO_ID}")
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)


def test_update_video():
    """测试更新视频信息接口"""
    print_separator(f"更新视频信息 (PUT /{TEST_VIDEO_ID})")
    
    # 准备测试数据 - 使用正确的字段名称
    update_data = {
        "title": "更新后的视频标题",
        "video_content": "这是更新后的视频描述内容"  # 正确的字段名称
    }
    
    # 发送PUT请求
    response = requests.put(f"{BASE_URL}/{TEST_VIDEO_ID}", json=update_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)


def test_batch_enable_videos():
    """测试批量启用视频接口"""
    print_separator("批量启用视频 (POST /batch/enable)")
    
    # 准备测试数据
    batch_data = {
        "video_ids": [TEST_VIDEO_ID]
    }
    
    # 发送POST请求
    response = requests.post(f"{BASE_URL}/batch/enable", json=batch_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)


def test_batch_disable_videos():
    """测试批量禁用视频接口"""
    print_separator("批量禁用视频 (POST /batch/disable)")
    
    # 准备测试数据
    batch_data = {
        "video_ids": [TEST_VIDEO_ID]
    }
    
    # 发送POST请求
    response = requests.post(f"{BASE_URL}/batch/disable", json=batch_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)


if __name__ == "__main__":
    # 执行所有测试
    print("开始测试视频管理API...")
    
    # 0. 确保测试用户存在，并获取用户主键ID
    # 我们不需要使用global关键字，因为TEST_USER_ID已经是模块级变量
    user_id = ensure_test_user_exists()
    if user_id:
        # 直接修改模块级变量
        TEST_USER_ID = user_id
        print(f"使用测试用户ID: {TEST_USER_ID}")
    else:
        print("警告：未能获取用户ID，测试可能会失败")
    
    # 1. 创建视频
    created_video = test_create_video()
    
    # 1.5. 测试默认禁用状态
    test_create_video_default_disabled()
    
    # 等待一会以确保数据已经写入
    time.sleep(1)
    
    # 2. 获取视频列表
    test_get_videos()
    
    # 3. 获取视频详情
    test_get_video_detail()
    
    # 4. 更新视频信息
    test_update_video()
    
    # 5. 批量禁用视频
    test_batch_disable_videos()
    
    # 6. 批量启用视频
    test_batch_enable_videos()
    
    print("\n所有接口测试完成!") 