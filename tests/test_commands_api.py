#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json
import logging
import sys
from pprint import pprint

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout
)
logger = logging.getLogger("test_commands_api")

# 设置基础URL - 注意检查配置文件中的API前缀和端口设置
BASE_URL = "http://127.0.0.1:8000/api/v1/commands"  # 使用IP地址替代localhost

# 设置测试使用的机器码
TEST_MACHINE_CODE = "MACHINE_CODE_TEST_123"

"""
注意：当前测试运行可能会遇到服务器端报错：
'Provide' object has no attribute 'save_command' 或 'get_command'

这是因为服务器端依赖注入配置有问题。解决方法：

1. 检查app/api/routes/commands.py中的依赖注入配置
2. 确保app/main.py中已正确配置commands路由的容器：
   commands.router.container = container
3. 重启服务器后再尝试运行测试

如果问题仍然存在，可能需要修改app/core/container.py中command_repository的配置
"""

# 打印分隔符的函数
def print_separator(title):
    logger.info("="*50)
    logger.info(f"测试接口: {title}")
    logger.info("="*50)

def test_add_command():
    """测试添加指令接口"""
    print_separator("添加测试指令 (POST /test/add)")
    
    # 准备测试数据 - 登录指令
    request_data = {
        "machine_code": TEST_MACHINE_CODE,
        "command_type": "login",
        "ext_params": {"message": "这是一个测试登录指令"}
    }
    
    logger.info(f"发送请求: {json.dumps(request_data)}")
    
    # 发送POST请求，使用JSON格式而不是query参数
    response = requests.post(f"{BASE_URL}/test/add", json=request_data)
    
    # 打印响应
    logger.info(f"状态码: {response.status_code}")
    try:
        resp_json = response.json()
        logger.info(f"响应: {json.dumps(resp_json, ensure_ascii=False)}")
        return resp_json
    except Exception as e:
        logger.error(f"解析响应失败: {str(e)}")
        logger.info(response.text)
        return None

def test_add_reply_command():
    """测试添加回复指令接口"""
    print_separator("添加回复指令 (POST /test/add)")
    
    # 准备测试数据 - 回复指令
    request_data = {
        "machine_code": TEST_MACHINE_CODE,
        "command_type": "reply",
        "ext_params": {
            "message_id": "12345",
            "content": "这是一个自动回复内容",
            "reply_to": "测试用户"
        }
    }
    
    logger.info(f"发送请求: {json.dumps(request_data)}")
    
    # 发送POST请求，使用JSON格式
    response = requests.post(f"{BASE_URL}/test/add", json=request_data)
    
    # 打印响应
    logger.info(f"状态码: {response.status_code}")
    try:
        resp_json = response.json()
        logger.info(f"响应: {json.dumps(resp_json, ensure_ascii=False)}")
        return resp_json
    except Exception as e:
        logger.error(f"解析响应失败: {str(e)}")
        logger.info(response.text)
        return None

def test_add_crawl_command():
    """测试添加爬评指令接口"""
    print_separator("添加爬评指令 (POST /test/add)")
    
    # 准备测试数据 - 爬评指令
    request_data = {
        "machine_code": TEST_MACHINE_CODE,
        "command_type": "crawl_comment",
        "ext_params": {
            "post_id": "post98765",
            "depth": 2,
            "max_comments": 100
        }
    }
    
    # 发送POST请求，使用JSON格式
    response = requests.post(f"{BASE_URL}/test/add", json=request_data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    try:
        pprint(response.json())
    except Exception:
        print(response.text)

def test_get_command(retry_count=3, retry_delay=1):
    """测试获取指令接口，支持重试逻辑"""
    print_separator("获取指令 (POST /get)")
    
    # 准备测试数据
    request_data = {
        "machine_code": TEST_MACHINE_CODE
    }
    
    logger.info(f"发送请求: {json.dumps(request_data)}")
    
    # 支持重试的逻辑
    for attempt in range(retry_count):
        # 发送POST请求
        response = requests.post(f"{BASE_URL}/get", json=request_data)
        
        # 打印响应
        logger.info(f"尝试 #{attempt+1} - 状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                resp_json = response.json()
                logger.info(f"成功获取命令: {json.dumps(resp_json, ensure_ascii=False)}")
                return resp_json
            except Exception as e:
                logger.error(f"解析响应失败: {str(e)}")
                logger.info(response.text)
                return None
        else:
            try:
                error_info = response.json()
                logger.warning(f"获取失败: {json.dumps(error_info, ensure_ascii=False)}")
            except:
                logger.warning(f"获取失败: {response.text}")
            
            if attempt < retry_count - 1:
                # 如果还有重试次数，等待一段时间后再试
                logger.info(f"将在 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                logger.warning(f"达到最大重试次数 ({retry_count})，放弃获取")
    
    return None

def test_multiple_commands():
    """测试添加多个指令并依次获取"""
    print_separator("添加多个指令并依次获取测试")
    
    # 命令类型列表
    command_types = ["login", "update_qr", "relogin"]
    
    logger.info("1. 添加多个不同类型的指令")
    for i, cmd_type in enumerate(command_types):
        request_data = {
            "machine_code": TEST_MACHINE_CODE,
            "command_type": cmd_type,
            "ext_params": {"index": i, "message": f"测试指令 {i+1}"}
        }
        
        logger.info(f"添加命令 {i+1}/{len(command_types)}: {cmd_type}")
        logger.info(f"请求数据: {json.dumps(request_data)}")
        
        response = requests.post(f"{BASE_URL}/test/add", json=request_data)
        logger.info(f"添加 {cmd_type} 指令状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                resp_json = response.json()
                logger.info(f"响应: {json.dumps(resp_json, ensure_ascii=False)}")
            except:
                logger.info(response.text)
        
        # 短暂延迟，确保命令按顺序处理
        time.sleep(0.5)
    
    logger.info("\n2. 依次获取这些指令")
    for i in range(len(command_types)):
        logger.info(f"获取第 {i+1} 个指令:")
        
        # 等待命令可能被处理的时间
        time.sleep(1)
        
        command = test_get_command()
        
        if command:
            logger.info(f"成功获取指令 #{i+1}: {json.dumps(command, ensure_ascii=False)}")
        else:
            logger.warning(f"无法获取指令 #{i+1}")

def main():
    """主测试函数"""
    try:
        # 开始测试
        logger.info("开始测试指令API...")
        
        # 1. 添加登录指令
        test_add_command()
        
        # 等待一会以确保数据已经写入
        time.sleep(2)
        
        # 2. 获取指令
        command = test_get_command()
        
        # 3. 添加回复指令
        test_add_reply_command()
        
        # 4.
        time.sleep(2)
        test_get_command()
        
        # 5. 添加爬评指令
        test_add_crawl_command()
        
        # 6.
        time.sleep(2)
        test_get_command()
        
        # 7. 多指令测试
        test_multiple_commands()
        
        logger.info("\n所有指令接口测试完成!")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main() 