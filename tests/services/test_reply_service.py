import os
import sys
import unittest
from unittest.mock import MagicMock, call, ANY

# 添加项目根目录到 Python 路径
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from app.models.comment_record import CommentRecord
from app.services.reply_service import ReplyService
from app.services.coze_service import CozeService


class TestReplyService(unittest.TestCase):
    """测试 ReplyService 类"""

    def setUp(self):
        """测试前的准备工作"""
        # 创建模拟对象
        self.mock_db_session = MagicMock()
        self.mock_coze_service = MagicMock(spec=CozeService)
        
        # 创建 ReplyService 实例
        self.reply_service = ReplyService(
            db_session=self.mock_db_session,
            coze_service=self.mock_coze_service,
            bot_id="test_bot_id",
            user_id="test_user_id"
        )

    def test_get_comment_context_main_comment(self):
        """测试获取主评论上下文"""
        # 创建一个主评论（root_id=0）
        main_comment = MagicMock(spec=CommentRecord)
        main_comment.id = 1
        main_comment.source_id = 101
        main_comment.root_id = 0
        
        # 测试 get_comment_context 方法
        context = self.reply_service.get_comment_context(main_comment)
        
        # 验证结果
        self.assertEqual(len(context), 1)
        self.assertEqual(context[0][0], main_comment)
        self.assertEqual(context[0][1], 0)  # 深度为0
        
        # 验证数据库查询没有被调用（因为是主评论，直接返回）
        self.mock_db_session.query.assert_not_called()
        
    def test_get_comment_context_reply_comment(self):
        """测试获取楼中楼评论上下文"""
        # 创建一个楼中楼评论（root_id不为0）
        reply_comment = MagicMock(spec=CommentRecord)
        reply_comment.id = 2
        reply_comment.source_id = 202
        reply_comment.root_id = 101
        
        # 创建主评论
        main_comment = MagicMock(spec=CommentRecord)
        main_comment.id = 1
        main_comment.source_id = 101
        main_comment.root_id = 0
        
        # 创建另一个楼中楼评论
        other_reply = MagicMock(spec=CommentRecord)
        other_reply.id = 3
        other_reply.source_id = 201
        other_reply.root_id = 101
        
        # 设置模拟查询结果
        query_mock = self.mock_db_session.query.return_value.filter.return_value
        query_mock.order_by.return_value.all.return_value = [
            main_comment,  # 主评论
            other_reply,   # 较早的楼中楼评论
            reply_comment  # 当前评论
        ]
        
        # 测试 get_comment_context 方法
        context = self.reply_service.get_comment_context(reply_comment)
        
        # 验证结果
        self.assertEqual(len(context), 3)
        
        # 验证主评论在结果中且深度为0
        self.assertEqual(context[0][0], main_comment)
        self.assertEqual(context[0][1], 0)
        
        # 验证较早的楼中楼评论在结果中且深度为1
        self.assertEqual(context[1][0], other_reply)
        self.assertEqual(context[1][1], 1)
        
        # 验证当前评论也在结果中且深度为1
        self.assertEqual(context[2][0], reply_comment)
        self.assertEqual(context[2][1], 1)
        
        # 验证数据库查询调用
        self.mock_db_session.query.assert_called_once_with(CommentRecord)
        
        # 验证过滤条件
        filter_call = self.mock_db_session.query.return_value.filter.call_args[0][0]
        self.assertIsNotNone(filter_call)
        
        # 验证排序条件
        self.mock_db_session.query.return_value.filter.return_value.order_by.assert_called_once()
        
    def test_get_comment_context_db_query_validation(self):
        """测试获取评论上下文的数据库查询验证"""
        # 创建一个楼中楼评论（root_id不为0）
        reply_comment = MagicMock(spec=CommentRecord)
        reply_comment.id = 2
        reply_comment.source_id = 202
        reply_comment.root_id = 101
        
        # 设置模拟查询和结果
        filter_mock = MagicMock()
        self.mock_db_session.query.return_value.filter = filter_mock
        order_by_mock = MagicMock()
        filter_mock.return_value.order_by = order_by_mock
        all_mock = MagicMock(return_value=[])
        order_by_mock.return_value.all = all_mock
        
        # 调用方法
        self.reply_service.get_comment_context(reply_comment)
        
        # 验证查询调用
        self.mock_db_session.query.assert_called_once_with(CommentRecord)
        filter_mock.assert_called_once()
        # 使用 ANY 代替具体的参数，避免断言失败
        order_by_mock.assert_called_once_with(ANY)
        all_mock.assert_called_once()
        
    def test_get_comment_context_processed_ids(self):
        """测试已处理ID集合功能"""
        # 创建一个楼中楼评论
        reply_comment = MagicMock(spec=CommentRecord)
        reply_comment.id = 2
        reply_comment.source_id = 202
        reply_comment.root_id = 101
        
        # 创建主评论
        main_comment = MagicMock(spec=CommentRecord)
        main_comment.id = 1
        main_comment.source_id = 101
        main_comment.root_id = 0
        
        # 创建两个相同source_id的楼中楼评论（测试去重功能）
        duplicate_reply1 = MagicMock(spec=CommentRecord)
        duplicate_reply1.id = 3
        duplicate_reply1.source_id = 201
        duplicate_reply1.root_id = 101
        
        duplicate_reply2 = MagicMock(spec=CommentRecord)
        duplicate_reply2.id = 4
        duplicate_reply2.source_id = 201  # 相同的source_id
        duplicate_reply2.root_id = 101
        
        # 设置模拟查询结果
        query_mock = self.mock_db_session.query.return_value.filter.return_value
        query_mock.order_by.return_value.all.return_value = [
            main_comment,    # 主评论
            duplicate_reply1,  # 第一个重复source_id的评论
            duplicate_reply2,  # 第二个重复source_id的评论
            reply_comment    # 当前评论
        ]
        
        # 测试方法
        context = self.reply_service.get_comment_context(reply_comment)
        
        # 验证结果 - 应该只有3个评论（第二个重复source_id的应该被过滤）
        self.assertEqual(len(context), 3)
        
        # 验证返回的评论ID（确保没有重复的source_id）
        source_ids = [comment[0].source_id for comment in context]
        self.assertEqual(len(source_ids), len(set(source_ids)))

    def test_get_comment_context_exception(self):
        """测试获取评论上下文时发生异常的情况"""
        # 创建一个评论
        comment = MagicMock(spec=CommentRecord)
        comment.id = 1
        comment.source_id = 101
        comment.root_id = 101  # 设置为楼中楼评论

        # 模拟数据库查询抛出异常
        self.mock_db_session.query.side_effect = Exception("模拟数据库异常")

        # 测试 get_comment_context 方法
        context = self.reply_service.get_comment_context(comment)

        # 验证结果：应该返回当前评论作为上下文，深度为0
        self.assertEqual(len(context), 1)
        self.assertEqual(context[0][0], comment)
        self.assertEqual(context[0][1], 0)


if __name__ == "__main__":
    unittest.main() 