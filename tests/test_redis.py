#!/usr/bin/env python3
"""
Redis连接和操作测试脚本
"""
import os
import redis
import logging
from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("redis_test")

def test_redis_connection(host="mzg-redis"):
    """测试Redis连接"""
    logger.info("开始测试Redis连接...")
    
    # 从环境变量获取Redis配置，如果不存在则使用settings中的默认值
    redis_host = host
    redis_port = int(os.getenv("REDIS_PORT", settings.REDIS_PORT))
    redis_db = int(os.getenv("REDIS_DB", settings.REDIS_DB))
    redis_password = os.getenv("REDIS_PASSWORD", settings.REDIS_PASSWORD)
    redis_prefix = os.getenv("REDIS_PREFIX", settings.REDIS_PREFIX)
    
    logger.info(f"使用以下配置连接Redis: {redis_host}:{redis_port}, DB={redis_db}")
    
    try:
        # 创建Redis客户端
        r = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            password=redis_password,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5
        )
        
        # 测试连接
        ping_result = r.ping()
        logger.info(f"Redis连接测试结果: {ping_result}")
        
        # 测试基本操作
        test_key = f"{redis_prefix}test:key"
        test_value = "测试值"
        
        # 设置值
        r.set(test_key, test_value)
        logger.info(f"设置测试键值: {test_key} = {test_value}")
        
        # 获取值
        retrieved_value = r.get(test_key)
        logger.info(f"获取测试键值: {test_key} = {retrieved_value}")
        assert retrieved_value == test_value, f"值不匹配: 期望 {test_value}, 实际 {retrieved_value}"
        
        # 测试列表操作
        test_list_key = f"{redis_prefix}test:list"
        r.delete(test_list_key)  # 确保列表为空
        
        # 添加元素到列表
        r.rpush(test_list_key, "项目1", "项目2", "项目3")
        logger.info(f"向列表 {test_list_key} 添加了3个元素")
        
        # 获取列表长度
        list_len = r.llen(test_list_key)
        logger.info(f"列表 {test_list_key} 长度: {list_len}")
        assert list_len == 3, f"列表长度不正确: 期望 3, 实际 {list_len}"
        
        # 获取列表元素
        list_items = [r.lindex(test_list_key, i) for i in range(list_len)]
        logger.info(f"列表内容: {list_items}")
        
        # 弹出列表元素
        popped_item = r.lpop(test_list_key)
        logger.info(f"从列表弹出元素: {popped_item}")
        assert popped_item == "项目1", f"弹出元素不正确: 期望 '项目1', 实际 {popped_item}"
        
        # 清理测试数据
        r.delete(test_key)
        r.delete(test_list_key)
        logger.info("已清理测试数据")
        
        logger.info("Redis操作测试成功!")
        return True
    except Exception as e:
        logger.error(f"Redis测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 先尝试使用Docker容器名
    if not test_redis_connection("mzg-redis"):
        # 如果失败，尝试使用localhost/127.0.0.1
        logger.info("尝试使用localhost连接...")
        test_redis_connection("localhost") 