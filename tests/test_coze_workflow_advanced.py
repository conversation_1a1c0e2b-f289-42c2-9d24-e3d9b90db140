#!/usr/bin/env python
"""
测试Coze工作流接口的高级脚本
支持通过命令行参数指定测试参数
"""

import requests
import logging
import json
import os
import argparse
import sys


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_coze")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="测试Coze工作流接口")
    
    parser.add_argument(
        "--token", 
        type=str, 
        default=os.getenv(
            "COZE_API_TOKEN", 
            "pat_4aAwaGOJMK9z2qquHd1T6ZonzzJZ751HjuilGtvIVTRAIepEWval5U6oX9SwF6dE"
        ),
        help="Coze API Token"
    )
    
    parser.add_argument(
        "--workflow-id", 
        type=str, 
        default="7469697317944967195",
        help="工作流ID"
    )
    
    parser.add_argument(
        "--title", 
        type=str, 
        default="测试视频标题",
        help="视频标题"
    )
    
    parser.add_argument(
        "--content", 
        type=str, 
        default="这是一段测试视频内容描述，用于测试工作流接口。",
        help="视频内容"
    )
    
    parser.add_argument(
        "--product", 
        type=str, 
        default="这里是产品推荐信息。",
        help="产品信息"
    )
    
    parser.add_argument(
        "--comment", 
        type=str, 
        default="这是一条测试评论。",
        help="评论内容"
    )
    
    parser.add_argument(
        "--summary", 
        type=str, 
        default="这是视频内容摘要。",
        help="视频摘要"
    )
    
    return parser.parse_args()


def test_coze_workflow(args):
    """测试Coze工作流接口
    
    Args:
        args: 命令行参数
    """
    base_url = "https://api.coze.cn/v1"
    url = f"{base_url}/workflow/run"
    
    headers = {
        "Authorization": f"Bearer {args.token}",
        "Content-Type": "application/json",
    }
    
    # 测试数据
    test_data = {
        "parameters": {
            "shipinbiaoti": args.title,
            "zhengwen": args.content,
            "tuijian": args.product,
            "pinglun": args.comment,
            "zongjie": args.summary,
        },
        "workflow_id": args.workflow_id,
    }
    
    logger.info(f"调用Coze工作流: workflow_id={args.workflow_id}")
    logger.info(f"使用Token: {args.token[:10]}...{args.token[-5:]}")
    
    try:
        response = requests.post(url, headers=headers, json=test_data)
        result = response.json()
        
        # 打印响应状态码和内容
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get("code") == 0:
            logger.info("工作流调用成功!")
            # 解析返回的JSON字符串
            try:
                data = eval(result["data"])
                # 优先使用output2，如果为空则使用output
                output = data.get("optput2") or data.get("output") or data.get("output1")
                logger.info(f"工作流执行结果: {output}")
            except Exception as e:
                logger.error(f"解析结果失败: {str(e)}")
        else:
            logger.error(f"调用工作流失败: {result}")
    
    except Exception as e:
        logger.error(f"请求异常: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    args = parse_args()
    sys.exit(test_coze_workflow(args)) 