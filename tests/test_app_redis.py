#!/usr/bin/env python3
"""
测试应用中的Redis连接和操作
"""
import logging
from app.core.container import Container
from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("app_redis_test")


def test_container_redis():
    """测试容器中的Redis连接和操作"""
    logger.info("初始化容器...")
    
    # 创建容器实例
    container = Container()
    
    # 获取Redis实例
    redis_client = container.redis_client()
    
    # 判断是否为模拟客户端
    is_mock = hasattr(redis_client, 'data') and hasattr(redis_client, 'lists')
    logger.info(f"当前Redis客户端是否为模拟客户端: {is_mock}")
    
    if is_mock:
        logger.warning("当前使用的是模拟Redis客户端，请确保设置正确的Redis连接参数")
    else:
        logger.info("当前使用的是真实Redis客户端，将进行操作测试")
    
    # 测试基本操作
    redis_prefix = settings.REDIS_PREFIX
    test_key = f"{redis_prefix}test:container:key"
    test_value = "容器测试值"
    
    try:
        # 测试连接
        ping_result = redis_client.ping()
        logger.info(f"Redis连接测试结果: {ping_result}")
        
        # 设置值
        redis_client.set(test_key, test_value)
        logger.info(f"设置测试键值: {test_key} = {test_value}")
        
        # 获取值
        retrieved_value = redis_client.get(test_key)
        logger.info(f"获取测试键值: {test_key} = {retrieved_value}")
        assert retrieved_value == test_value, f"值不匹配: 期望 {test_value}, 实际 {retrieved_value}"
        
        # 测试列表操作
        test_list_key = f"{redis_prefix}test:container:list"
        redis_client.delete(test_list_key)  # 确保列表为空
        
        # 添加元素到列表
        redis_client.rpush(test_list_key, "容器项目1", "容器项目2", "容器项目3")
        logger.info(f"向列表 {test_list_key} 添加了3个元素")
        
        # 获取列表长度
        list_len = redis_client.llen(test_list_key)
        logger.info(f"列表 {test_list_key} 长度: {list_len}")
        assert list_len == 3, f"列表长度不正确: 期望 3, 实际 {list_len}"
        
        # 获取列表元素
        list_items = [redis_client.lindex(test_list_key, i) for i in range(list_len)]
        logger.info(f"列表内容: {list_items}")
        
        # 弹出列表元素
        popped_item = redis_client.lpop(test_list_key)
        logger.info(f"从列表弹出元素: {popped_item}")
        assert popped_item == "容器项目1", f"弹出元素不正确: 期望 '容器项目1', 实际 {popped_item}"
        
        # 清理测试数据
        redis_client.delete(test_key)
        redis_client.delete(test_list_key)
        logger.info("已清理测试数据")
        
        logger.info("Redis操作测试成功!")
        return True
    except Exception as e:
        logger.error(f"Redis测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    test_container_redis() 