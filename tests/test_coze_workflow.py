#!/usr/bin/env python
"""
测试Coze工作流接口
"""

import requests
import logging
import json
import os


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_coze")


# 从环境变量获取API token，如果不存在，则使用默认值
COZE_API_TOKEN = os.getenv(
    "COZE_API_TOKEN", 
    "pat_4aAwaGOJMK9z2qquHd1T6ZonzzJZ751HjuilGtvIVTRAIepEWval5U6oX9SwF6dE"
)


def test_coze_workflow():
    """测试Coze工作流接口"""
    base_url = "https://api.coze.cn/v1"
    url = f"{base_url}/workflow/run"
    
    headers = {
        "Authorization": f"Bearer {COZE_API_TOKEN}",
        "Content-Type": "application/json",
    }
    
    # 测试数据
    test_data = {
        "parameters": {
            "shipinbiaoti": "测试视频标题",
            "zhengwen": "这是一段测试视频内容描述，用于测试工作流接口。",
            "tuijian": "这里是产品推荐信息。",
            "pinglun": "这是一条测试评论。",
            "zongjie": "这是视频内容摘要。",
        },
        "workflow_id": "7469697317944967195",
    }
    
    logger.info("调用Coze工作流: workflow_id=7469697317944967195")
    
    try:
        response = requests.post(url, headers=headers, json=test_data)
        result = response.json()
        
        # 打印响应状态码和内容
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get("code") == 0:
            logger.info("工作流调用成功!")
            # 解析返回的JSON字符串
            try:
                data = eval(result["data"])
                # 优先使用output2，如果为空则使用output
                output = data.get("optput2") or data.get("output")
                logger.info(f"工作流执行结果: {output}")
            except Exception as e:
                logger.error(f"解析结果失败: {str(e)}")
        else:
            logger.error(f"调用工作流失败: {result}")
    
    except Exception as e:
        logger.error(f"请求异常: {str(e)}")


if __name__ == "__main__":
    test_coze_workflow() 