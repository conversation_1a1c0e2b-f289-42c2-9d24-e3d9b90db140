"""
视频API测试用例
"""
import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.models.video import VideoDetail
from app.core.config import settings

API_PREFIX = f"{settings.API_V1_STR}/videos"
client = TestClient(app)


@pytest.fixture
def test_video(db_session):
    """创建测试视频"""
    video = VideoDetail(
        video_id="**********9",
        title="测试视频",
        video_content="测试视频内容",
        video_url="https://example.com/test_video.mp4",
        video_script="测试视频文案",
        video_summary="测试视频总结",
        is_disabled=False,
        user_id=1
    )
    db_session.add(video)
    db_session.commit()
    db_session.refresh(video)
    yield video
    db_session.delete(video)
    db_session.commit()


def test_list_videos(client):
    """测试获取视频列表API"""
    response = client.get(f"{API_PREFIX}/")
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "total" in data


def test_list_videos_with_pagination(client, test_video):
    """测试视频列表分页功能"""
    # 默认分页
    response = client.get(f"{API_PREFIX}/")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    
    # 自定义分页
    response = client.get(f"{API_PREFIX}/?skip=0&limit=5")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["items"]) == 1
    
    # 超出范围的分页
    response = client.get(f"{API_PREFIX}/?skip=10&limit=5")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["items"]) == 0


def test_create_video(client, db_session):
    """测试创建视频API"""
    video_data = {
        "video_id": "BV987654321",
        "title": "新视频标题",
        "video_content": "新视频内容",
        "video_url": "https://example.com/new_video.mp4",
        "video_script": "新视频文案",
        "video_summary": "新视频总结",
        "reply_setting": "回复设定",
        "comment_control_plan": "控评方案",
        "promoted_products": [
            {"name": "产品1", "reason": "推荐理由1"},
            {"name": "产品2", "reason": "推荐理由2"}
        ],
        "common_questions": "常见问题与解答",
        "is_disabled": True,
        "user_id": 1
    }
    
    response = client.post(f"{API_PREFIX}/", json=video_data)
    assert response.status_code == 201
    data = response.json()
    assert data["video_id"] == video_data["video_id"]
    assert data["title"] == video_data["title"]
    assert data["video_content"] == video_data["video_content"]
    assert data["video_url"] == video_data["video_url"]
    assert data["video_script"] == video_data["video_script"]
    assert data["video_summary"] == video_data["video_summary"]
    assert data["reply_setting"] == video_data["reply_setting"]
    assert data["comment_control_plan"] == video_data["comment_control_plan"]
    assert data["promoted_products"] == video_data["promoted_products"]
    assert data["common_questions"] == video_data["common_questions"]
    assert data["is_disabled"] == video_data["is_disabled"]
    
    # 测试创建重复ID的视频
    response = client.post(f"{API_PREFIX}/", json=video_data)
    assert response.status_code == 409
    assert "已存在" in response.json()["detail"]
    
    # 清理测试数据
    db_video = db_session.query(VideoDetail).filter(
        VideoDetail.video_id == video_data["video_id"]
    ).first()
    if db_video:
        db_session.delete(db_video)
        db_session.commit()


def test_create_video_default_disabled(client, db_session):
    """测试创建视频时默认是禁用状态"""
    video_data = {
        "video_id": "BV111111111",
        "title": "默认禁用测试视频",
        "video_url": "https://example.com/default_disabled_video.mp4",
        "user_id": 1
        # 注意：这里没有设置 is_disabled 字段
    }
    
    response = client.post(f"{API_PREFIX}/", json=video_data)
    assert response.status_code == 201
    data = response.json()
    assert data["video_id"] == video_data["video_id"]
    assert data["title"] == video_data["title"]
    # 验证默认值是True（禁用状态）
    assert data["is_disabled"] is True
    
    # 清理测试数据
    db_video = db_session.query(VideoDetail).filter(
        VideoDetail.video_id == video_data["video_id"]
    ).first()
    if db_video:
        db_session.delete(db_video)
        db_session.commit()


def test_get_videos(client, test_video):
    """测试获取视频列表API"""
    response = client.get(f"{API_PREFIX}/")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] >= 1
    assert any(
        item["video_id"] == test_video.video_id for item in data["items"]
    )


def test_get_video(client, test_video):
    """测试获取单个视频API"""
    response = client.get(f"{API_PREFIX}/{test_video.video_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["video_id"] == test_video.video_id
    assert data["title"] == test_video.title
    assert data["video_content"] == test_video.video_content
    assert data["is_disabled"] == test_video.is_disabled
    
    # 测试获取不存在的视频
    response = client.get(f"{API_PREFIX}/BV99999999")
    assert response.status_code == 404
    assert "未找到视频" in response.json()["detail"]


def test_update_video(client, test_video):
    """测试更新视频API"""
    update_data = {
        "title": "更新后的视频标题",
        "video_content": "更新后的视频内容",
        "video_script": "更新后的视频文案",
        "video_summary": "更新后的视频总结",
        "reply_setting": "更新后的回复设定",
        "comment_control_plan": "更新后的控评方案",
        "promoted_products": [
            {"name": "更新后的产品1", "reason": "更新后的推荐理由1"}
        ],
        "common_questions": "更新后的常见问题",
        "is_disabled": True
    }
    
    response = client.put(
        f"{API_PREFIX}/{test_video.video_id}", 
        json=update_data
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == update_data["title"]
    assert data["video_content"] == update_data["video_content"]
    assert data["video_script"] == update_data["video_script"]
    assert data["video_summary"] == update_data["video_summary"]
    assert data["reply_setting"] == update_data["reply_setting"]
    assert data["comment_control_plan"] == (
        update_data["comment_control_plan"]
    )
    assert data["promoted_products"] == update_data["promoted_products"]
    assert data["common_questions"] == update_data["common_questions"]
    assert data["is_disabled"] == update_data["is_disabled"]
    # 未更新的字段应保持不变
    assert data["video_id"] == test_video.video_id
    assert data["video_url"] == test_video.video_url
    
    # 测试更新不存在的视频
    response = client.put(
        f"{API_PREFIX}/BV99999999", 
        json=update_data
    )
    assert response.status_code == 404
    assert "未找到视频" in response.json()["detail"]


def test_update_video_partial(client, test_video):
    """测试部分更新视频API"""
    # 只更新标题
    update_data = {"title": "只更新标题"}
    response = client.put(
        f"{API_PREFIX}/{test_video.video_id}", 
        json=update_data
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == update_data["title"]
    assert data["video_content"] == test_video.video_content
    
    # 只更新视频内容
    update_data = {"video_content": "只更新视频内容"}
    response = client.put(
        f"{API_PREFIX}/{test_video.video_id}", 
        json=update_data
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "只更新标题"  # 保持之前更新的值
    assert data["video_content"] == update_data["video_content"]
    
    # 只更新禁用状态
    update_data = {"is_disabled": True}
    response = client.put(
        f"{API_PREFIX}/{test_video.video_id}",
        json=update_data
    )
    assert response.status_code == 200
    data = response.json()
    assert data["is_disabled"] == update_data["is_disabled"]
    
    # 只更新新增的字段
    update_data = {"video_script": "只更新视频文案"}
    response = client.put(
        f"{API_PREFIX}/{test_video.video_id}",
        json=update_data
    )
    assert response.status_code == 200
    data = response.json()
    assert data["video_script"] == update_data["video_script"]


def test_error_handling(client):
    """测试错误处理"""
    # 测试无效的JSON数据
    response = client.post(
        f"{API_PREFIX}/", 
        data="invalid json data"
    )
    assert response.status_code == 422
    
    # 测试缺少必填字段
    incomplete_data = {
        "video_id": "**********"
        # 缺少必填的title和video_url字段
    }
    response = client.post(f"{API_PREFIX}/", json=incomplete_data)
    assert response.status_code == 422
