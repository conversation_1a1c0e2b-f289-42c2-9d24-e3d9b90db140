"""
Coze服务的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock

from app.services.coze_service import CozeService


class TestCozeService:
    """Coze服务测试类"""

    @pytest.fixture
    def coze_service(self):
        """创建CozeService实例"""
        return CozeService(api_token="fake_token")

    @patch("app.services.coze_service.requests.post")
    def test_chat_with_workflow_success(self, mock_post, coze_service):
        """测试workflow调用成功的情况"""
        # 模拟get_video_details的返回
        mock_video_details = {
            "title": "测试视频标题",
            "video_content": "测试视频内容",
            "product_info": "测试产品信息",
            "summarize": "测试总结信息"
        }
        
        # 模拟API响应
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "code": 0,
            "data": "{'output': '测试输出结果', 'optput2': ''}"
        }
        mock_post.return_value = mock_response
        
        # 模拟get_video_details方法
        with patch.object(
            coze_service, 
            "get_video_details", 
            return_value=mock_video_details
        ):
            test_message = "评论内容：这是一条测试评论   其他内容"
            result = coze_service.chat_with_workflow(
                user_id="test_user",
                bot_id="test_bot",
                message=test_message,
                video_url="https://www.bilibili.com/video/BV12345678"
            )
        
        # 验证结果
        assert result == "测试输出结果"
        
        # 验证API调用
        mock_post.assert_called_once()
        # 验证请求URL
        assert mock_post.call_args[0][0] == "https://api.coze.cn/v1/workflow/run"
        # 验证请求数据
        call_kwargs = mock_post.call_args[1]
        assert "json" in call_kwargs
        payload = call_kwargs["json"]
        assert payload["workflow_id"] == "7469697317944967195"
        assert payload["parameters"]["pinglun"] == "这是一条测试评论"
        assert payload["parameters"]["shipinbiaoti"] == "测试视频标题"
        
    @patch("app.services.coze_service.requests.post")
    def test_chat_with_workflow_prefer_output2(self, mock_post, coze_service):
        """测试优先使用output2的情况"""
        # 模拟get_video_details的返回
        mock_video_details = {
            "title": "测试视频标题",
            "video_content": "测试视频内容",
            "product_info": "测试产品信息",
            "summarize": "测试总结信息"
        }
        
        # 模拟API响应，这次包含output2
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "code": 0,
            "data": "{'output': '测试输出结果', 'optput2': '优先输出2'}"
        }
        mock_post.return_value = mock_response
        
        # 模拟get_video_details方法
        with patch.object(
            coze_service, 
            "get_video_details", 
            return_value=mock_video_details
        ):
            test_message = "评论内容：这是一条测试评论   其他内容"
            result = coze_service.chat_with_workflow(
                user_id="test_user",
                bot_id="test_bot",
                message=test_message,
                video_url="https://www.bilibili.com/video/BV12345678"
            )
        
        # 验证结果是否使用了output2
        assert result == "优先输出2"

    @patch("app.services.coze_service.requests.post")
    def test_chat_with_workflow_no_video_details(self, mock_post, coze_service):
        """测试没有视频详情的情况"""
        # 模拟get_video_details返回None
        with patch.object(coze_service, "get_video_details", return_value=None):
            result = coze_service.chat_with_workflow(
                user_id="test_user",
                bot_id="test_bot",
                message="测试消息",
                video_url="https://www.bilibili.com/video/invalid_id"
            )
        
        # 验证结果
        assert result is None
        # 验证没有发起API请求
        mock_post.assert_not_called()

    @patch("app.services.coze_service.requests.post")
    def test_chat_with_workflow_api_error(self, mock_post, coze_service):
        """测试API返回错误的情况"""
        # 模拟get_video_details的返回
        mock_video_details = {
            "title": "测试视频标题",
            "video_content": "测试视频内容",
            "product_info": "测试产品信息",
            "summarize": "测试总结信息"
        }
        
        # 模拟API错误响应
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "code": 1001,
            "message": "API错误"
        }
        mock_post.return_value = mock_response
        
        # 模拟get_video_details方法
        with patch.object(
            coze_service, 
            "get_video_details", 
            return_value=mock_video_details
        ):
            test_message = "评论内容：这是一条测试评论   其他内容"
            result = coze_service.chat_with_workflow(
                user_id="test_user",
                bot_id="test_bot",
                message=test_message,
                video_url="https://www.bilibili.com/video/BV12345678"
            )
        
        # 验证结果
        assert result is None

    @patch("app.services.coze_service.requests.post")
    def test_chat_with_workflow_with_original_and_context(self, mock_post, coze_service):
        """测试使用原评论和上下文评论参数的情况"""
        # 模拟get_video_details的返回
        mock_video_details = {
            "title": "测试视频标题",
            "video_script": "测试视频脚本",
            "video_summary": "测试视频摘要",
            "category": "测试分类",
            "reply_setting": "测试设置",
            "promoted_products": "测试产品信息"
        }
        
        # 模拟API响应
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "code": 0,
            "data": "{'output': '测试输出结果', 'optput1': '优先输出1'}"
        }
        mock_post.return_value = mock_response
        
        # 模拟get_video_details方法
        with patch.object(
            coze_service, 
            "get_video_details", 
            return_value=mock_video_details
        ):
            original_comment = "这是原始评论"
            context_comments = "用户A: 这是上下文评论1\n用户B: 这是上下文评论2"
            
            result = coze_service.chat_with_workflow(
                user_id="test_user",
                bot_id="test_bot",
                original_comment=original_comment,
                context_comments=context_comments,
                video_url="https://www.bilibili.com/video/BV12345678"
            )
        
        # 验证结果
        assert result == "优先输出1"
        
        # 验证API调用
        mock_post.assert_called_once()
        # 验证请求数据
        call_kwargs = mock_post.call_args[1]
        assert "json" in call_kwargs
        payload = call_kwargs["json"]
        assert payload["parameters"]["pinglun"] == original_comment
        assert payload["parameters"]["shangxiawen"] == context_comments 