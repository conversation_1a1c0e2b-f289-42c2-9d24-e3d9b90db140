#!/usr/bin/env python3
"""
测试命令仓储模块与Redis的连接
"""
import logging
from app.core.container import Container
from app.schemas.command import CommandType
from app.repositories.command_repository import CommandRepository

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("command_repo_test")


def test_command_repository():
    """测试命令仓储模块与Redis的连接和操作"""
    logger.info("初始化容器...")
    
    # 创建容器实例
    container = Container()
    
    # 获取Redis客户端
    redis_client = container.redis_client()
    
    # 判断是否为模拟客户端
    is_mock = hasattr(redis_client, 'data') and hasattr(redis_client, 'lists')
    logger.info(f"当前Redis客户端是否为模拟客户端: {is_mock}")
    
    # 创建命令仓储
    redis_prefix = container.config.REDIS_PREFIX()
    command_repo = CommandRepository(redis_client, redis_prefix)
    
    # 测试机器码
    test_machine_code = "TEST_MACHINE_123"
    
    try:
        # 先查看可用的指令类型
        logger.info(f"可用的指令类型: {[cmd.value for cmd in CommandType]}")
        
        # 测试保存命令
        ext_params = {"param1": "value1", "param2": "value2"}
        save_result = command_repo.save_command(
            test_machine_code, 
            CommandType.UPDATE_QR,  # 使用有效的命令类型
            ext_params
        )
        logger.info(f"保存命令结果: {save_result}")
        
        # 测试获取命令
        command = command_repo.get_command(test_machine_code)
        logger.info(f"获取命令结果: {command}")
        
        if command:
            logger.info(f"命令类型: {command.command_type}")
            logger.info(f"扩展参数: {command.ext_params}")
            
            # 验证命令是否正确
            assert command.machine_code == test_machine_code, "机器码不匹配"
            assert command.command_type == CommandType.UPDATE_QR, "命令类型不匹配"
            assert command.ext_params.get("param1") == "value1", "扩展参数不匹配"
            
            logger.info("命令仓储测试成功!")
            return True
        else:
            logger.error("获取命令失败，返回为空")
            return False
    except Exception as e:
        logger.error(f"命令仓储测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    test_command_repository() 