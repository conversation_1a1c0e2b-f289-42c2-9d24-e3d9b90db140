#!/usr/bin/env python
"""
批量测试Coze工作流接口
支持从配置文件加载多个测试场景
"""

import requests
import logging
import json
import os
import argparse
import sys
import time
from typing import List, Dict, Any


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_coze")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="批量测试Coze工作流接口")
    
    parser.add_argument(
        "--token", 
        type=str, 
        default=os.getenv(
            "COZE_API_TOKEN", 
            "pat_4aAwaGOJMK9z2qquHd1T6ZonzzJZ751HjuilGtvIVTRAIepEWval5U6oX9SwF6dE"
        ),
        help="Coze API Token"
    )
    
    parser.add_argument(
        "--workflow-id", 
        type=str, 
        default="7469697317944967195",
        help="工作流ID"
    )
    
    parser.add_argument(
        "--config", 
        type=str, 
        default="test_cases.json",
        help="包含测试场景的配置文件路径"
    )
    
    parser.add_argument(
        "--create-sample", 
        action="store_true",
        help="创建示例配置文件"
    )
    
    return parser.parse_args()


def create_sample_config(filename: str) -> None:
    """创建示例配置文件
    
    Args:
        filename: 配置文件名
    """
    sample_data = {
        "test_cases": [
            {
                "name": "测试场景1",
                "parameters": {
                    "title": "测试视频标题1",
                    "content": "这是测试场景1的视频内容描述",
                    "product": "测试场景1的产品信息",
                    "comment": "测试场景1的评论",
                    "summary": "测试场景1的摘要"
                }
            },
            {
                "name": "测试场景2",
                "parameters": {
                    "title": "测试视频标题2",
                    "content": "这是测试场景2的视频内容描述，更长一些",
                    "product": "测试场景2的产品信息，包含更多产品细节",
                    "comment": "测试场景2的评论，更具体一些",
                    "summary": "测试场景2的摘要，简明扼要"
                }
            }
        ]
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=4)
    
    logger.info(f"已创建示例配置文件: {filename}")


def load_test_cases(filename: str) -> List[Dict[str, Any]]:
    """加载测试场景
    
    Args:
        filename: 配置文件名
        
    Returns:
        List[Dict[str, Any]]: 测试场景列表
    """
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return data.get("test_cases", [])
    except FileNotFoundError:
        logger.error(f"配置文件不存在: {filename}")
        return []
    except json.JSONDecodeError:
        logger.error(f"配置文件格式错误: {filename}")
        return []


def test_workflow_case(
    token: str, workflow_id: str, case: Dict[str, Any]
) -> bool:
    """测试单个工作流场景
    
    Args:
        token: API Token
        workflow_id: 工作流ID
        case: 测试场景
        
    Returns:
        bool: 测试是否成功
    """
    params = case["parameters"]
    name = case["name"]
    
    base_url = "https://api.coze.cn/v1"
    url = f"{base_url}/workflow/run"
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
    }
    
    # 测试数据
    test_data = {
        "parameters": {
            "shipinbiaoti": params.get("title", ""),
            "zhengwen": params.get("content", ""),
            "tuijian": params.get("product", ""),
            "pinglun": params.get("comment", ""),
            "zongjie": params.get("summary", ""),
        },
        "workflow_id": workflow_id,
    }
    
    logger.info(f"测试场景「{name}」: workflow_id={workflow_id}")
    
    try:
        response = requests.post(url, headers=headers, json=test_data)
        result = response.json()
        
        # 打印响应状态码和内容
        logger.info(f"响应状态码: {response.status_code}")
        
        if result.get("code") == 0:
            logger.info(f"场景「{name}」工作流调用成功!")
            # 解析返回的JSON字符串
            try:
                data = eval(result["data"])
                # 优先使用output2，如果为空则使用output
                output = (
                    data.get("optput2") or data.get("output") 
                    or data.get("output1")
                )
                logger.info(f"工作流执行结果: {output}")
                return True
            except Exception as e:
                logger.error(f"解析结果失败: {str(e)}")
        else:
            logger.error(f"场景「{name}」调用工作流失败: {result}")
    
    except Exception as e:
        logger.error(f"场景「{name}」请求异常: {str(e)}")
    
    return False


def batch_test(args):
    """批量测试工作流
    
    Args:
        args: 命令行参数
        
    Returns:
        int: 退出码
    """
    # 如果需要创建示例配置，则创建后退出
    if args.create_sample:
        create_sample_config(args.config)
        return 0
    
    # 加载测试场景
    test_cases = load_test_cases(args.config)
    if not test_cases:
        logger.error("未找到有效的测试场景")
        return 1
    
    logger.info(f"共加载了 {len(test_cases)} 个测试场景")
    
    # 记录结果
    success_count = 0
    failed_count = 0
    
    # 遍历测试场景
    for case in test_cases:
        if test_workflow_case(args.token, args.workflow_id, case):
            success_count += 1
        else:
            failed_count += 1
        
        # 添加间隔，避免请求过于频繁
        time.sleep(2)
    
    # 输出测试结果统计
    logger.info("="*50)
    logger.info(f"测试完成: 成功 {success_count}, 失败 {failed_count}")
    logger.info("="*50)
    
    return 0 if failed_count == 0 else 1


if __name__ == "__main__":
    args = parse_args()
    sys.exit(batch_test(args)) 