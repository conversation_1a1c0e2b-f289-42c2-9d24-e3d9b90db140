#!/usr/bin/env python3
"""
测试API接口使用Redis的功能
"""
import requests
import json
import logging
import uuid

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("redis_api_test")

# 测试服务器地址
BASE_URL = "http://localhost:8000/api/v1"


def test_command_api():
    """测试命令API接口是否能正常使用Redis功能"""
    logger.info("开始测试命令API接口...")
    
    # 生成随机测试机器码
    test_machine_code = f"TEST_MACHINE_{uuid.uuid4().hex[:8]}"
    logger.info(f"使用测试机器码: {test_machine_code}")
    
    # 1. 先添加一个测试命令
    try:
        command_data = {
            "machine_code": test_machine_code,
            "command_type": "update_qr",
            "ext_params": {
                "test_param": "test_value",
                "timestamp": str(uuid.uuid4())
            }
        }
        
        logger.info(f"添加测试命令: {json.dumps(command_data)}")
        
        # 发送添加命令请求
        add_response = requests.post(
            f"{BASE_URL}/commands/test/add",
            json=command_data
        )
        
        # 检查响应
        if add_response.status_code == 200:
            add_result = add_response.json()
            logger.info(f"添加命令响应: {json.dumps(add_result)}")
            logger.info("命令添加成功!")
        else:
            logger.error(f"添加命令失败: {add_response.status_code} - {add_response.text}")
            return False
        
        # 2. 获取刚才添加的命令
        get_data = {
            "machine_code": test_machine_code
        }
        
        logger.info(f"获取刚添加的命令: {json.dumps(get_data)}")
        
        # 发送获取命令请求
        get_response = requests.post(
            f"{BASE_URL}/commands/get",
            json=get_data
        )
        
        # 检查响应
        if get_response.status_code == 200:
            get_result = get_response.json()
            logger.info(f"获取命令响应: {json.dumps(get_result)}")
            
            # 验证返回的命令是否与添加的一致
            assert get_result["machine_code"] == test_machine_code, "机器码不匹配"
            assert get_result["command_type"] == "update_qr", "命令类型不匹配"
            assert get_result["ext_params"]["test_param"] == "test_value", "扩展参数不匹配"
            
            logger.info("命令获取成功且数据一致!")
            return True
        else:
            logger.error(f"获取命令失败: {get_response.status_code} - {get_response.text}")
            return False
            
    except Exception as e:
        logger.error(f"测试命令API失败: {str(e)}")
        return False


def test_qrcode_api():
    """测试二维码API接口是否能正常使用Redis功能"""
    logger.info("开始测试二维码API接口...")
    
    # 生成随机测试机器码
    test_machine_code = f"TEST_MACHINE_{uuid.uuid4().hex[:8]}"
    logger.info(f"使用测试机器码: {test_machine_code}")
    
    # 测试二维码数据
    test_qrcode = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z/C/HgAGgwJ/lK3Q6wAAAABJRU5ErkJggg=="
    
    try:
        # 1. 上传二维码
        upload_data = {
            "machine_code": test_machine_code,
            "qrcode_base64": test_qrcode
        }
        
        logger.info(f"上传测试二维码: {test_machine_code}")
        
        # 发送上传二维码请求
        upload_response = requests.post(
            f"{BASE_URL}/users/qrcode/upload",
            json=upload_data
        )
        
        # 检查响应
        if upload_response.status_code == 200:
            upload_result = upload_response.json()
            logger.info(f"上传二维码响应: {json.dumps(upload_result)}")
            logger.info("二维码上传成功!")
        else:
            logger.error(f"上传二维码失败: {upload_response.status_code} - {upload_response.text}")
            return False
        
        # 2. 获取刚才上传的二维码
        logger.info(f"获取刚上传的二维码: {test_machine_code}")
        
        # 发送获取二维码请求
        get_response = requests.get(
            f"{BASE_URL}/users/qrcode/{test_machine_code}"
        )
        
        # 检查响应
        if get_response.status_code == 200:
            get_result = get_response.json()
            logger.info("二维码获取成功!")
            
            # 验证返回的二维码是否与上传的一致
            assert get_result["machine_code"] == test_machine_code, "机器码不匹配"
            assert get_result["qrcode_base64"] == test_qrcode, "二维码数据不匹配"
            
            logger.info("二维码获取成功且数据一致!")
            return True
        else:
            logger.error(f"获取二维码失败: {get_response.status_code} - {get_response.text}")
            return False
            
    except Exception as e:
        logger.error(f"测试二维码API失败: {str(e)}")
        return False


if __name__ == "__main__":
    # 测试命令API
    command_result = test_command_api()
    logger.info(f"命令API测试结果: {'成功' if command_result else '失败'}")

    # 测试二维码API
    qrcode_result = test_qrcode_api()
    logger.info(f"二维码API测试结果: {'成功' if qrcode_result else '失败'}")

    # 总结测试结果
    if command_result and qrcode_result:
        logger.info("所有API Redis功能测试通过!")
    else:
        logger.error("部分API Redis功能测试失败!")
