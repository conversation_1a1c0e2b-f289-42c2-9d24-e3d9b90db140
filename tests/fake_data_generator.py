"""
生成假数据的脚本，用于开发和测试
"""
import random
from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from faker import Faker

from app.models.user import BiliUser, Base
from app.models.video import VideoDetail
from app.models.comment_record import CommentRecord
from app.models.comment_reply import CommentReply, ReplyStatus
from app.core.config import settings

# 配置 Faker
fake = Faker(['zh_CN'])

# 创建数据库连接
DB_URL = (f"mysql+pymysql://{settings.DB_USER}:{settings.DB_PASSWORD}"
          f"@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}")
engine = create_engine(DB_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

# 确保所有表存在
Base.metadata.create_all(bind=engine)


def generate_fake_data(
    user_count=5,
    video_per_user=3,
    comments_per_video=5,
    replies_per_comment=2,
    include_comment_thread=True
):
    """
    生成假数据

    Args:
        user_count: 生成的用户数量
        video_per_user: 每个用户拥有的视频数量
        comments_per_video: 每个视频的评论数量
        replies_per_comment: 每条评论的回复数量
        include_comment_thread: 是否包含楼中楼评论结构
    """
    print("开始生成假数据...")

    try:
        # 生成用户数据
        users = []
        for i in range(user_count):
            user = BiliUser(
                user_id=str(fake.random_number(digits=9)),
                username=fake.user_name(),
                avatar_url=(
                    f"https://api.dicebear.com/7.x/personas/svg?seed={i}"
                ),
                profile_url=(
                    f"https://space.bilibili.com/"
                    f"{fake.random_number(digits=9)}"
                ),
                description=fake.sentence(),
                machine_code=fake.uuid4(),
                machine_name=fake.word() + "-PC",
                login_status=random.choice(["online", "offline"]),
                last_login_time=fake.date_time_this_year()
            )
            db.add(user)
            db.flush()
            users.append(user)
            print(f"已创建用户: {user.username}")

        # 生成视频数据
        videos = []
        for user in users:
            for j in range(video_per_user):
                bv_id = fake.lexify(text='???????????')
                video = VideoDetail(
                    video_id=f"BV{bv_id}",
                    title=fake.sentence(nb_words=6),
                    video_content=fake.paragraph(),
                    video_url=f"https://www.bilibili.com/video/BV{bv_id}",
                    promoted_products={
                        "product": fake.paragraph(nb_sentences=2)
                    },
                    video_summary=fake.paragraph(nb_sentences=3),
                    user_id=user.user_id
                )
                db.add(video)
                db.flush()
                videos.append(video)
                print(f"已为用户 {user.username} 创建视频: {video.title}")

        # 生成评论数据
        comments = []
        for video in videos:
            # 场景1: 主评论和一条直接回复
            if include_comment_thread:
                # 创建主评论
                reply_num = fake.random_number(digits=9)
                reply_url = f"{video.video_url}/#reply{reply_num}"
                root_comment = CommentRecord(
                    source_id=1000000 + len(comments),
                    video_id=video.video_id,
                    source_content="你好",
                    reply_time=fake.date_time_this_year(),
                    uri=reply_url,
                    user_mid=11111,  # 用户 A
                    user_nickname="AAAA",
                    root_id=0,  # 主评论
                    target_id=0,  # 主评论
                    comment_type=CommentRecord.TYPE_NO_REPLY,
                    reply_count=1,
                    like_count=random.randint(0, 100),
                    importance_level=random.randint(1, 5),
                    status="pending",
                    is_top=False,
                    is_blocked=False
                )
                db.add(root_comment)
                db.flush()
                comments.append(root_comment)
                print(f"已为视频 {video.title} 创建主评论")

                # 创建楼中楼回复
                reply_num = fake.random_number(digits=9)
                reply_url = f"{video.video_url}/#reply{reply_num}"
                sub_comment = CommentRecord(
                    source_id=2000000 + len(comments),
                    video_id=video.video_id,
                    source_content="不好",
                    reply_time=fake.date_time_this_year(),
                    uri=reply_url,
                    user_mid=22222,  # 用户 B
                    user_nickname="BBBB",
                    root_id=root_comment.source_id,  # 指向主评论
                    target_id=root_comment.source_id,  # 直接回复主评论
                    root_reply_content=root_comment.source_content,
                    target_reply_content=root_comment.source_content,
                    comment_type=CommentRecord.TYPE_REPLY,
                    reply_count=0,
                    like_count=random.randint(0, 50),
                    importance_level=random.randint(1, 3),
                    status="pending",
                    is_top=False,
                    is_blocked=False
                )
                db.add(sub_comment)
                db.flush()
                comments.append(sub_comment)
                print("已为主评论创建楼中楼回复")

                # 场景2: 主评论和多条连续回复的楼中楼
                # 创建另一个主评论
                reply_num = fake.random_number(digits=9)
                reply_url = f"{video.video_url}/#reply{reply_num}"
                root_comment2 = CommentRecord(
                    source_id=3000000 + len(comments),
                    video_id=video.video_id,
                    source_content="你好",
                    reply_time=fake.date_time_this_year(),
                    uri=reply_url,
                    user_mid=11111,  # 用户 A
                    user_nickname="AAAA",
                    root_id=0,  # 主评论
                    target_id=0,  # 主评论
                    comment_type=CommentRecord.TYPE_NO_REPLY,
                    reply_count=5,
                    like_count=random.randint(0, 100),
                    importance_level=random.randint(1, 5),
                    status="pending",
                    is_top=False,
                    is_blocked=False
                )
                db.add(root_comment2)
                db.flush()
                comments.append(root_comment2)
                print(f"已为视频 {video.title} 创建主评论(场景2)")

                # 创建5个连续的楼中楼评论
                prev_comment = root_comment2
                for i in range(5):
                    # 用户设置
                    user_mid = 22222 if i % 2 == 0 else 33333
                    nickname = "BBBB" if i % 2 == 0 else "CCCCC"

                    # 设置内容
                    if i % 2 == 0:
                        content = "不好" if i == 0 else f"不好{i+1}"
                    else:
                        content = "哈哈" if i == 1 else f"哈哈{i//2}"

                    # 设置URL
                    reply_num = fake.random_number(digits=9)
                    reply_url = f"{video.video_url}/#reply{reply_num}"

                    sub_comment = CommentRecord(
                        source_id=4000000 + len(comments) + i,
                        video_id=video.video_id,
                        source_content=content,
                        reply_time=fake.date_time_this_year(),
                        uri=reply_url,
                        user_mid=user_mid,
                        user_nickname=nickname,
                        root_id=root_comment2.source_id,  # 指向主评论
                        target_id=prev_comment.source_id,  # 指向上一条评论
                        root_reply_content=root_comment2.source_content,
                        target_reply_content=prev_comment.source_content,
                        comment_type=CommentRecord.TYPE_REPLY,
                        reply_count=0,
                        like_count=random.randint(0, 50),
                        importance_level=random.randint(1, 3),
                        status="pending",
                        is_top=False,
                        is_blocked=False
                    )
                    db.add(sub_comment)
                    db.flush()
                    comments.append(sub_comment)
                    prev_comment = sub_comment
                    print(f"已为主评论创建第{i+1}条楼中楼回复(场景2)")
            
            # 常规评论
            for k in range(comments_per_video):
                comment_time = fake.date_time_this_year()
                reply_num = fake.random_number(digits=9)
                comment = CommentRecord(
                    source_id=5000000 + len(comments),
                    video_id=video.video_id,
                    source_content=fake.paragraph(),
                    reply_time=comment_time,
                    uri=f"{video.video_url}/#reply{reply_num}",
                    user_mid=fake.random_number(digits=9),
                    user_nickname=fake.user_name(),
                    root_id=0,  # 主评论
                    target_id=0,  # 主评论
                    comment_type=random.choice([
                        CommentRecord.TYPE_NO_REPLY, 
                        CommentRecord.TYPE_MENTION, 
                        CommentRecord.TYPE_REPLY
                    ]),
                    reply_count=random.randint(0, 10),
                    like_count=random.randint(0, 100),
                    importance_level=random.randint(1, 5),
                    status=random.choice(["pending", "replied", "ignored"]),
                    is_top=random.random() < 0.1,  # 10% 概率置顶
                    is_blocked=random.random() < 0.05  # 5% 概率屏蔽
                )
                db.add(comment)
                db.flush()
                comments.append(comment)
                print(f"已为视频 {video.title} 创建常规评论")
        
        # 生成评论回复数据
        for comment in comments:
            # 随机生成0-n条回复
            for reply_index in range(random.randint(0, replies_per_comment)):
                status = random.choice(list(ReplyStatus))
                reply_time = None
                if status in [ReplyStatus.completed]:
                    reply_time = datetime.now() - timedelta(
                        days=random.randint(1, 30)
                    )
                
                error_msg = None
                if status == ReplyStatus.failed:
                    error_msg = fake.sentence()
                    
                reply = CommentReply(
                    comment_id=comment.id,
                    comment_source_id=comment.source_id,
                    reply_content=fake.paragraph(),
                    needs_content_change=random.random() < 0.3,  # 30% 概率
                    status=status,
                    reply_time=reply_time,
                    error_message=error_msg
                )
                
                if reply.needs_content_change:
                    reply.changed_reply_content = fake.paragraph()
                
                db.add(reply)
                comm_id = comment.id
                print(f"已为评论 ID:{comm_id} 创建回复")
        
        # 提交所有更改
        db.commit()
        print("假数据生成完成！")
        
        # 打印统计信息
        print(f"共创建: {user_count} 位用户")
        print(f"共创建: {len(videos)} 个视频")
        print(f"共创建: {len(comments)} 条评论")
        
        reply_count = db.query(CommentReply).count()
        print(f"共创建: {reply_count} 条回复")
        
    except Exception as e:
        db.rollback()
        print(f"生成假数据时出错: {str(e)}")
    finally:
        db.close()


if __name__ == "__main__":
    # 可以根据需要调整参数
    generate_fake_data(
        user_count=10,
        video_per_user=5,
        comments_per_video=8,
        replies_per_comment=3
    )

