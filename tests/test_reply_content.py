import logging
import os
import sys
from typing import Dict, Any, Optional
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
import requests
import json

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings
from app.models.comment_record import CommentRecord
from app.services.coze_service import CozeService
from app.services.reply_service import ReplyService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_reply_content")


# 调试版本的 CozeService
class DebugCozeService(CozeService):
    """调试版本的 CozeService，用于诊断 API 调用问题"""
    
    def get_video_details(self, video_url: str) -> Optional[Dict[str, Any]]:
        """返回测试视频详情数据"""
        logger.info(f"获取视频详情: {video_url}")
        # 返回所有必需的字段
        return {
            "title": "测试视频标题",
            "video_content": "这是一个测试视频的内容描述。视频展示了产品的各种功能和使用场景。",
            "product_info": "这是产品相关信息，包括产品特点、优势和用途。",
            "video_script": "这是视频脚本内容，详细描述了视频中的内容和场景。讲解了产品的使用方法和优势。",
            "promoted_products": "推广产品A、推广产品B",
            "video_summary": "视频主要介绍了产品的功能和使用方法，展示了实际应用场景。",
            "category": "科技",
            "reply_setting": "友好、专业",
            "comment_control_plan": "适度控评"
        }
    
    def chat_with_workflow(
        self, user_id: str, bot_id: str, message: str = "",
        video_url: str = "", original_comment: str = "", context_comments: str = ""
    ) -> Optional[str]:
        """调试版的工作流调用，处理所有可能的输出字段"""
        try:
            # 获取视频详情
            video_details = self.get_video_details(video_url)
            if not video_details:
                logger.error(f"未找到视频详情: {video_url}")
                return None

            # 处理评论内容
            # 优先使用原评论参数，否则从消息中提取
            comment = original_comment
            if not comment and "内容：" in message:
                message_parts = message.split("内容：")
                if len(message_parts) > 1:
                    comment = message_parts[1].strip()
            
            logger.info(f"提取的评论内容: {comment}")
            
            # 调用workflow
            url = f"{self.base_url.replace('v3', 'v1')}/workflow/run"
            payload = {
                "parameters": {
                    "shipinbiaoti": video_details["title"],
                    "zhengwen": video_details["video_script"],
                    "tuijian": video_details["promoted_products"],
                    "pinglun": comment,
                    "zongjie": video_details["video_summary"],
                    "pinlei": video_details["category"],
                    "sheding": video_details["reply_setting"],
                    "kongping": video_details.get("comment_control_plan", ""),
                    "shangxiawen": context_comments if context_comments else message
                },
                "workflow_id": "7469697317944967195",
            }

            logger.info(f"调用Coze工作流")
            response = requests.post(url, headers=self.headers, json=payload)
            result = response.json()
            
            # 打印关键 API 响应信息
            logger.info(f"API响应代码: {result.get('code')}, 消息: {result.get('msg')}")

            if result.get("code") != 0:
                logger.error(f"调用workflow失败: {result}")
                return None
                
            # 解析返回的JSON字符串
            try:
                data = eval(result["data"])
                
                # 检查 output1 字段是否包含 "不需要回复"
                if "output1" in data and data["output1"] == "不需要回复":
                    logger.info("Coze返回了'不需要回复'指令")
                    return "不需要回复"
                
                # 检查其他输出字段
                for field in ["output2", "output", "result"]:
                    if field in data and data[field]:
                        return data[field]
                
                # 如果没有有效输出，返回默认回复
                return "感谢您的评论！我们会继续努力提供更好的内容和服务。🌟"
                
            except Exception as e:
                logger.error(f"解析数据失败: {str(e)}")
                # 出现解析错误时返回默认回复
                return "谢谢您的评论！我们非常重视您的反馈。💖"

        except Exception as e:
            logger.error(f"调用workflow失败: {str(e)}")
            return None


def get_db_session() -> Session:
    """创建数据库会话"""
    # 构建数据库连接字符串
    db_url = (f"mysql+pymysql://{settings.DB_USER}:{settings.DB_PASSWORD}"
              f"@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}")
    engine = create_engine(db_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()


def main():
    """主测试函数"""
    db_session = get_db_session()
    
    try:
        # 获取ID为13的评论记录
        comment = db_session.query(CommentRecord).filter(
            CommentRecord.id == 13
        ).first()
        
        if not comment:
            logger.error("未找到ID为13的评论记录")
            return
        
        logger.info(f"获取到评论记录: {comment}")
        logger.info(f"评论内容: {comment.source_content}")
        
        # 初始化调试版的Coze服务
        coze_service = DebugCozeService(
            api_token=settings.COZE_API_TOKEN
        )
        
        # 初始化回复服务
        reply_service = ReplyService(
            db_session=db_session,
            coze_service=coze_service,
            bot_id=settings.COZE_BOT_ID,
            user_id=settings.COZE_USER_ID
        )
        
        # 测试生成回复内容
        logger.info("开始生成回复内容...")
        reply_content = reply_service.generate_reply_content(comment)
        
        if reply_content:
            logger.info(f"成功生成回复内容: {reply_content}")
        else:
            logger.info("API返回不需要回复，按预期返回None")
    
    except Exception as e:
        logger.error(f"测试过程发生错误: {str(e)}")
    finally:
        db_session.close()


if __name__ == "__main__":
    main() 