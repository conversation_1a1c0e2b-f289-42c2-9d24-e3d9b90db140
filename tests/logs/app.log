{"asctime": "2025-05-15 13:59:30,678", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-15 13:59:30,678", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-15 13:59:30,678", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-15 13:59:30,716", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-15 13:59:30,730", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-15 13:59:30,730", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-15 13:59:30,730", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-15 13:59:30,737", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-15 13:59:30,737", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-15 13:59:30,737", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 13:59:30,737", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-15 13:59:30,737", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 13:59:30,737", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-15 13:59:30,737", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-15 13:59:30,737", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 13:59:30,738", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-15 13:59:30,738", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-15 13:59:30,738", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:04:30.737859+08:00 (in 299.999698 seconds)"}
{"asctime": "2025-05-15 13:59:30,738", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-15 13:59:30,738", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 13:59:30,738", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-15 13:59:30,738", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:04:30.737859+08:00 (in 299.999562 seconds)"}
{"asctime": "2025-05-15 13:59:30,738", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-15 13:59:30,765", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-15 13:59:30,766", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,767", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-15 13:59:30,768", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,768", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-15 13:59:30,768", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-15 13:59:30,769", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-15 13:59:30,769", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,769", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-15 13:59:30,770", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,770", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-15 13:59:30,771", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,771", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-15 13:59:30,771", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,772", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-15 13:59:30,772", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,772", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-15 13:59:30,773", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,773", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 13:59:30,773", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 13:59:30,774", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 13:59:30,774", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-15 13:59:30,774", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,774", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-15 13:59:30,775", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,775", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-15 13:59:30,775", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,775", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-15 13:59:30,776", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,777", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-15 13:59:30,777", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,777", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-15 13:59:30,777", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,778", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-15 13:59:30,778", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,778", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-15 13:59:30,779", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,782", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-15 13:59:30,783", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,783", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 13:59:30,783", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 13:59:30,783", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 13:59:30,783", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-15 13:59:30,784", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-15 13:59:30,785", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 13:59:30,791", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 13:59:30,792", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 13:59:30,815", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 13:59:30,821", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 13:59:30,824", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 13:59:30,824", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 13:59:30,826", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 13:59:30,827", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/BV12345678 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 13:59:30,830", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 13:59:30,831", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/BV12345678 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 13:59:30,833", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 13:59:30,835", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/BV12345678 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 13:59:30,837", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 13:59:30,838", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 13:59:30,844", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-15 13:59:30,845", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 13:59:30,846", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 13:59:30,846", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-15 14:00:53,803", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-15 14:00:53,804", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-15 14:00:53,804", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-15 14:00:53,841", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-15 14:00:53,857", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-15 14:00:53,857", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-15 14:00:53,857", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-15 14:00:53,864", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-15 14:00:53,864", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-15 14:00:53,864", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:00:53,864", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-15 14:00:53,864", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:00:53,864", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-15 14:00:53,864", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-15 14:00:53,864", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-15 14:00:53,864", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:00:53,865", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-15 14:00:53,865", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:05:53.864787+08:00 (in 299.999694 seconds)"}
{"asctime": "2025-05-15 14:00:53,865", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-15 14:00:53,865", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:00:53,865", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-15 14:00:53,865", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:05:53.864787+08:00 (in 299.999539 seconds)"}
{"asctime": "2025-05-15 14:00:53,865", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-15 14:00:53,894", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-15 14:00:53,895", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,896", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-15 14:00:53,897", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,898", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-15 14:00:53,898", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-15 14:00:53,898", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-15 14:00:53,898", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,899", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-15 14:00:53,900", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,900", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-15 14:00:53,901", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,901", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-15 14:00:53,901", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,901", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-15 14:00:53,902", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,902", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-15 14:00:53,903", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,903", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:00:53,903", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:00:53,903", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:00:53,903", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-15 14:00:53,904", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,904", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-15 14:00:53,904", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,905", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-15 14:00:53,905", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,905", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-15 14:00:53,906", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,906", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-15 14:00:53,906", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,907", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-15 14:00:53,907", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,907", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-15 14:00:53,907", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,907", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-15 14:00:53,908", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,911", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-15 14:00:53,912", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,912", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:00:53,912", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:00:53,912", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:00:53,912", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-15 14:00:53,913", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:00:53,914", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:00:53,920", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:00:53,922", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:00:53,944", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:00:53,950", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:00:53,956", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:00:53,960", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:00:53,966", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:00:53,968", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/BV12345678 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:00:53,971", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:00:53,972", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/BV12345678 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:00:53,974", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:00:53,976", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/BV12345678 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:00:53,978", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:00:53,979", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:00:53,985", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-15 14:00:53,987", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:00:53,988", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:00:53,989", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-15 14:01:35,763", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-15 14:01:35,763", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-15 14:01:35,763", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-15 14:01:35,778", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-15 14:01:35,789", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-15 14:01:35,789", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-15 14:01:35,789", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-15 14:01:35,796", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-15 14:01:35,796", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-15 14:01:35,796", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:01:35,796", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-15 14:01:35,796", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:01:35,797", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-15 14:01:35,797", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-15 14:01:35,797", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-15 14:01:35,797", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-15 14:01:35,797", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-15 14:01:35,797", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-15 14:01:35,797", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-15 14:01:35,797", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:01:35,797", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:06:35.796977+08:00 (in 299.999577 seconds)"}
{"asctime": "2025-05-15 14:01:35,817", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-15 14:01:35,818", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,819", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-15 14:01:35,820", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,820", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-15 14:01:35,820", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-15 14:01:35,821", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-15 14:01:35,821", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,821", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-15 14:01:35,822", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,822", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-15 14:01:35,822", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,823", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-15 14:01:35,823", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,823", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-15 14:01:35,823", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,824", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-15 14:01:35,824", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,824", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:01:35,824", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:01:35,825", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:01:35,825", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-15 14:01:35,825", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,825", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-15 14:01:35,826", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,826", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-15 14:01:35,826", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,826", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-15 14:01:35,827", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,827", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-15 14:01:35,827", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,827", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-15 14:01:35,827", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,827", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-15 14:01:35,828", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,828", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-15 14:01:35,829", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,830", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-15 14:01:35,831", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,832", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:01:35,832", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:01:35,832", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:01:35,832", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-15 14:01:35,832", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:01:35,833", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:01:35,837", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:01:35,844", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/videos/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:01:35,845", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:01:35,848", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/videos/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:01:35,849", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/videos/?skip=0&limit=5 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:01:35,850", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/videos/?skip=10&limit=5 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:01:35,851", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:01:35,853", "levelname": "WARNING", "name": "app.repositories.video_repository", "message": "未找到视频ID: BV87654321"}
{"asctime": "2025-05-15 14:01:35,853", "levelname": "INFO", "name": "app.repositories.video_repository", "message": "添加新视频详情: BV87654321"}
{"asctime": "2025-05-15 14:01:35,853", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/videos/ \"HTTP/1.1 201 Created\""}
{"asctime": "2025-05-15 14:01:35,854", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/videos/ \"HTTP/1.1 409 Conflict\""}
{"asctime": "2025-05-15 14:01:35,855", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:01:35,856", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/videos/BV12345678 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:01:35,857", "levelname": "WARNING", "name": "app.repositories.video_repository", "message": "未找到视频ID: BV99999999"}
{"asctime": "2025-05-15 14:01:35,857", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/videos/BV99999999 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:01:35,858", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:01:35,861", "levelname": "INFO", "name": "app.repositories.video_repository", "message": "更新视频详情: BV12345678"}
{"asctime": "2025-05-15 14:01:35,861", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/videos/BV12345678 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:01:35,862", "levelname": "WARNING", "name": "app.repositories.video_repository", "message": "未找到视频ID: BV99999999"}
{"asctime": "2025-05-15 14:01:35,862", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/videos/BV99999999 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:01:35,863", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:01:35,865", "levelname": "INFO", "name": "app.repositories.video_repository", "message": "更新视频详情: BV12345678"}
{"asctime": "2025-05-15 14:01:35,866", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/videos/BV12345678 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:01:35,867", "levelname": "INFO", "name": "app.repositories.video_repository", "message": "更新视频详情: BV12345678"}
{"asctime": "2025-05-15 14:01:35,867", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/videos/BV12345678 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:01:35,868", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:01:35,869", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/videos/ \"HTTP/1.1 422 Unprocessable Entity\""}
{"asctime": "2025-05-15 14:01:35,870", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/videos/ \"HTTP/1.1 422 Unprocessable Entity\""}
{"asctime": "2025-05-15 14:01:35,873", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-15 14:01:35,874", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:01:35,874", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:01:35,874", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-15 14:09:03,751", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-15 14:09:03,751", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-15 14:09:03,751", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-15 14:09:03,785", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-15 14:09:03,801", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-15 14:09:03,802", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-15 14:09:03,802", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-15 14:09:03,809", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-15 14:09:03,809", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-15 14:09:03,809", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:09:03,809", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-15 14:09:03,809", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:09:03,809", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-15 14:09:03,809", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-15 14:09:03,810", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:09:03,810", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-15 14:09:03,810", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-15 14:09:03,810", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:14:03.809905+08:00 (in 299.999715 seconds)"}
{"asctime": "2025-05-15 14:09:03,810", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-15 14:09:03,810", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:09:03,810", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-15 14:09:03,810", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:14:03.809905+08:00 (in 299.999579 seconds)"}
{"asctime": "2025-05-15 14:09:03,810", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-15 14:09:03,843", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-15 14:09:03,843", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,845", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-15 14:09:03,845", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,846", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-15 14:09:03,846", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-15 14:09:03,847", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-15 14:09:03,847", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,848", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-15 14:09:03,848", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,849", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-15 14:09:03,849", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,850", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-15 14:09:03,850", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,850", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-15 14:09:03,850", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,851", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-15 14:09:03,852", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,852", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:09:03,852", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:09:03,852", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:09:03,852", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-15 14:09:03,852", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,852", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-15 14:09:03,853", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,853", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-15 14:09:03,853", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,854", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-15 14:09:03,854", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,854", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-15 14:09:03,855", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,855", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-15 14:09:03,855", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,855", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-15 14:09:03,855", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,855", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-15 14:09:03,856", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,859", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-15 14:09:03,860", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,860", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:09:03,860", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:09:03,860", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:09:03,860", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-15 14:09:03,861", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:09:03,861", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:09:03,866", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:09:03,874", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/comments/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:09:03,895", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:09:03,896", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/comments/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:09:03,898", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:09:03,899", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/comments/batch \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:09:03,902", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:09:03,904", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:09:03,906", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:09:03,907", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:09:03,910", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:09:03,911", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: DELETE http://testserver/api/v1/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:09:03,917", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-15 14:09:03,920", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:09:03,921", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:09:03,921", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-15 14:10:03,593", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-15 14:10:03,594", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-15 14:10:03,594", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-15 14:10:03,632", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-15 14:10:03,645", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-15 14:10:03,646", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-15 14:10:03,646", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-15 14:10:03,652", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-15 14:10:03,652", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-15 14:10:03,652", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:10:03,652", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-15 14:10:03,652", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:15:03.653376+08:00 (in 299.999667 seconds)"}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:15:03.653376+08:00 (in 299.999465 seconds)"}
{"asctime": "2025-05-15 14:10:03,653", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-15 14:10:03,686", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-15 14:10:03,687", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,688", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-15 14:10:03,689", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,689", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-15 14:10:03,690", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-15 14:10:03,690", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-15 14:10:03,690", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,691", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-15 14:10:03,692", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,692", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-15 14:10:03,693", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,693", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-15 14:10:03,693", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,693", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-15 14:10:03,694", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,694", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-15 14:10:03,695", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,695", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:03,695", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:03,695", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:03,696", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-15 14:10:03,696", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,696", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-15 14:10:03,697", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,697", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-15 14:10:03,697", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,697", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-15 14:10:03,698", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,698", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-15 14:10:03,699", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,699", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-15 14:10:03,699", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,699", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-15 14:10:03,699", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,699", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-15 14:10:03,700", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,703", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-15 14:10:03,704", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,704", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:03,704", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:03,704", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:03,704", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-15 14:10:03,705", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:03,706", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:03,710", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:03,717", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/comments/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:10:03,739", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:03,740", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/comments/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:10:03,742", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:03,743", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/comments/batch \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:10:03,746", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:03,748", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:10:03,750", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:03,751", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:10:03,754", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:03,755", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: DELETE http://testserver/api/v1/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:10:03,761", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-15 14:10:03,763", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:10:03,764", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:10:03,764", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-15 14:10:11,380", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-15 14:10:11,380", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-15 14:10:11,380", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-15 14:10:11,394", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-15 14:10:11,410", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-15 14:10:11,410", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-15 14:10:11,410", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-15 14:10:11,417", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-15 14:10:11,417", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-15 14:10:11,417", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:10:11,417", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-15 14:10:11,417", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:15:11.418078+08:00 (in 299.999537 seconds)"}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:10:11,418", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:15:11.418078+08:00 (in 299.999454 seconds)"}
{"asctime": "2025-05-15 14:10:11,439", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-15 14:10:11,440", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,441", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-15 14:10:11,442", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,442", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-15 14:10:11,442", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-15 14:10:11,442", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-15 14:10:11,443", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,443", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-15 14:10:11,443", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,444", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-15 14:10:11,444", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,444", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-15 14:10:11,444", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,444", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-15 14:10:11,445", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,445", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-15 14:10:11,446", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,446", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:11,446", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:11,446", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:11,446", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-15 14:10:11,446", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,447", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-15 14:10:11,447", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,447", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-15 14:10:11,447", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,447", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-15 14:10:11,448", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,448", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-15 14:10:11,448", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,448", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-15 14:10:11,449", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,449", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-15 14:10:11,449", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,449", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-15 14:10:11,450", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,452", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-15 14:10:11,453", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,453", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:11,453", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:11,453", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:11,453", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-15 14:10:11,454", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:10:11,454", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:10:11,459", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:11,466", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:10:11,467", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:11,471", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:10:11,472", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/?skip=0&limit=5 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:10:11,473", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/?skip=10&limit=5 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:10:11,474", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:11,476", "levelname": "WARNING", "name": "app.repositories.video_repository", "message": "未找到视频ID: BV87654321"}
{"asctime": "2025-05-15 14:10:11,477", "levelname": "INFO", "name": "app.repositories.video_repository", "message": "添加新视频详情: BV87654321"}
{"asctime": "2025-05-15 14:10:11,477", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/ \"HTTP/1.1 201 Created\""}
{"asctime": "2025-05-15 14:10:11,478", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/ \"HTTP/1.1 409 Conflict\""}
{"asctime": "2025-05-15 14:10:11,479", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:11,481", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/BV12345678 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:10:11,482", "levelname": "WARNING", "name": "app.repositories.video_repository", "message": "未找到视频ID: BV99999999"}
{"asctime": "2025-05-15 14:10:11,482", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/videos/BV99999999 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:10:11,483", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:11,486", "levelname": "INFO", "name": "app.repositories.video_repository", "message": "更新视频详情: BV12345678"}
{"asctime": "2025-05-15 14:10:11,487", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/BV12345678 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:10:11,487", "levelname": "WARNING", "name": "app.repositories.video_repository", "message": "未找到视频ID: BV99999999"}
{"asctime": "2025-05-15 14:10:11,487", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/BV99999999 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:10:11,489", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:11,491", "levelname": "INFO", "name": "app.repositories.video_repository", "message": "更新视频详情: BV12345678"}
{"asctime": "2025-05-15 14:10:11,491", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/BV12345678 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:10:11,492", "levelname": "INFO", "name": "app.repositories.video_repository", "message": "更新视频详情: BV12345678"}
{"asctime": "2025-05-15 14:10:11,492", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/videos/BV12345678 \"HTTP/1.1 200 OK\""}
{"asctime": "2025-05-15 14:10:11,494", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:10:11,495", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/ \"HTTP/1.1 422 Unprocessable Entity\""}
{"asctime": "2025-05-15 14:10:11,496", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/videos/ \"HTTP/1.1 422 Unprocessable Entity\""}
{"asctime": "2025-05-15 14:10:11,499", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-15 14:10:11,500", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:10:11,500", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:10:11,501", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-15 14:11:40,175", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-15 14:11:40,175", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-15 14:11:40,176", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-15 14:11:40,216", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-15 14:11:40,230", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-15 14:11:40,230", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-15 14:11:40,230", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-15 14:11:40,237", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-15 14:11:40,237", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-15 14:11:40,237", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:11:40,237", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:11:40,237", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-15 14:11:40,237", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-15 14:11:40,238", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-15 14:11:40,238", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-15 14:11:40,238", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:11:40,238", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-15 14:11:40,238", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:16:40.237927+08:00 (in 299.999510 seconds)"}
{"asctime": "2025-05-15 14:11:40,238", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-15 14:11:40,238", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:11:40,238", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-15 14:11:40,238", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:16:40.237927+08:00 (in 299.999297 seconds)"}
{"asctime": "2025-05-15 14:11:40,238", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-15 14:11:40,272", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-15 14:11:40,273", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,274", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-15 14:11:40,274", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,274", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-15 14:11:40,275", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-15 14:11:40,275", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-15 14:11:40,275", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,276", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-15 14:11:40,276", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,277", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-15 14:11:40,277", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,278", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-15 14:11:40,278", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,278", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-15 14:11:40,279", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,279", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-15 14:11:40,280", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,280", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:11:40,280", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:11:40,280", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:11:40,280", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-15 14:11:40,280", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,281", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-15 14:11:40,281", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,282", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-15 14:11:40,282", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,282", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-15 14:11:40,282", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,283", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-15 14:11:40,283", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,283", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-15 14:11:40,283", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,284", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-15 14:11:40,284", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,284", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-15 14:11:40,285", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,287", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-15 14:11:40,288", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,289", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:11:40,289", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:11:40,289", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:11:40,289", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-15 14:11:40,289", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:11:40,290", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:11:40,294", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:11:40,300", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/comments/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:11:40,322", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:11:40,323", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/comments/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:11:40,326", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:11:40,327", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/comments/batch \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:11:40,330", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:11:40,331", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:11:40,333", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:11:40,334", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:11:40,337", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:11:40,338", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: DELETE http://testserver/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:11:40,343", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-15 14:11:40,345", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:11:40,346", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:11:40,346", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-15 14:13:26,054", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-15 14:13:26,054", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-15 14:13:26,054", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-15 14:13:26,086", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-15 14:13:26,100", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-15 14:13:26,100", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-15 14:13:26,100", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-15 14:13:26,107", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-15 14:13:26,107", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-15 14:13:26,107", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:13:26,107", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-15 14:13:26,107", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:13:26,107", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-15 14:13:26,107", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-15 14:13:26,107", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-15 14:13:26,107", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:13:26,108", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-15 14:13:26,108", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:18:26.107772+08:00 (in 299.999729 seconds)"}
{"asctime": "2025-05-15 14:13:26,108", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-15 14:13:26,108", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:13:26,108", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-15 14:13:26,108", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:18:26.107772+08:00 (in 299.999573 seconds)"}
{"asctime": "2025-05-15 14:13:26,108", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-15 14:13:26,187", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-15 14:13:26,191", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:13:26,192", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:13:26,192", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-15 14:14:12,591", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-15 14:14:12,592", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-15 14:14:12,592", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-15 14:14:12,628", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-15 14:14:12,644", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-15 14:14:12,644", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-15 14:14:12,644", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-15 14:14:12,651", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-15 14:14:12,651", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-15 14:14:12,651", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:14:12,651", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-15 14:14:12,651", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:14:12,651", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-15 14:14:12,652", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-15 14:14:12,652", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-15 14:14:12,652", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-15 14:14:12,652", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-15 14:14:12,652", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-15 14:14:12,652", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-15 14:14:12,652", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:14:12,652", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-15 14:19:12.651934+08:00 (in 299.999513 seconds)"}
{"asctime": "2025-05-15 14:14:12,681", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-15 14:14:12,682", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,682", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-15 14:14:12,683", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,683", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-15 14:14:12,684", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-15 14:14:12,684", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-15 14:14:12,684", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,685", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-15 14:14:12,685", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,686", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-15 14:14:12,686", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,686", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-15 14:14:12,687", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,687", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-15 14:14:12,687", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,688", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-15 14:14:12,688", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,689", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:14:12,689", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:14:12,689", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:14:12,689", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-15 14:14:12,689", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,689", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-15 14:14:12,690", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,690", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-15 14:14:12,690", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,690", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-15 14:14:12,691", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,691", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-15 14:14:12,692", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,692", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-15 14:14:12,692", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,692", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-15 14:14:12,692", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,693", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-15 14:14:12,693", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,695", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-15 14:14:12,696", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,697", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:14:12,697", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:14:12,697", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:14:12,697", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-15 14:14:12,697", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-15 14:14:12,698", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-15 14:14:12,704", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:14:12,710", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/comments/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:14:12,731", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:14:12,731", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/comments/ \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:14:12,733", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:14:12,734", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: POST http://testserver/api/v1/comments/batch \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:14:12,737", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:14:12,738", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: GET http://testserver/api/v1/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:14:12,740", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:14:12,741", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: PUT http://testserver/api/v1/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:14:12,744", "levelname": "DEBUG", "name": "asyncio", "message": "Using selector: KqueueSelector"}
{"asctime": "2025-05-15 14:14:12,744", "levelname": "INFO", "name": "httpx", "message": "HTTP Request: DELETE http://testserver/api/v1/comments/1 \"HTTP/1.1 404 Not Found\""}
{"asctime": "2025-05-15 14:14:12,749", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-15 14:14:12,751", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-15 14:14:12,752", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-15 14:14:12,752", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-16 19:06:17,814", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-16 19:06:17,814", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-16 19:06:17,814", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-16 19:06:17,852", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-16 19:06:17,873", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-16 19:06:17,873", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-16 19:06:17,873", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-16 19:06:17,884", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-16 19:06:17,885", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-16 19:06:17,885", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-16 19:06:17,885", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-16 19:06:17,885", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-16 19:06:17,885", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-16 19:06:17,885", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-16 19:06:17,885", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-16 19:06:17,885", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-16 19:06:17,885", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-16 19:06:17,886", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-16 19:11:17.885696+08:00 (in 299.999693 seconds)"}
{"asctime": "2025-05-16 19:06:17,886", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-16 19:06:17,886", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-16 19:06:17,886", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-16 19:06:17,886", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-16 19:11:17.885696+08:00 (in 299.999523 seconds)"}
{"asctime": "2025-05-16 19:06:17,886", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-16 19:06:17,926", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-16 19:06:17,927", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,928", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-16 19:06:17,929", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,930", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-16 19:06:17,930", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-16 19:06:17,931", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-16 19:06:17,931", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,932", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-16 19:06:17,933", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,933", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-16 19:06:17,934", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,935", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-16 19:06:17,935", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,935", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-16 19:06:17,935", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,936", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-16 19:06:17,936", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,937", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-16 19:06:17,937", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-16 19:06:17,937", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-16 19:06:17,937", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-16 19:06:17,937", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,938", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-16 19:06:17,938", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,939", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-16 19:06:17,939", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,939", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-16 19:06:17,940", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,940", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-16 19:06:17,941", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,941", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-16 19:06:17,941", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,941", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-16 19:06:17,941", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,942", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-16 19:06:17,943", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,946", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-16 19:06:17,947", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,948", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-16 19:06:17,948", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-16 19:06:17,948", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-16 19:06:17,948", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-16 19:06:17,949", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-16 19:06:17,949", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-16 19:06:17,953", "levelname": "INFO", "name": "app.services.coze_service", "message": "调用Coze工作流: workflow_id=7469697317944967195"}
{"asctime": "2025-05-16 19:06:17,953", "levelname": "INFO", "name": "app.services.coze_service", "message": "工作流执行成功: user_id=test_user"}
{"asctime": "2025-05-16 19:06:17,954", "levelname": "INFO", "name": "app.services.coze_service", "message": "调用Coze工作流: workflow_id=7469697317944967195"}
{"asctime": "2025-05-16 19:06:17,954", "levelname": "INFO", "name": "app.services.coze_service", "message": "工作流执行成功: user_id=test_user"}
{"asctime": "2025-05-16 19:06:17,955", "levelname": "ERROR", "name": "app.services.coze_service", "message": "未找到视频详情: https://www.bilibili.com/video/invalid_id"}
{"asctime": "2025-05-16 19:06:17,955", "levelname": "INFO", "name": "app.services.coze_service", "message": "调用Coze工作流: workflow_id=7469697317944967195"}
{"asctime": "2025-05-16 19:06:17,956", "levelname": "ERROR", "name": "app.services.coze_service", "message": "调用workflow失败: {'code': 1001, 'message': 'API错误'}"}
{"asctime": "2025-05-16 19:06:17,959", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-16 19:06:17,961", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-16 19:06:17,962", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-16 19:06:17,962", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-16 19:06:29,965", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-16 19:06:29,965", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-16 19:06:29,965", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-16 19:06:29,996", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-16 19:06:30,010", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-16 19:06:30,010", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-16 19:06:30,010", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-16 19:06:30,020", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-16 19:06:30,021", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-16 19:06:30,021", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-16 19:06:30,021", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-16 19:06:30,021", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-16 19:11:30.022040+08:00 (in 299.999663 seconds)"}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-16 19:11:30.022040+08:00 (in 299.999491 seconds)"}
{"asctime": "2025-05-16 19:06:30,022", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-16 19:06:30,031", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-16 19:06:30,033", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-16 19:06:30,034", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-16 19:06:30,034", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-19 16:40:39,503", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-19 16:40:39,503", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-19 16:40:39,503", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-19 16:40:39,525", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-19 16:40:39,540", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-19 16:40:39,540", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-19 16:40:39,540", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-19 16:40:39,550", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-19 16:40:39,551", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-19 16:40:39,551", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:40:39,551", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-19 16:40:39,551", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-19 16:45:39.552197+08:00 (in 299.999754 seconds)"}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-19 16:45:39.552197+08:00 (in 299.999620 seconds)"}
{"asctime": "2025-05-19 16:40:39,552", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-19 16:40:39,594", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-19 16:40:39,595", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,596", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-19 16:40:39,597", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,599", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-19 16:40:39,600", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-19 16:40:39,600", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-19 16:40:39,604", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,605", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-19 16:40:39,605", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,606", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-19 16:40:39,606", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,607", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-19 16:40:39,607", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,607", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-19 16:40:39,608", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,608", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-19 16:40:39,609", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,609", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:40:39,609", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:40:39,609", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:40:39,609", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-19 16:40:39,610", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,611", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-19 16:40:39,612", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,612", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-19 16:40:39,612", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,613", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-19 16:40:39,613", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,614", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-19 16:40:39,614", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,614", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-19 16:40:39,615", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,615", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-19 16:40:39,615", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,615", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-19 16:40:39,617", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,619", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-19 16:40:39,620", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,621", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:40:39,621", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:40:39,621", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:40:39,621", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-19 16:40:39,622", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:40:39,623", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:40:39,657", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "开始处理待回复任务"}
{"asctime": "2025-05-19 16:40:39,657", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "获取到2条待处理的评论回复任务"}
{"asctime": "2025-05-19 16:40:39,657", "levelname": "ERROR", "name": "app.tasks.comment_tasks", "message": "处理单个回复任务异常: "}
{"asctime": "2025-05-19 16:40:39,657", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论回复任务处理结果: {'total_replies': 2, 'success_count': 1, 'failed_count': 1}"}
{"asctime": "2025-05-19 16:40:39,665", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-19 16:40:39,667", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:40:39,668", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-19 16:40:39,669", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-19 16:42:04,388", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-19 16:42:04,388", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-19 16:42:04,388", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-19 16:42:04,412", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-19 16:42:04,422", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-19 16:42:04,422", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-19 16:42:04,422", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-19 16:42:04,433", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-19 16:42:04,433", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-19 16:42:04,433", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:42:04,433", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-19 16:42:04,433", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-19 16:47:04.434166+08:00 (in 299.999609 seconds)"}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-19 16:47:04.434166+08:00 (in 299.999438 seconds)"}
{"asctime": "2025-05-19 16:42:04,434", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-19 16:42:04,463", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-19 16:42:04,464", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,465", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-19 16:42:04,466", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,467", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-19 16:42:04,467", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-19 16:42:04,467", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-19 16:42:04,468", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,469", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-19 16:42:04,469", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,470", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-19 16:42:04,470", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,471", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-19 16:42:04,471", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,471", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-19 16:42:04,472", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,472", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-19 16:42:04,473", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,474", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:04,474", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:04,474", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:04,474", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-19 16:42:04,474", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,474", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-19 16:42:04,475", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,476", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-19 16:42:04,476", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,476", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-19 16:42:04,477", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,478", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-19 16:42:04,478", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,478", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-19 16:42:04,478", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,479", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-19 16:42:04,479", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,479", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-19 16:42:04,480", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,483", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-19 16:42:04,484", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,484", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:04,484", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:04,484", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:04,484", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-19 16:42:04,485", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:04,485", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:04,491", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "开始处理待回复任务"}
{"asctime": "2025-05-19 16:42:04,491", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "获取到2条待处理的评论回复任务"}
{"asctime": "2025-05-19 16:42:04,491", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论回复任务处理结果: {'total_replies': 2, 'success_count': 1, 'failed_count': 1}"}
{"asctime": "2025-05-19 16:42:04,494", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-19 16:42:04,496", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:42:04,497", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-19 16:42:04,497", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
{"asctime": "2025-05-19 16:42:12,064", "levelname": "INFO", "name": "app.core.logging_setup", "message": "日志配置完成: 日志级别=DEBUG, 日志文件=/Users/<USER>/Work/baiju/project/wecom-command-bridge/tests/logs/app.log, 编码=utf-8"}
{"asctime": "2025-05-19 16:42:12,064", "levelname": "INFO", "name": "app.core.logging_setup", "message": "系统环境变量: PYTHONIOENCODING=None, LANG=zh_CN.UTF-8, LC_ALL=zh_CN.UTF-8"}
{"asctime": "2025-05-19 16:42:12,064", "levelname": "INFO", "name": "app.core.logging_setup", "message": "默认编码: utf-8"}
{"asctime": "2025-05-19 16:42:12,079", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库连接初始化成功"}
{"asctime": "2025-05-19 16:42:12,094", "levelname": "INFO", "name": "app.services.db_service", "message": "数据库表结构创建成功"}
{"asctime": "2025-05-19 16:42:12,094", "levelname": "INFO", "name": "app.core.logging_setup", "message": "数据库初始化成功"}
{"asctime": "2025-05-19 16:42:12,094", "levelname": "INFO", "name": "app.core.logging_setup", "message": "开始初始化应用..."}
{"asctime": "2025-05-19 16:42:12,104", "levelname": "INFO", "name": "app.core.logging_setup", "message": "上传目录已确认: uploads"}
{"asctime": "2025-05-19 16:42:12,104", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler started"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已启动"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_unreplied_comments\" to job store \"default\""}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_unreplied_comments"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Added job \"process_pending_replies\" to job store \"default\""}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "任务已添加: process_pending_replies"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=300秒, 回复间隔=600秒"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "INFO", "name": "app.core.logging_setup", "message": "评论任务初始化成功: {'unreplied_comments': 'process_unreplied_comments', 'pending_replies': 'process_pending_replies'}"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "INFO", "name": "app.core.logging_setup", "message": "应用初始化完成"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-19 16:47:12.105426+08:00 (in 299.999529 seconds)"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:42:12,105", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Next wakeup is due at 2025-05-19 16:47:12.105426+08:00 (in 299.999449 seconds)"}
{"asctime": "2025-05-19 16:42:12,124", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.address`."}
{"asctime": "2025-05-19 16:42:12,124", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.address` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,125", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.automotive`."}
{"asctime": "2025-05-19 16:42:12,126", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.automotive` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,126", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.bank`."}
{"asctime": "2025-05-19 16:42:12,126", "levelname": "DEBUG", "name": "faker.factory", "message": "Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider."}
{"asctime": "2025-05-19 16:42:12,127", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.barcode`."}
{"asctime": "2025-05-19 16:42:12,127", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.barcode` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,127", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.color`."}
{"asctime": "2025-05-19 16:42:12,128", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.color` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,128", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.company`."}
{"asctime": "2025-05-19 16:42:12,129", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.company` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,129", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.credit_card`."}
{"asctime": "2025-05-19 16:42:12,129", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.credit_card` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,129", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.currency`."}
{"asctime": "2025-05-19 16:42:12,130", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.currency` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,130", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.date_time`."}
{"asctime": "2025-05-19 16:42:12,131", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.date_time` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,131", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:12,131", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:12,131", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:12,131", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.geo`."}
{"asctime": "2025-05-19 16:42:12,131", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.geo` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,131", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.internet`."}
{"asctime": "2025-05-19 16:42:12,132", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.internet` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,132", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.isbn`."}
{"asctime": "2025-05-19 16:42:12,132", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.isbn` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,132", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.job`."}
{"asctime": "2025-05-19 16:42:12,133", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.job` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,133", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.lorem`."}
{"asctime": "2025-05-19 16:42:12,133", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.lorem` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,134", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.misc`."}
{"asctime": "2025-05-19 16:42:12,134", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.misc` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,134", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.passport`."}
{"asctime": "2025-05-19 16:42:12,134", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.passport` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,134", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.person`."}
{"asctime": "2025-05-19 16:42:12,135", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.person` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,137", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.phone_number`."}
{"asctime": "2025-05-19 16:42:12,138", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.phone_number` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,138", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:12,138", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:12,138", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:12,138", "levelname": "DEBUG", "name": "faker.factory", "message": "Looking for locale `en_US` in provider `faker.providers.ssn`."}
{"asctime": "2025-05-19 16:42:12,139", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.ssn` has been localized to `en_US`."}
{"asctime": "2025-05-19 16:42:12,139", "levelname": "DEBUG", "name": "faker.factory", "message": "Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider."}
{"asctime": "2025-05-19 16:42:12,141", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论任务设置完成: 未回复间隔=600秒, 回复间隔=1200秒"}
{"asctime": "2025-05-19 16:42:12,142", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "开始处理未回复评论"}
{"asctime": "2025-05-19 16:42:12,142", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "未回复评论处理完成: 总数=5, 成功=3, 失败=2"}
{"asctime": "2025-05-19 16:42:12,143", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "开始处理未回复评论"}
{"asctime": "2025-05-19 16:42:12,143", "levelname": "ERROR", "name": "app.tasks.comment_tasks", "message": "处理未回复评论任务异常: 测试异常"}
{"asctime": "2025-05-19 16:42:12,145", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "开始处理待回复任务"}
{"asctime": "2025-05-19 16:42:12,145", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "获取到2条待处理的评论回复任务"}
{"asctime": "2025-05-19 16:42:12,145", "levelname": "ERROR", "name": "app.tasks.comment_tasks", "message": "处理单个回复任务异常: "}
{"asctime": "2025-05-19 16:42:12,145", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论回复任务处理结果: {'total_replies': 2, 'success_count': 1, 'failed_count': 1}"}
{"asctime": "2025-05-19 16:42:12,146", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "开始处理待回复任务"}
{"asctime": "2025-05-19 16:42:12,146", "levelname": "ERROR", "name": "app.tasks.comment_tasks", "message": "处理待回复任务异常: 测试异常"}
{"asctime": "2025-05-19 16:42:12,148", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "开始处理待回复任务"}
{"asctime": "2025-05-19 16:42:12,148", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "获取到2条待处理的评论回复任务"}
{"asctime": "2025-05-19 16:42:12,148", "levelname": "INFO", "name": "app.tasks.comment_tasks", "message": "评论回复任务处理结果: {'total_replies': 2, 'success_count': 1, 'failed_count': 1}"}
{"asctime": "2025-05-19 16:42:12,151", "levelname": "INFO", "name": "apscheduler.scheduler", "message": "Scheduler has been shut down"}
{"asctime": "2025-05-19 16:42:12,151", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "Looking for jobs to run"}
{"asctime": "2025-05-19 16:42:12,152", "levelname": "DEBUG", "name": "apscheduler.scheduler", "message": "No jobs; waiting until a job is added"}
{"asctime": "2025-05-19 16:42:12,152", "levelname": "INFO", "name": "app.services.scheduler_service", "message": "后台任务调度器已关闭"}
