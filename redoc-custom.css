/* 为ReDoc添加自定义CSS样式 */
body {
    margin: 0;
    padding: 0;
}
.scrollbar-container::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
}
redoc[spec-url] .menu-content {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 
    "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}
redoc[spec-url] .api-content {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 
    "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 改善参数描述的可读性 */
redoc table.params {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

redoc .param-name {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
    font-weight: 500;
}

redoc .param-info-box {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 改善字段描述样式 */
redoc .field-description {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 增强中文显示效果 */
div[data-role="redoc"], [data-section-id] {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
