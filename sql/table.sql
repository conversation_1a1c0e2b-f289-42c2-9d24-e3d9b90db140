create table bili_users
(
    id              int auto_increment comment '主键ID'
        primary key,
    user_id         varchar(20) default '' not null comment 'B站用户ID',
    username        varchar(50)            not null comment '用户名',
    avatar_url      varchar(256)           null comment '头像URL',
    profile_url     varchar(256)           null comment '主页URL',
    description     text                   null comment '用户描述',
    machine_code    varchar(128)           null comment '关联的机器码',
    machine_name    varchar(128)           null comment '机器名称',
    login_status    varchar(20)            null comment '登录状态',
    last_login_time datetime               null comment '最后登录时间',
    created_at      datetime               null comment '创建时间',
    updated_at      datetime               null comment '更新时间',
    is_disabled     tinyint(1)  default 0  not null,
    constraint user_id
        unique (user_id)
);

create table comment_records
(
    id                   int auto_increment comment '内部主键ID'
        primary key,
    source_id            bigint        not null comment '评论的唯一ID (rpid)',
    video_id             varchar(20)   null comment '关联的视频ID',
    source_content       text          null comment '评论的内容',
    root_id              bigint        null comment '主楼评论a的rpid (楼中楼评论)',
    root_reply_content   text          null comment '主楼评论的内容 (楼中楼评论)',
    target_id            bigint        null comment '回复的评论的rpid (楼中楼评论)',
    target_reply_content text          null comment '回复的评论的内容 (楼中楼评论)',
    reply_time           datetime      not null comment '回复时间戳 (秒级)',
    uri                  varchar(255)  null comment '评论的超链接',
    user_mid             bigint        not null comment '回复评论的用户的uid',
    user_nickname        varchar(255)  null comment '回复评论的用户的昵称',
    path                 varchar(1000) null comment '评论链路径，格式如 /root_id/parent_id/current_id/',
    comment_type         int default 0 not null comment '评论类型 (0=no reply, 1=mention, 2=reply)',
    reply_count          int           null comment '回复数量',
    like_count           int           null comment '点赞数量',
    importance_level     int           null comment '重要程度',
    status               varchar(20)   null comment '处理状态',
    is_top               tinyint(1)    null comment '是否置顶',
    is_blocked           tinyint(1)    null comment '是否屏蔽',
    created_at           datetime      null comment '记录创建时间',
    updated_at           datetime      null comment '记录更新时间',
    constraint source_id
        unique (source_id)
);

create index video_id
    on comment_records (video_id);

create table comment_replies
(
    id                    bigint auto_increment comment '回复任务的唯一ID'
        primary key,
    comment_id            int                                                                               not null comment '关联的评论记录ID',
    comment_source_id     bigint                                                                            not null comment '要回复的评论的source_id',
    reply_content         text                                                                              not null comment '原始回复的内容',
    needs_content_change  tinyint(1)                                                                        null comment '是否需要变更评论内容',
    changed_reply_content text                                                                              null comment '变更后的评论内容',
    status                enum ('pending', 'waiting_for_confirmation', 'processing', 'completed', 'failed') null comment '回复任务状态',
    reply_time            datetime                                                                          null comment '实际回复时间',
    error_message         text                                                                              null comment '回复失败时的错误信息',
    created_at            datetime                                                                          null comment '任务创建时间',
    updated_at            datetime                                                                          null comment '任务更新时间',
    comment_context       text                                                                              null comment '评论的上下文内容',
    is_satisfied          tinyint(1)                                                                        null comment '用户是否对回复感到满意'
);

create index comment_id
    on comment_replies (comment_id);

create table video_details
(
    id                   int auto_increment comment '主键ID'
        primary key,
    video_id             varchar(20)             not null comment '视频ID',
    title                varchar(256)            not null comment '视频标题',
    video_content        text                    null comment '视频内容描述',
    video_url            varchar(256)            not null comment '视频链接',
    user_id              varchar(100) default '' not null comment 'B站账号ID',
    created_at           datetime                null comment '创建时间',
    updated_at           datetime                null comment '更新时间',
    video_script         text                    null comment '视频文案',
    video_summary        text                    null comment '视频总结',
    reply_setting        text                    null comment '回复设定',
    comment_control_plan text                    null comment '控评方案',
    category             varchar(50)             null comment '视频品类',
    promoted_products    json                    null comment '主推产品，JSON格式存储多个产品信息',
    common_questions     text                    null comment '常见问题',
    is_disabled          tinyint(1)   default 1  not null,
    constraint video_id
        unique (video_id)
);

create index user_id
    on video_details (user_id);

