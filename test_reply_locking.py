#!/usr/bin/env python3
"""
测试回复服务的数据库锁机制

这个脚本用于验证数据库级别的锁机制是否正常工作
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.container import Container
from app.services.reply_service import ReplyService
from app.models.comment_reply import CommentReply, ReplyStatus
from app.models.comment_record import CommentRecord
from app.models.user import BiliUser
from app.models.video import VideoDetail


def create_test_data(db_session):
    """创建测试数据"""
    # 创建测试用户
    test_user = BiliUser(
        username="test_user",
        nickname="测试用户",
        uid=12345,
        is_enabled=True
    )
    db_session.add(test_user)
    db_session.flush()
    
    # 创建测试视频
    test_video = VideoDetail(
        video_id="test_video_123",
        video_url="https://test.com/video/123",
        title="测试视频",
        user_id=test_user.id,
        is_enabled=True
    )
    db_session.add(test_video)
    db_session.flush()
    
    # 创建测试评论
    test_comment = CommentRecord(
        source_id=123456,
        user_nickname="测试评论用户",
        source_content="这是一条测试评论",
        reply_time=datetime.now(),
        video_id=test_video.video_id
    )
    db_session.add(test_comment)
    db_session.flush()
    
    # 创建测试回复任务
    test_reply = CommentReply(
        comment_id=test_comment.id,
        comment_source_id=test_comment.source_id,
        reply_content="测试回复内容",
        status=ReplyStatus.pending
    )
    db_session.add(test_reply)
    db_session.commit()
    
    return test_reply.id


def test_concurrent_processing(reply_id, thread_id, results):
    """测试并发处理"""
    try:
        # 创建独立的数据库连接
        container = Container()
        db = container.db_service().get_db()
        
        # 创建模拟的Coze服务
        class MockCozeService:
            def chat_with_workflow(self, *args, **kwargs):
                # 模拟处理时间
                time.sleep(2)
                return "模拟回复内容"
        
        # 创建回复服务
        reply_service = ReplyService(
            db, MockCozeService(), "test_bot", "test_user"
        )
        
        # 尝试获取锁并处理任务
        reply = db.query(CommentReply).filter(CommentReply.id == reply_id).first()
        if reply:
            success = reply_service._process_single_reply_with_lock(reply)
            results[thread_id] = {
                'success': success,
                'timestamp': datetime.now(),
                'thread_id': thread_id
            }
            print(f"线程 {thread_id}: 处理结果 = {success}")
        else:
            results[thread_id] = {
                'success': False,
                'error': 'Reply not found',
                'thread_id': thread_id
            }
            
    except Exception as e:
        results[thread_id] = {
            'success': False,
            'error': str(e),
            'thread_id': thread_id
        }
        print(f"线程 {thread_id}: 发生异常 = {str(e)}")
    finally:
        if 'db' in locals():
            container.db_service().close_db(db)


def main():
    """主测试函数"""
    print("开始测试回复服务的数据库锁机制...")
    
    # 初始化容器
    container = Container()
    db = container.db_service().get_db()
    
    try:
        # 创建测试数据
        print("创建测试数据...")
        reply_id = create_test_data(db)
        print(f"创建的回复任务ID: {reply_id}")
        
        # 启动多个线程同时处理同一个任务
        print("启动多个线程同时处理同一个任务...")
        threads = []
        results = {}
        
        # 创建3个线程同时处理同一个任务
        for i in range(3):
            thread = threading.Thread(
                target=test_concurrent_processing,
                args=(reply_id, i, results)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 分析结果
        print("\n测试结果分析:")
        success_count = 0
        for thread_id, result in results.items():
            print(f"线程 {thread_id}: {result}")
            if result.get('success'):
                success_count += 1
        
        print(f"\n成功处理的线程数: {success_count}")
        print(f"预期结果: 只有1个线程应该成功处理任务")
        
        if success_count == 1:
            print("✅ 测试通过: 数据库锁机制正常工作")
        else:
            print("❌ 测试失败: 数据库锁机制可能存在问题")
        
        # 检查最终的任务状态
        final_reply = db.query(CommentReply).filter(CommentReply.id == reply_id).first()
        if final_reply:
            print(f"最终任务状态: {final_reply.status}")
        
    except Exception as e:
        print(f"测试过程中发生异常: {str(e)}")
    finally:
        # 清理测试数据
        try:
            db.query(CommentReply).filter(CommentReply.id == reply_id).delete()
            db.commit()
            print("清理测试数据完成")
        except:
            pass
        container.db_service().close_db(db)


if __name__ == "__main__":
    main()
