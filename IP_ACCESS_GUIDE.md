# IP访问控制说明

## 私有IP地址范围（局域网IP）

根据 RFC 1918 标准，以下IP地址段被定义为私有地址：

### IPv4 私有地址段
- **10.0.0.0/8** (10.0.0.0 - **************)
  - 大型企业网络常用
  
- ************/12** (********** - **************)
  - 中型企业网络常用
  - **您的IP ************* 属于此范围**
  
- *************/16** (*********** - ***************)
  - 家庭和小型办公网络常用

### 特殊地址
- ***********/8** (127.0.0.1 等)
  - 回环地址（localhost）

## 当前配置说明

### 默认设置
```bash
ALLOWED_IPS=*************        # 您的公网IP
ALLOW_LOCAL_ACCESS=True          # 允许局域网IP访问
IP_RESTRICTION_ENABLED=True      # 启用IP限制
```

### 访问规则
1. **公网IP *************** → ✅ 允许（在允许列表中）
2. **局域网IP *************** → ✅ 允许（私有IP且允许局域网访问）
3. **其他局域网IP** → ✅ 允许（如果 ALLOW_LOCAL_ACCESS=True）
4. **其他公网IP** → ❌ 拒绝（不在允许列表中）

## 配置选项

### 严格模式（仅允许特定公网IP）
```bash
ALLOWED_IPS=*************
ALLOW_LOCAL_ACCESS=False
IP_RESTRICTION_ENABLED=True
```

### 开发模式（完全禁用IP限制）
```bash
IP_RESTRICTION_ENABLED=False
```

### 多IP支持
```bash
ALLOWED_IPS=*************,*******,*******
```

## 为什么支持局域网IP？

1. **实际网络环境**：在企业网络中，客户端通常通过NAT访问服务器
2. **开发便利性**：本地开发和测试时需要局域网访问
3. **安全性**：局域网IP本身就有一定的安全隔离
4. **灵活性**：可以通过配置控制是否允许局域网访问

## 安全建议

1. **生产环境**：建议设置 `ALLOW_LOCAL_ACCESS=False`，只允许特定公网IP
2. **开发环境**：可以设置 `ALLOW_LOCAL_ACCESS=True` 或完全禁用IP限制
3. **测试环境**：根据网络架构灵活配置

## 日志示例

```
[INFO] IP访问限制配置: 启用=True, 允许局域网=True, 允许IP列表=['*************']
[INFO] 客户端IP访问检查: ************* (原始: *************, 局域网: True) 尝试访问 get_command
[INFO] 局域网IP访问允许: *************
``` 