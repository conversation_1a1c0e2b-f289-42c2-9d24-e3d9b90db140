#!/usr/bin/env python3
"""
修复首次启用标记脚本

用于为已导入但缺少首次启用标记的禁用视频重新添加标记
"""

from sqlalchemy.orm import Session
from app.models.video import VideoDetail
from app.services.video_first_enable_service import VideoFirstEnableService
from app.core.redis_client import get_redis_client
from app.core.database import get_db
import sys
import os
import logging
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def find_disabled_videos_without_marks(
        db: Session,
        first_enable_service: VideoFirstEnableService) -> List[VideoDetail]:
    """查找禁用状态但缺少首次启用标记的视频"""
    # 获取所有禁用的视频
    disabled_videos = db.query(VideoDetail).filter(
        VideoDetail.is_disabled).all()

    videos_without_marks = []

    for video in disabled_videos:
        # 检查是否存在首次启用标记
        key = first_enable_service.first_enable_key_format.format(
            video_id=video.video_id)
        if not first_enable_service.redis.exists(key):
            videos_without_marks.append(video)

    return videos_without_marks


def fix_first_enable_marks(
        videos: List[VideoDetail],
        first_enable_service: VideoFirstEnableService) -> dict:
    """为视频添加首次启用标记"""
    results = {
        'success': [],
        'failed': []
    }

    for video in videos:
        try:
            if first_enable_service.mark_video_for_first_enable(
                    video.video_id):
                results['success'].append(video.video_id)
                logger.info(f"✓ 为视频 {video.video_id} 添加首次启用标记成功")
            else:
                results['failed'].append(video.video_id)
                logger.error(f"✗ 为视频 {video.video_id} 添加首次启用标记失败")
        except Exception as e:
            results['failed'].append(video.video_id)
            logger.error(f"✗ 为视频 {video.video_id} 添加首次启用标记时发生异常: {str(e)}")

    return results


def main():
    """主函数"""
    print("🔧 首次启用标记修复工具")
    print("=" * 50)

    try:
        # 初始化数据库连接
        db = next(get_db())

        # 初始化Redis连接
        redis_client = get_redis_client()

        # 初始化服务
        first_enable_service = VideoFirstEnableService(redis_client)

        print("🔍 正在查找缺少首次启用标记的禁用视频...")

        # 查找需要修复的视频
        videos_without_marks = find_disabled_videos_without_marks(
            db, first_enable_service)

        if not videos_without_marks:
            print("✅ 所有禁用视频都已有首次启用标记，无需修复")
            return

        print(f"📋 找到 {len(videos_without_marks)} 个需要修复的视频:")
        for video in videos_without_marks:
            print(f"   - {video.video_id}: {video.title}")

        # 询问用户确认
        confirm = input(
            f"\n❓ 是否为这 {
                len(videos_without_marks)} 个视频添加首次启用标记? (y/N): ")
        if confirm.lower() != 'y':
            print("❌ 操作已取消")
            return

        print("\n🔧 正在修复首次启用标记...")

        # 执行修复
        results = fix_first_enable_marks(
            videos_without_marks, first_enable_service)

        # 输出结果
        print("\n📊 修复结果:")
        print(f"   ✅ 成功: {len(results['success'])} 个")
        print(f"   ❌ 失败: {len(results['failed'])} 个")

        if results['failed']:
            print("\n❌ 修复失败的视频:")
            for video_id in results['failed']:
                print(f"   - {video_id}")

        if results['success']:
            print(f"\n🎉 成功为 {len(results['success'])} 个视频添加了首次启用标记")
            print("   这些视频在下次启用时将被正确识别为首次启用")

    except Exception as e:
        logger.error(f"修复过程中发生错误: {str(e)}")
        print(f"❌ 修复失败: {str(e)}")
    finally:
        if 'db' in locals():
            db.close()


if __name__ == "__main__":
    main()
