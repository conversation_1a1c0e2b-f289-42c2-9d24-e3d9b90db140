#!/usr/bin/env python3
"""
测试API导入功能的脚本
"""

import os
import sys
import requests

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app.core.config import settings  # noqa: E402


def test_api_connection():
    """测试API连接"""
    api_base_url = (
        f"http://{settings.HOST}:{settings.PORT}{settings.API_V1_STR}"
    )
    health_url = f"http://{settings.HOST}:{settings.PORT}/health"
    videos_url = f"{api_base_url}/videos/"

    print("测试API连接...")
    print(f"API基础URL: {api_base_url}")
    print(f"健康检查URL: {health_url}")
    print(f"视频API URL: {videos_url}")
    print("-" * 50)

    try:
        # 测试健康检查
        print("1. 测试健康检查接口...")
        response = requests.get(health_url, timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查接口正常")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 健康检查接口异常: {response.status_code}")
            return False

        # 测试视频列表接口
        print("\n2. 测试视频列表接口...")
        response = requests.get(videos_url, timeout=10)
        if response.status_code == 200:
            print("✅ 视频列表接口正常")
            data = response.json()
            print(f"   当前视频数量: {data.get('total', 0)}")
        else:
            print(f"❌ 视频列表接口异常: {response.status_code}")
            print(f"   错误信息: {response.text}")

        return True

    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        print("   请确保API服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("API导入功能测试")
    print("=" * 60)

    if test_api_connection():
        print("\n🎉 API连接测试通过！")
        print("现在可以使用 import_excel_data.py 脚本通过API导入数据了")
    else:
        print("\n❌ API连接测试失败！")
        print("请先启动API服务器，然后再运行导入脚本")

    print("\n使用方法:")
    print("1. 确保API服务器正在运行")
    print("2. 将Excel文件放在项目根目录，命名为 imp_data.xlsx")
    print("3. 运行: python scripts/import_excel_data.py")


if __name__ == "__main__":
    main() 