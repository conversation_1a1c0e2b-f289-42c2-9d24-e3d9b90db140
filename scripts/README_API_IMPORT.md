# Excel数据API导入功能说明

## 概述

`import_excel_data.py` 脚本已经修改为通过API接口的方式导入Excel数据到数据库，而不是直接操作数据库。

## 主要变化

### 1. 导入方式变更
- **之前**: 直接使用 `VideoService` 操作数据库
- **现在**: 通过HTTP API调用 `/api/v1/videos/` 接口

### 2. 新增功能
- API连接测试
- 更好的错误处理
- 网络超时处理
- 连接失败重试机制

### 3. 保持的功能
- Excel数据解析
- 用户验证（仍需要数据库连接来验证用户存在）
- 数据清理和转换
- 详细的日志记录
- 错误统计和报告

## 使用方法

### 1. 启动API服务器

首先确保API服务器正在运行：

```bash
# 方法1: 直接运行
python run.py

# 方法2: 使用模块方式
python -m app

# 方法3: 使用Docker
docker-compose up -d
```

### 2. 测试API连接

运行测试脚本确认API服务器可用：

```bash
python scripts/test_api_import.py
```

预期输出：
```
============================================================
API导入功能测试
============================================================
测试API连接...
API基础URL: http://0.0.0.0:8000/api/v1
健康检查URL: http://0.0.0.0:8000/health
视频API URL: http://0.0.0.0:8000/api/v1/videos/
--------------------------------------------------
1. 测试健康检查接口...
✅ 健康检查接口正常
   响应: {'status': 'ok', 'service': 'wecom-command-bridge', 'timestamp': **********}

2. 测试视频列表接口...
✅ 视频列表接口正常
   当前视频数量: 0

🎉 API连接测试通过！
现在可以使用 import_excel_data.py 脚本通过API导入数据了
```

### 3. 准备Excel文件

将要导入的Excel文件放在项目根目录，命名为 `imp_data.xlsx`。

Excel文件应包含以下列（支持中英文列名）：

| 英文列名 | 中文列名 | 必填 | 说明 |
|---------|---------|------|------|
| UID | UID | ✅ | B站用户ID |
| video_url | 链接/视频链接 | ✅ | B站视频URL |
| title | 标题/视频标题 | ✅ | 视频标题 |
| video_content | 内容描述/视频内容 | ❌ | 视频内容描述 |
| video_script | 文案/视频文案 | ❌ | 视频文案 |
| video_summary | 总结/视频总结 | ❌ | 视频总结 |
| reply_setting | 回复设定 | ❌ | 回复设定 |
| comment_control_plan | 控评方案 | ❌ | 控评方案 |
| category | 品类/所属品类 | ❌ | 视频分类 |
| promoted_products | 主推产品/产品推荐策略 | ❌ | 主推产品信息 |
| common_questions | 常见问题 | ❌ | 常见问题 |

### 4. 运行导入脚本

```bash
python scripts/import_excel_data.py
```

## API接口说明

### 创建视频接口

- **URL**: `POST /api/v1/videos/`
- **Content-Type**: `application/json`
- **请求体**: VideoCreate对象的JSON格式

### 响应状态码

- `201`: 创建成功
- `409`: 视频ID已存在（会被跳过）
- `400`: 请求数据无效
- `500`: 服务器内部错误

## 错误处理

脚本会处理以下类型的错误：

1. **API连接错误**: 无法连接到API服务器
2. **超时错误**: API请求超时（30秒）
3. **重复数据**: 视频ID已存在（自动跳过）
4. **数据验证错误**: Excel数据格式不正确
5. **用户不存在**: UID在数据库中不存在

## 日志记录

脚本会记录详细的操作日志，包括：

- API连接状态
- 每行数据的处理结果
- 错误详情
- 导入统计信息

## 优势

### 1. 解耦合
- 脚本不再直接依赖数据库连接
- 可以在不同的环境中运行
- 更容易部署和维护

### 2. 一致性
- 使用与前端相同的API接口
- 确保数据验证逻辑一致
- 自动触发相关的业务逻辑（如首次启用标记）

### 3. 可扩展性
- 可以轻松添加认证机制
- 支持负载均衡
- 可以监控API调用

### 4. 安全性
- 通过API层进行数据验证
- 可以添加访问控制
- 减少直接数据库访问

## 注意事项

1. **API服务器必须运行**: 导入前确保API服务器可用
2. **网络连接**: 确保脚本运行环境可以访问API服务器
3. **用户验证**: 脚本仍需要数据库连接来验证用户存在
4. **首次启用**: 首次启用标记将在视频首次启用时自动创建，而不是在导入时

## 故障排除

### 1. API连接失败
```
❌ 无法连接到API服务器: http://0.0.0.0:8000/api/v1
```
**解决方案**: 检查API服务器是否运行，端口是否正确

### 2. 用户不存在
```
⚠️ 跳过行：用户ID 123456 不存在于数据库中
```
**解决方案**: 确保用户已经在数据库中存在

### 3. 视频ID重复
```
⚠️ 视频ID已存在，跳过: BV**********
```
**解决方案**: 这是正常行为，重复的视频会被自动跳过

### 4. Excel文件格式错误
```
❌ 跳过行：缺少标题
```
**解决方案**: 检查Excel文件格式，确保必填字段不为空 