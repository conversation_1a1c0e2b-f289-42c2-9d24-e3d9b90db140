#!/usr/bin/env python3
"""
检查首次启用标记状态脚本

用于检查指定视频或所有禁用视频的首次启用标记状态
"""

from sqlalchemy.orm import Session
from app.models.video import VideoDetail
from app.services.video_first_enable_service import VideoFirstEnableService
from app.core.redis_client import get_redis_client
from app.core.database import get_db
import sys
import os
import argparse
import logging
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_video_first_enable_status(
        video_id: str,
        first_enable_service: VideoFirstEnableService,
        db: Session) -> dict:
    """检查单个视频的首次启用状态"""
    # 从数据库获取视频信息
    video = db.query(VideoDetail).filter(
        VideoDetail.video_id == video_id).first()

    if not video:
        return {
            'video_id': video_id,
            'exists': False,
            'error': '视频不存在'
        }

    # 检查Redis中的首次启用标记
    key = first_enable_service.first_enable_key_format.format(
        video_id=video_id)
    has_mark = first_enable_service.redis.exists(key)

    return {
        'video_id': video_id,
        'exists': True,
        'title': video.title,
        'is_disabled': video.is_disabled,
        'has_first_enable_mark': bool(has_mark),
        'status': 'normal' if (
            video.is_disabled and has_mark) or (
            not video.is_disabled and not has_mark) else 'abnormal'}


def check_all_disabled_videos(
        first_enable_service: VideoFirstEnableService,
        db: Session) -> List[dict]:
    """检查所有禁用视频的首次启用状态"""
    disabled_videos = db.query(VideoDetail).filter(
        VideoDetail.is_disabled).all()

    results = []
    for video in disabled_videos:
        status = check_video_first_enable_status(
            video.video_id, first_enable_service, db)
        results.append(status)

    return results


def print_status_report(results: List[dict]):
    """打印状态报告"""
    if not results:
        print("📭 没有找到任何视频")
        return

    normal_count = sum(1 for r in results if r.get('status') == 'normal')
    abnormal_count = sum(1 for r in results if r.get('status') == 'abnormal')

    print("📊 检查结果统计:")
    print(f"   ✅ 正常: {normal_count} 个")
    print(f"   ⚠️  异常: {abnormal_count} 个")
    print(f"   📝 总计: {len(results)} 个")

    if abnormal_count > 0:
        print("\n⚠️  异常视频详情:")
        for result in results:
            if result.get('status') == 'abnormal':
                video_id = result['video_id']
                is_disabled = result['is_disabled']
                has_mark = result['has_first_enable_mark']
                title = result.get('title', 'N/A')

                if is_disabled and not has_mark:
                    issue = "禁用状态但缺少首次启用标记"
                elif not is_disabled and has_mark:
                    issue = "启用状态但仍有首次启用标记"
                else:
                    issue = "未知异常"

                print(f"   🔸 {video_id}: {issue}")
                print(f"      标题: {title}")
                print(f"      状态: {'禁用' if is_disabled else '启用'}")
                print(f"      标记: {'有' if has_mark else '无'}")
                print()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='检查视频首次启用标记状态')
    parser.add_argument('--video-id', '-v', help='指定要检查的视频ID')
    parser.add_argument('--all', '-a', action='store_true', help='检查所有禁用视频')
    parser.add_argument('--verbose', action='store_true', help='显示详细信息')

    args = parser.parse_args()

    if not args.video_id and not args.all:
        parser.print_help()
        return

    print("🔍 首次启用标记状态检查工具")
    print("=" * 50)

    try:
        # 初始化数据库连接
        db = next(get_db())

        # 初始化Redis连接
        redis_client = get_redis_client()

        # 初始化服务
        first_enable_service = VideoFirstEnableService(redis_client)

        if args.video_id:
            # 检查指定视频
            print(f"🎯 检查视频: {args.video_id}")
            result = check_video_first_enable_status(
                args.video_id, first_enable_service, db)

            if not result['exists']:
                print(f"❌ {result['error']}")
                return

            print("\n📋 视频信息:")
            print(f"   ID: {result['video_id']}")
            print(f"   标题: {result['title']}")
            print(f"   状态: {'禁用' if result['is_disabled'] else '启用'}")
            mark_text = '有' if result['has_first_enable_mark'] else '无'
            print(f"   首次启用标记: {mark_text}")
            status_text = '✅ 正常' if result['status'] == 'normal' else '⚠️ 异常'
            print(f"   状态评估: {status_text}")

            if result['status'] == 'abnormal':
                if (result['is_disabled'] and
                        not result['has_first_enable_mark']):
                    print("\n💡 建议: 该视频处于禁用状态但缺少首次启用标记，"
                          "可以运行修复脚本添加标记")
                elif (not result['is_disabled'] and
                      result['has_first_enable_mark']):
                    print("\n💡 建议: 该视频已启用但仍有首次启用标记，"
                          "这可能是异常情况")

        elif args.all:
            # 检查所有禁用视频
            print("🔍 检查所有禁用视频的首次启用标记状态...")
            results = check_all_disabled_videos(first_enable_service, db)

            print_status_report(results)

            if args.verbose:
                print("\n📝 详细信息:")
                for result in results:
                    status_icon = ("✅" if result['status'] == 'normal'
                                   else "⚠️")
                    mark_status = ("有" if result['has_first_enable_mark']
                                   else "无")
                    print(f"   {status_icon} {result['video_id']}: "
                          f"标记={mark_status}")

    except Exception as e:
        logger.error(f"检查过程中发生错误: {str(e)}")
        print(f"❌ 检查失败: {str(e)}")
    finally:
        if 'db' in locals():
            db.close()


if __name__ == "__main__":
    main()
