#!/usr/bin/env python3
"""
数据库初始化脚本

用于独立运行数据库初始化，检查和创建wecom_bridge数据库及表。
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入必须在路径设置之后
from app.utils.db_init import init_database  # noqa: E402


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger("init_database")
    
    logger.info("=" * 50)
    logger.info("开始数据库初始化...")
    logger.info("=" * 50)
    
    # 获取SQL文件路径
    sql_file_path = os.path.join(project_root, "sql", "table.sql")
    
    if os.path.exists(sql_file_path):
        logger.info(f"找到SQL文件: {sql_file_path}")
        success = init_database(sql_file_path)
    else:
        logger.warning(f"SQL文件不存在: {sql_file_path}")
        logger.info("将只进行基本的数据库和表检查...")
        success = init_database()
    
    logger.info("=" * 50)
    if success:
        logger.info("✅ 数据库初始化成功！")
        print("\n🎉 数据库初始化完成！")
        print("📋 检查内容:")
        print("   - wecom_bridge 数据库")
        print("   - bili_users 表")
        print("   - comment_records 表") 
        print("   - comment_replies 表")
        print("   - video_details 表")
    else:
        logger.error("❌ 数据库初始化失败！")
        print("\n💥 数据库初始化失败！")
        print("请检查:")
        print("   - MySQL服务是否运行")
        print("   - 数据库连接配置是否正确")
        print("   - 用户权限是否足够")
        sys.exit(1)
    
    logger.info("=" * 50)


if __name__ == "__main__":
    main() 