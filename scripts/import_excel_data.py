#!/usr/bin/env python3
"""
Excel数据导入脚本

从根目录的imp_data.xlsx文件导入数据到video_details表
通过API接口方式入库
"""

import os
import sys
import logging
import pandas as pd
import requests
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入必须在路径设置之后
from app.core.config import settings  # noqa: E402
from app.schemas.video import VideoCreate  # noqa: E402

# API配置
API_BASE_URL = f"http://wecom.m-zgtx.com{settings.API_V1_STR}"
VIDEOS_API_URL = f"{API_BASE_URL}/videos/"

# 用户ID映射：数据库主键ID -> B站用户ID
USER_ID_MAPPING = {
    2: "3494376470743898",
    3: "1628877491", 
    4: "454219515"
}


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger("import_excel_data")


def clean_data(value: Any) -> Optional[str]:
    """清理数据，处理NaN和空值"""
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return None
    return str(value).strip()


def extract_video_id_from_url(url: str) -> Optional[str]:
    """从B站视频URL中提取视频ID"""
    if not url:
        return None

    import re
    # 匹配B站视频URL中的BV号
    patterns = [
        r'BV[a-zA-Z0-9]+',  # 直接匹配BV号
        r'/video/(BV[a-zA-Z0-9]+)',  # 从完整URL中提取
        r'bilibili\.com/video/(BV[a-zA-Z0-9]+)',  # 从bilibili.com URL中提取
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            if pattern.startswith('BV'):
                return match.group(0)
            else:
                return match.group(1)

    return None


def clean_product_text(text: str) -> str:
    """清理产品文本，去掉无意义的符号"""
    if not text:
        return ""

    import re
    # 去掉 ###, $, **, - 等符号
    text = re.sub(r'^###\s*', '', text)  # 去掉开头的###
    text = re.sub(r'\s*\$$', '', text)   # 去掉结尾的$
    text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # 去掉**包围的内容，保留文字
    text = re.sub(r'^-\s*', '', text, flags=re.MULTILINE)  # 去掉行首的-
    text = re.sub(r'^\s*-\s*', '', text, flags=re.MULTILINE)  # 去掉行首的空格和-

    return text.strip()


def parse_promoted_products(products_str: str) -> Optional[list]:
    """解析主推产品字符串为JSON格式

    支持的格式：
    1. JSON格式：直接解析
    2. 多产品换行格式：每个产品以###开头，后续行为详情
    3. 简单格式：逗号分隔或单个产品
    """
    if not products_str:
        return None

    try:
        import json
        import re

        # 清理输入字符串
        products_str = products_str.strip()

        # 情况1: 如果已经是JSON格式，直接解析
        if products_str.startswith('[') and products_str.endswith(']'):
            try:
                return json.loads(products_str)
            except json.JSONDecodeError:
                pass

        # 情况2: 处理多产品换行格式
        # 检查是否包含多个###标记的产品
        if '###' in products_str:
            products = []

            # 按行分割
            lines = products_str.split('\n')
            current_product = None
            current_reason_lines = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检查是否是新产品的开始（以###开头）
                if line.startswith('###'):
                    # 保存前一个产品
                    if current_product:
                        reason = '\n'.join(current_reason_lines).strip()
                        reason = clean_product_text(reason)
                        products.append({
                            "name": current_product,
                            "reason": reason
                        })

                    # 开始新产品
                    current_product = clean_product_text(line)
                    current_reason_lines = []
                else:
                    # 这是当前产品的详情行
                    if current_product:
                        current_reason_lines.append(line)

            # 保存最后一个产品
            if current_product:
                reason = '\n'.join(current_reason_lines).strip()
                reason = clean_product_text(reason)
                products.append({
                    "name": current_product,
                    "reason": reason
                })

            return products if products else None

        # 情况3: 逗号分隔的产品列表
        if ',' in products_str:
            products = []
            product_list = [p.strip()
                            for p in products_str.split(',') if p.strip()]
            for product in product_list:
                # 检查是否包含详细描述
                if '(' in product and ')' in product:
                    match = re.match(r'^([^(]+)', product)
                    if match:
                        name = match.group(1).strip()
                        products.append({"name": name, "reason": product})
                    else:
                        products.append({"name": product, "reason": ""})
                else:
                    products.append({"name": product, "reason": ""})
            return products if products else None

        # 情况4: 单个产品
        # 检查是否包含详细描述
        if '(' in products_str and ')' in products_str:
            match = re.match(r'^([^(]+)', products_str)
            if match:
                name = match.group(1).strip()
                return [{"name": name, "reason": products_str}]

        # 默认情况：直接作为产品名称
        return [{"name": products_str.strip(), "reason": ""}]

    except Exception as e:
        logging.warning(f"解析主推产品失败: {products_str}, 错误: {e}")
        # 发生错误时，至少尝试提取基本的产品名称
        try:
            # 尝试提取最基本的产品名称
            if products_str.startswith('###'):
                product_name = clean_product_text(products_str)
                return [{"name": product_name, "reason": ""}]
            else:
                return [{"name": products_str.strip(), "reason": ""}]
        except Exception:
            return None


def convert_excel_row_to_video_create(
    row: pd.Series, logger: logging.Logger
) -> Optional[VideoCreate]:
    """将Excel行数据转换为VideoCreate对象"""
    try:
        # 从Excel中获取UID（B站用户ID）
        user_id_raw = row.get('UID')
        if pd.isna(user_id_raw) or user_id_raw == '' or str(
                user_id_raw).strip() == '':
            logger.warning("跳过行：缺少UID")
            return None

        # 处理数字格式的UID，去掉小数点
        try:
            if isinstance(user_id_raw, float):
                bilibili_user_id = str(int(user_id_raw))
            else:
                bilibili_user_id = str(user_id_raw).strip()
        except (ValueError, TypeError):
            logger.warning(f"跳过行：UID格式无效: {user_id_raw}")
            return None

        # 根据B站用户ID查找对应的数据库主键ID
        db_user_id = None
        for db_id, bili_id in USER_ID_MAPPING.items():
            if bili_id == bilibili_user_id:
                db_user_id = db_id
                break
        
        if db_user_id is None:
            logger.warning(
                f"跳过行：未找到B站用户ID {bilibili_user_id} "
                f"对应的数据库主键ID"
            )
            return None

        # 获取视频URL并提取视频ID
        video_url = clean_data(
            row.get(
                '视频链接', row.get(
                    'video_url', '')))
        if not video_url:
            logger.warning("跳过行：缺少视频链接")
            return None

        # 从URL中提取视频ID
        video_id = extract_video_id_from_url(video_url)
        if not video_id:
            logger.warning(f"跳过行：无法从URL中提取视频ID: {video_url}")
            return None

        # 处理主推产品（产品推荐策略）
        promoted_products_str = clean_data(
            row.get('产品推荐策略', row.get('promoted_products', ''))
        )
        promoted_products = None
        if promoted_products_str:
            promoted_products = parse_promoted_products(
                promoted_products_str
            )

        # 创建VideoCreate对象，使用数据库主键ID
        video_create = VideoCreate(
            video_id=video_id,
            user_id=db_user_id,  # 使用数据库主键ID
            title=clean_data(
                row.get(
                    '视频标题', row.get(
                        'title', '')))[:256],
            video_content=clean_data(
                row.get(
                    '视频内容', row.get(
                        'video_content', ''))),
            video_url=video_url,
            video_script=clean_data(
                row.get(
                    '视频文案', row.get(
                        'video_script', ''))),
            video_summary=clean_data(
                row.get(
                    '视频总结', row.get(
                        'video_summary', ''))),
            reply_setting=clean_data(
                row.get(
                    '回复设定', row.get(
                        'reply_setting', ''))),
            comment_control_plan=clean_data(
                row.get(
                    '控评方案', row.get(
                        'comment_control_plan', ''))),
            category=clean_data(
                row.get(
                    '所属品类', row.get(
                        'category', ''))),
            promoted_products=promoted_products,  # 使用解析后的产品数据
            common_questions=clean_data(
                row.get(
                    '常见问题', row.get(
                        'common_questions', ''))),
            is_disabled=True  # 默认禁用
        )

        logger.info(
            f"成功转换行数据: B站用户ID={bilibili_user_id} -> "
            f"数据库主键ID={db_user_id}, 视频ID={video_id}"
        )
        return video_create

    except Exception as e:
        logger.error(f"转换Excel行数据失败: {str(e)}")
        return None


def create_video_via_api(
    video_create: VideoCreate,
    logger: logging.Logger
) -> Dict[str, Any]:
    """通过API接口创建视频"""
    try:
        # 将VideoCreate对象转换为字典
        video_data = video_create.model_dump()

        # 发送POST请求到视频创建API
        response = requests.post(
            VIDEOS_API_URL,
            json=video_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        # 检查响应状态
        if response.status_code == 201:
            # 创建成功
            result = response.json()
            video_id = result.get('video_id')
            logger.info(
                f"API创建视频成功: {video_create.title}，video_id: {video_id}"
            )
            return {
                'success': True,
                'data': result,
                'status_code': response.status_code
            }
        elif response.status_code == 409:
            # 视频ID已存在
            logger.warning(f"视频ID已存在，跳过: {video_create.video_id}")
            return {
                'success': False,
                'error': 'duplicate',
                'message': '视频ID已存在',
                'status_code': response.status_code
            }
        else:
            # 其他错误
            error_detail = response.text
            try:
                error_json = response.json()
                error_detail = error_json.get('detail', error_detail)
            except (ValueError, KeyError):
                pass

            logger.error(f"API创建视频失败: {error_detail}")
            return {
                'success': False,
                'error': 'api_error',
                'message': error_detail,
                'status_code': response.status_code
            }

    except requests.exceptions.Timeout:
        error_msg = "API请求超时"
        logger.error(error_msg)
        return {
            'success': False,
            'error': 'timeout',
            'message': error_msg
        }
    except requests.exceptions.ConnectionError:
        error_msg = "无法连接到API服务器"
        logger.error(error_msg)
        return {
            'success': False,
            'error': 'connection_error',
            'message': error_msg
        }
    except Exception as e:
        error_msg = f"API调用异常: {str(e)}"
        logger.error(error_msg)
        return {
            'success': False,
            'error': 'unknown_error',
            'message': error_msg
        }


def test_api_connection(logger: logging.Logger) -> bool:
    """测试API连接"""
    try:
        # 使用指定的域名进行健康检查
        health_url = "http://wecom.m-zgtx.com/health"
        response = requests.get(health_url, timeout=10)
        if response.status_code == 200:
            logger.info(f"API服务器连接正常: {health_url}")
            return True
        else:
            logger.error(f"API服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"无法连接到API服务器: {str(e)}")
        return False


def import_excel_data(excel_file_path: str,
                      logger: logging.Logger) -> Dict[str, Any]:
    """导入Excel数据到数据库（通过API接口）"""
    results = {
        'total_rows': 0,
        'success_count': 0,
        'failed_count': 0,
        'skipped_count': 0,
        'errors': []
    }

    try:
        # 测试API连接
        logger.info(f"测试API连接: {API_BASE_URL}")
        if not test_api_connection(logger):
            error_msg = f"无法连接到API服务器: {API_BASE_URL}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
            return results

        # 读取Excel文件
        logger.info(f"正在读取Excel文件: {excel_file_path}")
        df = pd.read_excel(excel_file_path)
        results['total_rows'] = len(df)

        logger.info(f"Excel文件包含 {len(df)} 行数据")
        logger.info(f"列名: {list(df.columns)}")

        # 逐行处理数据
        for index, row in df.iterrows():
            try:
                logger.info(f"处理第 {index + 1} 行数据...")

                # 转换为VideoCreate对象
                video_create = convert_excel_row_to_video_create(
                    row, logger)
                if not video_create:
                    results['skipped_count'] += 1
                    continue

                # 通过API创建视频
                api_result = create_video_via_api(video_create, logger)

                if api_result['success']:
                    results['success_count'] += 1
                    logger.info(f"成功导入视频: {video_create.title}")
                elif api_result.get('error') == 'duplicate':
                    results['skipped_count'] += 1
                else:
                    error_msg = (
                        f"第 {index + 1} 行导入失败: "
                        f"{api_result.get('message', '未知错误')}"
                    )
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
                    results['failed_count'] += 1

            except Exception as e:
                error_msg = f"第 {index + 1} 行导入失败: {str(e)}"
                logger.error(error_msg)
                results['errors'].append(error_msg)
                results['failed_count'] += 1

    except Exception as e:
        error_msg = f"读取Excel文件失败: {str(e)}"
        logger.error(error_msg)
        results['errors'].append(error_msg)

    return results


def main():
    """主函数"""
    logger = setup_logging()

    logger.info("=" * 60)
    logger.info("开始导入Excel数据到video_details表（通过API接口）...")
    logger.info(f"API服务器地址: {API_BASE_URL}")
    logger.info("=" * 60)

    # Excel文件路径
    excel_file_path = os.path.join(project_root, "imp_data.xlsx")

    # 检查文件是否存在
    if not os.path.exists(excel_file_path):
        logger.error(f"Excel文件不存在: {excel_file_path}")
        print(f"\n❌ Excel文件不存在: {excel_file_path}")
        sys.exit(1)

    # 执行导入
    results = import_excel_data(excel_file_path, logger)

    # 输出结果
    logger.info("=" * 60)
    logger.info("导入完成！")
    logger.info(f"总行数: {results['total_rows']}")
    logger.info(f"成功导入: {results['success_count']}")
    logger.info(f"跳过行数: {results['skipped_count']}")
    logger.info(f"失败行数: {results['failed_count']}")

    if results['errors']:
        logger.info("错误详情:")
        for error in results['errors']:
            logger.error(f"  - {error}")

    print("\n📊 导入结果:")
    print(f"   总行数: {results['total_rows']}")
    print(f"   ✅ 成功: {results['success_count']}")
    print(f"   ⏭️  跳过: {results['skipped_count']}")
    print(f"   ❌ 失败: {results['failed_count']}")

    if results['success_count'] > 0:
        print(f"\n🎯 成功导入 {results['success_count']} 个视频（通过API接口）")
        print("💡 首次启用标记将在视频首次启用时自动创建")

    if results['errors']:
        print("\n⚠️  错误详情:")
        for error in results['errors'][:5]:  # 只显示前5个错误
            print(f"   - {error}")
        if len(results['errors']) > 5:
            print(f"   ... 还有 {len(results['errors']) - 5} 个错误")

    logger.info("=" * 60)


if __name__ == "__main__":
    main()
