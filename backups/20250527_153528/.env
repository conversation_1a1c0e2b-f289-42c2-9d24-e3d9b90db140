# 企业微信命令桥接服务环境配置文件
# 请根据实际环境修改以下配置

# ==================== 应用基础设置 ====================
DEBUG=True
HOST=0.0.0.0
PORT=8000

# ==================== 企业微信配置 ====================
# 企业微信回调验证Token
WECOM_TOKEN=4NhQgi1clESpIVhcsQjm6w8y

# 企业微信消息加密密钥
WECOM_ENCODING_AES_KEY=LDxKOE694Wny5PFy1TLiU9TTmwAuQWPYIb9GibrumoK

# 企业微信企业ID
WECOM_CORP_ID=ww3ca907d340c10530

# 企业微信应用ID
WECOM_AGENT_ID=1000015

# 企业微信应用Secret
WECOM_SECRET=WswthjH3xrXCP6h4rL1H5MXI2wLl-YELCHX_KaDc8mM

# ==================== 文件上传配置 ====================
# 文件上传目录
UPLOADS_DIR=uploads

# 最大文件大小 (字节，默认16MB)
MAX_CONTENT_LENGTH=16777216

# ==================== Redis 配置 ====================
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_PREFIX=wecom:

# ==================== MySQL 数据库配置 ====================
DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=wecom_bridge
DB_USER=root
DB_PASSWORD=rootpassword

# ==================== Coze API配置 ====================
# Coze API访问令牌
COZE_API_TOKEN=pat_4aAwaGOJMK9z2qquHd1T6ZonzzJZ751HjuilGtvIVTRAIepEWval5U6oX9SwF6dE

# Coze机器人ID
COZE_BOT_ID=

# Coze用户ID
COZE_USER_ID=user123

# ==================== 评论任务配置 ====================
# 未回复评论检查间隔 (秒，0表示禁用)
COMMENT_UNREPLIED_INTERVAL=0

# 待回复评论检查间隔 (秒，0表示禁用)
COMMENT_PENDING_INTERVAL=0

# ==================== 前端静态资源配置 ====================
# 前端静态文件目录
FRONTEND_STATIC_DIR=app/static

# ==================== 日志配置 ====================
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=logs/app.log

# 是否回复评论
IS_REPLY=FALSE