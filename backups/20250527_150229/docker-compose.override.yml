services:
  wecom-app:
    image: *************:5000/wecom-app:latest
    container_name: wecom-bridge-app
    environment:
      - DB_HOST=shared-mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=rootpassword
      - DB_NAME=wecom_bridge
      - REDIS_HOST=shared-redis
      - REDIS_PORT=6379
    ports:
      - "8000:8000"
    networks:
      - default
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

networks:
  default:
    name: wecom-bridge-network
