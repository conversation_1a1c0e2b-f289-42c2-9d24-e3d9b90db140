#!/bin/bash

# 企业微信命令桥接服务部署脚本 (优化版本 v2.1 - macOS兼容)
# 优化内容：安全性、代码结构、错误处理、配置管理、健康检查
# 兼容 bash 3.2+ (macOS 默认版本)

set -eo pipefail

# 脚本版本信息
SCRIPT_VERSION="2.1.0"
SCRIPT_NAME="企业微信命令桥接服务部署脚本"

# 颜色配置
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly RED='\033[0;31m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 日志配置
readonly LOG_FILE="deploy.log"

# 配置文件路径
readonly CONFIG_FILE=".deploy.env"
readonly COMPOSE_OVERRIDE="docker-compose.override.yml"

# 配置变量（替代关联数组）
REGISTRY_SERVER=""
REGISTRY_USERNAME=""
REGISTRY_PASSWORD=""
DEFAULT_IMAGE="wecom-app:latest"
LOCAL_IMAGE_NAME="wecom-app:local"
MYSQL_HOST="shared-mysql"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD=""
MYSQL_DB="wecom_bridge"
REDIS_HOST="shared-redis"
REDIS_PORT="6379"
APP_PORT="8000"
# 健康检查相关配置已移除
CONTAINER_NAME="wecom-bridge-app"

# 自动网络连接配置
AUTO_CONNECT_NETWORK="deploy_default" # 自动连接的网络名称，留空则不自动连接

# 全局变量
VERBOSE=false
DRY_RUN=false

# 在初始化完成后启用严格模式
set -u

# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    local color=""
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    case "$level" in
    "INFO") color="$GREEN" ;;
    "WARN") color="$YELLOW" ;;
    "ERROR") color="$RED" ;;
    "DEBUG") color="$CYAN" ;;
    *) color="$NC" ;;
    esac

    echo -e "${color}[$timestamp][$level] $message${NC}"
    # 同时写入日志文件，但不使用tee避免可能的问题
    echo "[$timestamp][$level] $message" >>"$LOG_FILE"
}

info() { log "INFO" "$@"; }
warn() { log "WARN" "$@"; }
error() { log "ERROR" "$@"; }
debug() { [ "$VERBOSE" = true ] && log "DEBUG" "$@" || true; }

# 进度指示器
show_progress() {
    local task="$1"
    local current="$2"
    local total="$3"
    local percent=$((current * 100 / total))

    echo "${task}... [$current/$total] $percent%"
}

# 错误处理函数
handle_error() {
    local exit_code=$?
    local line_no=$1
    error "脚本在第 $line_no 行发生错误，退出码: $exit_code"
    cleanup_on_error
    exit $exit_code
}

trap 'handle_error $LINENO' ERR

# 清理函数
cleanup_on_error() {
    warn "执行清理操作..."
    # 如果有临时文件，在这里清理
    [ -f "$COMPOSE_OVERRIDE.tmp" ] && rm -f "$COMPOSE_OVERRIDE.tmp"
}

# 配置管理
load_config() {
    info "加载配置..."

    # 从环境变量加载配置
    [ -n "${REGISTRY_SERVER:-}" ] && debug "从环境变量加载: REGISTRY_SERVER"
    [ -n "${REGISTRY_USERNAME:-}" ] && debug "从环境变量加载: REGISTRY_USERNAME"
    [ -n "${REGISTRY_PASSWORD:-}" ] && debug "从环境变量加载: REGISTRY_PASSWORD"
    [ -n "${MYSQL_PASSWORD:-}" ] && debug "从环境变量加载: MYSQL_PASSWORD"

    # 从配置文件加载
    if [ -f "$CONFIG_FILE" ]; then
        info "加载配置文件: $CONFIG_FILE"
        # 临时禁用严格模式来安全地source配置文件
        set +u
        # shellcheck source=/dev/null
        source "$CONFIG_FILE"
        set -u
        debug "配置文件加载完成"
    fi
}

# 配置验证
validate_config() {
    local errors=0

    info "验证配置..."

    # 检查必需的配置
    if [ -z "$MYSQL_PASSWORD" ]; then
        error "缺少必需配置: MYSQL_PASSWORD"
        ((errors++))
    fi

    # 验证端口号
    for port_name in "MYSQL_PORT" "REDIS_PORT" "APP_PORT"; do
        case "$port_name" in
        "MYSQL_PORT") port="$MYSQL_PORT" ;;
        "REDIS_PORT") port="$REDIS_PORT" ;;
        "APP_PORT") port="$APP_PORT" ;;
        esac

        if ! echo "$port" | grep -q '^[0-9]\+$' || [ "$port" -lt 1 ] || [ "$port" -gt 65535 ]; then
            error "无效的端口号: $port_name=$port"
            ((errors++))
        fi
    done

    if [ $errors -gt 0 ]; then
        error "配置验证失败，请检查配置"
        exit 1
    fi

    info "配置验证通过"
}

# 创建示例配置文件
create_sample_config() {
    if [ -f "$CONFIG_FILE" ]; then
        warn "配置文件已存在: $CONFIG_FILE"
        return 0
    fi

    info "创建示例配置文件: $CONFIG_FILE"

    cat >"$CONFIG_FILE" <<'EOF'
# 企业微信命令桥接服务配置文件
# 请根据实际环境修改以下配置

# 私有仓库配置
REGISTRY_SERVER="your-registry.com:5000"
REGISTRY_USERNAME="your-username"
REGISTRY_PASSWORD="your-password"

# 镜像配置
DEFAULT_IMAGE="wecom-app:latest"
LOCAL_IMAGE_NAME="wecom-app:local"

# 数据库配置
MYSQL_HOST="shared-mysql"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD="your-mysql-password"
MYSQL_DB="wecom_bridge"

# Redis配置
REDIS_HOST="shared-redis"
REDIS_PORT="6379"

# 应用配置
APP_PORT="8000"
CONTAINER_NAME="wecom-bridge-app"

# 健康检查配置
# 健康检查相关配置已移除

# 自动网络连接配置
AUTO_CONNECT_NETWORK="deploy_default"  # 自动连接的网络名称，留空则不自动连接
EOF

    warn "请编辑 $CONFIG_FILE 文件，填入正确的配置信息"
    info "配置文件创建完成: $CONFIG_FILE"
}

# 系统环境检查
check_prerequisites() {
    # 暂时禁用严格模式，避免可能的变量问题
    set +u

    info "检查系统环境..."

    local errors=0

    # 检查必需命令
    for cmd in "docker" "docker-compose"; do
        debug "检查命令: $cmd"
        if ! command -v "$cmd" >/dev/null 2>&1; then
            error "未找到必需命令: $cmd"
            ((errors++))
        else
            debug "找到命令: $cmd"
        fi
    done

    # 检查Docker daemon状态 - 使用兼容的超时方法
    debug "检查Docker daemon状态..."
    if command -v timeout >/dev/null 2>&1; then
        # Linux系统，使用timeout命令
        if ! timeout 10 docker info >/dev/null 2>&1; then
            error "Docker daemon未运行或响应超时"
            error "请确保Docker已启动并正常运行"
            ((errors++))
        else
            debug "Docker daemon运行正常"
        fi
    else
        # macOS系统，使用简单检查
        debug "使用简单Docker检查方法 (macOS兼容)"
        if ! docker info >/dev/null 2>&1; then
            error "Docker daemon未运行"
            error "请确保Docker已启动并正常运行"
            ((errors++))
        else
            debug "Docker daemon运行正常"
        fi
    fi

    # 检查docker-compose.yml文件
    debug "检查docker-compose.yml文件..."
    if [ ! -f "docker-compose.yml" ]; then
        warn "未找到docker-compose.yml文件 (部分功能可能不可用)"
        debug "docker-compose.yml文件不存在，但不影响基本功能"
    else
        debug "找到docker-compose.yml文件"
    fi

    if [ $errors -gt 0 ]; then
        error "系统环境检查失败"
        # 重新启用严格模式
        set -u
        exit 1
    fi

    info "系统环境检查通过"

    # 重新启用严格模式
    set -u
}

# 创建必要目录
ensure_directories() {
    info "创建必要目录..."

    local dirs="uploads logs backups"
    local current=0
    local total=3

    for dir in $dirs; do
        current=$((current + 1))
        show_progress "创建目录" "$current" "$total"

        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            debug "创建目录: $dir"
        fi
    done

    info "目录创建完成"
}

# 创建.env文件
create_env_file() {
    info "创建应用环境配置文件..."

    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 检查.env文件是否已存在
    if [ -f ".env" ]; then
        local backup_file=".env.backup.$(date +%Y%m%d_%H%M%S)"
        warn ".env 文件已存在，将备份到: $backup_file"
        cp ".env" "$backup_file"

        # 在非交互模式下，不覆盖现有文件
        if [ "$DRY_RUN" = true ]; then
            info "[DRY RUN] 会备份现有 .env 文件并创建新的配置"
            return 0
        fi

        # 询问用户是否要覆盖
        echo -e "${YELLOW}检测到现有的 .env 文件。${NC}"
        echo "1) 保留现有文件 (推荐)"
        echo "2) 覆盖为新配置"
        echo "3) 查看现有文件内容"

        while true; do
            read -p "请选择操作 [1-3]: " choice
            case $choice in
            1)
                info "保留现有 .env 文件"
                return 0
                ;;
            2)
                warn "将覆盖现有 .env 文件"
                break
                ;;
            3)
                echo -e "\n${CYAN}=== 现有 .env 文件内容 ===${NC}"
                cat .env
                echo -e "${CYAN}=========================${NC}\n"
                ;;
            *)
                echo "请输入 1、2 或 3"
                ;;
            esac
        done
    fi

    cat >.env <<EOF
# 应用基础配置
DEBUG=False
HOST=0.0.0.0
PORT=${APP_PORT}

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 数据库配置
DB_HOST=${MYSQL_HOST}
DB_PORT=${MYSQL_PORT}
DB_USER=${MYSQL_USER}
DB_PASSWORD=${MYSQL_PASSWORD}
DB_NAME=${MYSQL_DB}

# Redis配置
REDIS_HOST=${REDIS_HOST}
REDIS_PORT=${REDIS_PORT}

# 生成时间
GENERATED_AT=${timestamp}
EOF

    info "应用环境配置文件创建完成"
}

# Docker登录
docker_login() {
    if [ -z "$REGISTRY_SERVER" ] || [ -z "$REGISTRY_USERNAME" ] || [ -z "$REGISTRY_PASSWORD" ]; then
        debug "跳过Docker登录，缺少注册表配置"
        return 0
    fi

    info "登录Docker注册表: $REGISTRY_SERVER"

    if [ "$DRY_RUN" = true ]; then
        info "[DRY RUN] 会执行: docker login $REGISTRY_SERVER"
        return 0
    fi

    if ! echo "$REGISTRY_PASSWORD" | docker login "$REGISTRY_SERVER" -u "$REGISTRY_USERNAME" --password-stdin; then
        error "Docker登录失败"
        return 1
    fi

    info "Docker登录成功"
}

# 拉取镜像
pull_image() {
    local image="$1"

    info "拉取镜像: $image"

    if [ "$DRY_RUN" = true ]; then
        info "[DRY RUN] 会执行: docker pull $image"
        return 0
    fi

    local max_retries=3
    local retry=0

    while [ $retry -lt $max_retries ]; do
        if docker pull "$image"; then
            info "镜像拉取成功"
            return 0
        fi

        retry=$((retry + 1))
        warn "镜像拉取失败，重试 $retry/$max_retries"
        sleep 5
    done

    error "镜像拉取失败，已达最大重试次数"
    return 1
}

# 创建docker-compose override文件
create_compose_override() {
    local image="$1"

    info "创建Docker Compose配置..."

    cat >"$COMPOSE_OVERRIDE" <<EOF
services:
  wecom-app:
    image: ${image}
    container_name: ${CONTAINER_NAME}
    environment:
      - DB_HOST=${MYSQL_HOST}
      - DB_PORT=${MYSQL_PORT}
      - DB_USER=${MYSQL_USER}
      - DB_PASSWORD=${MYSQL_PASSWORD}
      - DB_NAME=${MYSQL_DB}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - TZ=Asia/Shanghai
      - MODE=prod
      - PYTHONIOENCODING=utf-8
      - LANG=zh_CN.UTF-8
      - LC_ALL=zh_CN.UTF-8
      - LANGUAGE=zh_CN.UTF-8
    ports:
      - "${APP_PORT}:${APP_PORT}"
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - default
    # healthcheck配置已移除
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 256M

networks:
  default:
    name: wecom-bridge-network
EOF

    debug "Docker Compose配置文件创建完成"
}

# 健康检查功能已移除

# 手动健康检查功能已移除

# 备份当前部署
backup_current_deployment() {
    info "备份当前部署..."

    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"

    # 备份配置文件
    [ -f "$COMPOSE_OVERRIDE" ] && cp "$COMPOSE_OVERRIDE" "$backup_dir/"
    [ -f ".env" ] && cp ".env" "$backup_dir/"

    # 备份容器信息
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        docker inspect "$CONTAINER_NAME" >"$backup_dir/container_info.json"
    fi

    info "备份完成: $backup_dir"
    echo "$backup_dir" >.last_backup
}

# 回滚到上一个版本
rollback() {
    info "执行回滚操作..."

    if [ ! -f ".last_backup" ]; then
        error "未找到备份信息，无法回滚"
        return 1
    fi

    local backup_dir
    backup_dir=$(cat .last_backup)

    if [ ! -d "$backup_dir" ]; then
        error "备份目录不存在: $backup_dir"
        return 1
    fi

    # 停止当前服务
    docker-compose down

    # 恢复配置文件
    [ -f "$backup_dir/$COMPOSE_OVERRIDE" ] && cp "$backup_dir/$COMPOSE_OVERRIDE" .
    [ -f "$backup_dir/.env" ] && cp "$backup_dir/.env" .

    # 启动服务
    docker-compose up -d

    info "回滚完成"
}

# 连接到外部网络
connect_to_network() {
    local network_name="$1"

    info "连接容器到网络: $network_name"

    # 检查网络是否存在
    debug "检查网络是否存在: docker network inspect '$network_name'"
    if ! docker network inspect "$network_name" >/dev/null 2>&1; then
        debug "网络列表:"
        debug "$(docker network ls)"
        error "网络不存在: $network_name"
        return 1
    fi
    debug "网络存在，继续连接"

    # 检查容器是否存在
    debug "检查容器是否在运行: docker ps | grep '$CONTAINER_NAME'"
    local container_line
    container_line=$(docker ps | grep "$CONTAINER_NAME" || true)
    debug "容器查找结果: '$container_line'"

    if [ -z "$container_line" ]; then
        warn "容器不存在或未运行: $CONTAINER_NAME, 跳过网络连接"
        return 0
    fi
    debug "容器正在运行，继续连接"

    # 连接网络
    if docker network connect "$network_name" "$CONTAINER_NAME" 2>/dev/null; then
        info "容器已连接到网络: $network_name"
    else
        warn "容器可能已连接到该网络"
    fi

    # 显示网络信息
    info "容器网络配置:"
    docker inspect -f '{{range $k, $v := .NetworkSettings.Networks}}{{$k}}: {{$v.IPAddress}}{{printf "\n"}}{{end}}' "$CONTAINER_NAME"
}

# 自动连接到配置的网络
auto_connect_network() {
    # 检查是否配置了自动连接网络
    if [ -z "$AUTO_CONNECT_NETWORK" ]; then
        debug "未配置自动连接网络，跳过网络连接"
        return 0
    fi

    info "自动连接到配置的网络: $AUTO_CONNECT_NETWORK"

    # 检查网络是否存在
    debug "检查网络是否存在: docker network inspect '$AUTO_CONNECT_NETWORK'"
    if ! docker network inspect "$AUTO_CONNECT_NETWORK" >/dev/null 2>&1; then
        debug "网络列表:"
        debug "$(docker network ls)"
        warn "自动连接网络不存在: $AUTO_CONNECT_NETWORK, 尝试智能网络连接"
        smart_network_connect
        return $?
    fi
    debug "网络存在，继续连接"

    # 检查容器是否存在
    debug "检查容器是否在运行: docker ps | grep '$CONTAINER_NAME'"
    local container_line
    container_line=$(docker ps | grep "$CONTAINER_NAME" || true)
    debug "容器查找结果: '$container_line'"

    if [ -z "$container_line" ]; then
        warn "容器不存在或未运行: $CONTAINER_NAME, 跳过网络连接"
        return 0
    fi
    debug "容器正在运行，继续连接"

    # 连接网络（使用容错处理，避免影响主流程）
    if docker network connect "$AUTO_CONNECT_NETWORK" "$CONTAINER_NAME" 2>/dev/null; then
        info "容器已自动连接到网络: $AUTO_CONNECT_NETWORK"
        # 显示网络信息
        info "容器网络配置:"
        docker inspect -f '{{range $k, $v := .NetworkSettings.Networks}}{{$k}}: {{$v.IPAddress}}{{printf "\n"}}{{end}}' "$CONTAINER_NAME"
        return 0
    else
        warn "容器可能已连接到网络: $AUTO_CONNECT_NETWORK, 或连接失败"
        return 1
    fi
}

# 智能网络连接 - 自动发现并连接到包含 MySQL 和 Redis 的网络
smart_network_connect() {
    info "执行智能网络连接..."

    # 检查容器是否存在
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        warn "容器不存在或未运行: $CONTAINER_NAME"
        return 1
    fi

    # 查找 MySQL 容器所在的网络
    local mysql_networks=""
    if docker ps | grep -q "shared-mysql"; then
        mysql_networks=$(docker inspect shared-mysql --format='{{range $k, $v := .NetworkSettings.Networks}}{{$k}} {{end}}' 2>/dev/null || true)
        debug "MySQL 容器网络: $mysql_networks"
    fi

    # 查找 Redis 容器所在的网络
    local redis_networks=""
    if docker ps | grep -q "shared-redis"; then
        redis_networks=$(docker inspect shared-redis --format='{{range $k, $v := .NetworkSettings.Networks}}{{$k}} {{end}}' 2>/dev/null || true)
        debug "Redis 容器网络: $redis_networks"
    fi

    # 找到共同网络
    local target_network=""
    for mysql_net in $mysql_networks; do
        for redis_net in $redis_networks; do
            if [ "$mysql_net" = "$redis_net" ] && [ "$mysql_net" != "bridge" ]; then
                target_network="$mysql_net"
                break 2
            fi
        done
    done

    if [ -n "$target_network" ]; then
        info "发现目标网络: $target_network (包含 MySQL 和 Redis)"
        if docker network connect "$target_network" "$CONTAINER_NAME" 2>/dev/null; then
            info "容器已连接到网络: $target_network"
            # 显示网络信息
            info "容器网络配置:"
            docker inspect -f '{{range $k, $v := .NetworkSettings.Networks}}{{$k}}: {{$v.IPAddress}}{{printf "\n"}}{{end}}' "$CONTAINER_NAME"
            return 0
        else
            warn "连接到网络失败或容器已连接: $target_network"
        fi
    else
        warn "未找到包含 MySQL 和 Redis 的共同网络"
        info "可用网络列表:"
        docker network ls

        # 尝试连接到第一个非 bridge 的 MySQL 网络
        for net in $mysql_networks; do
            if [ "$net" != "bridge" ]; then
                info "尝试连接到 MySQL 网络: $net"
                if docker network connect "$net" "$CONTAINER_NAME" 2>/dev/null; then
                    info "容器已连接到网络: $net"
                    return 0
                fi
            fi
        done
    fi

    return 1
}

# 显示服务状态
show_status() {
    info "服务状态信息:"

    echo -e "\n${GREEN}=== Docker Compose 服务 ===${NC}"
    docker-compose ps

    echo -e "\n${GREEN}=== 容器日志 (最近10行) ===${NC}"
    docker-compose logs --tail=10

    echo -e "\n${GREEN}=== 资源使用情况 ===${NC}"
    docker stats --no-stream "$CONTAINER_NAME" 2>/dev/null || true
}

# 启动服务
start_service() {
    local image="${1:-$DEFAULT_IMAGE}"

    # 如果没有指定镜像且配置了私有仓库，自动使用私有仓库的完整地址
    if [ "$image" = "$DEFAULT_IMAGE" ] && [ -n "$REGISTRY_SERVER" ]; then
        image="$REGISTRY_SERVER/$DEFAULT_IMAGE"
        info "自动使用私有仓库镜像: $image"
    fi

    info "启动应用服务，使用镜像: $image"

    # 备份当前部署
    backup_current_deployment

    # 准备环境
    ensure_directories
    create_env_file

    # 如果镜像包含注册表地址（包含域名或IP），先拉取
    if echo "$image" | grep -q '/' && echo "$image" | grep -q '\.' || echo "$image" | grep -q '^[0-9]'; then
        docker_login
        pull_image "$image"
    fi

    # 创建compose配置
    create_compose_override "$image"

    # 启动服务
    info "启动Docker Compose服务..."
    if [ "$DRY_RUN" = true ]; then
        info "[DRY RUN] 会执行: docker-compose down && docker-compose up -d"
    else
        docker-compose down
        docker-compose up -d
    fi

    # 健康检查和网络连接
    if [ "$DRY_RUN" = false ]; then
        # 先尝试自动连接网络，如果失败则进行智能连接
        info "检查并修复网络连接..."
        if ! auto_connect_network; then
            warn "自动网络连接失败，尝试智能网络连接..."
            smart_network_connect
        fi

        # 重启容器以确保网络连接生效
        info "重启容器以确保网络连接生效..."
        docker restart "$CONTAINER_NAME"
        sleep 5

        show_status
    fi

    info "应用服务启动完成"
}

# 停止服务
stop_service() {
    info "停止应用服务..."

    if [ "$DRY_RUN" = true ]; then
        info "[DRY RUN] 会执行: docker-compose down"
    else
        docker-compose down
    fi

    info "应用服务已停止"
}

# 重启服务
restart_service() {
    info "重启应用服务..."

    if [ "$DRY_RUN" = true ]; then
        info "[DRY RUN] 会执行: docker-compose restart"
    else
        docker-compose restart

        # 检查并修复网络连接
        info "检查并修复网络连接..."
        if ! auto_connect_network; then
            warn "自动网络连接失败，尝试智能网络连接..."
            smart_network_connect
        fi

        show_status
    fi

    info "应用服务已重启"
}

# 查看日志
show_logs() {
    local lines="${1:-50}"
    local follow="${2:-false}"

    info "显示应用服务日志 (最近 $lines 行)..."

    if [ "$follow" = true ]; then
        docker-compose logs -f --tail="$lines"
    else
        docker-compose logs --tail="$lines"
    fi
}

# 显示帮助信息
show_help() {
    cat <<EOF
${GREEN}$SCRIPT_NAME v$SCRIPT_VERSION${NC}

用法: $0 [选项] <命令> [参数]

${YELLOW}选项:${NC}
  -v, --verbose     详细输出模式
  -n, --dry-run     模拟执行模式（不实际执行操作）
  -h, --help        显示帮助信息
  --version         显示版本信息

${YELLOW}命令:${NC}
  ${GREEN}init${NC}                    初始化配置文件
  ${GREEN}start [镜像]${NC}            启动服务 (可选指定镜像)
  ${GREEN}stop${NC}                    停止服务
  ${GREEN}restart${NC}                 重启服务
  ${GREEN}status${NC}                  显示服务状态
  ${GREEN}logs [行数] [follow]${NC}    查看日志
  ${GREEN}pull-and-start [镜像]${NC}   拉取镜像并启动服务
  ${GREEN}local [镜像]${NC}            拉取镜像并启动服务 (兼容别名，已废弃)
  ${GREEN}connect-network <网络名>${NC} 连接容器到指定网络
  ${GREEN}backup${NC}                  备份当前部署
  ${GREEN}rollback${NC}                回滚到上一个版本
  # 健康检查命令已移除

${YELLOW}示例:${NC}
  $0 init                                    # 初始化配置
  $0 start                                   # 使用默认镜像启动
  $0 start my-registry.com/wecom-app:v2.0   # 使用指定镜像启动
  $0 pull-and-start                          # 从注册表拉取并启动
  $0 logs 100 true                          # 显示最近100行日志并跟踪
  $0 connect-network deploy_default          # 连接到外部网络
  $0 --dry-run start                         # 模拟启动过程

${YELLOW}配置文件:${NC}
  $CONFIG_FILE                    # 主配置文件
  .env                             # 应用环境变量文件

${YELLOW}自动网络连接:${NC}
  start 和 restart 命令会自动检测并修复网络连接问题
  通过配置 AUTO_CONNECT_NETWORK 变量可以指定目标网络
  默认自动连接到 deploy_default 网络，如果不存在会智能查找包含 MySQL 和 Redis 的网络
EOF
}

# 显示版本信息
show_version() {
    echo "$SCRIPT_NAME v$SCRIPT_VERSION"
}

# 主函数
main() {
    # 解析命令行参数
    while [ $# -gt 0 ]; do
        case $1 in
        -v | --verbose)
            VERBOSE=true
            shift
            ;;
        -n | --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h | --help)
            show_help
            exit 0
            ;;
        --version)
            show_version
            exit 0
            ;;
        -*)
            error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            break
            ;;
        esac
    done

    # 获取命令
    local command="${1:-help}"

    # 初始化
    info "启动 $SCRIPT_NAME v$SCRIPT_VERSION"
    [ "$DRY_RUN" = true ] && warn "运行在模拟模式下"

    # 对于init命令，不需要检查环境
    if [ "$command" != "init" ] && [ "$command" != "help" ]; then
        check_prerequisites
        load_config
        validate_config
    fi

    # 执行命令
    case "$command" in
    init)
        create_sample_config
        ;;
    start)
        start_service "${2:-}"
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        local lines="${2:-50}"
        local follow="${3:-false}"
        show_logs "$lines" "$follow"
        ;;
    pull-and-start | local)
        if [ "$command" = "local" ]; then
            warn "命令 'local' 已废弃，请使用 'pull-and-start'，当前会自动转换"
        fi
        local image="${2:-}"
        if [ -z "$image" ]; then
            if [ -n "$REGISTRY_SERVER" ]; then
                image="$REGISTRY_SERVER/$DEFAULT_IMAGE"
            else
                image="$DEFAULT_IMAGE"
            fi
        fi
        start_service "$image"
        ;;
    connect-network)
        if [ -z "${2:-}" ]; then
            error "请指定网络名称"
            exit 1
        fi
        connect_to_network "$2"
        ;;
    backup)
        backup_current_deployment
        ;;
    rollback)
        rollback
        ;;
    # 健康检查命令已移除
    health | debug-health)
        warn "健康检查功能已被移除"
        ;;
    help | "")
        show_help
        ;;
    *)
        error "未知命令: $command"
        show_help
        exit 1
        ;;
    esac

    info "操作完成"
}

# 执行主函数
main "$@"
