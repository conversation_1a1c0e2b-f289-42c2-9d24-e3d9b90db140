#!/bin/bash

# 简化版企业微信命令桥接服务部署脚本 (优化 CentOS 生产环境)

# 颜色配置
GREEN=$'\033[0;32m'
YELLOW=$'\033[1;33m'
RED=$'\033[0;31m'
NC=$'\033[0m' # No Color

# 默认私有仓库镜像
DEFAULT_REGISTRY_IMAGE="127.0.0.1:5000/wecom-app:latest"
# 私有仓库配置信息
REGISTRY_SERVER="*************:5000"
REGISTRY_USERNAME="root"
REGISTRY_PASSWORD="123qq123"
# 默认本地镜像名称
LOCAL_IMAGE_NAME="wecom-app:local"

# MySQL和Redis配置
MYSQL_HOST="shared-mysql"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD="rootpassword"
MYSQL_DB="wecom_bridge"
REDIS_HOST="shared-redis"
REDIS_PORT="6379"

# 检查是否安装了Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: 未安装Docker，请先安装Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: 未安装Docker Compose，请先安装Docker Compose${NC}"
    exit 1
fi

# 显示帮助信息
show_help() {
    echo -e "${GREEN}企业微信命令桥接服务 - 生产部署脚本${NC}"
    echo
    echo -e "用法: ./simple-deploy.sh [命令]"
    echo
    echo -e "可用命令:"
    echo -e "  ${GREEN}start${NC}               启动应用服务 (默认使用私有仓库镜像)"
    echo -e "  ${GREEN}stop${NC}                停止应用服务"
    echo -e "  ${GREEN}restart${NC}             重启应用服务"
    echo -e "  ${GREEN}logs${NC}                查看应用服务日志"
    echo -e "  ${GREEN}local${NC}               从私有仓库拉取镜像并启动服务"
    echo -e "  ${GREEN}use-registry-image${NC}  使用指定的镜像仓库镜像启动服务"
    echo -e "  ${GREEN}connect-network${NC}     将应用容器连接到指定网络"
    echo
    echo -e "示例:"
    echo -e "  ./simple-deploy.sh start                             # 使用默认私有仓库镜像启动服务"
    echo -e "  ./simple-deploy.sh local                             # 从私有仓库拉取镜像并启动服务"
    echo -e "  ./simple-deploy.sh use-registry-image 127.0.0.1:5000/wecom-app:v1.2  # 使用指定的镜像启动"
    echo -e "  ./simple-deploy.sh connect-network deploy_default    # 将容器连接到指定网络"
}

# 确保目录存在
ensure_dirs() {
    echo -e "${YELLOW}确保必要的目录存在...${NC}"
    # 创建上传目录
    mkdir -p uploads
    # 创建日志目录
    mkdir -p logs
    
    # 检查.env文件是否存在
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}提示: 本地.env文件不存在，将创建基本配置。${NC}"
        cat > .env << EOF
# 应用基础配置
DEBUG=False
HOST=0.0.0.0
PORT=8000

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 数据库配置
DB_HOST=${MYSQL_HOST}
DB_PORT=${MYSQL_PORT}
DB_USER=${MYSQL_USER}
DB_PASSWORD=${MYSQL_PASSWORD}
DB_NAME=${MYSQL_DB}
REDIS_HOST=${REDIS_HOST}
REDIS_PORT=${REDIS_PORT}
EOF
        echo -e "${GREEN}已创建基本.env配置文件${NC}"
    else
        echo -e "${GREEN}检测到本地.env文件，将优先使用本地配置${NC}"
        
        # 检查.env文件中是否包含数据库配置，如果没有则添加
        if ! grep -q "DB_HOST" .env; then
            echo -e "${YELLOW}在.env文件中添加数据库配置...${NC}"
            cat >> .env << EOF

# 数据库配置
DB_HOST=${MYSQL_HOST}
DB_PORT=${MYSQL_PORT}
DB_USER=${MYSQL_USER}
DB_PASSWORD=${MYSQL_PASSWORD}
DB_NAME=${MYSQL_DB}
REDIS_HOST=${REDIS_HOST}
REDIS_PORT=${REDIS_PORT}
EOF
            echo -e "${GREEN}已添加数据库配置到.env文件${NC}"
        fi
    fi
}

# 连接容器到指定网络
connect_network() {
    if [ -z "$1" ]; then
        echo -e "${RED}错误: 未指定网络名称${NC}"
        echo -e "用法: ./simple-deploy.sh connect-network <网络名称>"
        exit 1
    fi
    
    NETWORK_NAME="$1"
    CONTAINER_NAME="wecom-bridge-app"
    
    # 检查容器是否存在
    if ! docker ps -a | grep -q "${CONTAINER_NAME}"; then
        echo -e "${RED}错误: 容器 ${CONTAINER_NAME} 不存在${NC}"
        exit 1
    fi
    
    # 检查网络是否存在
    if ! docker network ls | grep -q "${NETWORK_NAME}"; then
        echo -e "${RED}错误: 网络 ${NETWORK_NAME} 不存在${NC}"
        exit 1
    fi
    
    # 检查容器是否已连接到该网络
    if docker network inspect "${NETWORK_NAME}" | grep -q "${CONTAINER_NAME}"; then
        echo -e "${YELLOW}容器 ${CONTAINER_NAME} 已连接到网络 ${NETWORK_NAME}${NC}"
    else
        echo -e "${YELLOW}将容器 ${CONTAINER_NAME} 连接到网络 ${NETWORK_NAME}...${NC}"
        docker network connect "${NETWORK_NAME}" "${CONTAINER_NAME}"
        echo -e "${GREEN}容器已成功连接到网络${NC}"
    fi
    
    # 显示容器网络信息
    echo -e "${GREEN}容器网络配置:${NC}"
    docker inspect -f '{{range $k, $v := .NetworkSettings.Networks}}{{$k}}: {{$v.IPAddress}}{{printf "\n"}}{{end}}' "${CONTAINER_NAME}"
}

# 使用指定镜像启动服务
use_registry_image() {
    # 检查参数
    if [ -z "$1" ]; then
        echo -e "${YELLOW}未指定镜像，使用默认镜像: ${DEFAULT_REGISTRY_IMAGE}${NC}"
        REGISTRY_IMAGE="${DEFAULT_REGISTRY_IMAGE}"
    else
        REGISTRY_IMAGE="$1"
    fi
    
    echo -e "${YELLOW}使用指定镜像: ${REGISTRY_IMAGE}${NC}"
    
    # 检查docker-compose.yml
    if [ ! -f "docker-compose.yml" ]; then
        echo -e "${RED}错误: 未找到docker-compose.yml文件${NC}"
        exit 1
    fi
    
    # 删除之前的override文件
    if [ -f "docker-compose.override.yml" ]; then
        echo -e "${YELLOW}删除旧的docker-compose.override.yml文件${NC}"
        rm docker-compose.override.yml
    fi
    
    # 创建新的override文件来设置镜像和网络配置
    echo -e "${YELLOW}创建docker-compose.override.yml配置镜像...${NC}"
    cat > docker-compose.override.yml << EOF
services:
  wecom-app:
    image: ${REGISTRY_IMAGE}
    environment:
      - DB_HOST=${MYSQL_HOST}
      - DB_PORT=${MYSQL_PORT}
      - DB_USER=${MYSQL_USER}
      - DB_PASSWORD=${MYSQL_PASSWORD}
      - DB_NAME=${MYSQL_DB}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
    networks:
      - default

# 如果需要连接到外部网络，可以手动运行:
# ./simple-deploy.sh connect-network <网络名称>
EOF
    
    # 拉取镜像
    echo -e "${YELLOW}拉取镜像: ${REGISTRY_IMAGE}${NC}"
    if ! docker pull ${REGISTRY_IMAGE}; then
        echo -e "${RED}拉取镜像 ${REGISTRY_IMAGE} 失败。请检查镜像名称、仓库连接和权限。${NC}"
        exit 1
    fi
    
    # 确保必要目录存在
    ensure_dirs
    
    # 启动服务
    echo -e "${YELLOW}启动应用服务...${NC}"
    docker-compose down
    docker-compose up -d
    
    # 检查服务状态
    echo -e "${GREEN}应用服务已使用指定镜像启动，当前状态:${NC}"
    docker-compose ps
    
    # 提醒用户可能需要连接到其他网络
    echo -e "${YELLOW}提示: 如果应用需要连接到其他服务(如MySQL或Redis)所在的网络，请运行:${NC}"
    echo -e "${GREEN}./simple-deploy.sh connect-network <网络名称>${NC}"
    echo -e "${YELLOW}例如: ./simple-deploy.sh connect-network deploy_default${NC}"
}

# 从私有仓库拉取镜像并启动
pull_from_private_registry() {
    echo -e "${GREEN}从私有仓库拉取镜像...${NC}"
    
    # 登录私有仓库
    echo -e "${YELLOW}登录私有仓库: ${REGISTRY_SERVER}${NC}"
    if [ ! -z "${REGISTRY_USERNAME}" ] && [ ! -z "${REGISTRY_PASSWORD}" ]; then
        if ! docker login ${REGISTRY_SERVER} -u ${REGISTRY_USERNAME} -p ${REGISTRY_PASSWORD}; then
            echo -e "${RED}登录私有仓库失败。请检查仓库地址和登录凭证。${NC}"
            exit 1
        fi
    fi
    
    # 构建私有仓库镜像地址
    REGISTRY_IMAGE="${REGISTRY_SERVER}/wecom-app:latest"
    echo -e "${YELLOW}使用私有仓库镜像: ${REGISTRY_IMAGE}${NC}"
    
    # 使用指定镜像启动服务
    use_registry_image "${REGISTRY_IMAGE}"
}

# 主函数
main() {
    case "$1" in
        start)
            echo -e "${GREEN}启动应用服务...${NC}"
            # 使用默认镜像启动
            use_registry_image "${DEFAULT_REGISTRY_IMAGE}"
            ;;
            
        stop)
            echo -e "${GREEN}停止应用服务...${NC}"
            docker-compose down
            echo -e "${YELLOW}应用服务已停止${NC}"
            ;;
            
        restart)
            echo -e "${GREEN}重启应用服务...${NC}"
            docker-compose restart
            echo -e "${YELLOW}应用服务已重启${NC}"
            docker-compose ps
            ;;
            
        logs)
            echo -e "${GREEN}查看应用服务日志...${NC}"
            docker-compose logs -f
            ;;
            
        local)
            # 从私有仓库拉取镜像并启动
            pull_from_private_registry
            ;;
            
        use-registry-image)
            # 调用使用指定镜像的函数
            use_registry_image "$2"
            ;;
            
        connect-network)
            # 连接容器到指定网络
            connect_network "$2"
            ;;
            
        "" | help | -h | --help)
            show_help
            ;;
        *)
            echo -e "${RED}未知命令: $1${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 