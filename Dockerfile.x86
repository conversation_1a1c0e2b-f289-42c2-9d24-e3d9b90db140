FROM --platform=linux/amd64 python:3.10-slim-buster

WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码（包括static文件夹）
COPY . .

# 确保static文件夹被正确复制
RUN if [ -d "/app/app/static" ]; then       echo "static文件夹已成功复制到镜像中";       ls -la /app/app/static/;     else       echo "警告: static文件夹未找到";     fi

# 确保.env文件被正确复制到镜像中
COPY .env.docker /app/.env

# 添加提示信息表明.env和static已经包含在镜像中
RUN echo "环境配置文件(.env)和静态文件(static)已包含在镜像中" > /app/.env_included

# 启动命令
CMD ["python", "-m", "app"]
